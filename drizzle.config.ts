import { defineConfig } from 'drizzle-kit';
import * as dotenv from 'dotenv';

// .env.local 파일 로드
dotenv.config({ path: '.env.local' });

export default defineConfig({
  dialect: 'postgresql', // PostgreSQL 사용
  schema: './src/lib/db/schema.ts',
  out: './drizzle',
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },
  verbose: true,
  strict: false, // CHECK 제약조건 파싱 오류 우회
  introspect: {
    casing: 'camel',
  },
});
