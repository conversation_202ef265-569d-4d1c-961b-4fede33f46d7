# .github/workflows/pr-build-check.yml
name: PR Build Check

# PR이 생성될 때만 실행
on:
  pull_request:
    types: [opened]
    branches: [ main, develop ]

jobs:
  build-check:
    runs-on: ubuntu-latest

    steps:
      # 코드 체크아웃
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      # Node.js 설정
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18' # 프로젝트에 맞는 Node.js 버전으로 변경
          cache: 'npm'

      # 의존성 설치
      - name: Install dependencies
        run: npm ci

      # 빌드 실행
      - name: Run build
        run: npm run build