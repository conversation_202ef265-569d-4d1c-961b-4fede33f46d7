-- RLS Policies for Partners Table
-- File: database/partners_rls_policies.sql
-- Description: Row Level Security policies for partners table

-- Enable RLS on partners table
ALTER TABLE partners ENABLE ROW LEVEL SECURITY;

-- INSERT policy: Users can only create partner records for themselves
CREATE POLICY "partners_insert_policy" ON partners
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- SELECT policy: Users can only view their own partner records
CREATE POLICY "partners_select_policy" ON partners
  FOR SELECT USING (auth.uid() = user_id);

-- UPDATE policy: Users can only update their own partner records
CREATE POLICY "partners_update_policy" ON partners
  FOR UPDATE USING (auth.uid() = user_id);

-- Admin policy: Admin users can view all partner records
-- This policy allows admin users (defined in ADMIN_EMAILS env var) to access all records
CREATE POLICY "partners_admin_all_policy" ON partners
  FOR ALL USING (
    auth.jwt() ->> 'email' = ANY(string_to_array(current_setting('app.admin_emails', true), ','))
  );

-- Note: Run this SQL in Supabase Dashboard → SQL Editor
-- Make sure to set app.admin_emails setting if using admin policy:
-- ALTER DATABASE postgres SET app.admin_emails = '<EMAIL>,<EMAIL>,<EMAIL>';