-- ===== 강사 전문분야 테이블 =====

/**
 * 강사 전문분야 테이블
 * 
 * @description 
 * - 강사가 가르칠 수 있는 운동 분야와 각 분야별 경력을 저장
 * - 온보딩 2단계 정보 (N개 선택 가능)
 * - 한 강사가 여러 전문분야를 가질 수 있음 (1:N 관계)
 * 
 * @relationships
 * - instructors.id → instructor_specialties.instructor_id (1:N)
 * 
 * @business_rules
 * - 같은 강사가 같은 전문분야를 중복 등록할 수 없음
 * - 클래스 검색 시 이 테이블을 통해 필터링
 * 
 * @example_data
 * - 강사A: YOGA(5년), PILATES(3년)
 * - 강사B: FITNESS(10년), CROSSFIT(7년), BOXING(2년)
 */
CREATE TABLE instructor_specialties (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  instructor_id UUID NOT NULL,           -- instructors.id 참조 (FK 제약 없음)
  specialty specialty NOT NULL,
  experience_years INTEGER NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 인덱스 생성
CREATE INDEX idx_instructor_specialties_instructor_id ON instructor_specialties(instructor_id);
CREATE INDEX idx_instructor_specialties_specialty ON instructor_specialties(specialty);
CREATE INDEX idx_instructor_specialties_experience ON instructor_specialties(experience_years);

-- 중복 전문분야 방지 (같은 강사가 같은 전문분야 중복 등록 불가)
CREATE UNIQUE INDEX idx_instructor_specialties_unique 
  ON instructor_specialties(instructor_id, specialty);

-- 경력 년수 제약 (0년 이상 50년 이하)
ALTER TABLE instructor_specialties 
  ADD CONSTRAINT chk_experience_years 
  CHECK (experience_years >= 0 AND experience_years <= 50);