-- ===== Supabase 마이그레이션: 스케줄 그룹 시스템 =====

-- 1. class_schedule_groups 테이블 생성
CREATE TABLE class_schedule_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_template_id UUID NOT NULL,
  group_name TEXT NOT NULL,
  sessions_per_week INTEGER NOT NULL CHECK (sessions_per_week >= 1 AND sessions_per_week <= 7),
  max_group_size INTEGER NOT NULL CHECK (max_group_size > 0),
  group_type TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. 기존 class_schedules 테이블 백업 (선택사항)
-- CREATE TABLE class_schedules_backup AS SELECT * FROM class_schedules;

-- 3. class_schedules 테이블 구조 변경
-- 3-1. 새로운 컬럼 추가
ALTER TABLE class_schedules 
ADD COLUMN schedule_group_id UUID,
ADD COLUMN day_of_week TEXT;

-- 3-2. 기존 데이터 마이그레이션이 필요한 경우 여기서 처리
-- (기존 데이터가 있다면 수동으로 마이그레이션 스크립트 작성 필요)

-- 3-3. 기존 컬럼 제거
ALTER TABLE class_schedules 
DROP COLUMN IF EXISTS class_template_id,
DROP COLUMN IF EXISTS title,
DROP COLUMN IF EXISTS days_of_week,
DROP COLUMN IF EXISTS start_date,
DROP COLUMN IF EXISTS end_date;

-- 3-4. class_templates 테이블에서 session_count 컬럼 제거
ALTER TABLE class_templates 
DROP COLUMN IF EXISTS session_count;

-- 3-5. NOT NULL 제약 조건 추가
ALTER TABLE class_schedules 
ALTER COLUMN schedule_group_id SET NOT NULL,
ALTER COLUMN day_of_week SET NOT NULL;

-- 4. 트리거 함수 생성 (update_updated_at_column이 없는 경우)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 5. 업데이트 트리거 추가
CREATE TRIGGER update_class_schedule_groups_updated_at
  BEFORE UPDATE ON class_schedule_groups
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 6. 인덱스 추가 (성능 최적화)
CREATE INDEX idx_class_schedule_groups_class_template_id ON class_schedule_groups(class_template_id);
CREATE INDEX idx_class_schedules_schedule_group_id ON class_schedules(schedule_group_id);
CREATE INDEX idx_class_schedules_day_of_week ON class_schedules(day_of_week);

-- 7. 예시 데이터 삽입 (개발/테스트용)
-- 주의: 실제 class_template_id는 존재하는 값으로 변경 필요
/*
INSERT INTO class_schedule_groups (class_template_id, group_name, sessions_per_week, max_group_size, group_type) VALUES
('your-class-template-id-1', '월/목 수업', 2, 4, '4인 그룹 수업'),
('your-class-template-id-2', '화/금 수업', 2, 3, '3인 그룹 수업');

INSERT INTO class_schedules (schedule_group_id, day_of_week, start_time, end_time) VALUES
((SELECT id FROM class_schedule_groups WHERE group_name = '월/목 수업' LIMIT 1), 'MONDAY', '10:00', '10:50'),
((SELECT id FROM class_schedule_groups WHERE group_name = '월/목 수업' LIMIT 1), 'THURSDAY', '10:00', '10:50');
*/