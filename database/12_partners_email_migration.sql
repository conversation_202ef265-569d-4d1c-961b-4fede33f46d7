-- ===== Partners Email Column Migration =====
-- 
-- This migration adds the email column to the partners table
-- with proper handling of existing records.
-- 
-- Applied: 2025-01-24
-- Status: COMPLETED
-- 
-- Changes:
-- 1. Added email column (text, NOT NULL, UNIQUE)
-- 2. Updated existing records with placeholder emails
-- 3. Applied NOT NULL and UNIQUE constraints
-- 
-- NOTE: Existing records have placeholder emails that need to be 
-- updated with real email addresses from Supabase auth.users.

-- Step 1: Add email column as nullable
ALTER TABLE "partners" ADD COLUMN "email" text;

-- Step 2: Update existing records with placeholder emails
-- (This was done programmatically during migration)
-- Example pattern: partner_{user_id_without_hyphens}@placeholder.temp

-- Step 3: Make email column NOT NULL
ALTER TABLE "partners" ALTER COLUMN "email" SET NOT NULL;

-- Step 4: Add unique constraint
ALTER TABLE "partners" ADD CONSTRAINT "partners_email_unique" UNIQUE("email");

-- ===== Verification Queries =====
-- 
-- Check table structure:
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_schema = 'public' AND table_name = 'partners'
-- ORDER BY ordinal_position;
-- 
-- Check constraints:
-- SELECT conname, contype, pg_get_constraintdef(oid) 
-- FROM pg_constraint 
-- WHERE conrelid = 'public.partners'::regclass;
-- 
-- Check sample records:
-- SELECT id, contact_name, email, user_id 
-- FROM partners 
-- ORDER BY created_at 
-- LIMIT 5;