-- ===== 클래스 일정 테이블 (새 구조) =====

/**
 * 클래스 일정 테이블
 * 
 * @description 
 * - 스케줄 그룹에 속하는 개별 수업 일정 (요일별)
 * - 하나의 그룹이 여러 개의 개별 일정을 가짐 (주 2회면 2개 일정)
 * 
 * @relationships
 * - class_schedule_groups.id → class_schedules.schedule_group_id (N:1, FK 제약 없음)
 * - class_schedules.id ← class_enrollments.class_schedule_id (1:N)
 * 
 * @business_rules
 * - day_of_week는 단일 요일만 저장
 * - start_time < end_time 이어야 함
 * - 같은 그룹 내에서 중복 요일 불가
 */
CREATE TABLE class_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_group_id UUID NOT NULL,       -- class_schedule_groups.id 참조 (FK 제약 없음)
  day_of_week TEXT NOT NULL,             -- 단일 요일 (MONDAY, TUESDAY, WEDNESDAY, etc.)
  start_time TIME NOT NULL,              -- 수업 시작 시간 (예: '10:00')
  end_time TIME NOT NULL,                -- 수업 종료 시간 (예: '11:00')
  is_active BOOLEAN DEFAULT true,        -- 스케줄 활성 상태
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 업데이트 트리거: UPDATE 시 updated_at 자동 갱신
CREATE TRIGGER update_class_schedules_updated_at
  BEFORE UPDATE ON class_schedules
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 예시 데이터 (개발/테스트용)
-- 주의: 실제 schedule_group_id는 존재하는 값으로 업데이트 필요
INSERT INTO class_schedules (schedule_group_id, day_of_week, start_time, end_time) VALUES
-- 기초 체력 수업 - 월/목 수업 그룹
((SELECT id FROM class_schedule_groups WHERE group_name = '월/목 수업' LIMIT 1), 'MONDAY', '10:00', '10:50'),
((SELECT id FROM class_schedule_groups WHERE group_name = '월/목 수업' LIMIT 1), 'THURSDAY', '10:00', '10:50'),

-- 하타 요가 - 화/금 수업 그룹  
((SELECT id FROM class_schedule_groups WHERE group_name = '화/금 수업' LIMIT 1), 'TUESDAY', '10:00', '11:00'),
((SELECT id FROM class_schedule_groups WHERE group_name = '화/금 수업' LIMIT 1), 'FRIDAY', '10:00', '11:00'),

-- 필라테스 - 월/수/금 수업 그룹
((SELECT id FROM class_schedule_groups WHERE group_name = '월/수/금 수업' LIMIT 1), 'MONDAY', '10:00', '10:55'),
((SELECT id FROM class_schedule_groups WHERE group_name = '월/수/금 수업' LIMIT 1), 'WEDNESDAY', '10:00', '10:55'),
((SELECT id FROM class_schedule_groups WHERE group_name = '월/수/금 수업' LIMIT 1), 'FRIDAY', '10:00', '10:55');