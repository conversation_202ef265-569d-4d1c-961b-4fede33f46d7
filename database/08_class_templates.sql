-- ===== 클래스 템플릿 테이블 =====

/**
 * 클래스 템플릿 테이블
 * 
 * @description 
 * - '기초 체력 수업', '요가 입문' 등 하나의 상품 단위인 클래스의 기본 정보
 * - 강사가 등록하는 클래스의 템플릿 역할
 * - 실제 스케줄과는 분리된 클래스 메타데이터
 * 
 * @relationships
 * - studios.id → class_templates.studio_id (N:1, FK 제약 없음)
 * - instructors.id → class_templates.instructor_id (N:1, FK 제약 없음)
 * - class_templates.id ← class_schedule_groups.class_template_id (1:N)
 * - class_templates.id ← class_reviews.class_template_id (1:N)
 * 
 * @business_rules
 * - category와 specialty는 코드 레벨에서 enum으로 검증
 * - level은 'beginner', 'intermediate', 'advanced', 'all_levels' 중 하나
 * - max_capacity는 1 이상이어야 함
 * - price_per_session은 0 이상이어야 함
 */
CREATE TABLE class_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  studio_id UUID NOT NULL,               -- studios.id 참조 (FK 제약 없음)
  instructor_id UUID NOT NULL,           -- instructors.id 참조 (FK 제약 없음)
  title TEXT NOT NULL,                   -- 클래스 제목 (예: "기초 체력 수업")
  description TEXT,                      -- 상세한 수업 소개
  curriculum JSONB,                      -- 커리큘럼 정보 (JSON 형태로 유연하게)
  category TEXT NOT NULL,                -- 대분류: 'fitness', 'yoga', 'pilates', 'dance', 'martial_arts', 'wellness'
  specialty TEXT NOT NULL,               -- 소분류: 'PILATES', 'CROSSFIT', 'YOGA' 등 (기존 specialty 활용)
  level TEXT NOT NULL,                   -- 난이도: 'beginner', 'intermediate', 'advanced', 'all_levels'
  duration_minutes INTEGER NOT NULL CHECK (duration_minutes > 0),  -- 수업 시간 (분 단위)
  price_per_session DECIMAL(10, 2) NOT NULL CHECK (price_per_session >= 0),  -- 회당 가격
  max_capacity INTEGER NOT NULL CHECK (max_capacity > 0),  -- 최대 수용 인원
  is_active BOOLEAN DEFAULT true,        -- 클래스 활성 상태
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 업데이트 트리거: UPDATE 시 updated_at 자동 갱신
CREATE TRIGGER update_class_templates_updated_at
  BEFORE UPDATE ON class_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 예시 데이터 (개발/테스트용)
-- 주의: 실제 studio_id와 instructor_id는 존재하는 값으로 업데이트 필요
INSERT INTO class_templates (title, description, category, specialty, level, duration_minutes, price_per_session, max_capacity, curriculum, studio_id, instructor_id) VALUES
('운동 경험이 없는 분들을 위한 기초 체력 수업', 
 '안녕하세요! 5년차 재활 훈련 경력을 가진 코치입니다. 무릎 부상보다는 몸에 부담가지 않고 지속 가능한 운동을 알려드립니다.',
 'fitness', 'FITNESS', 'beginner', 50, 20000.00, 4,
 '{"goals": ["기초 체력 향상", "부상 예방"], "weeks": [{"week": 1, "focus": "몸 상태 체크 및 개인별 목표 설정"}, {"week": 2, "focus": "기본 호흡법과 스트레칭"}, {"week": 3, "focus": "가벼운 근력 운동 시작"}, {"week": 4, "focus": "진전된 기능 운동"}]}',
 (SELECT id FROM studios WHERE name = '강남 피트니스 센터' LIMIT 1),
 (SELECT id FROM instructors LIMIT 1)),

('초보자를 위한 하타 요가', 
 '요가를 처음 시작하는 분들을 위한 기초 하타 요가 수업입니다. 기본 자세와 호흡법부터 차근차근 배워보세요.',
 'yoga', 'YOGA', 'beginner', 60, 25000.00, 8,
 '{"style": "하타 요가", "focus": ["기본 자세", "호흡법", "명상"], "benefits": ["유연성 향상", "스트레스 해소", "집중력 개선"]}',
 (SELECT id FROM studios WHERE name = '선릉 요가 스튜디오' LIMIT 1),
 (SELECT id FROM instructors LIMIT 1)),

('소도구를 활용한 필라테스', 
 '소도구(볼, 밴드, 링)를 활용하여 더욱 효과적인 필라테스 운동을 경험해보세요. 코어 강화에 특화된 수업입니다.',
 'pilates', 'PILATES', 'intermediate', 55, 30000.00, 6,
 '{"equipment": ["필라테스 볼", "저항 밴드", "필라테스 링"], "focus": ["코어 강화", "자세 교정", "근력 향상"], "target": ["복근", "등근육", "골반저근"]}',
 (SELECT id FROM studios WHERE name = '논현 필라테스 스튜디오' LIMIT 1),
 (SELECT id FROM instructors LIMIT 1));