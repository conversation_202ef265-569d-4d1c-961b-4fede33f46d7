-- ===== 회원 관련 테이블 =====

/**
 * 회원 정보 테이블
 * 
 * @description 
 * - Supabase auth.users와 1:1 관계로 연결
 * - 모든 회원 유형(학생, 강사, 관리자)의 기본 정보를 저장
 * - 강사 온보딩 1단계 정보 포함 (name, phone)
 * 
 * @relationships
 * - auth.users.id → members.id (1:1, PK로 직접 연결)
 * - members.id ← instructors.member_id (1:1, 강사인 경우)
 * 
 * @business_rules
 * - id는 auth.users.id와 동일한 UUID 사용
 * - role에 따라 추가 테이블 연결 (INSTRUCTOR → instructors 테이블)
 * - status가 'ACTIVE'인 경우만 서비스 사용 가능
 */
CREATE TABLE members (
  id UUID PRIMARY KEY,                    -- auth.users.id와 동일한 UUID
  nickname TEXT,
  name TEXT,                              -- 온보딩 1단계: 실명
  phone TEXT,                             -- 온보딩 1단계: 전화번호
  gender gender,                          -- 온보딩 1단계: 성별
  birth_date DATE,
  role member_role NOT NULL DEFAULT 'STUDENT',
  status member_status DEFAULT 'ACTIVE',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 인덱스 생성
CREATE INDEX idx_members_role ON members(role);
CREATE INDEX idx_members_status ON members(status);
CREATE INDEX idx_members_created_at ON members(created_at);

-- 업데이트 트리거 (updated_at 자동 갱신)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_members_updated_at
  BEFORE UPDATE ON members
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();