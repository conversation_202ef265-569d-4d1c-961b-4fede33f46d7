-- ===== 클래스 일정 테이블 마이그레이션 =====

/**
 * 기존 class_schedules 테이블을 새로운 구조로 마이그레이션
 * 
 * 변경사항:
 * - class_template_id 제거 → schedule_group_id 추가
 * - title 제거
 * - days_of_week (배열) 제거 → day_of_week (단일) 추가  
 * - start_date, end_date 제거 (그룹 레벨에서 관리)
 */

-- 1. 기존 테이블 백업 (필요시)
-- CREATE TABLE class_schedules_backup AS SELECT * FROM class_schedules;

-- 2. 기존 테이블 구조 변경
-- 2-1. 새로운 컬럼 추가
ALTER TABLE class_schedules 
ADD COLUMN schedule_group_id UUID,
ADD COLUMN day_of_week TEXT;

-- 2-2. 기존 컬럼 제거 (데이터 마이그레이션 후)
-- ALTER TABLE class_schedules 
-- DROP COLUMN class_template_id,
-- DROP COLUMN title,
-- DROP COLUMN days_of_week,
-- DROP COLUMN start_date,
-- DROP COLUMN end_date;

-- 3. NOT NULL 제약 조건 추가 (데이터 마이그레이션 후)
-- ALTER TABLE class_schedules 
-- ALTER COLUMN schedule_group_id SET NOT NULL,
-- ALTER COLUMN day_of_week SET NOT NULL;

-- 4. 새로운 테이블 구조 (최종 목표)
/*
CREATE TABLE class_schedules_new (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_group_id UUID NOT NULL,     -- class_schedule_groups.id 참조 (FK 제약 없음)
  day_of_week TEXT NOT NULL,           -- 단일 요일 (MONDAY, TUESDAY, etc.)
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
*/

-- 업데이트 트리거는 기존과 동일하게 유지