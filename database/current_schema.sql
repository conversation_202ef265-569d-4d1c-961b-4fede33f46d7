-- ===== 현재 데이터베이스 스키마 (최신 버전) =====
-- 
-- 이 파일은 모든 테이블의 최신 상태를 반영합니다.
-- 스케줄 그룹 시스템과 session_count 제거가 반영되어 있습니다.
-- 

-- 1. 기본 ENUM 타입들
CREATE TYPE class_category AS ENUM ('fitness', 'yoga', 'pilates', 'dance', 'martial_arts', 'wellness');
CREATE TYPE class_level AS ENUM ('beginner', 'intermediate', 'advanced', 'all_levels');
CREATE TYPE studio_type AS ENUM ('personal', 'group', 'mixed');
CREATE TYPE enrollment_status AS ENUM ('pending', 'confirmed', 'cancelled');

-- 2. 강사 관련 테이블들 (기존 구조 유지)
CREATE TABLE instructors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL UNIQUE,
  bio TEXT,
  experience_years INTEGER DEFAULT 0,
  is_verified BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. 스튜디오 테이블 (기존 구조 유지)  
CREATE TABLE studios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  address TEXT NOT NULL,
  description TEXT,
  studio_type studio_type NOT NULL,
  phone TEXT,
  email TEXT,
  website TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. 클래스 템플릿 테이블 (session_count 제거됨)
CREATE TABLE class_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  studio_id UUID NOT NULL,               -- studios.id 참조 (FK 제약 없음)
  instructor_id UUID NOT NULL,           -- instructors.id 참조 (FK 제약 없음)
  title TEXT NOT NULL,                   -- 클래스 제목 (예: "기초 체력 수업")
  description TEXT,                      -- 상세한 수업 소개
  curriculum JSONB,                      -- 커리큘럼 정보 (JSON 형태로 유연하게)
  category TEXT NOT NULL,                -- 대분류: 'fitness', 'yoga', 'pilates', 'dance', 'martial_arts', 'wellness'
  specialty TEXT NOT NULL,               -- 소분류: 'PILATES', 'CROSSFIT', 'YOGA' 등 (기존 specialty 활용)
  level TEXT NOT NULL,                   -- 난이도: 'beginner', 'intermediate', 'advanced', 'all_levels'
  duration_minutes INTEGER NOT NULL CHECK (duration_minutes > 0),  -- 수업 시간 (분 단위)
  price_per_session DECIMAL(10, 2) NOT NULL CHECK (price_per_session >= 0),  -- 회당 가격
  max_capacity INTEGER NOT NULL CHECK (max_capacity > 0),  -- 최대 수용 인원
  is_active BOOLEAN DEFAULT true,        -- 클래스 활성 상태
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. 클래스 스케줄 그룹 테이블 (신규)
CREATE TABLE class_schedule_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_template_id UUID NOT NULL,                    -- class_templates.id 참조 (FK 제약 없음)
  group_name TEXT NOT NULL,                           -- "월/목 수업" 등 자동 생성된 이름
  sessions_per_week INTEGER NOT NULL CHECK (sessions_per_week >= 1 AND sessions_per_week <= 7), -- 주당 횟수
  max_group_size INTEGER NOT NULL CHECK (max_group_size > 0), -- 그룹 최대 인원
  group_type TEXT NOT NULL,                           -- "4인 그룹 수업", "개인 수업" 등 자동 생성
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. 클래스 스케줄 테이블 (schedule_group_id 참조로 변경)
CREATE TABLE class_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_group_id UUID NOT NULL,       -- class_schedule_groups.id 참조 (FK 제약 없음)
  day_of_week TEXT NOT NULL,             -- 단일 요일 (MONDAY, TUESDAY, WEDNESDAY, etc.)
  start_time TIME NOT NULL,              -- 수업 시작 시간 (예: '10:00')
  end_time TIME NOT NULL,                -- 수업 종료 시간 (예: '11:00')
  is_active BOOLEAN DEFAULT true,        -- 스케줄 활성 상태
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 7. 수강 신청 테이블 (기존 구조 유지)
CREATE TABLE class_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL,              -- members.id 참조 (FK 제약 없음)
  class_schedule_id UUID NOT NULL,      -- class_schedules.id 참조 (FK 제약 없음)
  status enrollment_status DEFAULT 'pending',
  paid_amount DECIMAL(10, 2),
  enrolled_at TIMESTAMPTZ DEFAULT NOW(),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. 후기 테이블 (기존 구조 유지)
CREATE TABLE class_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_template_id UUID NOT NULL,      -- class_templates.id 참조 (FK 제약 없음)
  member_id UUID NOT NULL,              -- members.id 참조 (FK 제약 없음)
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  comment TEXT,
  is_anonymous BOOLEAN DEFAULT false,
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===== 관계 요약 =====
-- 
-- class_templates (1) ←→ (N) class_schedule_groups
-- class_schedule_groups (1) ←→ (N) class_schedules  
-- class_schedules (1) ←→ (N) class_enrollments
-- class_templates (1) ←→ (N) class_reviews
-- 
-- 핵심 변경사항:
-- 1. class_templates에서 session_count 제거
-- 2. class_schedule_groups 테이블 신규 추가 
-- 3. class_schedules가 schedule_group_id 참조로 변경
-- 4. 그룹 기반 스케줄링 시스템으로 전환