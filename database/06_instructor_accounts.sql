-- ===== 강사 정산 계좌 테이블 =====

/**
 * 강사 정산 계좌 테이블
 * 
 * @description 
 * - 강사의 수익 정산을 위한 계좌 정보를 저장
 * - 온보딩 5단계 정보 (마지막 단계)
 * - 강사당 하나의 계좌만 등록 가능 (1:1 관계)
 * 
 * @relationships
 * - instructors.id → instructor_accounts.instructor_id (1:1)
 * 
 * @business_rules
 * - 온보딩 완료를 위해 반드시 등록되어야 함
 * - 보안상 계좌번호는 암호화하여 저장 권장
 * - 계좌 인증 완료 후에만 정산 가능
 * 
 * @security_notes
 * - account_number는 민감정보로 암호화 저장 필요
 * - 계좌 검증 API 연동 고려 (토스페이먼츠, 이니시스 등)
 * 
 * @example_data
 * - 강사A: 국민은행, 123-456-789012, 김강사, verified=true
 * - 강사B: 신한은행, 987-654-321098, 박트레이너, verified=false
 */
CREATE TABLE instructor_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  instructor_id UUID NOT NULL UNIQUE,    -- instructors.id 참조 (FK 제약 없음)
  bank_name TEXT NOT NULL,
  account_number TEXT NOT NULL,
  account_holder TEXT NOT NULL,
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 인덱스 생성
CREATE INDEX idx_instructor_accounts_instructor_id ON instructor_accounts(instructor_id);
CREATE INDEX idx_instructor_accounts_verified ON instructor_accounts(is_verified);
CREATE INDEX idx_instructor_accounts_bank ON instructor_accounts(bank_name);

-- 업데이트 트리거
CREATE TRIGGER update_instructor_accounts_updated_at
  BEFORE UPDATE ON instructor_accounts
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 계좌번호 형식 제약 (기본적인 한국 계좌번호 패턴)
ALTER TABLE instructor_accounts 
  ADD CONSTRAINT chk_account_number_format 
  CHECK (account_number ~ '^[0-9-]+$' AND LENGTH(account_number) >= 10);

-- 예금주명 제약 (한글, 영문만 허용)
ALTER TABLE instructor_accounts 
  ADD CONSTRAINT chk_account_holder_format 
  CHECK (account_holder ~ '^[가-힣A-Za-z\s]+$' AND LENGTH(account_holder) >= 2);