-- ===== 클래스 후기 테이블 =====

/**
 * 클래스 후기 테이블
 * 
 * @description 
 * - 클래스 또는 강사에 대한 후기와 평점을 관리
 * - 수강 완료 후 작성 가능한 리뷰 시스템
 * - 강사 평점 계산 및 클래스 추천 시스템의 기반 데이터
 * 
 * @relationships
 * - members.id → class_reviews.member_id (N:1, FK 제약 없음)
 * - class_templates.id → class_reviews.class_template_id (N:1, FK 제약 없음)
 * - instructors.id → class_reviews.instructor_id (N:1, FK 제약 없음)
 * 
 * @business_rules
 * - rating은 1~5 사이의 정수 값
 * - 수강 완료한 클래스에 대해서만 후기 작성 가능 (비즈니스 로직으로 제어)
 * - 한 명의 회원이 같은 클래스에 대해 여러 번 후기 작성 가능 (재수강 시)
 * - comment는 선택사항이지만 rating은 필수
 */
CREATE TABLE class_reviews
(
    id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    member_id         UUID    NOT NULL,                                     -- members.id 참조 (FK 제약 없음)
    class_template_id UUID    NOT NULL,                                     -- class_templates.id 참조 (FK 제약 없음)
    instructor_id     UUID    NOT NULL,                                     -- instructors.id 참조 (FK 제약 없음)
    rating            INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5), -- 1~5 별점
    comment           TEXT,                                                 -- 후기 내용 (선택사항)
    is_anonymous      BOOLEAN          DEFAULT false,                       -- 익명 후기 여부
    is_verified       BOOLEAN          DEFAULT false,                       -- 수강 확인된 후기 여부 (관리자 검증)
    helpful_count     INTEGER          DEFAULT 0,                           -- 도움이 됐어요 수 (향후 기능)
    created_at        TIMESTAMPTZ      DEFAULT NOW()                        -- 후기는 수정되지 않으므로 updated_at 불필요
);

-- 예시 데이터 (개발/테스트용)
-- 주의: 실제 member_id, class_template_id, instructor_id는 존재하는 값으로 업데이트 필요
INSERT INTO class_reviews (member_id, class_template_id, instructor_id, rating, comment, is_verified)
VALUES
-- 기초 체력 수업 후기들
((SELECT id FROM members WHERE role = 'STUDENT' LIMIT 1),
(SELECT id FROM class_templates WHERE title LIKE '%기초 체력%' LIMIT 1),
(SELECT id FROM instructors LIMIT 1), 5, '코치님이 정말 친절하고 운동을 처음 시작하는 저에게 맞춰서 잘 가르쳐주셨어요. 무릎 부상 이력이 있다고 말씀드렸더니 그에 맞는 운동으로 조정해주셔서 감사했습니다.', true),

((SELECT id FROM members WHERE role = 'STUDENT' OFFSET 1 LIMIT 1),
 (SELECT id FROM class_templates WHERE title LIKE '%기초 체력%' LIMIT 1),
 (SELECT id FROM instructors LIMIT 1),
 4, '운동 강도가 적절했고 설명도 자세했어요. 다만 시설이 조금 오래된 느낌이라 별 하나 뺐습니다.', true),

((SELECT id FROM members WHERE role = 'STUDENT' OFFSET 2 LIMIT 1),
 (SELECT id FROM class_templates WHERE title LIKE '%기초 체력%' LIMIT 1),
 (SELECT id FROM instructors LIMIT 1),
 5, '정말 만족스러운 수업이었어요! 운동을 꾸준히 할 수 있을 것 같습니다.', true),

-- 요가 수업 후기들
((SELECT id FROM members WHERE role = 'STUDENT' LIMIT 1),
 (SELECT id FROM class_templates WHERE title LIKE '%하타 요가%' LIMIT 1),
 (SELECT id FROM instructors LIMIT 1),
 5, '요가를 처음 배우는데도 차근차근 알려주셔서 좋았어요. 스튜디오 분위기도 정말 좋습니다.', true),

((SELECT id FROM members WHERE role = 'STUDENT' OFFSET 1 LIMIT 1),
 (SELECT id FROM class_templates WHERE title LIKE '%하타 요가%' LIMIT 1),
 (SELECT id FROM instructors LIMIT 1),
 4, '마음이 평온해지는 수업입니다. 스트레스 해소에 많은 도움이 됐어요.', true),

-- 필라테스 수업 후기들
((SELECT id FROM members WHERE role = 'STUDENT' OFFSET 2 LIMIT 1),
 (SELECT id FROM class_templates WHERE title LIKE '%필라테스%' LIMIT 1),
 (SELECT id FROM instructors LIMIT 1),
 5, '소도구를 활용한 수업이라 더 효과적인 것 같아요. 코어 근육이 많이 강화된 느낌입니다!', true),

((SELECT id FROM members WHERE role = 'STUDENT' LIMIT 1),
 (SELECT id FROM class_templates WHERE title LIKE '%필라테스%' LIMIT 1),
 (SELECT id FROM instructors LIMIT 1),
 4, '운동 강도가 적당하고 자세 교정도 세심하게 해주셔서 좋았습니다.', true);