-- ===== 강사 관련 테이블 =====

/**
 * 강사 정보 테이블
 * 
 * @description 
 * - members 테이블과 1:1 관계로 강사 전용 정보를 저장
 * - 온보딩 3단계 정보 포함 (short_bio, detailed_bio)
 * - 강사 활동 상태 및 평점 관리
 * 
 * @relationships
 * - members.id → instructors.member_id (1:1)
 * - instructors.id ← instructor_specialties.instructor_id (1:N)
 * - instructors.id ← instructor_certificates.instructor_id (1:N)
 * - instructors.id ← instructor_accounts.instructor_id (1:1)
 * 
 * @business_rules
 * - 레코드 존재 여부로 온보딩 완료 상태 판단
 * - is_active가 false인 경우 강사 활동 중단 상태
 * - rating은 리뷰 등록 시 자동 계산됨
 */
CREATE TABLE instructors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL UNIQUE,        -- members.id 참조 (FK 제약 없음)
  short_bio TEXT,                         -- 온보딩 3단계: 한줄 소개
  detailed_bio TEXT,                      -- 온보딩 3단계: 상세 설명
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 인덱스 생성
CREATE INDEX idx_instructors_member_id ON instructors(member_id);
CREATE INDEX idx_instructors_is_active ON instructors(is_active);
CREATE INDEX idx_instructors_created_at ON instructors(created_at);

-- 업데이트 트리거
CREATE TRIGGER update_instructors_updated_at
  BEFORE UPDATE ON instructors
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();