-- ===== 스튜디오/센터 테이블 =====

/**
 * 스튜디오 정보 테이블
 * 
 * @description 
 * - 피트니스 센터, 요가 스튜디오, 필라테스 스튜디오 등 모든 운동 공간 정보
 * - 확장성을 고려하여 다양한 운동 카테고리 지원
 * - 지리적 위치 정보와 편의시설 정보 포함
 * 
 * @relationships
 * - studios.id ← class_templates.studio_id (1:N)
 * 
 * @business_rules
 * - studio_type은 코드 레벨에서 enum으로 검증
 * - is_active가 false인 경우 새로운 클래스 등록 불가
 * - amenities와 links는 JSON 형태로 유연하게 관리
 */
CREATE TABLE studios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  address TEXT NOT NULL,
  studio_type TEXT NOT NULL,              -- 'fitness', 'yoga', 'pilates', 'dance', 'martial_arts', 'wellness'
  latitude DECIMAL(10, 8),                -- 위도 (지도 표시용)
  longitude DECIMAL(11, 8),               -- 경도 (지도 표시용)
  nearest_station TEXT,                   -- 근처 지하철역 (예: "강남역 2호선")
  amenities JSONB,                        -- 편의시설 목록 (샤워실, 주차장, 락커 등)
  links JSONB,                            -- 관련 링크 (네이버, 인스타그램, 홈페이지 등)
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 예시 데이터 (개발/테스트용)
INSERT INTO studios (name, description, address, studio_type, latitude, longitude, nearest_station, amenities, links) VALUES
('강남 피트니스 센터', '강남역 도보 5분 거리의 프리미엄 피트니스 센터', '서울시 강남구 강남대로 123', 'fitness', 37.4979, 127.0276, '강남역 2호선',
  '{"parking": true, "shower": true, "locker": true, "towel": true, "wifi": true}', 
  '{"naver": "https://place.naver.com/example", "instagram": "https://instagram.com/example"}'),
('선릉 요가 스튜디오', '조용하고 평화로운 요가 전문 스튜디오', '서울시 강남구 선릉로 456', 'yoga', 37.5044, 127.0489, '선릉역 2호선',
  '{"parking": false, "shower": true, "locker": true, "mat": true, "props": true}',
  '{"website": "https://example-yoga.com", "instagram": "https://instagram.com/example-yoga"}'),
('논현 필라테스 스튜디오', '소수정예 필라테스 전문 스튜디오', '서울시 강남구 논현로 789', 'pilates', 37.5103, 127.0284, '논현역 7호선',
  '{"parking": true, "shower": true, "locker": true, "equipment": true, "private_room": true}',
  '{"naver": "https://place.naver.com/pilates-example", "kakao": "https://place.map.kakao.com/example"}');