-- ===== 클래스 스케줄 그룹 테이블 =====

/**
 * 클래스 스케줄 그룹 테이블
 * 
 * @description 
 * - 주 2회, 주 3회 등 묶음 단위의 수업 스케줄 그룹을 관리
 * - "월/목 수업", "화/금 수업" 등 그룹별로 관리
 * - 그룹별 최대 인원 및 수업 타입 설정
 * 
 * @relationships
 * - class_templates.id → class_schedule_groups.class_template_id (N:1, FK 제약 없음)
 * - class_schedule_groups.id ← class_schedules.schedule_group_id (1:N)
 * 
 * @business_rules
 * - sessions_per_week와 실제 스케줄 개수가 일치해야 함
 * - max_group_size는 1 이상이어야 함
 */
CREATE TABLE class_schedule_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_template_id UUID NOT NULL,                    -- class_templates.id 참조 (FK 제약 없음)
  group_name TEXT NOT NULL,                           -- "월/목 수업"
  sessions_per_week INTEGER NOT NULL CHECK (sessions_per_week >= 1 AND sessions_per_week <= 7), -- 주당 횟수
  max_group_size INTEGER NOT NULL CHECK (max_group_size > 0), -- 그룹 최대 인원
  group_type TEXT NOT NULL,                           -- "4인 그룹 수업", "개인 수업" 등
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 업데이트 트리거: UPDATE 시 updated_at 자동 갱신
CREATE TRIGGER update_class_schedule_groups_updated_at
  BEFORE UPDATE ON class_schedule_groups
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 예시 데이터 (개발/테스트용)
-- 주의: 실제 class_template_id는 존재하는 값으로 업데이트 필요
INSERT INTO class_schedule_groups (class_template_id, group_name, sessions_per_week, max_group_size, group_type) VALUES
((SELECT id FROM class_templates WHERE title LIKE '%기초 체력%' LIMIT 1), 
 '월/목 수업', 2, 4, '4인 그룹 수업'),
((SELECT id FROM class_templates WHERE title LIKE '%하타 요가%' LIMIT 1), 
 '화/금 수업', 2, 3, '3인 그룹 수업'),
((SELECT id FROM class_templates WHERE title LIKE '%필라테스%' LIMIT 1), 
 '월/수/금 수업', 3, 6, '6인 그룹 수업');