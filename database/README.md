# 데이터베이스 스키마 관리

이 폴더는 ShallWe 프로젝트의 PostgreSQL 데이터베이스 스키마를 관리합니다.

## 파일 구조

### DDL 파일들 (실행 순서대로)
1. `01_enums.sql` - 열거형 타입 정의
2. `02_members.sql` - 회원 정보 테이블
3. `03_instructors.sql` - 강사 정보 테이블
4. `04_instructor_specialties.sql` - 강사 전문분야 테이블
5. `05_instructor_certificates.sql` - 강사 자격증 테이블
6. `06_instructor_accounts.sql` - 강사 정산 계좌 테이블

### 실행 방법

```bash
# Supabase SQL Editor에서 순서대로 실행
psql -h [HOST] -U [USER] -d [DATABASE] -f 01_enums.sql
psql -h [HOST] -U [USER] -d [DATABASE] -f 02_members.sql
psql -h [HOST] -U [USER] -d [DATABASE] -f 03_instructors.sql
psql -h [HOST] -U [USER] -d [DATABASE] -f 04_instructor_specialties.sql
psql -h [HOST] -U [USER] -d [DATABASE] -f 05_instructor_certificates.sql
psql -h [HOST] -U [USER] -d [DATABASE] -f 06_instructor_accounts.sql
```

## 테이블 관계

```
auth.users (Supabase)
    ↓ 1:1
members (회원 기본 정보)
    ↓ 1:1
instructors (강사 정보)
    ↓ 1:N
┌─── instructor_specialties (전문분야)
├─── instructor_certificates (자격증)
└─── instructor_accounts (정산 계좌) 1:1
```

## 온보딩 단계별 테이블 매핑

1. **1단계**: 기본 정보 → `members` 테이블
2. **2단계**: 전문분야 → `instructor_specialties` 테이블
3. **3단계**: 자기소개 → `instructors` 테이블 (short_bio, detailed_bio)
4. **4단계**: 자격증 → `instructor_certificates` 테이블
5. **5단계**: 정산계좌 → `instructor_accounts` 테이블

## 주요 설계 원칙

### 1. FK 제약 없음
- 애플리케이션 레벨에서 관계 관리
- 유연한 데이터 마이그레이션
- 성능 최적화

### 2. PostgreSQL ENUM 사용
- 타입 안정성
- 성능 최적화
- 데이터 무결성

### 3. 업데이트 트리거
- `updated_at` 자동 갱신
- 데이터 변경 이력 추적

### 4. 인덱스 최적화
- 검색 성능 향상
- 고유 제약 조건
- 복합 인덱스 활용

## 보안 고려사항

### 1. 민감정보 처리
- 계좌번호: 암호화 저장 권장
- 개인정보: 최소 수집 원칙

### 2. 접근 제어
- RLS (Row Level Security) 적용
- 역할 기반 접근 제어

### 3. 데이터 검증
- 제약 조건 활용
- 애플리케이션 레벨 검증

## 확장 계획

### 1. 센터 관리 시스템
- 센터 테이블 추가
- 강사-센터 매핑 테이블

### 2. 클래스 관리 시스템
- 클래스 테이블 추가
- 수강 신청 시스템

### 3. 결제 시스템
- 결제 정보 테이블
- 정산 관리 시스템

## 마이그레이션 가이드

### 기존 테이블 수정
```sql
-- bio 필드 분리
ALTER TABLE instructors 
  ADD COLUMN short_bio TEXT,
  ADD COLUMN detailed_bio TEXT;

-- 기존 bio 데이터 마이그레이션
UPDATE instructors 
  SET detailed_bio = bio 
  WHERE bio IS NOT NULL;

-- 기존 bio 필드 제거
ALTER TABLE instructors DROP COLUMN bio;
```

### 새로운 테이블 추가
모든 DDL 파일을 순서대로 실행하면 자동으로 생성됩니다.

## 백업 및 복구

### 백업
```bash
pg_dump -h [HOST] -U [USER] -d [DATABASE] --schema-only > schema_backup.sql
```

### 복구
```bash
psql -h [HOST] -U [USER] -d [DATABASE] -f schema_backup.sql
```