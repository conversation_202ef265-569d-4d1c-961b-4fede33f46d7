-- ===== 강사 자격증 테이블 =====

/**
 * 강사 자격증 테이블
 * 
 * @description 
 * - 강사가 보유한 자격증 정보를 저장
 * - 온보딩 4단계 정보 (자격증 등록)
 * - 한 강사가 여러 자격증을 가질 수 있음 (1:N 관계)
 * 
 * @relationships
 * - instructors.id → instructor_certificates.instructor_id (1:N)
 * 
 * @business_rules
 * - 같은 강사가 같은 자격증을 중복 등록할 수 없음
 * - 만료일이 있는 자격증은 유효성 체크 필요
 * - 이미지 업로드는 선택사항 (나중에 구현)
 * 
 * @example_data
 * - 강사A: 스포츠생활지도사 1급, 문화체육관광부, 2020-03-15
 * - 강사B: 요가얼라이언스 RYT-200, Yoga Alliance, 2019-06-10
 */
CREATE TABLE instructor_certificates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  instructor_id UUID NOT NULL,           -- instructors.id 참조 (FK 제약 없음)
  certificate_name TEXT NOT NULL,
  issuing_organization TEXT NOT NULL,
  issue_date DATE NOT NULL,
  expiry_date DATE,                       -- nullable
  certificate_number TEXT,                -- nullable
  image_url TEXT,                         -- nullable (나중에 사용)
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 인덱스 생성
CREATE INDEX idx_instructor_certificates_instructor_id ON instructor_certificates(instructor_id);
CREATE INDEX idx_instructor_certificates_name ON instructor_certificates(certificate_name);
CREATE INDEX idx_instructor_certificates_organization ON instructor_certificates(issuing_organization);
CREATE INDEX idx_instructor_certificates_issue_date ON instructor_certificates(issue_date);
CREATE INDEX idx_instructor_certificates_verified ON instructor_certificates(is_verified);

-- 업데이트 트리거
CREATE TRIGGER update_instructor_certificates_updated_at
  BEFORE UPDATE ON instructor_certificates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 중복 자격증 방지 (같은 강사가 같은 자격증 중복 등록 불가)
CREATE UNIQUE INDEX idx_instructor_certificates_unique 
  ON instructor_certificates(instructor_id, certificate_name, issuing_organization);

-- 날짜 제약 (만료일이 발급일보다 이후여야 함)
ALTER TABLE instructor_certificates 
  ADD CONSTRAINT chk_expiry_date 
  CHECK (expiry_date IS NULL OR expiry_date > issue_date);

-- 발급일 제약 (과거 날짜여야 함)
ALTER TABLE instructor_certificates 
  ADD CONSTRAINT chk_issue_date 
  CHECK (issue_date <= CURRENT_DATE);