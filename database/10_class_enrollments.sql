-- ===== 클래스 등록 테이블 =====

/**
 * 클래스 등록 테이블
 * 
 * @description 
 * - 회원이 특정 수업 일정을 등록/수강신청한 내역을 관리
 * - enrollment(등록) 개념으로 지속적인 수업 참여를 의미
 * - 결제 정보와 연동하여 수강료 관리
 * 
 * @relationships
 * - members.id → class_enrollments.member_id (N:1, FK 제약 없음)
 * - class_schedules.id → class_enrollments.class_schedule_id (N:1, FK 제약 없음)
 * 
 * @business_rules
 * - status는 'pending', 'confirmed', 'cancelled', 'completed', 'waitlisted' 중 하나
 * - 같은 member_id와 class_schedule_id 조합은 active 상태에서 중복 불가
 * - paid_amount는 0 이상이어야 함
 * - payment_id가 있으면 paid_amount도 있어야 함
 */
CREATE TABLE class_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL,              -- members.id 참조 (FK 제약 없음)
  class_schedule_id UUID NOT NULL,      -- class_schedules.id 참조 (FK 제약 없음)
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'confirmed', 'cancelled', 'completed', 'waitlisted'
  enrolled_at TIMESTAMPTZ DEFAULT NOW(), -- 등록 신청 시점
  payment_id TEXT,                      -- 결제 ID (외부 결제 시스템 연동)
  paid_amount DECIMAL(10, 2),           -- 실제 결제 금액
  notes TEXT,                           -- 특이사항 (요청사항, 주의사항 등)
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT payment_consistency CHECK (
    (payment_id IS NULL AND paid_amount IS NULL) OR 
    (payment_id IS NOT NULL AND paid_amount IS NOT NULL)
  )
);

-- 복합 인덱스: 중복 등록 방지 및 조회 성능 향상
CREATE UNIQUE INDEX idx_class_enrollments_member_schedule_active 
ON class_enrollments(member_id, class_schedule_id) 
WHERE status IN ('pending', 'confirmed', 'waitlisted');

-- 예시 데이터 (개발/테스트용)
-- 주의: 실제 member_id와 class_schedule_id는 존재하는 값으로 업데이트 필요
INSERT INTO class_enrollments (member_id, class_schedule_id, status, payment_id, paid_amount, notes) VALUES
-- 첫 번째 회원이 여러 수업에 등록
((SELECT id FROM members WHERE role = 'STUDENT' LIMIT 1),
 (SELECT id FROM class_schedules LIMIT 1),
 'confirmed', 'pay_20240116_001', 20000.00, '무릎 부상 이력 있음'),

((SELECT id FROM members WHERE role = 'STUDENT' LIMIT 1),
 (SELECT id FROM class_schedules OFFSET 1 LIMIT 1),
 'confirmed', 'pay_20240116_002', 25000.00, NULL),

-- 다른 회원들의 등록 현황
((SELECT id FROM members WHERE role = 'STUDENT' OFFSET 1 LIMIT 1),
 (SELECT id FROM class_schedules LIMIT 1),
 'pending', NULL, NULL, '체험 수업 신청'),

((SELECT id FROM members WHERE role = 'STUDENT' OFFSET 1 LIMIT 1),
 (SELECT id FROM class_schedules OFFSET 2 LIMIT 1),
 'waitlisted', NULL, NULL, '대기자 등록'),

-- 취소된 등록
((SELECT id FROM members WHERE role = 'STUDENT' OFFSET 2 LIMIT 1),
 (SELECT id FROM class_schedules OFFSET 1 LIMIT 1),
 'cancelled', NULL, NULL, '개인 사정으로 취소');