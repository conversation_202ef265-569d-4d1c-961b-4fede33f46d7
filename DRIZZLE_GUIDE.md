# Drizzle ORM 완전 가이드

이 문서는 ShallWe 프로젝트에서 Drizzle ORM을 효과적으로 사용하는 방법을 설명합니다.

## 🤔 왜 Drizzle ORM인가?

### Supabase + Drizzle의 시너지
- **Supabase**: 인프라, 인증, 실시간 기능 제공
- **Drizzle**: 타입 안전한 데이터베이스 쿼리 제공
- **조합의 이점**: 각각의 강점을 모두 활용

### 기존 방식 vs Drizzle ORM

#### 기존 Supabase 클라이언트 방식
```javascript
// 타입 안전성 부족
const { data, error } = await supabase
  .from('orders')
  .select('*, order_items(*, products(*))')
  .eq('user_id', userId);

// 런타임에만 오류 발견 가능
if (data) {
  const price = data[0].order_items[0].product.price; // 타입 에러 위험
}
```

#### Drizzle ORM 방식
```typescript
// 완전한 타입 안전성
const ordersWithItems = await db
  .select()
  .from(orders)
  .leftJoin(orderItems, eq(orders.id, orderItems.orderId))
  .leftJoin(products, eq(orderItems.productId, products.id))
  .where(eq(orders.userId, userId));

// 컴파일 타임에 타입 검증
const price = ordersWithItems[0].products?.price; // 타입 안전
```

## 🏗️ 프로젝트 구조

```
app/lib/db/
├── schema.ts           # 데이터베이스 스키마 정의
├── index.ts           # DB 클라이언트 설정
├── types.ts           # 사용자 정의 타입
├── queries/           # 재사용 가능한 쿼리
│   ├── users.ts
│   ├── products.ts
│   ├── orders.ts
│   └── payments.ts
└── migrations/        # Drizzle 마이그레이션 파일
    ├── 0000_initial.sql
    └── meta/
```

## 📋 스키마 정의 가이드

### 기본 테이블 정의
```typescript
// app/lib/db/schema.ts
import { pgTable, text, uuid, numeric, timestamp, bigint, integer } from 'drizzle-orm/pg-core';

export const products = pgTable('products', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  description: text('description'),
  price: numeric('price').notNull(),
  imageUrl: text('image_url'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
});
```

### 관계 정의 (Relations)
```typescript
import { relations } from 'drizzle-orm';

export const ordersRelations = relations(orders, ({ one, many }) => ({
  // 하나의 주문은 하나의 사용자에 속함
  user: one(users, { 
    fields: [orders.userId], 
    references: [users.id] 
  }),
  
  // 하나의 주문은 여러 주문 항목을 가짐
  orderItems: many(orderItems),
  
  // 하나의 주문은 여러 결제를 가질 수 있음
  payments: many(payments),
}));
```

### 타입 추출
```typescript
// app/lib/db/types.ts
import type { InferSelectModel, InferInsertModel } from 'drizzle-orm';
import { products, orders, orderItems } from './schema';

// Select 타입 (데이터베이스에서 읽어올 때)
export type Product = InferSelectModel<typeof products>;
export type Order = InferSelectModel<typeof orders>;
export type OrderItem = InferSelectModel<typeof orderItems>;

// Insert 타입 (데이터베이스에 삽입할 때)
export type NewProduct = InferInsertModel<typeof products>;
export type NewOrder = InferInsertModel<typeof orders>;
export type NewOrderItem = InferInsertModel<typeof orderItems>;

// 커스텀 타입 (조인 결과 등)
export type OrderWithItems = Order & {
  orderItems: (OrderItem & {
    product: Product;
  })[];
};
```

## 🔧 쿼리 작성 가이드

### 기본 CRUD 작업

#### Create (생성)
```typescript
// app/lib/db/queries/products.ts
import { db } from '../index';
import { products } from '../schema';
import type { NewProduct } from '../types';

export async function createProduct(data: NewProduct) {
  const [product] = await db
    .insert(products)
    .values(data)
    .returning();
  
  return product;
}

// 여러 개 생성
export async function createProducts(data: NewProduct[]) {
  return await db
    .insert(products)
    .values(data)
    .returning();
}
```

#### Read (읽기)
```typescript
import { eq, like, and, or, desc, asc } from 'drizzle-orm';

// 단일 상품 조회
export async function getProductById(id: string) {
  const [product] = await db
    .select()
    .from(products)
    .where(eq(products.id, id))
    .limit(1);
  
  return product;
}

// 검색 및 필터링
export async function searchProducts(query: string, limit = 10) {
  return await db
    .select()
    .from(products)
    .where(
      or(
        like(products.name, `%${query}%`),
        like(products.description, `%${query}%`)
      )
    )
    .orderBy(desc(products.createdAt))
    .limit(limit);
}

// 페이지네이션
export async function getProductsPaginated(page = 1, pageSize = 10) {
  const offset = (page - 1) * pageSize;
  
  return await db
    .select()
    .from(products)
    .orderBy(desc(products.createdAt))
    .limit(pageSize)
    .offset(offset);
}
```

#### Update (수정)
```typescript
export async function updateProduct(id: string, data: Partial<NewProduct>) {
  const [updatedProduct] = await db
    .update(products)
    .set({
      ...data,
      updatedAt: new Date(),
    })
    .where(eq(products.id, id))
    .returning();
  
  return updatedProduct;
}
```

#### Delete (삭제)
```typescript
export async function deleteProduct(id: string) {
  const [deletedProduct] = await db
    .delete(products)
    .where(eq(products.id, id))
    .returning();
  
  return deletedProduct;
}
```

### 복잡한 조인 쿼리

#### 주문과 주문 항목 조회
```typescript
// app/lib/db/queries/orders.ts
export async function getOrderWithItems(orderId: string) {
  return await db
    .select({
      // 주문 정보
      orderId: orders.id,
      orderStatus: orders.status,
      orderTotal: orders.totalAmount,
      orderCreatedAt: orders.createdAt,
      
      // 주문 항목 정보
      itemId: orderItems.id,
      itemQuantity: orderItems.quantity,
      itemPrice: orderItems.price,
      
      // 상품 정보
      productId: products.id,
      productName: products.name,
      productImageUrl: products.imageUrl,
    })
    .from(orders)
    .leftJoin(orderItems, eq(orders.id, orderItems.orderId))
    .leftJoin(products, eq(orderItems.productId, products.id))
    .where(eq(orders.id, orderId));
}

// 사용자의 모든 주문 조회
export async function getUserOrders(userId: string) {
  return await db
    .select()
    .from(orders)
    .where(eq(orders.userId, userId))
    .orderBy(desc(orders.createdAt));
}
```

### 트랜잭션 처리

#### 주문 생성 트랜잭션
```typescript
import { db } from '../index';

export async function createOrderWithItems(
  userId: string,
  items: { productId: string; quantity: number; price: string }[]
) {
  return await db.transaction(async (tx) => {
    // 1. 주문 생성
    const [order] = await tx
      .insert(orders)
      .values({
        userId,
        status: 'pending',
        totalAmount: items.reduce((sum, item) => 
          sum + parseFloat(item.price) * item.quantity, 0
        ).toString(),
      })
      .returning();

    // 2. 주문 항목 생성
    const orderItemsData = items.map(item => ({
      orderId: order.id,
      productId: item.productId,
      quantity: item.quantity,
      price: item.price,
    }));

    const createdOrderItems = await tx
      .insert(orderItems)
      .values(orderItemsData)
      .returning();

    return {
      order,
      orderItems: createdOrderItems,
    };
  });
}
```

## 🔄 Remix 통합

### Loader에서 데이터 조회
```typescript
// app/routes/products.$id.tsx
import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { getProductById } from '@/lib/db/queries/products';

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const productId = params.id!;
  
  const product = await getProductById(productId);
  
  if (!product) {
    throw new Response('Product not found', { status: 404 });
  }
  
  return json({ product });
};
```

### Action에서 데이터 수정
```typescript
// app/routes/products.new.tsx
import type { ActionFunctionArgs } from '@remix-run/node';
import { redirect } from '@remix-run/node';
import { createProduct } from '@/lib/db/queries/products';

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  
  const productData = {
    name: formData.get('name') as string,
    description: formData.get('description') as string,
    price: formData.get('price') as string,
    imageUrl: formData.get('imageUrl') as string,
  };
  
  try {
    const product = await createProduct(productData);
    return redirect(`/products/${product.id}`);
  } catch (error) {
    return json({ error: '상품 생성에 실패했습니다.' }, { status: 400 });
  }
};
```

## 🎯 모범 사례

### 1. 쿼리 함수 분리
```typescript
// ❌ 나쁜 예: 컴포넌트에서 직접 쿼리
export const loader = async () => {
  const products = await db.select().from(products);
  return json({ products });
};

// ✅ 좋은 예: 별도 함수로 분리
export const loader = async () => {
  const products = await getAllProducts();
  return json({ products });
};
```

### 2. 타입 활용
```typescript
// 함수 시그니처에 타입 명시
export async function createOrder(data: NewOrder): Promise<Order> {
  // 구현
}

// 반환 타입 명시로 타입 안전성 확보
export async function getOrdersByStatus(
  status: string
): Promise<OrderWithItems[]> {
  // 구현
}
```

### 3. 에러 처리
```typescript
export async function safeGetProduct(id: string) {
  try {
    const product = await getProductById(id);
    return { success: true, data: product };
  } catch (error) {
    console.error('Failed to fetch product:', error);
    return { success: false, error: error.message };
  }
}
```

### 4. 재사용 가능한 쿼리 빌더
```typescript
// 공통 필터 함수
export function withPagination<T>(
  query: T,
  page: number,
  pageSize: number
) {
  const offset = (page - 1) * pageSize;
  return query.limit(pageSize).offset(offset);
}

// 사용 예
export async function getProductsPaginated(page = 1, pageSize = 10) {
  const baseQuery = db.select().from(products);
  return await withPagination(baseQuery, page, pageSize);
}
```

## 🔧 마이그레이션 관리

### 스키마 변경 워크플로우
```bash
# 1. 스키마 파일 수정 (app/lib/db/schema.ts)

# 2. 마이그레이션 생성
npm run db:generate

# 3. 생성된 마이그레이션 검토
cat app/lib/db/migrations/0001_*.sql

# 4. 마이그레이션 적용
npm run db:push

# 5. 스키마 타입 업데이트 (자동)
# TypeScript가 자동으로 새로운 타입을 인식
```

### 마이그레이션 예시
```sql
-- 0001_add_category_to_products.sql
ALTER TABLE "products" ADD COLUMN "category" text;
ALTER TABLE "products" ADD COLUMN "featured" boolean DEFAULT false;
```

## 🐛 디버깅 및 모니터링

### 쿼리 로깅
```typescript
// drizzle.config.ts에서 verbose 모드 활성화
export default {
  // ... 기타 설정
  verbose: true, // SQL 쿼리 로그 출력
} satisfies Config;
```

### Drizzle Studio 활용
```bash
# 데이터베이스 브라우저 실행
npm run db:studio

# 브라우저에서 https://local.drizzle.studio 접속
# - 테이블 구조 시각화
# - 데이터 직접 편집
# - 쿼리 실행
```

### 성능 모니터링
```typescript
// 쿼리 실행 시간 측정
export async function timedQuery<T>(
  queryName: string,
  queryFn: () => Promise<T>
): Promise<T> {
  const start = Date.now();
  try {
    const result = await queryFn();
    const duration = Date.now() - start;
    console.log(`Query ${queryName} took ${duration}ms`);
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    console.error(`Query ${queryName} failed after ${duration}ms:`, error);
    throw error;
  }
}

// 사용 예
const products = await timedQuery('getAllProducts', () => 
  db.select().from(products).limit(10)
);
```

## 🚀 프로덕션 고려사항

### 연결 풀 관리
```typescript
// app/lib/db/index.ts
const connectionString = process.env.DATABASE_URL!;

const client = postgres(connectionString, {
  max: 10, // 최대 연결 수
  idle_timeout: 20, // 유휴 연결 타임아웃 (초)
  connect_timeout: 10, // 연결 타임아웃 (초)
});
```

### 환경별 설정
```typescript
// 개발/프로덕션 환경 분리
const isDevelopment = process.env.NODE_ENV === 'development';

const client = postgres(connectionString, {
  debug: isDevelopment, // 개발 환경에서만 디버그 로그
  max: isDevelopment ? 5 : 20, // 환경별 연결 풀 크기
});
```

## 📚 참고 자료

- [Drizzle ORM 공식 문서](https://orm.drizzle.team/)
- [Drizzle + Supabase 가이드](https://orm.drizzle.team/docs/get-started-postgresql#supabase)
- [PostgreSQL 데이터 타입](https://www.postgresql.org/docs/current/datatype.html)
- [Drizzle Relations 가이드](https://orm.drizzle.team/docs/rqb)

---

이 가이드를 통해 ShallWe 프로젝트에서 Drizzle ORM을 효과적으로 활용할 수 있습니다. 추가 질문이나 특정 사용 사례에 대한 도움이 필요하면 언제든 문의하세요! 🚀