# Dependencies
node_modules/
.pnpm-store/

# Production builds
.next/
out/
dist/
build/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Cache
.cache/
.parcel-cache/

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Package manager
package-lock.json
yarn.lock
pnpm-lock.yaml

# Generated files
*.tsbuildinfo
*.d.ts

# Storybook build outputs
storybook-static/

# Database
*.db
*.sqlite

# Temporary files
*.tmp
*.temp
