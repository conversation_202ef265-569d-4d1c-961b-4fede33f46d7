# ShallWe 플랫폼 통합 문서

## 📋 문서 개요

이 문서는 ShallWe 플랫폼의 모든 도메인, API, 기술 스택을 종합적으로 정리한 마스터 문서입니다.

**작성 날짜**: 2025년 1월 17일  
**분석 대상**: 실제 구현 코드 + 계획된 확장 기능  
**문서 목적**: 프로젝트의 현재 상태와 미래 방향성 종합 가이드

## 🎯 프로젝트 현황

### 구현 완료 상태
- ✅ **강사 온보딩 시스템**: 5단계 프로세스 완료
- ✅ **클래스 관리 시스템**: 템플릿, 스케줄 그룹 관리
- ✅ **대시보드**: 실시간 통계 및 현황 확인
- ✅ **API 인프라**: RESTful API 구조 완성
- ✅ **데이터베이스**: PostgreSQL + Drizzle ORM
- ✅ **인증 시스템**: Supabase Auth + RLS

### 계획된 확장 기능
- 🔄 **클래스 라이프사이클 관리**: 모집→진행→완료 단계 관리
- 🔄 **Occurrence 시스템**: 실제 수업 인스턴스 관리
- 🔄 **출석 관리**: 개별 수업별 출석 체크
- 🔄 **다중 시간대 지원**: 하나의 클래스에 여러 시간대

## 📚 문서 구성

### 1. 📋 [종합 도메인 명세서](./COMPREHENSIVE_DOMAIN_SPEC.md)
**대상**: 기획팀, 개발팀, 운영팀  
**내용**: 비즈니스 요구사항 및 도메인 정책

- **Members 도메인**: 회원 관리 및 온보딩
- **Instructors 도메인**: 강사 정보 및 자격 관리
- **Classes 도메인**: 클래스 템플릿, 스케줄, 수강 신청
- **Studios 도메인**: 스튜디오 정보 관리
- **비즈니스 정책**: 상태 전환, 환불 정책, 권한 관리
- **확장 계획**: Schedule-Occurrence 구조, 출석 시스템

### 2. 🔌 [API 문서](./API_DOCUMENTATION.md)
**대상**: 개발팀, 프론트엔드 개발자  
**내용**: 실제 구현된 API 명세 및 사용법

- **인증 API**: 로그인, 온보딩, 권한 관리
- **강사 API**: 프로필, 대시보드, 클래스 관리
- **클래스 API**: CRUD, 스케줄 그룹, 수강 신청
- **스튜디오 API**: 목록 조회, 상세 정보
- **에러 처리**: 표준 에러 응답 및 상태 코드
- **확장 API**: 계획된 Occurrence, 출석 API

### 3. 📱 [페이지 및 사용자 플로우](./PAGES_AND_USER_FLOWS.md)
**대상**: 디자인팀, 프론트엔드 개발자  
**내용**: 구현된 UI/UX 및 사용자 경험

- **구현된 페이지**: 온보딩, 대시보드, 클래스 관리
- **사용자 플로우**: 강사 여정, 클래스 생성 과정
- **디자인 시스템**: 컬러, 타이포그래피, 컴포넌트
- **기술 구현**: React, Tailwind CSS, 반응형 디자인
- **성능 최적화**: 코드 스플리팅, 이미지 최적화
- **접근성**: 키보드 네비게이션, 스크린 리더 지원

### 4. 🏗️ [기술 아키텍처](./TECHNICAL_ARCHITECTURE.md)
**대상**: 개발팀, 데브옵스팀, 기술 리더  
**내용**: 시스템 아키텍처 및 기술적 설계

- **시스템 아키텍처**: 전체 구조, 데이터 흐름
- **데이터베이스 설계**: 스키마, 인덱스, 성능 최적화
- **보안 아키텍처**: 인증, 권한, RLS 정책
- **성능 최적화**: 프론트엔드/백엔드 성능 전략
- **배포 및 운영**: Vercel 배포, 모니터링
- **마이그레이션**: 단계별 확장 계획

## 🔍 빠른 참조

### 주요 기술 스택
```typescript
{
  "frontend": {
    "framework": "Next.js 15",
    "ui": "React 19",
    "styling": "Tailwind CSS v4",
    "components": "Radix UI"
  },
  "backend": {
    "api": "Next.js API Routes",
    "database": "PostgreSQL (Supabase)",
    "orm": "Drizzle ORM",
    "auth": "Supabase Auth"
  },
  "devops": {
    "deployment": "Vercel",
    "monitoring": "Vercel Analytics",
    "ci": "GitHub Actions"
  }
}
```

### 핵심 도메인 엔티티
```mermaid
erDiagram
    MEMBERS {
        uuid id PK
        string name
        string phone
        string role
        string status
    }
    
    INSTRUCTORS {
        uuid id PK
        uuid member_id FK
        string bio
        boolean is_active
    }
    
    CLASS_TEMPLATES {
        uuid id PK
        uuid instructor_id FK
        uuid studio_id FK
        string title
        string category
        string level
        date recruitment_start_date
        date recruitment_end_date
        date class_start_date
        date class_end_date
        string status
    }
    
    CLASS_SCHEDULE_GROUPS {
        uuid id PK
        uuid class_template_id FK
        string group_name
        int max_participants
    }
    
    CLASS_SCHEDULES {
        uuid id PK
        uuid schedule_group_id FK
        string day_of_week
        time start_time
        time end_time
    }
    
    CLASS_ENROLLMENTS {
        uuid id PK
        uuid member_id FK
        uuid class_template_id FK
        uuid schedule_group_id FK
        string enrollment_status
        decimal paid_amount
    }
    
    MEMBERS ||--o{ INSTRUCTORS : "becomes"
    INSTRUCTORS ||--o{ CLASS_TEMPLATES : "creates"
    CLASS_TEMPLATES ||--o{ CLASS_SCHEDULE_GROUPS : "contains"
    CLASS_SCHEDULE_GROUPS ||--o{ CLASS_SCHEDULES : "has"
    CLASS_TEMPLATES ||--o{ CLASS_ENROLLMENTS : "receives"
    CLASS_SCHEDULE_GROUPS ||--o{ CLASS_ENROLLMENTS : "receives"
    MEMBERS ||--o{ CLASS_ENROLLMENTS : "makes"
```

### 주요 API 엔드포인트
```typescript
// 인증 및 온보딩
GET    /api/instructor/onboarding      // 온보딩 상태 확인
POST   /api/instructor/onboarding      // 온보딩 완료

// 강사 관리
GET    /api/instructor/profile         // 프로필 조회
PUT    /api/instructor/profile         // 프로필 수정
GET    /api/instructor/dashboard       // 대시보드 데이터

// 클래스 관리
GET    /api/instructor/classes         // 클래스 목록
POST   /api/instructor/classes         // 클래스 생성
GET    /api/instructor/classes/[id]    // 클래스 상세
PATCH  /api/instructor/classes/[id]    // 클래스 수정
DELETE /api/instructor/classes/[id]    // 클래스 삭제

// 스튜디오 관리
GET    /api/instructor/studios         // 스튜디오 목록
GET    /api/instructor/studios/[id]    // 스튜디오 상세

// 🔄 계획된 확장 API
GET    /api/instructor/classes/[id]/occurrences          // 실제 수업 목록
POST   /api/instructor/classes/[id]/occurrences          // 보강 수업 생성
PATCH  /api/instructor/classes/[id]/occurrences/[occ_id] // 수업 수정
```

## 📊 현재 vs 계획 비교

### 데이터 구조
| 현재 구현 | 계획된 확장 |
|-----------|-------------|
| `class_templates` | ✅ 구현 완료 |
| `class_schedule_groups` | ✅ 구현 완료 |
| `class_schedules` | ✅ 구현 완료 |
| `class_enrollments` | ✅ 구현 완료 |
| `class_occurrences` | 🔄 계획 중 |
| `class_attendances` | 🔄 계획 중 |

### 기능 구현
| 기능 | 현재 상태 | 계획 |
|------|-----------|------|
| 강사 온보딩 | ✅ 완료 | 자격증 이미지 업로드 |
| 클래스 생성 | ✅ 완료 | 라이프사이클 자동 관리 |
| 스케줄 관리 | ✅ 완료 | Occurrence 기반 관리 |
| 수강 신청 | ✅ 완료 | 출석 연동 |
| 대시보드 | ✅ 완료 | 실시간 통계 고도화 |

### 아키텍처 확장
```mermaid
graph TB
    subgraph "현재 구현"
        A[Template] --> B[Schedule Group]
        B --> C[Schedule]
        C --> D[Enrollment]
    end
    
    subgraph "계획된 확장"
        E[Template] --> F[Schedule Group]
        F --> G[Schedule]
        G --> H[Occurrence]
        H --> I[Attendance]
        E --> J[Enrollment]
        J --> I
    end
```

## 🚀 개발 가이드

### 로컬 개발 환경
```bash
# 프로젝트 클론
git clone https://github.com/your-org/shallwe.git
cd shallwe

# 의존성 설치
npm install

# 환경 변수 설정
cp .env.example .env.local
# .env.local에 Supabase 정보 입력

# 개발 서버 시작
npm run dev

# 타입 체크
npm run check-types

# 린트 및 포맷
npm run lint
npm run format
```

### 코드 규칙
- **TypeScript**: 모든 컴포넌트 및 유틸리티 타입 지정
- **ESLint**: 코드 품질 자동 검사
- **Prettier**: 코드 포맷팅 자동화
- **파일 구조**: 도메인별 폴더 구조 유지

### 데이터베이스 관리
```bash
# 스키마 변경 생성
npx drizzle-kit generate

# 스키마 적용 (Supabase Console에서 수동 실행)
# docs/schema.sql 참조
```

### 배포 프로세스
```bash
# 프로덕션 빌드
npm run build

# Vercel 배포
vercel deploy --prod

# 환경 변수 동기화
vercel env pull .env.local
```

## 🔮 미래 로드맵

### Phase 1: 현재 시스템 안정화 (1-2개월)
- [ ] 라이프사이클 필드 완전 활용
- [ ] 대시보드 V2 구현 (실시간 통계)
- [ ] 에러 처리 및 로깅 시스템 구축
- [ ] 모바일 최적화 완성

### Phase 2: 클래스 라이프사이클 시스템 (3-4개월)
- [ ] Occurrence 테이블 및 API 구현
- [ ] 출석 체크 시스템 구축
- [ ] 보강 수업 관리 기능
- [ ] 자동 상태 전환 시스템

### Phase 3: 고도화 및 확장 (5-6개월)
- [ ] 실시간 알림 시스템
- [ ] 고급 분석 대시보드
- [ ] 결제 시스템 통합
- [ ] 모바일 앱 개발

### Phase 4: 스케일링 (7-12개월)
- [ ] 마이크로서비스 아키텍처 전환
- [ ] 다중 지역 지원
- [ ] AI 기반 추천 시스템
- [ ] 파트너 API 개방

## 📞 문의 및 지원

### 개발팀
- **기술 문의**: 아키텍처, API 관련 질문
- **코드 리뷰**: Pull Request 검토
- **버그 리포트**: GitHub Issues 활용

### 기획팀
- **기능 요구사항**: 도메인 명세서 업데이트
- **UI/UX 피드백**: 사용자 플로우 개선
- **정책 변경**: 비즈니스 규칙 수정

### 운영팀
- **배포 요청**: Vercel 배포 관리
- **모니터링**: 성능 및 에러 추적
- **데이터 관리**: 백업 및 복구

---

**이 문서는 프로젝트의 현재 상태를 정확히 반영하며, 지속적으로 업데이트됩니다.**  
**마지막 업데이트**: 2025년 1월 17일