# 파트너 대시보드 구현 태스크 v1

## 개요

파트너 대시보드를 구현하기 위한 상세 태스크 목록과 구현 순서를 정의합니다. 강사 대시보드 UI 구조를 참조하여 파트너 특성에 맞게 조정된 기능을 구현합니다.

## 전체 구현 순서

```mermaid
graph TD
    A[타입 정의] --> B[UI 컴포넌트]
    B --> C[페이지 구현]
    C --> D[API 구현]
    D --> E[미들웨어 통합]
    E --> F[테스트]
    F --> G[배포]
```

## Phase 1: 타입 정의 및 스키마

### Task 1.1: TypeScript 타입 정의
- [ ] 파트너 대시보드 데이터 타입 정의
- [ ] 클래스 상태 및 통계 타입 정의
- [ ] 수업 일정 관련 타입 정의
- [ ] API 요청/응답 타입 정의

**파일 경로**: `src/types/partner-dashboard.ts`
**예상 소요시간**: 1시간
**담당자**: 개발자

```typescript
// 파트너 대시보드 데이터 타입
interface PartnerDashboardData {
  partner: {
    id: string;
    name: string;
    totalClasses: number;
    monthlyClasses: number;
  };
  classStats: {
    recruiting: number;
    ongoing: number;
    completed: number;
    upcoming: number;
  };
  monthlyStats: {
    totalClasses: number;
    totalAttendance: number;
    completionRate: number;
  };
  todayClasses: ClassOccurrence[];
  thisWeekClasses: ClassOccurrence[];
  nextWeekClasses: ClassOccurrence[];
}
```

### Task 1.2: 기존 타입 확장
- [ ] `src/types/partner.ts`에 대시보드 관련 타입 추가
- [ ] 클래스 및 수업 관련 타입 재사용성 검토
- [ ] 에러 타입 정의 추가

**파일 경로**: `src/types/partner.ts`
**예상 소요시간**: 30분
**담당자**: 개발자

## Phase 2: UI 컴포넌트 구현

### Task 2.1: 프로필 카드 컴포넌트
- [ ] `ProfileCard` 컴포넌트 구현
- [ ] 파트너 정보 표시 (이름, 아바타)
- [ ] 기본 통계 표시 (총 클래스, 이번 달 수업)
- [ ] 2x2 클래스 상태 그리드
- [ ] 3컬럼 월간 KPI 지표

**파일 경로**: 
- `src/components/partner/dashboard/ProfileCard.tsx`
- `src/components/partner/dashboard/ClassStatusGrid.tsx`
- `src/components/partner/dashboard/MonthlyStatsGrid.tsx`

**예상 소요시간**: 3시간
**담당자**: 개발자

### Task 2.2: 빠른 액션 그리드 컴포넌트
- [ ] `QuickActionGrid` 컴포넌트 구현
- [ ] 클래스 현황 버튼 (/partner/classes)
- [ ] 클래스 등록 버튼 (/partner/classes/new)
- [ ] 센터 관리 버튼 (/partner/studios)
- [ ] 강사 관리 버튼 (/partner/instructors)

**파일 경로**: 
- `src/components/partner/dashboard/QuickActionGrid.tsx`

**예상 소요시간**: 2시간
**담당자**: 개발자

### Task 2.3: 수업 일정 컴포넌트
- [ ] `TodayClasses` 컴포넌트 구현
- [ ] `WeeklySchedule` 컴포넌트 구현
- [ ] `EmptyState` 컴포넌트 구현
- [ ] 로딩 스켈레톤 UI
- [ ] 수업 상태별 색상 표시

**파일 경로**: 
- `src/components/partner/dashboard/TodayClasses.tsx`
- `src/components/partner/dashboard/WeeklySchedule.tsx`
- `src/components/partner/dashboard/EmptyState.tsx`

**예상 소요시간**: 4시간
**담당자**: 개발자

## Phase 3: 페이지 구현

### Task 3.1: 대시보드 레이아웃
- [ ] `/src/app/partner/dashboard/layout.tsx` 생성
- [ ] 파트너 전용 헤더 구성
- [ ] SEO 메타데이터 설정
- [ ] 하단 네비게이션 (모바일용)

**예상 소요시간**: 1시간
**담당자**: 개발자

### Task 3.2: 대시보드 메인 페이지
- [ ] `/src/app/partner/dashboard/page.tsx` 생성
- [ ] 모든 컴포넌트 통합
- [ ] 데이터 로딩 및 상태 관리
- [ ] 사용자 컨텍스트 활용
- [ ] 에러 처리 및 재시도 로직

**예상 소요시간**: 3시간
**담당자**: 개발자

### Task 3.3: 반응형 레이아웃 구현
- [ ] 모바일 (320px~) 단일 컬럼 스택
- [ ] 태블릿 (768px~) 2컬럼 그리드
- [ ] 데스크톱 (1024px~) 6컬럼 복합 그리드
- [ ] 브레이크포인트별 테스트

**예상 소요시간**: 2시간
**담당자**: 개발자

## Phase 4: API 구현

### Task 4.1: 대시보드 데이터 API
- [ ] `/api/partner/dashboard` 엔드포인트 생성
- [ ] JWT 토큰 인증 확인
- [ ] 파트너 권한 검증 
- [ ] 대시보드 데이터 조회 로직
- [ ] 에러 응답 처리

**파일 경로**: `src/app/api/partner/dashboard/route.ts`
**예상 소요시간**: 3시간
**담당자**: 개발자

### Task 4.2: 대시보드 데이터 처리 로직
- [ ] Supabase 클래스 데이터 조회
- [ ] 예약 현황 통계 계산
- [ ] 월간 KPI 지표 산출
- [ ] 수업 일정 데이터 가공
- [ ] 캐싱 전략 구현

**파일 경로**: `src/lib/api/partner/dashboard.ts`
**예상 소요시간**: 4시간
**담당자**: 개발자

### Task 4.3: Mock 데이터 작성
- [ ] 개발 환경용 Mock 데이터 생성
- [ ] 다양한 상태의 테스트 데이터
- [ ] 빈 상태 시나리오 데이터
- [ ] 에러 시나리오 데이터

**파일 경로**: `src/lib/mock/partner-dashboard.ts`
**예상 소요시간**: 1시간
**담당자**: 개발자

## Phase 5: 미들웨어 및 라우팅

### Task 5.1: 미들웨어 업데이트
- [ ] 파트너 대시보드 경로 보호 추가
- [ ] 파트너 상태(ACTIVE) 확인
- [ ] 비인증 시 로그인 페이지 리다이렉트
- [ ] 비활성 파트너 접근 차단

**파일 경로**: `src/middleware.ts`
**예상 소요시간**: 1시간
**담당자**: 개발자

### Task 5.2: URL 구조 최적화
- [ ] `/partner/dashboard` 경로 설정
- [ ] 빠른 액션 버튼 링크 검증
- [ ] 쿼리 파라미터 처리 (필터링용)
- [ ] 브라우저 히스토리 관리

**예상 소요시간**: 30분
**담당자**: 개발자

## Phase 6: 스타일링 및 반응형

### Task 6.1: 컴포넌트 스타일링
- [ ] Tailwind CSS 클래스 적용
- [ ] 강사 대시보드와 일관된 디자인
- [ ] 호버/포커스 효과 구현
- [ ] 다크 모드 고려 (미래 확장)

**예상 소요시간**: 2시간
**담당자**: 개발자

### Task 6.2: 모바일 최적화
- [ ] 터치 인터랙션 최적화
- [ ] 스와이프 제스처 구현
- [ ] 모바일 네비게이션 개선
- [ ] 가독성 향상 (폰트 크기, 간격)

**예상 소요시간**: 2시간
**담당자**: 개발자

### Task 6.3: 로딩 및 애니메이션
- [ ] 스켈레톤 UI 구현
- [ ] 데이터 로딩 애니메이션
- [ ] 페이지 전환 효과
- [ ] 마이크로 인터랙션 추가

**예상 소요시간**: 1.5시간
**담당자**: 개발자

## Phase 7: 테스트

### Task 7.1: 단위 테스트
- [ ] 대시보드 데이터 계산 로직 테스트
- [ ] API 함수 테스트
- [ ] 컴포넌트 렌더링 테스트
- [ ] 상태 관리 로직 테스트

**파일 경로**: `__tests__/partner/dashboard/`
**예상 소요시간**: 3시간
**담당자**: 개발자

### Task 7.2: 통합 테스트
- [ ] 대시보드 로딩 플로우 테스트
- [ ] 빠른 액션 네비게이션 테스트
- [ ] 에러 시나리오 테스트
- [ ] 권한 검증 테스트

**예상 소요시간**: 2시간
**담당자**: 개발자

### Task 7.3: E2E 테스트
- [ ] 파트너 로그인 → 대시보드 접근
- [ ] 대시보드 데이터 조회 및 표시
- [ ] 빠른 액션 버튼 클릭 플로우
- [ ] 모바일/데스크톱 크로스 브라우저 테스트

**예상 소요시간**: 2시간
**담당자**: 개발자

## Phase 8: 문서화 및 배포

### Task 8.1: 문서 업데이트
- [ ] API 문서 업데이트
- [ ] 컴포넌트 스토리북 작성
- [ ] README 업데이트
- [ ] 배포 가이드 작성

**예상 소요시간**: 1시간
**담당자**: 개발자

### Task 8.2: 코드 품질 검증
- [ ] TypeScript 타입 체크 (`tsc --noEmit`)
- [ ] ESLint 검사 (`npm run lint`)
- [ ] Prettier 포맷팅 (`npm run format`)
- [ ] 빌드 테스트 (`npm run build`)

**예상 소요시간**: 30분
**담당자**: 개발자

### Task 8.3: PR 및 배포
- [ ] PR 생성 및 상세 설명 작성
- [ ] 코드 리뷰 요청 및 피드백 반영
- [ ] 스테이징 환경 배포 및 테스트
- [ ] 프로덕션 배포

**예상 소요시간**: 1시간
**담당자**: 개발자

## 위험 요소 및 대응 방안

### 1. 기술적 위험
- **대시보드 데이터 복잡성**: 단계별 구현으로 복잡도 관리
- **성능 이슈**: 메모이제이션 및 지연 로딩 적용
- **강사 대시보드와의 일관성**: 공통 컴포넌트 추출 검토

### 2. 일정 위험
- **예상 지연 요소**: API 데이터 구조 변경, 디자인 피드백
- **대응 방안**: Mock 데이터 우선 개발, 단계별 검토 진행

### 3. 품질 위험
- **반응형 디자인 복잡성**: 브레이크포인트별 체계적 테스트
- **사용자 경험 일관성**: 강사 대시보드 UX 패턴 준수

## 총 예상 소요시간

| Phase | 예상 시간 | 비고 |
|-------|----------|------|
| Phase 1: 타입 정의 | 1.5시간 | |
| Phase 2: UI 컴포넌트 | 9시간 | **우선순위 높음** |
| Phase 3: 페이지 구현 | 6시간 | |
| Phase 4: API 구현 | 8시간 | |
| Phase 5: 미들웨어 | 1.5시간 | |
| Phase 6: 스타일링 | 5.5시간 | |
| Phase 7: 테스트 | 7시간 | |
| Phase 8: 문서화/배포 | 2.5시간 | |
| **총합** | **약 41시간** | 5-6일 소요 예상 |

## 완료 기준 (Definition of Done)

- [ ] 모든 기능 요구사항 구현 완료
- [ ] 강사 대시보드와 일관된 UI/UX
- [ ] 타입 체크 및 린트 통과
- [ ] 단위/통합/E2E 테스트 통과
- [ ] 모바일/태블릿/데스크톱 반응형 확인
- [ ] 접근성 기준 준수 (키보드 네비게이션)
- [ ] 코드 리뷰 완료
- [ ] 문서화 완료
- [ ] 스테이징 환경 테스트 완료

## 체크리스트

### 개발 전
- [ ] 강사 대시보드 구조 및 패턴 이해
- [ ] Supabase 파트너 관련 테이블 구조 파악
- [ ] 기존 파트너 인증 시스템 동작 확인

### 개발 중
- [ ] 커밋 메시지 규칙 준수 (feat, fix, style 등)
- [ ] 코드 스타일 가이드 준수
- [ ] 주기적인 빌드 및 타입 체크 실행
- [ ] 컴포넌트 재사용성 고려

### 개발 후
- [ ] 성능 최적화 확인 (Lighthouse 점수)
- [ ] 보안 취약점 검토 (XSS, CSRF)
- [ ] 브라우저 호환성 검증
- [ ] 사용자 피드백 수집 및 반영