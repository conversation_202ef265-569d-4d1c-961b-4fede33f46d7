# 파트너 대시보드 요구사항 v1

## 개요

파트너를 위한 메인 대시보드 페이지로, 예약 통계, 예약 현황, 스튜디오 현황을 시각적으로 제공하며 주요 액션을 빠르게 수행할 수 있는 중앙 관리 인터페이스입니다. 현재 비즈니스 모델(예약금 + 현장 결제)에 맞춰 예약 기반 지표를 중심으로 구성됩니다.

## 기능 요구사항

### 1. 예약 및 수업 통계

#### 1.1 예약 현황 요약
- **총 예약 건수** (필수)
  - 현재까지의 누적 예약 수
- **예상 수업료** (필수)
  - 현장 결제 예정 금액 추정
  - 클래스별 수업료 × 예약 건수
- **완료 수업료** (필수)
  - 실제 완료된 수업의 수업료 합계

### 2. 예약 현황

#### 2.1 실시간 예약 상태
1. 오늘의 예약 수 집계
2. 대기 중인 예약 건수
3. 확정된 예약 건수
4. 취소된 예약 건수
5. 완료된 예약 건수

#### 2.2 상태별 처리
- **대기중**: 파트너 승인 대기 상태
- **확정**: 예약 확정 완료 상태
- **취소**: 고객 또는 파트너 취소 상태
- **완료**: 서비스 제공 완료 상태

#### 2.3 에러 처리
- 예약 데이터 로딩 실패 시 재시도 옵션
- 네트워크 오류 시 캐시된 데이터 표시
- 권한 오류 시 로그인 페이지 리다이렉트

### 3. 스튜디오 현황

#### 3.1 스튜디오 관리 지표
- **등록된 스튜디오 수** (필수)
  - 활성 스튜디오 개수
  - 비활성 스튜디오 개수
- **활성 클래스 수** (필수)
  - 현재 진행 중인 클래스
  - 예약 가능한 클래스
- **강사 수** (필수)
  - 등록된 총 강사 수
  - 활성 강사 수

#### 3.2 빠른 액션 버튼
- 클래스 현황: `/partner/classes` 이동 (클래스 관리 및 등록)
- 클래스 등록: `/partner/classes/new` 이동 (새 클래스 만들기)
- 센터 관리: `/partner/studios` 이동 (스튜디오 조회 및 클래스 등록)
- 강사 관리: `/partner/instructors` 이동 (강사 관리)

### 4. 알림 및 액션

#### 4.1 실시간 알림
- 새로운 예약 알림 (실시간)
- 리뷰 등록 알림
- 결제 완료 알림
- 취소 요청 알림

#### 4.2 오늘의 수업

1. 시간별 수업 일정 표시
2. 수업 상태 (준비중, 진행중, 완료)
3. 강사 정보 및 수강생 수
4. 수업 상세 정보 접근
5. 빠른 수정/취소 액션

#### 4.3 일정 관리
- 이번 주 수업 일정
- 다음 주 수업 일정
- 빈 시간대 표시
- 일정 추가 버튼

### 5. API 명세

#### 5.1 대시보드 데이터 조회 API
**설명**: 파트너 대시보드에 필요한 모든 데이터를 한 번에 조회

**Request Headers:**
```typescript
{
  Authorization: string; // Bearer {access_token}
  'Content-Type': 'application/json';
}
```

**Request Body:**
```typescript
{
  timezone: string; // 시간대 (예: 'Asia/Seoul')
}
```

**Success Response (200):**
```typescript
{
  success: true;
  data: {
    bookingStats: {
      totalBookings: number; // 총 예약 건수
      expectedTuition: number; // 예상 수업료
      completedTuition: number; // 완료 수업료
    };
    bookings: {
      today: number; // 오늘 예약 수
      pending: number; // 대기 중 예약
      confirmed: number; // 확정 예약
      cancelled: number; // 취소 예약
      completed: number; // 완료 예약
    };
    studios: {
      total: number; // 총 스튜디오 수
      active: number; // 활성 스튜디오 수
      classes: number; // 활성 클래스 수
      instructors: number; // 강사 수
    };
    schedule: {
      today: Array<{
        id: string;
        title: string;
        startTime: string;
        endTime: string;
        instructor: string;
        students: number;
        status: 'pending' | 'confirmed' | 'ongoing' | 'completed';
      }>;
      thisWeek: Array<{
        date: string;
        classes: number;
      }>;
      nextWeek: Array<{
        date: string;
        classes: number;
      }>;
    };
    notifications: Array<{
      id: string;
      type: 'booking' | 'review' | 'payment' | 'cancellation';
      message: string;
      createdAt: string;
      read: boolean;
    }>;
  };
}
```

**Error Response (400/401/500):**
```typescript
{
  success: false;
  message: string;
  error?: string;
}
```

### 6. 보안 요구사항

#### 6.1 데이터 보호
- JWT 토큰을 통한 파트너 인증
- API 응답 데이터 암호화 (HTTPS)
- 민감한 매출 정보 로깅 금지

#### 6.2 접근 제어
- 파트너별 데이터 격리
- Rate Limiting: 100 requests/minute
- 비활성 파트너 접근 차단

#### 6.3 세션 보안
- 토큰 만료 시 자동 로그아웃
- 동시 세션 제한 (최대 3개)
- CSRF 토큰 검증

### 7. 사용자 경험

#### 7.1 반응형 디자인
- **모바일 (< 768px)**: 단일 컬럼 스택 레이아웃
- **태블릿 (768px ~ 1024px)**: 2컬럼 그리드
- **데스크톱 (> 1024px)**: 6컬럼 복합 그리드
- **접근성**: WCAG 2.1 AA 기준 준수

#### 7.2 사용자 피드백
- **로딩 상태**: 스켈레톤 UI로 로딩 표시
- **성공 메시지**: Toast 알림으로 액션 완료 표시
- **에러 메시지**: 구체적인 해결 방법 안내
- **포커스 관리**: 키보드 네비게이션 지원

#### 7.3 에러 처리
- **네트워크 오류**: "인터넷 연결을 확인해주세요" + 재시도 버튼
- **권한 오류**: "로그인이 필요합니다" + 로그인 페이지 이동
- **데이터 오류**: "데이터를 불러올 수 없습니다" + 새로고침 버튼
- **고객센터 연결**: 지속적 오류 시 고객센터 연결 버튼

### 8. 성능 요구사항

- **페이지 로드 시간**: 2초 이내
- **API 응답 시간**: 500ms 이내
- **차트 렌더링**: 1초 이내
- **동시 사용자**: 1000명 지원

### 9. 테스트 요구사항

- **단위 테스트**: 대시보드 컴포넌트, API 호출 로직
- **통합 테스트**: 대시보드 데이터 흐름
- **E2E 테스트**: 파트너 로그인 → 대시보드 조회 → 액션 실행

### 10. 모니터링 및 로깅

- **로깅 대상**: API 호출, 사용자 액션, 에러 발생
- **메트릭 수집**: 페이지 로드 시간, API 응답 시간, 에러율
- **알림 설정**: 에러율 5% 초과 시 슬랙 알림

## 제외 범위

- 실제 매출/수익 데이터 (현장 결제는 추적 불가)
- 예약 상세 편집 기능 (링크로만 이동)
- 실시간 채팅 기능
- 파트너 설정 관리
- 세금계산서/영수증 발행 기능

## 의존성

- 파트너 인증 시스템 (#25)
- 예약 관리 시스템
- 결제 시스템
- 알림 시스템

## 다음 단계

- 예약 상세 관리 페이지
- 매출 상세 분석 페이지
- 리뷰 관리 시스템
- 파트너 설정 페이지