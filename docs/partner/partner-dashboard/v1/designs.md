# 파트너 대시보드 설계 문서 v1

## 개요

파트너 대시보드의 UI/UX 설계로, 강사 대시보드 구조를 참조하여 파트너 특성에 맞게 조정된 레이아웃과 컴포넌트를 정의합니다. 모바일 우선 반응형 디자인으로 구성됩니다.

## 아키텍처 설계

### 전체 구조
```
파트너 대시보드 페이지 (/partner/dashboard)
├── 프로필 카드 (담당자 정보 + 기본 통계)
├── 클래스 상태 통계 (2x2 그리드)
├── 월간 KPI 지표 (3컬럼 그리드)
├── 빠른 액션 버튼 (2x2 그리드)
├── 오늘의 수업 섹션
├── 이번 주 수업 일정
└── 다음 주 수업 미리보기
```

### 컴포넌트 구조
```
src/app/partner/dashboard/
├── page.tsx (메인 대시보드 페이지)
└── components/
    ├── ProfileCard.tsx (프로필 + 기본 통계)
    ├── ClassStatusGrid.tsx (클래스 상태별 통계)
    ├── MonthlyStatsGrid.tsx (월간 KPI 지표)
    ├── QuickActionGrid.tsx (빠른 액션 버튼들)
    ├── TodayClasses.tsx (오늘의 수업 리스트)
    ├── WeeklySchedule.tsx (주간 일정)
    └── EmptyState.tsx (빈 상태 컴포넌트)
```

## UI/UX 설계

### 디자인 시스템

#### 색상 체계
- **Primary**: #6366F1 (indigo-500) - 주요 액션 버튼
- **Secondary**: #8B5CF6 (violet-500) - 보조 요소
- **Success**: #10B981 (emerald-500) - 진행중 상태
- **Warning**: #F59E0B (amber-500) - 시작예정 상태
- **Info**: #3B82F6 (blue-500) - 모집중 상태
- **Error**: #EF4444 (red-500) - 취소/오류 상태
- **Gray**: #6B7280 (gray-500) - 완료 상태

#### 타이포그래피
- **제목**: text-xl font-bold (20px, 700)
- **서브제목**: text-lg font-semibold (18px, 600)
- **본문**: text-sm font-medium (14px, 500)
- **캡션**: text-xs text-gray-500 (12px, 400)

#### 간격 체계
- **Small**: 12px (p-3)
- **Medium**: 16px (p-4)
- **Large**: 20px (p-5)
- **XLarge**: 24px (p-6)

### 레이아웃 설계

#### 모바일 (320px - 767px)
```
┌─────────────────────┐
│ 프로필 카드          │
│ ├─ 아바타 + 이름     │
│ ├─ 총 클래스/수업    │
│ ├─ 2x2 상태 그리드   │
│ └─ 3컬럼 KPI        │
├─────────────────────┤
│ 2x2 빠른 액션       │
├─────────────────────┤
│ 오늘의 수업         │
├─────────────────────┤
│ 이번 주 수업 일정   │
├─────────────────────┤
│ 다음 주 수업        │
└─────────────────────┘
```

#### 태블릿 (768px - 1023px)
```
┌─────────────────────┬─────────────────────┐
│ 프로필 카드          │ 빠른 액션           │
├─────────────────────┼─────────────────────┤
│ 오늘의 수업         │ 이번 주 일정        │
├─────────────────────┴─────────────────────┤
│ 다음 주 수업                           │
└─────────────────────────────────────────┘
```

#### 데스크톱 (1024px+)
```
┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐
│ 프로필  │ 액션1   │ 액션2   │ 오늘의  │ 이번주  │ 다음주  │
│ 카드    │         │         │ 수업    │ 일정    │ 수업    │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│         │ 액션3   │ 액션4   │         │         │         │
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘
```

## 컴포넌트 설계

### ProfileCard 컴포넌트

#### Props 인터페이스
```typescript
interface ProfileCardProps {
  partnerName: string;
  totalClasses: number;
  monthlyClasses: number;
  classStats: {
    recruiting: number;
    ongoing: number;
    completed: number;
    upcoming: number;
  };
  monthlyStats: {
    totalClasses: number;
    totalAttendance: number;
    completionRate: number;
  };
  onStatusClick: (status: string) => void;
}
```

#### 상태 관리
```typescript
interface ProfileCardState {
  isLoading: boolean;
  error: string | null;
}
```

#### 사용 예시
```tsx
<ProfileCard
  partnerName="최인걸"
  totalClasses={15}
  monthlyClasses={28}
  classStats={{
    recruiting: 0,
    ongoing: 0,
    completed: 0,
    upcoming: 1
  }}
  monthlyStats={{
    totalClasses: 0,
    totalAttendance: 0,
    completionRate: 0
  }}
  onStatusClick={(status) => router.push(`/partner/classes?status=${status}`)}
/>
```

### QuickActionGrid 컴포넌트

#### Props 인터페이스
```typescript
interface QuickActionGridProps {
  actions: Array<{
    id: string;
    title: string;
    description: string;
    icon: ReactNode;
    href: string;
    bgColor: string;
    iconColor: string;
  }>;
}
```

#### 사용 예시
```tsx
<QuickActionGrid
  actions={[
    {
      id: 'classes',
      title: '클래스 현황',
      description: '클래스 관리 및 등록',
      icon: <BuildingIcon />,
      href: '/partner/classes',
      bgColor: 'bg-indigo-100',
      iconColor: 'text-indigo-600'
    },
    {
      id: 'new-class',
      title: '클래스 등록',
      description: '새 클래스 만들기',
      icon: <PlusIcon />,
      href: '/partner/classes/new',
      bgColor: 'bg-green-100',
      iconColor: 'text-green-600'
    },
    {
      id: 'studios',
      title: '센터 관리',
      description: '스튜디오 조회 및 클래스 등록',
      icon: <BuildingOfficeIcon />,
      href: '/partner/studios',
      bgColor: 'bg-blue-100',
      iconColor: 'text-blue-600'
    },
    {
      id: 'instructors',
      title: '강사 관리',
      description: '강사 관리',
      icon: <UserGroupIcon />,
      href: '/partner/instructors',
      bgColor: 'bg-purple-100',
      iconColor: 'text-purple-600'
    }
  ]}
/>
```

### TodayClasses 컴포넌트

#### Props 인터페이스
```typescript
interface TodayClassesProps {
  classes: Array<{
    id: string;
    title: string;
    startTime: string;
    endTime: string;
    instructor: string;
    students: number;
    maxStudents: number;
    status: 'pending' | 'confirmed' | 'ongoing' | 'completed';
    studioName: string;
    notes?: string;
  }>;
  onClassClick: (classId: string) => void;
}
```

#### 상태 관리
```typescript
interface TodayClassesState {
  isLoading: boolean;
  error: string | null;
  refreshing: boolean;
}
```

### WeeklySchedule 컴포넌트

#### Props 인터페이스
```typescript
interface WeeklyScheduleProps {
  title: string;
  classes: Array<{
    id: string;
    title: string;
    date: string;
    startTime: string;
    endTime: string;
    instructor: string;
    studioName: string;
    status: string;
    attendance: number;
    maxParticipants: number;
  }>;
  onClassClick: (classId: string) => void;
  onViewAll: () => void;
  showViewAll?: boolean;
}
```

## 데이터 플로우

### 상태 관리
```
전역 상태 (Context/Zustand) ↔ 페이지 상태 ↔ 컴포넌트 로컬 상태
```

#### 전역 상태
- **dashboardData**: 대시보드 전체 데이터
- **user**: 파트너 사용자 정보
- **loading**: 로딩 상태 관리

#### 로컬 상태
- **refreshing**: 새로고침 상태
- **selectedPeriod**: 선택된 기간 (미래 확장용)
- **notifications**: 알림 상태

### 이벤트 플로우
```mermaid
graph TD
    A[페이지 로드] --> B[인증 확인]
    B --> C[대시보드 데이터 로드]
    C --> D[UI 렌더링]
    D --> E[사용자 액션]
    E --> F[상태 업데이트]
    F --> G[UI 리렌더링]
    G --> H[사이드 이펙트]
```

## API 설계

### 엔드포인트 구조
```
/api/partner/dashboard - 대시보드 데이터 조회
```

### 요청/응답 스키마
```typescript
// 요청 타입
interface DashboardRequest {
  timezone: string;
}

// 응답 타입
interface DashboardResponse {
  success: boolean;
  data?: {
    bookingStats: {
      totalBookings: number;
      expectedTuition: number;  
      completedTuition: number;
    };
    classStats: {
      recruiting: number;
      ongoing: number;
      completed: number;
      upcoming: number;
    };
    monthlyStats: {
      totalClasses: number;
      totalAttendance: number;
      completionRate: number;
    };
    todayClasses: Array<ClassOccurrence>;
    thisWeekClasses: Array<ClassOccurrence>;
    nextWeekClasses: Array<ClassOccurrence>;
    studios: {
      total: number;
      active: number;
      classes: number;
      instructors: number;
    };
  };
  error?: string;
}
```

## 라우팅 설계

### 경로 구조
```
/partner/
├── /dashboard (메인 대시보드)
├── /classes (클래스 관리)
├── /classes/new (새 클래스 등록)
├── /studios (센터 관리)
└── /instructors (강사 관리)
```

### 라우트 보호
- **공개 경로**: 없음 (파트너 전용)
- **인증 필요**: 모든 /partner/* 경로
- **권한 필요**: 파트너 상태가 ACTIVE인 경우만

## 상태별 처리 설계

### 로딩 상태 처리
- **표시 내용**: 스켈레톤 UI로 로딩 표시
- **사용 가능한 액션**: 없음
- **제한사항**: 모든 인터렉션 비활성화

### 오류 상태 처리  
- **표시 내용**: 에러 메시지 + 재시도 버튼
- **사용 가능한 액션**: 새로고침, 고객센터 연결
- **제한사항**: 데이터 관련 기능 비활성화

### 빈 상태 처리
- **표시 내용**: 일러스트 + 안내 메시지 + 액션 버튼
- **사용 가능한 액션**: 클래스 등록, 강사 초대 등
- **제한사항**: 없음

## 에러 처리 설계

### 에러 타입 분류
```typescript
enum ErrorType {
  NETWORK = 'network',
  AUTH = 'auth', 
  PERMISSION = 'permission',
  SERVER = 'server',
  VALIDATION = 'validation'
}
```

### 에러별 처리 방안
- **Network Error**: 인터넷 연결 확인 메시지 + 재시도
- **Auth Error**: 로그인 페이지로 리다이렉트
- **Permission Error**: 권한 없음 메시지 + 고객센터 연결
- **Server Error**: 일시적 오류 메시지 + 재시도

## 성능 고려사항

### 최적화 전략
- **지연 로딩**: 주간 일정 컴포넌트는 viewport 진입 시 로드
- **메모이제이션**: 통계 계산 로직 useMemo 적용
- **번들 최적화**: 아이콘 라이브러리 tree shaking
- **이미지 최적화**: Next.js Image 컴포넌트 사용

### 성능 메트릭
- **FCP (First Contentful Paint)**: < 1.5초
- **LCP (Largest Contentful Paint)**: < 2.5초  
- **CLS (Cumulative Layout Shift)**: < 0.1
- **FID (First Input Delay)**: < 100ms

## 보안 설계

### 인증/인가
- **인증 방식**: JWT Bearer 토큰
- **토큰 관리**: httpOnly 쿠키 + 메모리 저장
- **권한 체크**: 미들웨어에서 파트너 상태 확인

### 데이터 보호
- **입력 검증**: 클라이언트 + 서버 양쪽 검증
- **XSS 방지**: React 기본 이스케이핑 + CSP 헤더
- **CSRF 방지**: SameSite 쿠키 + CSRF 토큰

## 테스트 설계

### 테스트 전략
- **단위 테스트**: 각 컴포넌트 props/state 테스트
- **통합 테스트**: API 호출 + 상태 업데이트 플로우
- **E2E 테스트**: 파트너 로그인 → 대시보드 조회 → 액션

### 테스트 케이스
```typescript
describe('파트너 대시보드', () => {
  it('로딩 상태에서 스켈레톤을 표시한다', () => {
    // 테스트 코드
  });
  
  it('클래스 상태를 클릭하면 필터된 클래스 페이지로 이동한다', () => {
    // 테스트 코드
  });
  
  it('오늘의 수업이 없을 때 빈 상태를 표시한다', () => {
    // 테스트 코드
  });
});
```

## 배포 고려사항

### 환경별 설정
- **개발 환경**: Mock 데이터 사용
- **스테이징 환경**: 실제 API + 테스트 데이터
- **프로덕션 환경**: 실제 API + 운영 데이터

### 마이그레이션 계획
- **데이터베이스**: 기존 파트너 테이블 활용
- **설정 변경**: 미들웨어에 파트너 라우트 추가
- **의존성 업데이트**: 없음 (기존 라이브러리 활용)

## 향후 확장성

### 확장 가능한 구조
- **새로운 위젯 추가**: 컴포넌트 기반 모듈화
- **다국어 지원**: i18n 라이브러리 통합 준비
- **테마 지원**: CSS 변수 기반 컬러 시스템

### 기술 부채 관리
- **리팩토링 계획**: 강사/파트너 공통 컴포넌트 분리
- **성능 개선**: 가상화된 리스트 적용 검토
- **코드 품질**: ESLint 규칙 강화 + 타입 커버리지 90% 이상