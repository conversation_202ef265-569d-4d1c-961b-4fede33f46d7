# 파트너 회원가입 디자인 명세 v1

## 개요

파트너 회원가입 페이지의 UI/UX 디자인 가이드라인과 구현 방법을 정의합니다.

## 디자인 참고자료

### 피그마 링크
- **메인 디자인**: https://www.figma.com/design/koI2sFJdRiisatx3ciQVMp/%EC%89%98%EC%9C%84--Copy-?node-id=1058-1666&t=BNYbeWNDW7eQk5eN-4

### 벤치마킹
- 네이버 예약 파트너 센터 회원가입
- 카카오비즈니스 가입 프로세스
- 배달의민족 사장님 가입

## 레이아웃 구조

### 1. 전체 레이아웃
```
┌─────────────────────────────────────┐
│              Header                 │
│           (로고 + 진행단계)          │
├─────────────────────────────────────┤
│                                     │
│            Main Content             │
│         (회원가입 폼 영역)           │
│                                     │
├─────────────────────────────────────┤
│              Footer                 │
│          (도움말 링크 등)            │
└─────────────────────────────────────┘
```

### 2. 페이지 컨테이너
- **최대 너비**: 480px (모바일 최적화)
- **패딩**: 좌우 24px, 상하 32px
- **배경색**: `bg-gray-50`
- **중앙 정렬**: `mx-auto`

## 컴포넌트 설계

### 1. PhoneInput 컴포넌트 (신규)
```tsx
interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  placeholder?: string;
}
```

**기능:**
- 자동 하이픈 삽입 (010-0000-0000)
- 숫자만 입력 허용
- 형식 검증

### 2. PartnerSignupForm 컴포넌트
```tsx
interface PartnerSignupFormProps {
  onSubmit: (data: PartnerSignupData) => void;
  loading?: boolean;
}

interface PartnerSignupData {
  email: string;
  password: string;
  passwordConfirm: string;
  contactInfo: {
    name: string;
    phone: string;
  };
}
```

**필드 구성:**
- 이메일 (Input with 검증)
- 비밀번호 (PasswordInput with 강도 표시)
- 비밀번호 확인 (PasswordInput)
- 담당자명 (Input)
- 담당자 연락처 (PhoneInput with 형식 자동 변환)

## 사용할 UI 컴포넌트

### 기존 컴포넌트 활용
```tsx
// 기본 컴포넌트
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';

// 폼 관련
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
```

## 스타일 가이드

### 1. 색상 팔레트
```css
/* Primary Colors */
--primary: 주요 브랜드 컬러
--primary-foreground: 주요 텍스트 컬러

/* Semantic Colors */
--success: rgb(34, 197, 94)    /* 성공 상태 */
--error: rgb(239, 68, 68)      /* 에러 상태 */
--warning: rgb(245, 158, 11)   /* 경고 상태 */

/* Gray Scale */
--gray-50: rgb(249, 250, 251)
--gray-100: rgb(243, 244, 246)
--gray-500: rgb(107, 114, 128)
--gray-900: rgb(17, 24, 39)
```

### 2. 타이포그래피
```css
/* 페이지 제목 */
.page-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--gray-900);
}

/* 섹션 제목 */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-900);
}

/* 라벨 */
.field-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
}

/* 도움말 텍스트 */
.help-text {
  font-size: 12px;
  color: var(--gray-500);
}
```

### 3. 간격 시스템
```css
/* 컴포넌트 간 간격 */
.section-gap: 32px (8 * 4)
.field-gap: 20px (5 * 4)
.element-gap: 12px (3 * 4)

/* 내부 여백 */
.card-padding: 24px (6 * 4)
.input-padding: 12px 16px (3 * 4, 4 * 4)
```

## 반응형 디자인

### 1. 브레이크포인트
```css
/* Mobile First */
@media (max-width: 640px) {
  /* 기본 스타일 */
}

@media (min-width: 641px) {
  /* 태블릿 */
  .container {
    max-width: 640px;
  }
}

@media (min-width: 1024px) {
  /* 데스크톱 */
  .container {
    max-width: 720px;
  }
}
```

### 2. 모바일 최적화
- 터치 영역 최소 44px × 44px
- 가로 스크롤 방지
- 키보드 대응 viewport 조정
- iOS Safari 100vh 이슈 대응

## 사용자 인터랙션

### 1. 폼 검증 피드백
```tsx
// 성공 상태
<Input className="border-green-500 focus:ring-green-500" />
<span className="text-green-600 text-sm">✓ 사용 가능한 이메일입니다</span>

// 에러 상태
<Input className="border-red-500 focus:ring-red-500" />
<span className="text-red-600 text-sm">이미 등록된 이메일입니다</span>

// 로딩 상태
<Input disabled className="opacity-50" />
<Spinner className="w-4 h-4" />
```

### 2. 버튼 상태
```tsx
// 기본 상태
<Button>회원가입</Button>

// 로딩 상태
<Button disabled>
  <Spinner className="w-4 h-4 mr-2" />
  처리 중...
</Button>

// 완료 상태
<Button className="bg-green-600 hover:bg-green-700">
  회원가입 완료
</Button>
```

### 3. 애니메이션
```css
/* 폼 필드 포커스 */
.input-focus {
  transition: border-color 200ms, box-shadow 200ms;
}

/* 검증 메시지 등장 */
.validation-message {
  animation: fadeIn 200ms ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 로딩 스피너 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
```

## 접근성 (Accessibility)

### 1. 키보드 내비게이션
- Tab 순서 논리적 배치 (이메일 → 비밀번호 → 비밀번호 확인 → 담당자명 → 연락처 → 제출)
- Enter로 폼 제출
- 필드 간 자동 포커스 이동

### 2. 스크린 리더 지원
```tsx
// 라벨 연결
<Label htmlFor="business-name">스튜디오명</Label>
<Input id="business-name" aria-describedby="business-name-help" />
<span id="business-name-help">영업에 사용하는 스튜디오 이름을 입력해주세요</span>

// 에러 메시지
<Input aria-invalid="true" aria-describedby="email-error" />
<span id="email-error" role="alert">이미 등록된 이메일입니다</span>

// 폼 상태
<form role="form" aria-label="파트너 회원가입">
  <fieldset>
    <legend>계정 정보</legend>
    <!-- 이메일, 비밀번호 필드 -->
  </fieldset>
  <fieldset>
    <legend>담당자 정보</legend>
    <!-- 담당자명, 연락처 필드 -->
  </fieldset>
</form>
```

### 3. 컬러 대비
- WCAG 2.1 AA 기준 준수 (4.5:1 이상)
- 컬러에만 의존하지 않는 상태 표시
- 다크모드 대응 (선택적)

## 성능 최적화

### 1. 이미지 최적화
- WebP 포맷 사용
- 적절한 크기로 리사이징
- Lazy Loading 적용

### 2. 코드 스플리팅
```tsx
// 폰 입력 컴포넌트는 필요시에만 로드 (선택적)
const PhoneInput = lazy(() => import('./common/PhoneInput'));
```

### 3. 폰트 최적화
```css
/* 웹폰트 로딩 최적화 */
@font-face {
  font-family: 'Pretendard';
  font-display: swap;
  src: url('/fonts/Pretendard-Regular.woff2') format('woff2');
}
```

## 에러 상태 디자인

### 1. 필드별 에러
- 붉은색 테두리
- 아래쪽에 에러 메시지
- 아이콘으로 시각적 강조

### 2. 전체 폼 에러
```tsx
<Alert variant="destructive">
  <AlertTriangle className="h-4 w-4" />
  <AlertDescription>
    회원가입 중 오류가 발생했습니다. 다시 시도해주세요.
  </AlertDescription>
</Alert>
```

### 3. 네트워크 에러
- 재시도 버튼 제공
- 고객센터 연결 링크
- 오프라인 상태 감지

```

이러한 디자인 시스템을 기반으로 일관성 있고 사용하기 쉬운 파트너 회원가입 페이지를 구현합니다.