# 파트너 회원가입 구현 태스크 v1

## 개요

파트너 회원가입 기능을 구현하기 위한 상세 태스크 목록과 구현 순서를 정의합니다.

## 전체 구현 순서

```mermaid
graph TD
    A[환경 준비] --> B[데이터베이스 스키마]
    B --> C[API 구현]
    C --> D[타입 정의]
    D --> E[스키마 검증]
    E --> F[UI 컴포넌트]
    F --> G[페이지 구현]
    G --> H[테스트]
    H --> I[배포]
```

## Phase 1: 환경 준비

### Task 1.1: 브랜치 생성 및 정리
- [ ] `git stash drop` (기존 stash 제거)
- [ ] `git checkout -b feature/partner-signup` (새 브랜치 생성)
- [ ] 브랜치 보호 규칙 확인

**예상 소요시간**: 10분
**담당자**: 개발자
**의존성**: 없음

### Task 1.2: 개발 환경 확인
- [ ] Node.js 버전 확인 (Node 18+)
- [ ] 패키지 설치 상태 확인 (`npm install`)
- [ ] 환경변수 설정 확인 (`.env.local`)
- [ ] Supabase 연결 테스트

**예상 소요시간**: 15분
**담당자**: 개발자
**의존성**: Task 1.1

## Phase 2: 데이터베이스 스키마

### Task 2.1: partners 테이블 스키마 간소화
- [ ] 기존 `partners` 테이블 구조 확인
- [ ] 최소 필드만 정의 (contact_name, contact_phone, status)
- [ ] RLS 정책 설계

**예상 소요시간**: 20분
**담당자**: 개발자
**의존성**: Task 1.2

### Task 2.2: 마이그레이션 스크립트 작성
- [ ] Drizzle 스키마 파일 업데이트 (`src/lib/db/schema.ts`)
- [ ] 간소화된 스키마로 마이그레이션 생성
- [ ] 로컬 DB에 적용 및 검증

**파일 경로**: 
- `src/lib/db/schema.ts`
- `drizzle/migrations/xxxx_simplify_partner_signup.sql`

**예상 소요시간**: 30분
**담당자**: 개발자
**의존성**: Task 2.1

### Task 2.3: RLS 정책 구현
- [ ] partners 테이블 RLS 활성화
- [ ] INSERT 정책 (본인만 생성 가능)
- [ ] SELECT 정책 (본인만 조회 가능)
- [ ] UPDATE 정책 (본인만 수정 가능)

**예상 소요시간**: 20분
**담당자**: 개발자
**의존성**: Task 2.2

## Phase 3: 백엔드 API 구현

### Task 3.1: API 라우트 생성
- [ ] `/src/app/api/partner/register/route.ts` 생성
- [ ] POST 메서드 구현
- [ ] 요청/응답 타입 정의
- [ ] 에러 처리 구조 설계

**파일 경로**: `src/app/api/partner/register/route.ts`
**예상 소요시간**: 40분
**담당자**: 개발자
**의존성**: Task 2.3

### Task 3.2: 간소화된 회원가입 로직 구현
- [ ] Supabase Auth 회원가입 연동
- [ ] partners 테이블 최소 데이터 저장
- [ ] 트랜잭션 처리

**예상 소요시간**: 40분
**담당자**: 개발자
**의존성**: Task 3.1

### Task 3.3: 유효성 검증 구현
- [ ] 서버사이드 검증 로직
- [ ] 이메일 중복 체크
- [ ] 연락처 형식 검증

**예상 소요시간**: 20분
**담당자**: 개발자
**의존성**: Task 3.2

### Task 3.4: API 테스트
- [ ] Postman/Thunder Client로 API 테스트
- [ ] 정상 케이스 테스트
- [ ] 에러 케이스 테스트
- [ ] 중복 데이터 테스트

**예상 소요시간**: 25분
**담당자**: 개발자
**의존성**: Task 3.3

## Phase 4: 타입 정의 및 스키마

### Task 4.1: TypeScript 타입 정의
- [ ] 파트너 회원가입 요청 타입
- [ ] 파트너 회원가입 응답 타입
- [ ] 에러 응답 타입
- [ ] 주소 데이터 타입

**파일 경로**: `src/types/partner.ts`
**예상 소요시간**: 20분
**담당자**: 개발자
**의존성**: Task 3.1

### Task 4.2: 간소화된 Zod 스키마 정의
- [ ] 계정 정보 스키마 (이메일, 비밀번호)
- [ ] 담당자 정보 스키마 (이름, 연락처)
- [ ] 통합 회원가입 스키마
- [ ] 에러 메시지 정의

**파일 경로**: `src/schemas/partner.ts`
**예상 소요시간**: 25분
**담당자**: 개발자
**의존성**: Task 4.1

## Phase 5: UI 컴포넌트 구현

### Task 5.1: 최소 공통 컴포넌트 구현
- [ ] `PhoneInput` 컴포넌트 (형식 자동 변환)

**파일 경로**: 
- `src/components/partner/common/PhoneInput.tsx`

**예상 소요시간**: 30분
**담당자**: 개발자
**의존성**: Task 4.2

### Task 5.2: 단일 폼 컴포넌트 구현
- [ ] `PartnerSignupForm` 컴포넌트 (통합 폼)
- [ ] React Hook Form 설정
- [ ] 실시간 유효성 검증
- [ ] 로딩 상태 처리

**파일 경로**:
- `src/components/partner/signup/PartnerSignupForm.tsx`

**예상 소요시간**: 60분
**담당자**: 개발자
**의존성**: Task 5.1

### Task 5.3: 컴포넌트 스토리북 작성 (선택적)
- [ ] `PhoneInput.stories.tsx`
- [ ] `PartnerSignupForm.stories.tsx`
- [ ] 다양한 상태별 스토리

**예상 소요시간**: 25분
**담당자**: 개발자
**의존성**: Task 5.2

## Phase 6: 페이지 구현

### Task 6.1: 레이아웃 구성
- [ ] `/src/app/(blank)/partner/register/layout.tsx` 생성
- [ ] 헤더 및 푸터 구성
- [ ] 반응형 레이아웃 적용
- [ ] SEO 메타데이터 설정

**예상 소요시간**: 30분
**담당자**: 개발자
**의존성**: Task 5.2

### Task 6.2: 간소화된 메인 페이지 구현
- [ ] `/src/app/(blank)/partner/register/page.tsx` 생성
- [ ] 단일 폼 페이지 구현
- [ ] 간단한 상태 관리 (useState)
- [ ] API 호출 및 에러 처리

**예상 소요시간**: 50분
**담당자**: 개발자
**의존성**: Task 6.1

### Task 6.3: 성공/실패 페이지
- [ ] 회원가입 성공 페이지
- [ ] 이메일 인증 안내 페이지
- [ ] 에러 페이지 (재시도 기능 포함)
- [ ] 로딩 상태 UI

**예상 소요시간**: 40분
**담당자**: 개발자
**의존성**: Task 6.2

## Phase 7: 스타일링 및 UX

### Task 7.1: 반응형 디자인 적용
- [ ] 모바일 최적화 (320px~)
- [ ] 태블릿 최적화 (768px~)
- [ ] 데스크톱 최적화 (1024px~)
- [ ] 터치 인터페이스 최적화

**예상 소요시간**: 50분
**담당자**: 개발자
**의존성**: Task 6.3

### Task 7.2: 최소 애니메이션 및 UX 개선
- [ ] 폼 검증 피드백 효과
- [ ] 로딩 스피너 애니메이션
- [ ] 기본 호버/포커스 효과

**예상 소요시간**: 20분
**담당자**: 개발자
**의존성**: Task 7.1

### Task 7.3: 접근성 개선
- [ ] 키보드 내비게이션 지원
- [ ] 스크린 리더 호환성
- [ ] ARIA 속성 추가
- [ ] 컬러 대비 확인

**예상 소요시간**: 30분
**담당자**: 개발자
**의존성**: Task 7.2

## Phase 8: 테스트

### Task 8.1: 단위 테스트 작성
- [ ] 유틸리티 함수 테스트
- [ ] Zod 스키마 검증 테스트
- [ ] 컴포넌트 렌더링 테스트
- [ ] API 로직 테스트

**파일 경로**: `__tests__/partner/signup/`
**예상 소요시간**: 60분
**담당자**: 개발자
**의존성**: Task 7.3

### Task 8.2: 통합 테스트 작성
- [ ] API 엔드포인트 테스트
- [ ] 데이터베이스 연동 테스트
- [ ] 폼 제출 테스트
- [ ] 에러 시나리오 테스트

**예상 소요시간**: 45분
**담당자**: 개발자
**의존성**: Task 8.1

### Task 8.3: E2E 테스트 작성 (선택적)
- [ ] 전체 회원가입 플로우 테스트
- [ ] 브라우저별 호환성 테스트
- [ ] 모바일 디바이스 테스트
- [ ] 성능 테스트

**예상 소요시간**: 40분
**담당자**: 개발자
**의존성**: Task 8.2

## Phase 9: 품질 검증 및 배포

### Task 9.1: 코드 품질 검증
- [ ] TypeScript 타입 체크 (`npm run check-types`)
- [ ] ESLint 검사 (`npm run lint`)
- [ ] Prettier 포맷팅 (`npm run format`)
- [ ] 빌드 테스트 (`npm run build`)

**예상 소요시간**: 15분
**담당자**: 개발자
**의존성**: Task 8.3

### Task 9.2: 수동 테스트
- [ ] 각 브라우저별 테스트 (Chrome, Safari, Firefox)
- [ ] 모바일 디바이스 테스트 (iOS, Android)
- [ ] 네트워크 상태별 테스트 (느린 연결, 오프라인)
- [ ] 접근성 도구로 검증

**예상 소요시간**: 30분
**담당자**: 개발자
**의존성**: Task 9.1

### Task 9.3: 문서화 및 PR 준비
- [ ] README 업데이트
- [ ] API 문서 작성
- [ ] 컴포넌트 문서 작성
- [ ] PR 템플릿 작성

**예상 소요시간**: 25분
**담당자**: 개발자
**의존성**: Task 9.2

### Task 9.4: Pull Request 생성
- [ ] PR 제목 및 설명 작성
- [ ] 스크린샷/비디오 첨부
- [ ] 체크리스트 확인
- [ ] 리뷰어 지정

**예상 소요시간**: 10분
**담당자**: 개발자
**의존성**: Task 9.3

## 위험 요소 및 대응 방안

### 1. 기술적 위험
- **Supabase Auth 연동 이슈**: 기존 코드 참고하여 해결
- **타입 오류**: 점진적 타입 적용으로 해결

### 2. 일정 위험
- **예상 지연 요소**: 디자인 변경 요청, 추가 기능 요구
- **대응 방안**: MVP 우선 구현 후 점진적 개선

### 3. 품질 위험
- **보안 취약점**: OWASP 체크리스트 준수
- **성능 이슈**: 코드 스플리팅 및 최적화 적용

## 총 예상 소요시간

| Phase | 예상 시간 | 비고 |
|--------|----------|------|
| Phase 1: 환경 준비 | 25분 | |
| Phase 2: 데이터베이스 | 70분 | 간소화 |
| Phase 3: 백엔드 API | 85분 | 간소화 |
| Phase 4: 타입/스키마 | 45분 | 간소화 |
| Phase 5: UI 컴포넌트 | 115분 | 대폭 간소화 |
| Phase 6: 페이지 구현 | 120분 | 단일 페이지 |
| Phase 7: 스타일링/UX | 70분 | 최소 구현 |
| Phase 8: 테스트 | 80분 | 핵심 테스트만 |
| Phase 9: 품질/배포 | 80분 | |
| **총합** | **약 11시간** | 1-2일 소요 예상 |

## 완료 기준 (Definition of Done)

- [ ] 모든 기능 요구사항 구현 완료
- [ ] 타입 체크 및 린트 통과
- [ ] 단위 테스트 및 통합 테스트 통과
- [ ] 브라우저별 호환성 확인
- [ ] 접근성 기준 준수
- [ ] 코드 리뷰 완료
- [ ] 문서화 완료
- [ ] 스테이징 환경 배포 및 테스트 완료

이 태스크 계획을 바탕으로 체계적이고 안정적인 파트너 회원가입 기능을 구현합니다.