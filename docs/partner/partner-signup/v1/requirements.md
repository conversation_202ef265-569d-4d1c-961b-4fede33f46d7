# 파트너 회원가입 기능 요구사항 v1

## 개요

파트너(스튜디오 운영자)가 ShallWe 플랫폼에 가입할 수 있는 회원가입 시스템을 구현합니다.

## 기능 요구사항

### 1. 회원가입 폼

#### 1.1 기본 계정 정보
- **이메일** (필수)
  - 이메일 형식 검증
  - 중복 체크 필요
  - 인증 이메일 발송
- **비밀번호** (필수)
  - 최소 8자, 영문+숫자+특수문자
  - 비밀번호 확인 필드

#### 1.2 담당자 정보
- **담당자명** (필수)
  - 최대 50자
  - 한글/영문만 허용
- **담당자 연락처** (필수)
  - 휴대폰 번호 형식 (010-0000-0000)

### 2. 유효성 검증

#### 2.1 클라이언트 사이드 검증
- React Hook Form + Zod 스키마 사용
- 실시간 필드별 검증
- 사용자 친화적 에러 메시지

#### 2.2 서버 사이드 검증
- API 레벨에서 모든 필드 재검증
- SQL Injection 방지
- XSS 방지를 위한 입력값 이스케이핑

#### 2.3 비즈니스 규칙 검증
- 이메일 중복 체크
- 연락처 형식 검증 (010-0000-0000)
- 비밀번호 정책 검증

### 3. 인증 시스템

#### 3.1 Supabase Auth 연동
- 이메일/비밀번호 기반 회원가입
- 이메일 인증 필수
- 비밀번호 정책: 최소 8자, 영문+숫자+특수문자

#### 3.2 계정 상태 관리
- **PENDING**: 가입 완료, 승인 대기
- **ACTIVE**: 승인 완료, 서비스 이용 가능
- **SUSPENDED**: 계정 정지
- **REJECTED**: 가입 거부

### 4. 데이터 저장

#### 4.1 partners 테이블 스키마
```sql
CREATE TABLE partners (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  contact_name TEXT NOT NULL,
  contact_phone TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'PENDING', -- PENDING, ACTIVE, SUSPENDED, REJECTED
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**주의**: 비즈니스 정보(사업자등록번호, 스튜디오명, 주소 등)는 별도 이슈에서 "스튜디오 등록" 시에 추가 예정

#### 4.2 데이터 무결성
- 외래키 제약조건 설정 (user_id → auth.users.id)
- NOT NULL 제약조건

### 5. API 명세

#### 5.1 POST /api/partner/register
**Request Body:**
```typescript
{
  email: string;
  password: string;
  contactInfo: {
    name: string;
    phone: string;
  };
}
```

**Success Response (201):**
```typescript
{
  success: true;
  message: "회원가입이 완료되었습니다. 이메일 인증을 확인해주세요.";
  data: {
    partnerId: string;
    status: "PENDING";
  };
}
```

**Error Response (400):**
```typescript
{
  success: false;
  message: string;
  errors: {
    field: string;
    message: string;
  }[];
}
```

### 6. 보안 요구사항

#### 6.1 데이터 보호
- 개인정보 암호화 저장
- HTTPS 통신 강제
- CSRF 토큰 검증

#### 6.2 접근 제어
- Rate Limiting (분당 5회 제한)
- IP 기반 차단 기능
- 봇 방지 (선택적)

### 7. 사용자 경험

#### 7.1 반응형 디자인
- 모바일 우선 설계
- 태블릿/데스크톱 최적화
- 접근성 준수 (WCAG 2.1 AA)

#### 7.2 사용자 피드백
- 로딩 상태 표시
- 성공/실패 메시지
- 단계별 진행률 표시

#### 7.3 에러 처리
- 친화적인 에러 메시지
- 재시도 기능
- 고객센터 연결 링크

### 8. 성능 요구사항

- 페이지 로드 시간: 3초 이내
- API 응답 시간: 1초 이내
- 동시 사용자: 100명 이상 지원

### 9. 테스트 요구사항

- 단위 테스트: 핵심 함수 커버리지 80% 이상
- 통합 테스트: API 엔드포인트 테스트
- E2E 테스트: 전체 회원가입 플로우

### 10. 모니터링 및 로깅

- 회원가입 성공/실패 로그
- 성능 메트릭 수집
- 에러 추적 (Sentry 등)

## 제외 범위

- 소셜 로그인 (카카오, 네이버 등)
- 본인인증 (아이핀, 휴대폰 인증)
- 결제 정보 등록
- 파트너 승인 관리자 페이지
- **비즈니스 정보 수집** (사업자등록번호, 스튜디오명, 주소 등은 별도 "스튜디오 등록" 이슈에서 처리)

## 관련 이슈

- **다음 단계**: 스튜디오 등록 기능 (#27) - 비즈니스 정보 수집 및 검증
- **연관 기능**: 파트너 로그인 (#25), 파트너 대시보드 (#26)