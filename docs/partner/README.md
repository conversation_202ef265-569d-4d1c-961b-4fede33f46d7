# 파트너 시스템 문서

ShallWe 플랫폼의 파트너(스튜디오 운영자) 시스템에 대한 종합 문서입니다.

## 개요

파트너 시스템은 스튜디오 운영자가 자신의 비즈니스를 관리할 수 있는 종합 관리 플랫폼입니다. 네이버 예약과 유사한 기능을 제공하여 스튜디오, 강사, 클래스를 체계적으로 관리할 수 있습니다.

## 주요 기능

1. **파트너 인증 시스템**
   - 회원가입 및 로그인
   - 비즈니스 검증 및 승인

2. **대시보드**
   - 매출 통계 및 분석
   - 예약 현황 모니터링
   - 실시간 알림

3. **스튜디오 관리**
   - 스튜디오 등록 및 정보 관리
   - 시설 정보 및 사진 관리

4. **강사 관리**
   - 강사 등록 및 프로필 관리
   - 전문 분야 및 자격증 관리

5. **클래스 관리**
   - 클래스 등록 및 스케줄 설정
   - 수강생 관리 및 출석 체크

## 기능별 문서

### 파트너 회원가입
- [v1 Requirements](./partner-signup/v1/requirements.md)
- [v1 Designs](./partner-signup/v1/designs.md)
- [v1 Tasks](./partner-signup/v1/tasks.md)

## 기술 스택

- **Frontend**: Next.js 15, React 19, Tailwind CSS
- **Backend**: Next.js API Routes, Supabase
- **Database**: PostgreSQL (Supabase)
- **Authentication**: Supabase Auth
- **UI Components**: Radix UI

## 개발 가이드라인

1. **코드 스타일**: 기존 프로젝트의 ESLint/Prettier 설정 준수
2. **타입 안전성**: TypeScript strict mode 사용
3. **컴포넌트**: 기존 UI 컴포넌트 재사용 우선
4. **API**: RESTful API 설계 원칙 준수
5. **보안**: RLS(Row Level Security) 정책 적용

## 브랜치 전략

- `feature/partner-{기능명}` 형태로 브랜치 생성
- PR 생성 시 관련 이슈 번호 연결
- 코드 리뷰 후 main 브랜치에 병합

## 버전 관리

각 기능별로 v1, v2 등의 버전으로 문서를 관리하며, 주요 변경사항이 있을 때 새 버전 디렉터리를 생성합니다.