# 파트너 로그인 구현 태스크 v1

## 개요

파트너 로그인 기능을 구현하기 위한 상세 태스크 목록과 구현 순서를 정의합니다.

## 전체 구현 순서

```mermaid
graph TD
    A[타입 정의] --> B[UI 컴포넌트]
    B --> C[페이지 구현]
    C --> D[API 구현]
    D --> E[미들웨어 통합]
    E --> F[테스트]
    F --> G[배포]
```

## Phase 1: 타입 정의 및 스키마

### Task 1.1: TypeScript 타입 정의
- [ ] 로그인 요청/응답 타입 정의
- [ ] 파트너 상태 타입 정의
- [ ] 에러 타입 정의

**파일 경로**: `src/types/partner.ts` (기존 파일에 추가)
**예상 소요시간**: 20분
**담당자**: 개발자

```typescript
// 예시
interface PartnerLoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface PartnerLoginResponse {
  success: boolean;
  data?: {
    partnerId: string;
    status: PartnerStatus;
    redirectUrl: string;
  };
  error?: {
    message: string;
    field?: string;
  };
}
```

### Task 1.2: Zod 스키마 정의
- [ ] 로그인 폼 검증 스키마
- [ ] 이메일 형식 검증
- [ ] 에러 메시지 정의

**파일 경로**: `src/schemas/partner.ts` (기존 파일에 추가)
**예상 소요시간**: 15분
**담당자**: 개발자

## Phase 2: UI 컴포넌트 구현

### Task 2.1: 기본 입력 컴포넌트
- [ ] `EmailInput` 컴포넌트 구현
- [ ] `PasswordInput` 컴포넌트 구현 (토글 기능)
- [ ] 실시간 유효성 검증
- [ ] 에러 상태 표시

**파일 경로**: 
- `src/components/partner/login/EmailInput.tsx`
- `src/components/partner/login/PasswordInput.tsx`

**예상 소요시간**: 40분
**담당자**: 개발자

### Task 2.2: 로그인 폼 컴포넌트
- [ ] `PartnerLoginForm` 컴포넌트 구현
- [ ] React Hook Form 설정
- [ ] 폼 제출 처리
- [ ] 로딩 상태 처리

**파일 경로**: `src/components/partner/login/PartnerLoginForm.tsx`
**예상 소요시간**: 50분
**담당자**: 개발자

### Task 2.3: 추가 UI 컴포넌트
- [ ] `RememberMeCheckbox` 컴포넌트
- [ ] `StatusMessage` 컴포넌트 (PENDING, SUSPENDED 상태)
- [ ] 링크 컴포넌트 (비밀번호 찾기, 회원가입)

**파일 경로**: 
- `src/components/partner/login/RememberMeCheckbox.tsx`
- `src/components/partner/login/StatusMessage.tsx`

**예상 소요시간**: 30분
**담당자**: 개발자

## Phase 3: 페이지 구현

### Task 3.1: 로그인 페이지 레이아웃
- [ ] `/src/app/partner/login/layout.tsx` 생성
- [ ] 헤더/푸터 구성
- [ ] SEO 메타데이터 설정

**예상 소요시간**: 20분
**담당자**: 개발자

### Task 3.2: 로그인 페이지 구현
- [ ] `/src/app/partner/login/page.tsx` 생성
- [ ] 폼 컴포넌트 통합
- [ ] 로그인 로직 구현
- [ ] 상태별 리다이렉트 처리

**예상 소요시간**: 40분
**담당자**: 개발자

### Task 3.3: 상태별 안내 페이지
- [ ] `/src/app/partner/pending/page.tsx` (승인 대기)
- [ ] `/src/app/partner/suspended/page.tsx` (계정 정지)
- [ ] 각 상태별 UI 구현

**예상 소요시간**: 30분
**담당자**: 개발자

## Phase 4: API 구현

### Task 4.1: 파트너 상태 확인 API
- [ ] `/api/partner/check-status` 엔드포인트 생성
- [ ] 인증 토큰 검증
- [ ] 파트너 상태 조회
- [ ] 상태별 응답 처리

**파일 경로**: `src/app/api/partner/check-status/route.ts`
**예상 소요시간**: 30분
**담당자**: 개발자

### Task 4.2: 로그인 처리 로직
- [ ] Supabase Auth 로그인 연동
- [ ] 파트너 정보 조회
- [ ] 세션 관리
- [ ] Remember Me 기능 구현

**파일 경로**: `src/lib/api/partner/auth.ts`
**예상 소요시간**: 40분
**담당자**: 개발자

## Phase 5: 미들웨어 및 라우팅

### Task 5.1: 미들웨어 업데이트
- [ ] 파트너 인증 확인 로직 추가
- [ ] 파트너 상태별 접근 제어
- [ ] 공개 경로 설정 (`/partner/login`)

**파일 경로**: `src/middleware.ts`
**예상 소요시간**: 30분
**담당자**: 개발자

### Task 5.2: 라우트 보호
- [ ] 파트너 전용 경로 보호
- [ ] 미인증 시 로그인 페이지로 리다이렉트
- [ ] 상태별 접근 권한 설정

**예상 소요시간**: 20분
**담당자**: 개발자

## Phase 6: 스타일링 및 반응형

### Task 6.1: 컴포넌트 스타일링
- [ ] Tailwind CSS 클래스 적용
- [ ] 보라색 테마 일관성
- [ ] 호버/포커스 효과

**예상 소요시간**: 30분
**담당자**: 개발자

### Task 6.2: 반응형 디자인
- [ ] 모바일 최적화 (320px~)
- [ ] 태블릿 최적화 (768px~)
- [ ] 데스크톱 최적화 (1024px~)

**예상 소요시간**: 25분
**담당자**: 개발자

### Task 6.3: 애니메이션 및 전환
- [ ] 로딩 애니메이션
- [ ] 페이지 전환 효과
- [ ] 마이크로 인터랙션

**예상 소요시간**: 20분
**담당자**: 개발자

## Phase 7: 테스트

### Task 7.1: 단위 테스트
- [ ] 유효성 검증 로직 테스트
- [ ] API 함수 테스트
- [ ] 컴포넌트 렌더링 테스트

**파일 경로**: `__tests__/partner/login/`
**예상 소요시간**: 40분
**담당자**: 개발자

### Task 7.2: 통합 테스트
- [ ] 로그인 플로우 테스트
- [ ] 상태별 리다이렉트 테스트
- [ ] 에러 시나리오 테스트

**예상 소요시간**: 30분
**담당자**: 개발자

### Task 7.3: E2E 테스트
- [ ] 전체 로그인 시나리오
- [ ] Remember Me 기능 테스트
- [ ] 브라우저 호환성 테스트

**예상 소요시간**: 30분
**담당자**: 개발자

## Phase 8: 문서화 및 배포

### Task 8.1: 문서 작성
- [ ] API 문서 업데이트
- [ ] 컴포넌트 문서 작성
- [ ] 사용자 가이드 작성

**예상 소요시간**: 25분
**담당자**: 개발자

### Task 8.2: 코드 품질 검증
- [ ] TypeScript 타입 체크
- [ ] ESLint 검사
- [ ] Prettier 포맷팅
- [ ] 빌드 테스트

**예상 소요시간**: 15분
**담당자**: 개발자

### Task 8.3: PR 및 배포
- [ ] PR 생성 및 설명 작성
- [ ] 코드 리뷰 반영
- [ ] 스테이징 배포
- [ ] 프로덕션 배포

**예상 소요시간**: 30분
**담당자**: 개발자

## 위험 요소 및 대응 방안

### 1. 기술적 위험
- **Supabase Auth 연동 이슈**: 기존 코드 참고, 문서 확인
- **세션 관리 복잡성**: Remember Me 기능 단순화 검토

### 2. 일정 위험
- **예상 지연 요소**: 상태별 처리 로직 복잡성
- **대응 방안**: MVP 우선 구현 (기본 로그인만)

### 3. 품질 위험
- **보안 취약점**: 철저한 입력값 검증
- **사용성 이슈**: 사용자 테스트 진행

## 총 예상 소요시간

| Phase | 예상 시간 | 비고 |
|-------|----------|------|
| Phase 1: 타입 정의 | 35분 | |
| Phase 2: UI 컴포넌트 | 120분 | **우선순위 높음** |
| Phase 3: 페이지 구현 | 90분 | |
| Phase 4: API 구현 | 70분 | |
| Phase 5: 미들웨어 | 50분 | |
| Phase 6: 스타일링 | 75분 | |
| Phase 7: 테스트 | 100분 | |
| Phase 8: 문서화/배포 | 70분 | |
| **총합** | **약 10시간** | 1.5일 소요 예상 |

## 완료 기준 (Definition of Done)

- [ ] 모든 기능 요구사항 구현 완료
- [ ] 타입 체크 및 린트 통과
- [ ] 단위 테스트 및 통합 테스트 통과
- [ ] 반응형 디자인 확인
- [ ] 접근성 기준 준수
- [ ] 코드 리뷰 완료
- [ ] 문서화 완료
- [ ] 스테이징 환경 테스트 완료

## 체크리스트

### 개발 전
- [ ] 파트너 회원가입 기능 이해
- [ ] Supabase Auth 문서 확인
- [ ] 기존 미들웨어 구조 파악

### 개발 중
- [ ] 커밋 메시지 규칙 준수
- [ ] 코드 스타일 가이드 준수
- [ ] 주기적인 테스트 실행

### 개발 후
- [ ] 성능 최적화 확인
- [ ] 보안 취약점 검토
- [ ] 사용자 피드백 수집