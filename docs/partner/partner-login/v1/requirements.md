# 파트너 로그인 기능 요구사항 v1

## 개요

파트너(스튜디오 운영자)가 ShallWe 플랫폼에 로그인할 수 있는 인증 시스템을 구현합니다.
파트너 회원가입 완료 후 계정 상태에 따라 적절한 서비스 접근 권한을 제공합니다.

## 기능 요구사항

### 1. 로그인 폼

#### 1.1 로그인 정보 입력
- **이메일** (필수)
  - 이메일 형식 검증
  - 회원가입 시 사용한 이메일
- **비밀번호** (필수)
  - 마스킹 처리
  - 비밀번호 표시/숨기기 토글

#### 1.2 추가 기능
- **로그인 상태 유지**
  - "로그인 상태 유지" 체크박스
  - 체크 시 30일간 로그인 유지
  - 체크 해제 시 브라우저 종료 시 로그아웃
- **비밀번호 찾기**
  - 비밀번호 재설정 페이지로 이동 링크
- **회원가입 링크**
  - 파트너 회원가입 페이지로 이동

### 2. 인증 처리

#### 2.1 로그인 프로세스
1. 이메일 + 비밀번호 입력
2. Supabase Auth 인증
3. 파트너 정보 조회
4. 파트너 상태 확인
5. 상태별 리다이렉트

#### 2.2 파트너 상태별 처리
- **ACTIVE**: 파트너 대시보드로 이동
- **PENDING**: 승인 대기 안내 페이지로 이동
- **SUSPENDED**: 계정 정지 안내 페이지로 이동
- **REJECTED**: 가입 거절 안내 페이지로 이동

#### 2.3 에러 처리
- 이메일 미존재
- 비밀번호 불일치
- 이메일 미인증 상태
- 서버 오류

### 3. 유효성 검증

#### 3.1 클라이언트 사이드 검증
- React Hook Form + Zod 스키마 사용
- 실시간 필드 검증
- 사용자 친화적 에러 메시지

#### 3.2 서버 사이드 검증
- API 레벨에서 모든 필드 재검증
- SQL Injection 방지
- XSS 방지를 위한 입력값 이스케이핑

### 4. 인증 시스템

#### 4.1 Supabase Auth 연동
- 이메일/비밀번호 기반 로그인
- 세션 관리
- 리프레시 토큰 처리

### 5. API 명세

#### 5.1 GET /api/partner/check-status
**설명**: 로그인 후 파트너 상태 확인

**Request Headers:**
```typescript
{
  Authorization: string; // Bearer token
}
```

**Success Response (200):**
```typescript
{
  success: true;
  data: {
    partnerId: string;
    status: "ACTIVE" | "PENDING" | "SUSPENDED" | "REJECTED";
    message?: string; // 상태별 안내 메시지
  };
}
```

**Error Response (401):**
```typescript
{
  success: false;
  message: "인증되지 않은 요청입니다.";
}
```

### 6. 보안 요구사항

#### 6.1 데이터 보호
- 비밀번호 전송 시 HTTPS 강제
- CSRF 토큰 검증
- 민감 정보 로깅 금지

#### 6.2 접근 제어
- Rate Limiting (분당 5회 로그인 시도 제한)
- 5회 실패 시 15분간 계정 잠금
- IP 기반 차단 기능

#### 6.3 세션 보안
- Secure, HttpOnly 쿠키 사용
- 세션 하이재킹 방지
- 동시 로그인 제한 (선택적)

### 7. 사용자 경험

#### 7.1 반응형 디자인
- 모바일 우선 설계
- 태블릿/데스크톱 최적화
- 접근성 준수 (WCAG 2.1 AA)

#### 7.2 사용자 피드백
- 로딩 상태 표시
- 성공/실패 메시지
- 자동 포커스 관리

#### 7.3 에러 처리
- 친화적인 에러 메시지
- 해결 방법 안내
- 고객센터 연결 링크

### 8. 성능 요구사항

- 페이지 로드 시간: 2초 이내
- 로그인 처리 시간: 1초 이내
- 동시 사용자: 100명 이상 지원

### 9. 테스트 요구사항

- 단위 테스트: 유효성 검증 로직
- 통합 테스트: 로그인 플로우
- E2E 테스트: 전체 사용자 시나리오

### 10. 모니터링 및 로깅

- 로그인 성공/실패 로그
- 비정상 접근 패턴 감지
- 성능 메트릭 수집

## 제외 범위

- 소셜 로그인 (카카오, 네이버 등)
- 2단계 인증 (2FA)
- 생체 인증
- SSO (Single Sign-On)

## 의존성

- Supabase Auth 시스템
- 파트너 회원가입 기능 (#24)
- 기존 미들웨어 시스템

## 다음 단계

- 비밀번호 찾기/재설정 기능
- 파트너 대시보드
- 계정 설정 페이지