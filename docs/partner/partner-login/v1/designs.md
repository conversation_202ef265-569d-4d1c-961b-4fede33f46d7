# 파트너 로그인 디자인 명세 v1

## 개요

파트너 로그인 페이지의 UI/UX 디자인을 정의합니다. 파트너 회원가입 페이지와 일관된 디자인 언어를 사용하여 통일된 사용자 경험을 제공합니다.

## 페이지 구조

### URL 및 라우팅
- **경로**: `/partner/login`
- **레이아웃**: 독립 레이아웃 (헤더/푸터 포함)
- **메타데이터**: 
  - Title: "파트너 로그인 | ShallWe"
  - Description: "ShallWe 파트너 로그인 페이지"

### 전체 레이아웃
```
┌─────────────────────────────────────┐
│          Header (간소화)            │
├─────────────────────────────────────┤
│                                     │
│      ┌─────────────────┐           │
│      │                 │           │
│      │   로그인 폼     │           │
│      │                 │           │
│      └─────────────────┘           │
│                                     │
├─────────────────────────────────────┤
│             Footer                  │
└─────────────────────────────────────┘
```

## UI 컴포넌트 설계

### 1. 로그인 폼 컨테이너
```typescript
interface LoginFormContainerProps {
  className?: string;
}
```
- **스타일**:
  - 배경: `bg-white`
  - 그림자: `shadow-lg`
  - 모서리: `rounded-lg`
  - 패딩: `p-8` (모바일: `p-6`)
  - 최대 너비: `max-w-md`

### 2. 헤더 영역
- **로고**: "SHALLWE" (보라색, `text-3xl font-bold text-purple-600`)
- **제목**: "파트너 로그인" (`text-xl font-semibold text-gray-900`)
- **부제목**: "파트너 계정으로 로그인하세요" (`text-sm text-gray-500`)

### 3. 입력 필드

#### 3.1 이메일 입력
```typescript
interface EmailInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
}
```
- **라벨**: "이메일" (필수 표시)
- **placeholder**: "<EMAIL>"
- **아이콘**: 이메일 아이콘 (좌측)
- **유효성 표시**: 
  - 유효: 초록색 체크 아이콘
  - 무효: 빨간색 X 아이콘

#### 3.2 비밀번호 입력
```typescript
interface PasswordInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  showPassword: boolean;
  onToggleShow: () => void;
}
```
- **라벨**: "비밀번호" (필수 표시)
- **placeholder**: "비밀번호를 입력하세요"
- **토글 버튼**: 눈 아이콘 (우측)
- **상태**: 
  - 기본: 마스킹 처리
  - 표시: 텍스트 표시

### 4. 추가 옵션

#### 4.1 로그인 상태 유지
```typescript
interface RememberMeProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
}
```
- **체크박스**: 보라색 테마 (`text-purple-600`)
- **라벨**: "로그인 상태 유지" (`text-sm text-gray-600`)
- **위치**: 폼 하단, 좌측 정렬

#### 4.2 비밀번호 찾기
- **링크**: "비밀번호를 잊으셨나요?" (`text-sm text-purple-600 hover:text-purple-700`)
- **위치**: 폼 하단, 우측 정렬

### 5. 버튼

#### 5.1 로그인 버튼
```typescript
interface LoginButtonProps {
  loading?: boolean;
  disabled?: boolean;
  onClick: () => void;
}
```
- **스타일**:
  - 배경: `bg-purple-600 hover:bg-purple-700`
  - 텍스트: `text-white font-medium`
  - 크기: `w-full py-3`
  - 로딩: 스피너 + "로그인 중..."

#### 5.2 회원가입 링크
- **텍스트**: "아직 계정이 없으신가요? 회원가입"
- **스타일**: `text-sm text-gray-600` + `text-purple-600 hover:text-purple-700`
- **위치**: 로그인 버튼 하단

## 디자인 시스템

### 색상 팔레트
```scss
// Primary (Purple)
$purple-50: #f9f5ff;
$purple-100: #f3e8ff;
$purple-200: #e9d5ff;
$purple-300: #d8b4fe;
$purple-400: #c084fc;
$purple-500: #a855f7;
$purple-600: #9333ea;  // 주 색상
$purple-700: #7e22ce;

// Neutral (Gray)
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Status
$green-500: #10b981;  // 성공
$red-500: #ef4444;    // 에러
$yellow-500: #f59e0b; // 경고
```

### 타이포그래피
```scss
// 폰트: Inter (기본), 시스템 폰트 폴백

// 제목
h1: font-size: 1.875rem; // 30px
h2: font-size: 1.25rem;  // 20px
h3: font-size: 0.875rem; // 14px

// 본문
body: font-size: 1rem;    // 16px
small: font-size: 0.875rem; // 14px
```

### 스페이싱
```scss
// 기본 단위: 4px
$spacing-1: 4px;
$spacing-2: 8px;
$spacing-3: 12px;
$spacing-4: 16px;
$spacing-5: 20px;
$spacing-6: 24px;
$spacing-8: 32px;
```

## 상호작용 디자인

### 1. 폼 유효성 검증
- **실시간 검증**: 입력 중 debounce 300ms
- **에러 표시**: 필드 하단, 빨간색 텍스트
- **성공 표시**: 필드 우측, 초록색 체크

### 2. 로딩 상태
```typescript
// 로딩 중 UI 변화
- 입력 필드: disabled
- 버튼: 스피너 + "로그인 중..."
- 전체 폼: opacity 0.6
```

### 3. 에러 처리
```typescript
interface ErrorState {
  field?: string; // 특정 필드 에러
  message: string;
}
```
- **필드 에러**: 해당 필드 하단 표시
- **전체 에러**: Alert 컴포넌트로 상단 표시
- **에러 색상**: `border-red-500`, `text-red-600`

### 4. 성공 처리
- **토스트 메시지**: "로그인되었습니다"
- **리다이렉트**: 파트너 상태에 따라
  - ACTIVE → `/partner/dashboard`
  - PENDING → `/partner/pending`
  - SUSPENDED → `/partner/suspended`

## 상태별 화면

### 1. 기본 상태
- 모든 필드 활성화
- 포커스: 이메일 필드

### 2. 로딩 중
- 모든 필드 비활성화
- 로그인 버튼에 스피너
- 폼 전체 투명도 감소

### 3. 에러 상태
- 에러 메시지 표시
- 에러 필드 빨간색 테두리
- 포커스: 에러 필드

### 4. 파트너 상태별 안내
#### PENDING (승인 대기)
```
┌─────────────────────────────┐
│     ⏰ (대기 아이콘)        │
│                             │
│   승인 대기 중입니다        │
│                             │
│ 운영팀에서 검토 후         │
│ 연락드리겠습니다.          │
│                             │
│  [문의하기]  [홈으로]      │
└─────────────────────────────┘
```

#### SUSPENDED (계정 정지)
```
┌─────────────────────────────┐
│     ⚠️ (경고 아이콘)        │
│                             │
│   계정이 정지되었습니다     │
│                             │
│ 자세한 내용은 고객센터로   │
│ 문의해주세요.              │
│                             │
│     [고객센터 연결]         │
└─────────────────────────────┘
```

## 반응형 디자인

### 모바일 (320px ~ 767px)
```scss
.login-container {
  padding: 1.5rem; // 24px
  margin: 1rem;    // 16px
  
  .form-title {
    font-size: 1.5rem; // 24px
  }
  
  .input-field {
    font-size: 1rem;   // 16px
    padding: 0.75rem;  // 12px
  }
}
```

### 태블릿 (768px ~ 1023px)
```scss
.login-container {
  max-width: 448px;  // 28rem
  margin: 2rem auto;
  
  .form-layout {
    padding: 2rem; // 32px
  }
}
```

### 데스크톱 (1024px ~)
```scss
.login-container {
  max-width: 448px;  // 28rem
  margin: 3rem auto;
  
  .form-layout {
    padding: 3rem; // 48px
  }
}
```

## 접근성

### 1. 키보드 네비게이션
- Tab 순서: 이메일 → 비밀번호 → 로그인 유지 → 로그인 버튼
- Enter 키: 폼 제출
- Esc 키: 에러 메시지 닫기

### 2. 스크린 리더
```html
<label for="email" class="sr-only">이메일</label>
<input 
  id="email" 
  aria-label="이메일 입력"
  aria-required="true"
  aria-invalid={hasError}
  aria-describedby="email-error"
/>
```

### 3. 색상 대비
- 텍스트/배경: 최소 4.5:1
- 버튼 텍스트: 최소 3:1
- 에러 메시지: WCAG AA 준수

## 애니메이션 및 전환

### 1. 마이크로 인터랙션
```scss
// 호버 효과
.button {
  transition: all 0.2s ease;
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
}

// 포커스 효과
.input {
  transition: border-color 0.2s ease;
  &:focus {
    border-color: $purple-500;
    outline: none;
    box-shadow: 0 0 0 3px rgba($purple-500, 0.1);
  }
}
```

### 2. 로딩 애니메이션
- 스피너: 360도 회전, 1초
- 페이드: opacity 0 → 1, 0.3초

## 컴포넌트 구조

```
/partner/login
├── PartnerLoginPage.tsx
├── components/
│   ├── LoginForm.tsx
│   ├── EmailInput.tsx
│   ├── PasswordInput.tsx
│   ├── RememberMeCheckbox.tsx
│   └── StatusMessage.tsx
└── hooks/
    └── usePartnerLogin.ts
```

## 디자인 참고사항

1. **일관성**: 파트너 회원가입 페이지와 동일한 디자인 언어 사용
2. **단순성**: 불필요한 요소 제거, 핵심 기능에 집중
3. **명확성**: 에러 메시지와 상태 표시를 명확하게
4. **접근성**: 모든 사용자가 쉽게 사용할 수 있도록