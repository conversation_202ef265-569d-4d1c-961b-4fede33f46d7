## **✅ 쉘위 파트너 센터 \- 수업 시간 관리 페이지 정책서**

## **1\. 페이지 개요**

| 항목 | 내용 |
| ----- | ----- |
| 페이지명 | 수업 시간 조회 및 확정 관리 페이지 |
| URL |  |
| 주요 목적 | 파트너(센터/트레이너)가 현재 모집 중/진행 중인 수업 시간별 신청 현황을 실시간으로 조회하고, 모집 완료 또는 임박한 수업을 빠르게 확정할 수 있도록 지원 |
| 주요 사용자 | 센터 관리자 및 트레이너 (1계정 1센터 기준, 멀티센터 미지원) |
| 접근 방식 | 센터/트레이너 로그인 후 ‘클래스 진행 현황’ 페이지 접근 |

## **2\. 주요 기능 및 UI 구성**

### **2-1. 상단 필터**

| 필터 항목 | 설명 |
| :---- | :---- |
| 강사 | 등록된 트레이너 목록 중 선택 |
| 운동 종류 | 등록된 운동 분야 (예: 요가, 필라테스 등) 필터링 |
| 신청 상태 | 전체, 모집 중, 진행 중, 완료 상태 필터링 |

### **2-2. 수업 시간 카드 구성**

| 항목 | 설명 |
| :---- | :---- |
| 강사명 | 해당 수업을 담당하는 강사 표시 (예: 최하얀 코치님) |
| 수업명 | 등록된 수업명 (수업 이름 \+ 최대 인원 수 명시) |
| 요일/시간 | 해당 수업의 요일 및 시간 표시 (ex. 월/금 10:00\~11:00) |
| 신청자 정보 요약 | 신청자 수 및 연령 정보 (예: 45\~54세 여성 2명) |
| 상태 배지 | 모집 중 / 진행 중 / 완료 상태 표시 및 컬러 구분 |
| 신청자 정보 보기 버튼 | 클릭 시 신청자 상세 정보 노출 |
| 수업 확정 버튼 | 정원 수 이상 모집 시 노출되며 클릭 시 확정 처리 및 알림 발송 |
| 수업 상세 보기 | 수업 상세페이지로 이동 (노출 불가 상태여도 접근 가능) |

---

## **3\. 수업 확정/취소/완료 로직**

### **3-1. 모집 완료 기준**

* 수업 시간별 신청자 수가 1명 이상인 경우부터 수업 확정 버튼 활성화됨

### **3-2. 수업 확정 처리**

| 항목 | 내용 |
| :---- | :---- |
| 확정 처리 | 신청자 1명 이상부터 확정 가능 확정 버튼 클릭 시 ‘시작일 설정’ 캘린더 팝업 노출 날짜 선택 \> 수업 확정 안내하기 클릭 시 메시지 발송 |
| 회원 알림 | 수업 확정 시 신청자에게 알림톡 발송 |
| 회원 취소 | 확정 이후에도 회원은 개별 수업 취소 가능 (단, 환불 정책 따름) |
| 센터 알림 | 회원 취소 발생 시, 센터 관리자에게 취소 알림 제공 |
| 수업 취소 가능 | 센터는 수업 참가 인원이 부족할 경우 전체 수업 시간 취소 가능 |

※ 일정 변경은 불가능하며, 별도 공지 없음

### **3-3. 자동 완료 전환**

| 상태 | 전환 조건 |
| :---- | :---- |
| 진행 중 → 완료 | 수업 마지막 일정의 다음날 오전 00시 자동 전환 |

---

## **4\. 신청자 정보 보기**

| 뷰 종류 | 노출 항목 |
| :---- | :---- |
| 요약 뷰 | 신청자 연령 min/max, 성별 및 인원수 (예: 여성 3명, 4554세) |
| 상세 뷰 | 신청자별 상세 정보 \- 이름(예: 최\*\*) \- 성별 \- 연령 \- 희망 운동 수준 \- 운동 목적 \- 주의 필요 부위 |

---

## **5\. 알림 정책**

* 수업 확정 시: 신청자 전체에게 알림톡 발송  
* 회원 개별 취소 시: 센터 관리자에게 알림톡 발송  
* 수업 전체 취소 시: 신청자 전원에게 취소 알림 발송 및 환불 안내

---

## **6\. 기타 정책 및 예외**

| 항목 | 내용 |
| :---- | :---- |
| 확정 후 일정 변경 | 제공하지 않음 |
| 수업 노출 제한 | 노출 설정이 OFF된 수업도 URL 또는 마이페이지 통해 접근 가능 (단, 예약 불가) |
| 통계 오류 | 일부 수치는 실시간 계산 오류 가능성 있음 (초기 MVP 기준 대응 미진함) |

---

## **7\. 확인 및 향후 고려 사항 (To-be)**

* 신청자 상태 변경 로그 저장 및 수업 취소 이력 확인 기능  
* 신청자 개별 수업 확정 여부 안내 기능 개선  
* 파트너 대상 UX 가이드 별도 제공 필요 (높은 복잡도 고려)

### **1\. 수업 확정 플로우 및 취소 처리**

| 단계 | 설명 |
| ----- | ----- |
| ① 정원 이상 모집 | 수업 시간별 신청 인원이 `1명 이상`인 경우, '수업 확정' 버튼 활성화 |
| ② 센터가 수업 확정 | 센터/트레이너가 수업 확정 시점에 맞춰 `확정 알림톡` 회원에게 발송됨 |
| ③ 회원 취소 가능 | 확정 이후에도 회원은 개별 수업 취소 가능 (단, 환불 가능 기간 이내) |
| ④ 센터 알림 | 회원 취소 발생 시, 센터에게 알림 제공 (예: "화요일 10:00 수업 1인 취소 발생") |
| ⑤ 센터 수업 취소 결정 가능 | 취소 인원이 많아 수업 진행이 어려운 경우 센터에서 `수업 전체 취소` 가능 (취소 시 전원 환불 및 안내톡 발송) |

🔹 취소된 수업은 회원 마이페이지 및 센터 수업 관리 목록에서 ‘취소됨’ 상태로 표시  
🔹 수업 확정 후 일정 변경은 불가하고, 이를 안내하는 별도 메시지는 제공하지 않음

