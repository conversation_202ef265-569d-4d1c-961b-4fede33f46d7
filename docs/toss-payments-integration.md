# 토스페이먼츠 결제위젯 v2 연동 가이드

## 개요

기존의 수동 결제수단 선택 UI를 토스페이먼츠 결제위젯 v2로 대체하여 더 나은 사용자 경험과 안전한 결제 환경을 제공합니다.

## 주요 변경사항

### 1. 환경변수 추가
- `NEXT_PUBLIC_TOSS_CLIENT_KEY`: 토스페이먼츠 클라이언트 키 (브라우저에서 접근 가능)
- `TOSS_SECRET_KEY`: 토스페이먼츠 시크릿 키 (서버에서만 접근 가능)

### 2. 새로운 컴포넌트
- `src/components/payment/TossPaymentWidget.tsx`: 토스페이먼츠 결제위젯 컴포넌트
- `src/lib/api/toss-payments.ts`: 토스페이먼츠 API 클라이언트
- `src/app/api/payment/toss/confirm/route.ts`: 결제 승인 API 엔드포인트

### 3. 업데이트된 페이지
- `src/app/(main)/payment/page.tsx`: 토스 결제위젯으로 대체
- `src/app/(main)/payment/success/page.tsx`: 토스페이먼츠 결제 승인 처리 추가
- `src/app/(main)/payment/fail/page.tsx`: 결제 실패 페이지 추가

## 설정 방법

### 1. 환경변수 설정

`.env.local` 파일에 다음 환경변수를 추가하세요:

```bash
# 토스페이먼츠 설정
NEXT_PUBLIC_TOSS_CLIENT_KEY=test_ck_테스트키
TOSS_SECRET_KEY=test_sk_테스트키
```

### 2. 토스페이먼츠 개발자센터 설정

1. [토스페이먼츠 개발자센터](https://developers.tosspayments.com/)에 가입
2. 새 애플리케이션 생성
3. 클라이언트 키와 시크릿 키 발급
4. 성공/실패 리다이렉트 URL 설정:
   - 성공 URL: `https://your-domain.com/payment/success`
   - 실패 URL: `https://your-domain.com/payment/fail`

## 결제 플로우

### 1. 결제 시작
1. 사용자가 결제 페이지 접근
2. 토스페이먼츠 결제위젯 초기화
3. 결제수단 및 약관 UI 렌더링

### 2. 결제 요청
1. 사용자가 결제수단 선택 및 약관 동의
2. 결제 버튼 클릭
3. 토스페이먼츠 결제창 열림
4. 사용자 결제 정보 입력

### 3. 결제 승인
1. 토스페이먼츠에서 결제 승인 처리
2. 성공 시 `successUrl`로 리다이렉트 (paymentKey, orderId, amount 포함)
3. 실패 시 `failUrl`로 리다이렉트 (오류 코드 및 메시지 포함)

### 4. 결제 확정
1. 성공 페이지에서 결제 승인 API 호출
2. 토스페이먼츠 서버에 결제 확정 요청
3. 결제 완료 정보 표시

## API 엔드포인트

### POST /api/payment/toss/confirm
결제 승인을 처리하는 엔드포인트

**요청 파라미터:**
```json
{
  "paymentKey": "string",
  "orderId": "string", 
  "amount": number
}
```

**응답:**
```json
{
  "success": boolean,
  "data": {
    "paymentKey": "string",
    "orderId": "string",
    "status": "string",
    "method": "string",
    "totalAmount": number,
    "approvedAt": "string",
    "receipt": {
      "url": "string"
    }
  }
}
```

## 테스트 방법

### 1. 테스트 카드 정보
토스페이먼츠 테스트 환경에서 사용할 수 있는 테스트 카드:
- 카드번호: 4966-0000-0000-0004
- 유효기간: 12/28
- CVC: 123

### 2. 테스트 시나리오
1. 결제 페이지 접근
2. 결제위젯 로딩 확인
3. 결제수단 선택
4. 결제 정보 입력
5. 결제 승인 확인
6. 성공 페이지 정보 확인

## 보안 고려사항

### 1. 환경변수 관리
- 시크릿 키는 절대 클라이언트에 노출되지 않도록 주의
- 프로덕션 환경에서는 반드시 실제 키 사용

### 2. 결제 금액 검증
- 클라이언트에서 전달된 금액을 서버에서 재검증
- 데이터베이스에 저장된 주문 정보와 비교

### 3. 결제 중복 방지
- orderId를 고유하게 생성
- 동일한 주문에 대한 중복 결제 방지 로직 구현

## 문제 해결

### 1. 결제위젯이 로드되지 않는 경우
- 환경변수 설정 확인
- 네트워크 연결 상태 확인
- 브라우저 콘솔 오류 메시지 확인

### 2. 결제 승인이 실패하는 경우
- 시크릿 키 정확성 확인
- API 엔드포인트 정상 동작 확인
- 토스페이먼츠 서버 상태 확인

### 3. 리다이렉트 URL 오류
- 개발자센터에 정확한 URL 등록 확인
- 프로토콜(http/https) 일치 확인

## 참고 자료

- [토스페이먼츠 결제위젯 v2 문서](https://docs.tosspayments.com/sdk/v2/js)
- [토스페이먼츠 API 가이드](https://docs.tosspayments.com/guides)
- [결제위젯 샘플 프로젝트](https://github.com/tosspayments/payment-widget-sample)