# 클래스 라이프사이클 관리 기능 요구사항

## 개요

현재 ShallWe 플랫폼의 클래스 시스템은 추상적인 템플릿과 스케줄만 제공하여 실제 클래스 운영에 필요한 핵심 기능들이 부재한 상황입니다. 이를 해결하기 위해 클래스의 전체 라이프사이클을 관리할 수 있는 시스템을 구축합니다.

## 현재 문제점

### 1. 클래스 기간 정보 부재

```mermaid
graph TD
    A[현재 class_templates] --> B[title, category, instructor_id]
    A --> C[price, max_capacity, duration]
    A --> D[❌ 모집 기간 없음]
    A --> E[❌ 수업 기간 없음]
    
    D --> F[Dashboard 상태 구분 불가]
    E --> F
    F --> G["모집중/진행중/종료" 구분 어려움]
    F --> H[다가오는 수업이 실제 수업 아님]
```

**문제 상황:**
- 클래스 템플릿에 시작일/종료일 정보가 없음
- Dashboard에서 "모집 중인 수업", "진행 중인 수업", "종료된 수업" 구분 불가
- "다가오는 수업"이 실제 다가오는 수업이 아닌 추상적 템플릿 정보
- 수강 신청 기간 설정 불가

### 2. 개별 수업 관리 불가

**문제 상황:**
- 실제 수업 인스턴스가 존재하지 않음
- 개별 수업 취소, 시간 조정, 보강 수업 처리 불가
- 휴강 시 수강생 알림 및 환불 처리 어려움
- 수업별 다른 정원 설정이나 특이사항 기록 불가

**실제 운영 요구사항:**
```
시나리오: 4주 요가 클래스 (매주 월/수/금)
- 2주차 금요일: 강사 개인사정으로 휴강 → 다음 주 토요일 보강
- 3주차 월요일: 체험 수업으로 정원 15명 → 20명 확대  
- 4주차 수요일: 스튜디오 사정으로 시간 변경 (10시 → 11시)
```

### 3. 출석 및 실행 기록 부재

**문제 상황:**
- 수업이 실제로 진행되었는지 기록 없음
- 수강생별 출석 현황 관리 불가
- 강사의 실제 수업 운영 현황 파악 어려움
- 수업 품질 관리 및 개선을 위한 데이터 부족

## 비즈니스 요구사항

### 다중 시간대 지원

하나의 클래스 템플릿이 여러 시간대 그룹을 지원해야 합니다.

**🆕 MVP 스펙 (간소화 버전):**
- 하나의 그룹 내 모든 개별 스케줄은 동일한 시간대 사용
- 요일만 다르게 선택 가능 (예: 월/수/금 19:00-20:00)
- UI에서 요일 다중선택 + 시간 통일 방식 적용

```
예시: "4주 요가 클래스"
├── 전체 클래스 정보
│   ├── 수업 기간: 2024-12-02 ~ 2024-12-27
│   └── 모집 기간: 2024-11-15 ~ 2024-11-30
├── 오전반1
│   └── 월/수/금 10:00-11:00 (정원 10명)
└── 저녁반1  
    └── 월/수/금 19:00-20:00 (정원 15명)
```

**수강생 관점:**
- 같은 클래스의 다른 시간대 선택 가능
- 시간대별 독립적인 정원 관리
- 시간대별 다른 가격 정책 적용 가능
- 모든 그룹이 동일한 수업 기간 및 모집 기간 사용

### 🔄 스케줄 그룹별 기간 관리 (향후 개선 사항)

**현재 구현:**
- 모든 스케줄 그룹은 클래스 템플릿의 기간을 공유
- 그룹별 독립적인 기간 설정 미지원
- 동일한 모집 기간과 수업 기간 적용

**향후 개선 방향:**
- 그룹별 서로 다른 수업 시작일/종료일 설정 가능
- 그룹별 서로 다른 모집 기간 설정 가능
- 템플릿 기간은 전체 클래스의 범위 개념으로 활용
- 그룹별 독립적인 상태 관리

**향후 운영 시나리오 (미구현):**
```
"8주 필라테스 클래스" 템플릿 (현재 구현)
├── 전체 클래스 정보
│   ├── 수업 기간: 2024-12-02 ~ 2025-01-27
│   └── 모집 기간: 2024-11-15 ~ 2024-11-30
├── 평일 오전반
│   └── 수업: 월/수/금 09:00-10:00
├── 평일 저녁반
│   └── 수업: 월/수/금 19:00-20:00  
└── 주말 집중반
    └── 수업: 토/일 10:00-12:00
```

### 효율적인 검색 지원

수강생이 원하는 조건으로 클래스를 쉽게 찾을 수 있어야 합니다.

**검색 시나리오:**
```sql
-- 화요일 저녁 요가 수업 찾기
"화요일 + 18시 이후 + 요가 (specialty)"

-- 평일 오전 필라테스 수업 찾기  
"월~금 + 09~12시 + 필라테스 (specialty)"

-- 12월 시작하는 초급 수업 찾기
"12월 시작 + 초급 레벨"
```

### 유연한 운영 지원

실제 클래스 운영에 필요한 다양한 상황을 지원해야 합니다.

**운영 시나리오:**
- **휴강 처리**: 개별 수업 취소 + 수강생 자동 알림
- **보강 수업**: 휴강한 수업의 대체 수업 스케줄링
- **시간 조정**: 특정 날짜만 시간 변경
- **정원 조정**: 체험 수업, 특별 이벤트로 정원 변경
- **강사 메모**: 수업별 특이사항 기록

## 기능 요구사항

### 1. 클래스 템플릿 확장

```typescript
// 기존 정보 유지
title, description, specialty, level, instructor_id, studio_id,
max_capacity, price_per_session, duration_minutes, curriculum

// 🆕 라이프사이클 관리 (클래스 템플릿 레벨)
recruitment_start_date: date,    // 모집 시작일
recruitment_end_date: date,      // 모집 종료일
class_start_date: date,          // 수업 시작일
class_end_date: date,            // 수업 종료일

// 상태 관리
status: 'upcoming' | 'recruiting' | 'ongoing' | 'completed' | 'cancelled'
```

**Dashboard 상태 구분:**
```typescript
function getClassStatus(template, now = new Date()) {
  if (now < template.recruitment_start_date) return 'upcoming';
  if (now <= template.recruitment_end_date) return 'recruiting';
  if (now <= template.class_end_date) return 'ongoing';
  return 'completed';
}
```

### 2. 스케줄 그룹 및 패턴 관리

```typescript
// 스케줄 그룹 관리 (현재 구현)
class_schedule_groups {
  id: uuid,
  class_template_id: uuid,
  group_name: string,              // "오후반", "저녁반", "주말반"
  group_description?: string,      // 그룹 설명
  max_participants: number,        // 그룹별 정원
  price_per_session?: decimal,     // 그룹별 가격 (옵션)
  sessions_per_week: number,       // 주당 횟수
  is_active: boolean
}
// 주의: 현재 구현에서는 그룹별 독립적인 기간 관리를 지원하지 않음
// 모든 그룹이 템플릿의 기간을 공유함

// 스케줄 패턴 (검색 최적화)
class_schedules {
  id: uuid,
  schedule_group_id: uuid,
  day_of_week: string,             // 'MONDAY', 'TUESDAY', ...
  start_time: time,                // '14:00:00'
  end_time: time                   // '15:00:00'
}
```

### 3. 실제 수업 인스턴스 관리

```typescript
class_occurrences {
  id: uuid,
  class_template_id: uuid,
  schedule_group_id: uuid,
  class_schedule_id: uuid,         // 어떤 패턴에서 생성되었는지
  
  // 실제 수업 정보
  occurrence_date: date,           // '2024-12-02'
  start_time: time,                // 개별 조정 가능
  end_time: time,
  max_participants?: number,       // 개별 조정 가능 (NULL 가능, 그룹 설정 상속)
  
  // 상태 관리
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled',
  cancellation_reason?: string,
  instructor_notes?: string,
  
  // 보강 수업 관리
  is_substitute_class: boolean,
  original_occurrence_id?: uuid
}
```

### 4. 출석 기록 시스템

```typescript
class_attendances {
  id: uuid,
  class_occurrence_id: uuid,       // 특정 수업 참조
  member_id: uuid,                 // 수강생 참조
  
  attendance_status: 'present' | 'absent' | 'late' | 'excused',
  checked_in_at?: timestamp,
  notes?: string,
  
  created_at: timestamp
}
```

### 5. 수강 신청 시스템

```typescript
class_enrollments {
  id: uuid,
  member_id: uuid,
  class_template_id: uuid,         // 전체 클래스에 신청
  schedule_group_id: uuid,         // 특정 시간대 그룹 선택
  
  enrollment_status: 'pending' | 'confirmed' | 'cancelled' | 'completed',
  enrolled_at: timestamp,
  payment_id?: string,
  paid_amount: decimal,
  
  // 환불 관리
  refund_amount?: decimal,
  refunded_at?: timestamp,
  refund_reason?: string
}
```

## 사용자 스토리

### 강사 관점

**US-1: 클래스 생성 및 기간 설정**
```
As a 강사
I want to 클래스 생성 시 모집 기간과 수업 기간을 설정할 수 있다
So that 수강생들이 명확한 일정을 확인하고 신청할 수 있다

Given: 새로운 요가 클래스를 개설하려고 한다
When: 클래스 정보 입력 시
- 모집 기간: 2024-11-15 ~ 2024-11-30
- 수업 기간: 2024-12-02 ~ 2024-12-27
- 시간대: 월/수/금 14:00-15:00 (오후반), 19:00-20:00 (저녁반)
Then: 자동으로 12개 수업 일정이 생성된다 (각 시간대별 6회씩)
```

**US-2: 개별 수업 관리**
```
As a 강사  
I want to 특정 날짜 수업을 개별적으로 관리할 수 있다
So that 예상치 못한 상황에 유연하게 대응할 수 있다

Scenario: 휴강 처리
Given: 12월 10일 월요일 수업이 예정되어 있다
When: 개인사정으로 해당 수업을 취소한다
Then: 
- 해당 수업 상태가 'cancelled'로 변경된다
- 신청한 수강생들에게 자동 알림이 발송된다
- 12월 14일 금요일에 보강 수업을 생성할 수 있다
```

**🔄 US-3: 구조화된 클래스 소개 입력**
```
As a 강사
I want to 클래스 소개를 구조화된 형태로 입력할 수 있다
So that 수강생들에게 체계적인 정보를 제공할 수 있다

Scenario: 클래스 소개 입력
Given: "4주 요가 클래스"를 개설하려고 한다
When: 클래스 정보 입력 시
Then: 
- 수업 소개 (overview)
- 수업 추천 대상 (targetAudience) - 태그 형식
- 커리큘럼 (curriculum) - 주차별 상세 내용
- 준비물 (materials) - 태그 형식
위 4가지 항목을 구조화된 형태로 입력할 수 있다
```

**US-4: 출석 체크 및 기록**
```
As a 강사
I want to 수업 후 출석을 체크하고 메모를 남길 수 있다  
So that 수강생별 참여도를 관리하고 수업 품질을 개선할 수 있다

Given: 12월 2일 월요일 14시 수업이 완료되었다
When: 출석 체크를 진행한다
Then:
- 수강생별 출석/결석/지각 상태를 기록한다
- 수업 관련 특이사항을 메모로 남긴다
- Dashboard에서 출석률 통계를 확인할 수 있다
```

### 수강생 관점

**US-5: 조건별 클래스 검색**
```
As a 수강생
I want to 원하는 조건으로 클래스를 검색할 수 있다
So that 내 일정에 맞는 클래스를 쉽게 찾을 수 있다

Scenario: 평일 저녁 요가 수업 찾기
Given: 직장인으로 평일 저녁에만 시간이 있다
When: "요가 + 평일 + 18시 이후" 조건으로 검색한다
Then: 조건에 맞는 클래스들이 시간대별로 표시된다
And: 각 시간대별 잔여 정원을 확인할 수 있다
```

**US-6: 시간대 선택 신청**
```
As a 수강생
I want to 같은 클래스의 다른 시간대 중 선택하여 신청할 수 있다
So that 내 일정에 가장 적합한 시간에 참여할 수 있다

Given: "4주 요가 클래스"에 오후반과 저녁반이 있다
When: 저녁반(월/수/금 19:00-20:00)을 선택하여 신청한다
Then: 
- 저녁반 12회 수업에 모두 참여할 권한을 얻는다
- 오후반 수업에는 참여할 수 없다
- 개별 수업별 출석 체크가 가능하다
```

### 시스템 관점

**US-7: 자동 상태 관리**
```
As a 시스템
I want to 클래스와 수업의 상태를 자동으로 관리할 수 있다
So that 정확한 정보를 실시간으로 제공할 수 있다

Given: 클래스들이 다양한 단계에 있다
When: 시간이 흘러간다
Then:
- 모집 시작일이 되면 'recruiting' 상태로 변경
- 모집 종료일이 되면 신규 신청을 차단
- 수업 종료일이 지나면 'completed' 상태로 변경
- 수업 시작 2시간 전에 강사에게 알림 발송
```

## 비기능 요구사항

### 성능 요구사항

**검색 성능:**
- 조건별 클래스 검색 응답 시간 1초 이내
- 10만 개 occurrence 데이터에서 실시간 검색 지원
- 인덱스 최적화: (day_of_week, start_time), (occurrence_date), (status)

**Dashboard 성능:**
- 강사 Dashboard 로딩 시간 2초 이내
- 실시간 수강 신청 현황 반영
- 캐싱 활용으로 반복 조회 최적화

### 확장성 요구사항

**데이터 확장성:**
- 강사별 수백 개 클래스 템플릿 지원
- 클래스당 수천 개 occurrence 생성 지원
- 월간 수만 건 출석 기록 처리

**기능 확장성:**
- 새로운 클래스 형태(워크숍, 캠프 등) 쉽게 추가
- 복잡한 반복 패턴(격주, 월별 등) 지원 가능
- 다중 강사, 팀 티칭 시스템 확장 준비

### 호환성 요구사항

**기존 시스템 호환성:**
- 현재 class_templates 데이터 100% 보존
- 기존 API 엔드포인트 하위 호환성 보장
- 점진적 마이그레이션으로 서비스 중단 없음

**데이터 마이그레이션:**
- 기존 class_schedules 데이터를 새 구조로 변환
- 기존 class_enrollments와 새 시스템 연동
- 마이그레이션 중 데이터 일관성 보장

## 제약사항

### 기술적 제약사항

- **DB 레벨 enum 금지**: 모든 상태값은 코드 레벨에서만 관리
- **점진적 마이그레이션**: 기존 테이블 구조 최대한 보존하며 확장
- **소프트 레퍼런스**: 성능을 위해 FK 제약 조건 사용하지 않음

### 비즈니스 제약사항

- **서비스 중단 금지**: 무중단 배포 및 점진적 기능 활성화
- **강사 독립성**: 강사별 독립적인 클래스 관리 권한 유지
- **결제 연동**: 기존 결제 시스템과의 호환성 보장

## 현재 구현 상태와의 차이점

### 구현된 기능
1. ✅ 클래스 템플릿 라이프사이클 관리 (모집/수업 기간)
2. ✅ 다중 스케줄 그룹 지원
3. ✅ Occurrence 기반 개별 수업 관리
4. ✅ 구조화된 클래스 소개 입력 (4개 탭)
5. ✅ Dashboard v2 API
6. ✅ 기본적인 검색 및 필터링

### 미구현 기능
1. ❌ 그룹별 독립적인 기간 관리 (모든 그룹이 템플릿 기간 공유)
2. ❌ 출석 체크 UI (API는 구현됨)
3. ❌ 자동 알림 시스템
4. ❌ 고급 분석 및 리포팅 UI

### 필드명 변경사항
- `category` → `specialty` (운동 분야)
- `max_group_size` → `max_participants` (그룹 최대 인원)

## 우선순위

### Phase 1: 핵심 기능 (High Priority)
1. 클래스 템플릿 기간 정보 추가
2. Occurrence 기본 CRUD 기능
3. Dashboard 상태 구분 개선
4. 기본 검색 기능

### Phase 2: 고급 기능 (Medium Priority)
1. 다중 시간대 그룹 관리
2. 개별 수업 취소/조정 기능
3. 보강 수업 스케줄링
4. 출석 체크 시스템

### Phase 3: 최적화 (Low Priority)
1. 고급 검색 및 필터링
2. 자동 알림 시스템
3. 상세 분석 및 리포팅
4. 모바일 최적화

## 성공 기준

### 기능적 성공 기준

1. **상태 구분**: Dashboard에서 모집중/진행중/종료 클래스 명확히 구분
2. **실제 수업**: 구체적인 날짜와 시간의 수업 인스턴스 관리
3. **유연한 운영**: 개별 수업 취소, 시간 조정, 보강 처리 가능
4. **출석 관리**: 수업별 출석 기록 및 통계 제공
5. **검색 기능**: 조건별 클래스 검색 정확도 95% 이상

### 품질 성공 기준

1. **성능**: 검색 응답 시간 1초 이내, Dashboard 로딩 2초 이내
2. **안정성**: 마이그레이션 중 데이터 손실 0%, 서비스 중단 0%
3. **호환성**: 기존 API 100% 하위 호환, 기존 데이터 100% 보존
4. **사용성**: 강사 만족도 90% 이상, 수강생 만족도 85% 이상

### 비즈니스 성공 기준

1. **운영 효율성**: 클래스 관리 시간 50% 단축
2. **수강생 경험**: 클래스 검색 및 신청 과정 개선
3. **데이터 활용**: 출석률, 취소율 등 운영 지표 정확한 측정
4. **확장성**: 새로운 클래스 형태 쉽게 추가 가능

이 요구사항을 바탕으로 체계적이고 유연한 클래스 라이프사이클 관리 시스템을 구축하여, 강사와 수강생 모두에게 향상된 경험을 제공하고자 합니다.