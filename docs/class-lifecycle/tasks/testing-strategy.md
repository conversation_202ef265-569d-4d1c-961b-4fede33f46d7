# 클래스 라이프사이클 테스트 전략

## 테스트 개요

클래스 라이프사이클 관리 시스템의 품질과 안정성을 보장하기 위한 포괄적인 테스트 전략입니다. Schedule-Occurrence 관계의 복잡성과 실시간 운영 요구사항을 고려하여 체계적인 테스트 접근 방식을 적용합니다.

## 테스트 피라미드

```mermaid
graph TD
    A[E2E Tests - 5%] --> B[Integration Tests - 25%]
    B --> C[Unit Tests - 70%]
    
    subgraph "E2E Tests"
        D[사용자 시나리오 검증]
        E[브라우저 호환성]
        F[전체 워크플로우]
    end
    
    subgraph "Integration Tests"
        G[API 엔드포인트]
        H[데이터베이스 연동]
        I[서비스 간 통신]
        J[외부 시스템 연동]
    end
    
    subgraph "Unit Tests"
        K[개별 함수 로직]
        L[컴포넌트 동작]
        M[비즈니스 규칙]
        N[유틸리티 함수]
    end
    
    A -.-> D
    A -.-> E
    A -.-> F
    
    B -.-> G
    B -.-> H
    B -.-> I
    B -.-> J
    
    C -.-> K
    C -.-> L
    C -.-> M
    C -.-> N
```

### 테스트 커버리지 목표

```mermaid
pie title 코드 커버리지 목표
    "라인 커버리지" : 90
    "브랜치 커버리지" : 85
    "함수 커버리지" : 95
    "기타" : 5
```

## 단위 테스트 (Unit Tests)

### 핵심 도메인 로직 테스트

#### Schedule-Occurrence 관계 테스트

```typescript
// tests/unit/occurrence-generator.test.ts
describe('OccurrenceGenerator', () => {
  describe('generateOccurrences', () => {
    test('should generate correct number of occurrences for 4-week class', () => {
      const template = createMockTemplate({
        class_start_date: '2024-12-02',
        class_end_date: '2024-12-27'
      });
      
      const schedules = [
        createMockSchedule({ day_of_week: 'MONDAY', start_time: '10:00' }),
        createMockSchedule({ day_of_week: 'WEDNESDAY', start_time: '10:00' }),
        createMockSchedule({ day_of_week: 'FRIDAY', start_time: '10:00' })
      ];
      
      const occurrences = generateOccurrences(template, schedules);
      
      expect(occurrences).toHaveLength(12); // 4주 × 3일
      expect(occurrences[0].occurrence_date).toBe('2024-12-02'); // 월요일
      expect(occurrences[1].occurrence_date).toBe('2024-12-04'); // 수요일
      expect(occurrences[2].occurrence_date).toBe('2024-12-06'); // 금요일
    });
    
    test('should inherit schedule properties to occurrences', () => {
      const template = createMockTemplate();
      const schedule = createMockSchedule({
        day_of_week: 'TUESDAY',
        start_time: '14:00',
        end_time: '15:00',
        max_participants: 8
      });
      
      const occurrences = generateOccurrences(template, [schedule]);
      
      occurrences.forEach(occurrence => {
        expect(occurrence.start_time).toBe('14:00');
        expect(occurrence.end_time).toBe('15:00');
        expect(occurrence.max_participants).toBe(8);
        expect(occurrence.class_schedule_id).toBe(schedule.id);
      });
    });
    
    test('should exclude specified dates', () => {
      const template = createMockTemplate({
        class_start_date: '2024-12-02',
        class_end_date: '2024-12-13'
      });
      
      const schedule = createMockSchedule({ day_of_week: 'MONDAY' });
      const excludeDates = ['2024-12-09']; // 크리스마스 휴일
      
      const occurrences = generateOccurrences(template, [schedule], excludeDates);
      
      expect(occurrences).toHaveLength(1); // 12/2만 생성, 12/9 제외
      expect(occurrences[0].occurrence_date).toBe('2024-12-02');
    });
  });
  
  describe('inheritance and independence', () => {
    test('should allow independent modification after creation', async () => {
      const occurrence = createMockOccurrence({
        start_time: '10:00',
        max_participants: 10
      });
      
      // 개별 수업 수정
      const updated = await updateOccurrence(occurrence.id, {
        start_time: '11:00',
        max_participants: 15,
        instructor_notes: '체험 수업으로 정원 확대'
      });
      
      expect(updated.start_time).toBe('11:00');
      expect(updated.max_participants).toBe(15);
      expect(updated.instructor_notes).toBe('체험 수업으로 정원 확대');
      
      // 원본 스케줄은 영향받지 않음
      const originalSchedule = await getSchedule(occurrence.class_schedule_id);
      expect(originalSchedule.start_time).toBe('10:00');
    });
  });
});
```

#### 클래스 라이프사이클 상태 관리 테스트

```typescript
// tests/unit/class-lifecycle.test.ts
describe('ClassLifecycleService', () => {
  describe('status transitions', () => {
    test('should transition from upcoming to recruiting', () => {
      const template = createMockTemplate({
        recruitment_start_date: '2024-12-01',
        status: 'upcoming'
      });
      
      const now = new Date('2024-12-01T09:00:00Z');
      const newStatus = calculateClassStatus(template, now);
      
      expect(newStatus).toBe('recruiting');
    });
    
    test('should transition from recruiting to ongoing', () => {
      const template = createMockTemplate({
        recruitment_end_date: '2024-11-30',
        class_start_date: '2024-12-01',
        status: 'recruiting'
      });
      
      const now = new Date('2024-12-01T10:00:00Z');
      const newStatus = calculateClassStatus(template, now);
      
      expect(newStatus).toBe('ongoing');
    });
    
    test('should not allow invalid transitions', () => {
      const template = createMockTemplate({ status: 'completed' });
      
      expect(() => {
        updateClassStatus(template.id, 'recruiting');
      }).toThrow('Invalid status transition');
    });
  });
  
  describe('occurrence generation rules', () => {
    test('should not generate occurrences for past dates', () => {
      const template = createMockTemplate({
        class_start_date: '2024-11-01',
        class_end_date: '2024-11-30'
      });
      
      const now = new Date('2024-12-01');
      
      expect(() => {
        generateOccurrences(template, [], [], now);
      }).toThrow('Cannot generate occurrences for past class period');
    });
    
    test('should respect schedule group settings', () => {
      const scheduleGroup = createMockScheduleGroup({
        max_participants: 12,
        group_name: '오후반'
      });
      
      const schedule = createMockSchedule({
        schedule_group_id: scheduleGroup.id,
        max_participants: null // 그룹 설정 사용
      });
      
      const occurrences = generateOccurrences(template, [schedule]);
      
      occurrences.forEach(occurrence => {
        expect(occurrence.max_participants).toBe(12);
        expect(occurrence.schedule_group_id).toBe(scheduleGroup.id);
      });
    });
  });
});
```

#### 수강 신청 및 출석 로직 테스트

```typescript
// tests/unit/enrollment-service.test.ts
describe('EnrollmentService', () => {
  describe('enrollment validation', () => {
    test('should allow enrollment during recruitment period', async () => {
      const template = createMockTemplate({
        status: 'recruiting',
        recruitment_start_date: '2024-11-01',
        recruitment_end_date: '2024-11-30'
      });
      
      const scheduleGroup = createMockScheduleGroup({
        max_participants: 10,
        current_enrollments: 5
      });
      
      const now = new Date('2024-11-15');
      const result = await validateEnrollment(mockMember.id, template.id, scheduleGroup.id, now);
      
      expect(result.allowed).toBe(true);
      expect(result.available_spots).toBe(5);
    });
    
    test('should reject enrollment when fully booked', async () => {
      const scheduleGroup = createMockScheduleGroup({
        max_participants: 10,
        current_enrollments: 10
      });
      
      const result = await validateEnrollment(mockMember.id, template.id, scheduleGroup.id);
      
      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('정원 마감');
      expect(result.waitlist_available).toBe(true);
    });
    
    test('should prevent duplicate enrollment', async () => {
      // 기존 신청 생성
      await createEnrollment({
        member_id: mockMember.id,
        class_template_id: template.id,
        schedule_group_id: scheduleGroup.id,
        status: 'confirmed'
      });
      
      const result = await validateEnrollment(mockMember.id, template.id, scheduleGroup.id);
      
      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('이미 신청한 클래스입니다');
    });
  });
  
  describe('enrollment lifecycle', () => {
    test('should handle enrollment to attendance workflow', async () => {
      // 1. 수강 신청
      const enrollment = await createEnrollment({
        member_id: mockMember.id,
        class_template_id: template.id,
        schedule_group_id: scheduleGroup.id
      });
      
      expect(enrollment.status).toBe('pending');
      
      // 2. 결제 완료
      const confirmedEnrollment = await confirmEnrollment(enrollment.id, {
        payment_id: 'pay_123',
        paid_amount: 20000
      });
      
      expect(confirmedEnrollment.status).toBe('confirmed');
      expect(confirmedEnrollment.paid_amount).toBe(20000);
      
      // 3. 수업 출석
      const occurrence = createMockOccurrence({
        class_template_id: template.id,
        schedule_group_id: scheduleGroup.id
      });
      
      const attendance = await checkAttendance({
        class_occurrence_id: occurrence.id,
        member_id: mockMember.id,
        attendance_status: 'present',
        checked_in_at: new Date()
      });
      
      expect(attendance.attendance_status).toBe('present');
      expect(attendance.enrollment_id).toBe(enrollment.id);
    });
  });
});
```

### 컴포넌트 단위 테스트

```typescript
// tests/unit/components/OccurrenceManager.test.tsx
describe('OccurrenceManager', () => {
  test('should display occurrence list correctly', () => {
    const occurrences = [
      createMockOccurrence({ occurrence_date: '2024-12-02', status: 'scheduled' }),
      createMockOccurrence({ occurrence_date: '2024-12-04', status: 'cancelled' }),
      createMockOccurrence({ occurrence_date: '2024-12-06', status: 'completed' })
    ];
    
    render(<OccurrenceManager classId="class-123" occurrences={occurrences} />);
    
    expect(screen.getByText('2024-12-02')).toBeInTheDocument();
    expect(screen.getByText('예약 가능')).toBeInTheDocument();
    expect(screen.getByText('취소됨')).toBeInTheDocument();
    expect(screen.getByText('완료')).toBeInTheDocument();
  });
  
  test('should handle occurrence modification', async () => {
    const mockUpdateOccurrence = jest.fn();
    const occurrence = createMockOccurrence({
      occurrence_date: '2024-12-02',
      start_time: '10:00'
    });
    
    render(
      <OccurrenceManager 
        classId="class-123" 
        occurrences={[occurrence]}
        onUpdate={mockUpdateOccurrence}
      />
    );
    
    // 수정 버튼 클릭
    fireEvent.click(screen.getByTestId('edit-occurrence'));
    
    // 시간 변경
    fireEvent.change(screen.getByTestId('start-time-input'), {
      target: { value: '11:00' }
    });
    
    // 저장
    fireEvent.click(screen.getByTestId('save-occurrence'));
    
    await waitFor(() => {
      expect(mockUpdateOccurrence).toHaveBeenCalledWith(occurrence.id, {
        start_time: '11:00'
      });
    });
  });
});
```

## 통합 테스트 (Integration Tests)

### API 엔드포인트 테스트

```typescript
// tests/integration/class-lifecycle-api.test.ts
describe('Class Lifecycle API Integration', () => {
  beforeEach(async () => {
    await setupTestDatabase();
    await seedTestData();
  });
  
  describe('POST /api/instructor/classes', () => {
    test('should create class with auto-generated occurrences', async () => {
      const classData = {
        title: '4주 요가 클래스',
        category: 'yoga',
        level: 'beginner',
        studio_id: testStudio.id,
        recruitment_start_date: '2024-12-01',
        recruitment_end_date: '2024-12-15',
        class_start_date: '2024-12-16',
        class_end_date: '2025-01-13',
        schedule_groups: [
          {
            group_name: '오후반',
            max_participants: 10,
            schedules: [
              { day_of_week: 'MONDAY', start_time: '14:00', end_time: '15:00' },
              { day_of_week: 'WEDNESDAY', start_time: '14:00', end_time: '15:00' }
            ]
          }
        ]
      };
      
      const response = await request(app)
        .post('/api/instructor/classes')
        .set('Authorization', `Bearer ${instructorToken}`)
        .send(classData)
        .expect(201);
      
      const { class_template, schedule_groups, generated_occurrences } = response.body.data;
      
      // 클래스 템플릿 확인
      expect(class_template.title).toBe('4주 요가 클래스');
      expect(class_template.status).toBe('upcoming');
      
      // 스케줄 그룹 확인
      expect(schedule_groups).toHaveLength(1);
      expect(schedule_groups[0].group_name).toBe('오후반');
      
      // Occurrence 자동 생성 확인
      expect(generated_occurrences).toHaveLength(8); // 4주 × 2일
      
      // 첫 번째 Occurrence 확인
      const firstOccurrence = generated_occurrences.find(o => 
        o.occurrence_date === '2024-12-16'
      );
      expect(firstOccurrence.start_time).toBe('14:00:00');
      expect(firstOccurrence.status).toBe('scheduled');
    });
  });
  
  describe('PATCH /api/instructor/occurrences/{id}', () => {
    test('should update occurrence and send notifications', async () => {
      // 수강 신청된 Occurrence 생성
      const occurrence = await createTestOccurrence();
      await createTestEnrollment(occurrence.id);
      
      const updateData = {
        start_time: '15:00',
        instructor_notes: '체험 수업으로 1시간 연기',
        notify_enrollees: true
      };
      
      const response = await request(app)
        .patch(`/api/instructor/occurrences/${occurrence.id}`)
        .set('Authorization', `Bearer ${instructorToken}`)
        .send(updateData)
        .expect(200);
      
      const { occurrence: updated, notifications_sent } = response.body.data;
      
      expect(updated.start_time).toBe('15:00:00');
      expect(updated.instructor_notes).toBe('체험 수업으로 1시간 연기');
      expect(notifications_sent).toBeGreaterThan(0);
      
      // 데이터베이스 확인
      const savedOccurrence = await db.select().from(classOccurrences)
        .where(eq(classOccurrences.id, occurrence.id));
      expect(savedOccurrence[0].start_time).toBe('15:00:00');
    });
    
    test('should handle occurrence cancellation with substitute creation', async () => {
      const occurrence = await createTestOccurrence();
      
      const cancelData = {
        status: 'cancelled',
        cancellation_reason: '강사 개인사정',
        create_substitute: true,
        substitute_date: '2024-12-20',
        substitute_start_time: '10:00'
      };
      
      const response = await request(app)
        .patch(`/api/instructor/occurrences/${occurrence.id}`)
        .set('Authorization', `Bearer ${instructorToken}`)
        .send(cancelData)
        .expect(200);
      
      const { occurrence: cancelled, substitute_occurrence } = response.body.data;
      
      // 원본 수업 취소 확인
      expect(cancelled.status).toBe('cancelled');
      expect(cancelled.cancellation_reason).toBe('강사 개인사정');
      
      // 보강 수업 생성 확인
      expect(substitute_occurrence).toBeDefined();
      expect(substitute_occurrence.occurrence_date).toBe('2024-12-20');
      expect(substitute_occurrence.start_time).toBe('10:00:00');
      expect(substitute_occurrence.is_substitute_class).toBe(true);
      expect(substitute_occurrence.original_occurrence_id).toBe(occurrence.id);
    });
  });
  
  describe('GET /api/instructor/dashboard/v2', () => {
    test('should return occurrence-based dashboard data', async () => {
      // 테스트 데이터 생성
      await createCompleteClassWithOccurrences();
      
      const response = await request(app)
        .get('/api/instructor/dashboard/v2')
        .set('Authorization', `Bearer ${instructorToken}`)
        .expect(200);
      
      const { stats, upcoming_occurrences, recent_enrollments } = response.body.data;
      
      // 통계 확인
      expect(stats.total_occurrences).toBeGreaterThan(0);
      expect(stats.upcoming_occurrences).toBeGreaterThan(0);
      expect(stats.confirmed_enrollments).toBeGreaterThan(0);
      
      // 다가오는 수업 확인
      expect(upcoming_occurrences).toBeArray();
      expect(upcoming_occurrences[0]).toMatchObject({
        occurrence_date: expect.any(String),
        start_time: expect.any(String),
        class_title: expect.any(String),
        confirmed_enrollments: expect.any(Number),
        max_participants: expect.any(Number)
      });
      
      // 최근 신청 확인
      expect(recent_enrollments).toBeArray();
      if (recent_enrollments.length > 0) {
        expect(recent_enrollments[0]).toMatchObject({
          member_name: expect.any(String),
          class_title: expect.any(String),
          enrollment_status: expect.any(String)
        });
      }
    });
  });
});
```

### 데이터베이스 연동 테스트

```typescript
// tests/integration/database-operations.test.ts
describe('Database Operations Integration', () => {
  describe('complex queries', () => {
    test('should handle occurrence search with multiple filters', async () => {
      await createLargeTestDataset(); // 1000개 occurrence 생성
      
      const searchFilters = {
        instructor_id: testInstructor.id,
        date_range: {
          from: '2024-12-01',
          to: '2024-12-31'
        },
        status: ['scheduled', 'ongoing'],
        class_category: 'yoga',
        time_range: {
          start_after: '09:00',
          start_before: '18:00'
        }
      };
      
      const startTime = Date.now();
      const results = await searchOccurrences(searchFilters);
      const queryTime = Date.now() - startTime;
      
      expect(queryTime).toBeLessThan(1000); // 1초 이내
      expect(results.occurrences).toBeArray();
      
      // 필터 조건 확인
      results.occurrences.forEach(occurrence => {
        expect(occurrence.instructor_id).toBe(testInstructor.id);
        expect(['scheduled', 'ongoing']).toContain(occurrence.status);
        expect(occurrence.class_category).toBe('yoga');
      });
    });
    
    test('should maintain data consistency during concurrent updates', async () => {
      const occurrence = await createTestOccurrence({ max_participants: 10 });
      
      // 동시에 여러 신청 시도
      const enrollmentPromises = Array.from({ length: 15 }, (_, i) => 
        enrollInOccurrence(occurrence.id, `member-${i}`)
      );
      
      const results = await Promise.allSettled(enrollmentPromises);
      
      // 성공한 신청과 실패한 신청 확인
      const successful = results.filter(r => r.status === 'fulfilled');
      const failed = results.filter(r => r.status === 'rejected');
      
      expect(successful).toHaveLength(10); // 정원만큼만 성공
      expect(failed).toHaveLength(5);
      
      // 실제 데이터베이스 상태 확인
      const enrollmentCount = await countEnrollments(occurrence.id);
      expect(enrollmentCount).toBe(10);
    });
  });
  
  describe('transaction handling', () => {
    test('should rollback on occurrence creation failure', async () => {
      const invalidTemplate = createMockTemplate({
        class_start_date: '2024-12-01',
        class_end_date: '2024-11-30' // 잘못된 날짜
      });
      
      await expect(
        createClassWithOccurrences(invalidTemplate, scheduleGroups)
      ).rejects.toThrow();
      
      // 롤백 확인
      const createdTemplate = await getClassTemplate(invalidTemplate.id);
      expect(createdTemplate).toBeNull();
      
      const createdOccurrences = await getOccurrencesByTemplate(invalidTemplate.id);
      expect(createdOccurrences).toHaveLength(0);
    });
  });
});
```

### 외부 시스템 연동 테스트

```typescript
// tests/integration/notification-integration.test.ts
describe('Notification System Integration', () => {
  test('should send email notifications for occurrence changes', async () => {
    const mockEmailService = jest.mocked(emailService);
    const occurrence = await createTestOccurrence();
    const enrollment = await createTestEnrollment(occurrence.id);
    
    await updateOccurrence(occurrence.id, {
      status: 'cancelled',
      cancellation_reason: '강사 개인사정'
    });
    
    expect(mockEmailService.sendCancellationEmail).toHaveBeenCalledWith(
      enrollment.member.email,
      expect.objectContaining({
        class_title: occurrence.class_title,
        occurrence_date: occurrence.occurrence_date,
        cancellation_reason: '강사 개인사정'
      })
    );
  });
  
  test('should handle email service failures gracefully', async () => {
    const mockEmailService = jest.mocked(emailService);
    mockEmailService.sendCancellationEmail.mockRejectedValue(new Error('SMTP Error'));
    
    const occurrence = await createTestOccurrence();
    
    // 이메일 실패해도 수업 업데이트는 성공해야 함
    await expect(
      updateOccurrence(occurrence.id, { status: 'cancelled' })
    ).resolves.toBeDefined();
    
    // 실패한 알림은 재시도 큐에 추가되어야 함
    const retryQueue = await getNotificationRetryQueue();
    expect(retryQueue).toHaveLength(1);
  });
});
```

## E2E 테스트 (End-to-End Tests)

### 주요 사용자 시나리오 테스트

```typescript
// tests/e2e/instructor-class-management.spec.ts
test.describe('Instructor Class Management', () => {
  test('complete class lifecycle management', async ({ page }) => {
    await loginAsInstructor(page);
    
    // 1. 클래스 생성
    await test.step('Create new class with schedule groups', async () => {
      await page.goto('/instructor/classes/create');
      
      await page.fill('[data-testid=class-title]', '4주 요가 마스터 클래스');
      await page.selectOption('[data-testid=category]', 'yoga');
      await page.selectOption('[data-testid=level]', 'intermediate');
      
      // 기간 설정
      await page.fill('[data-testid=recruitment-start]', '2024-12-01');
      await page.fill('[data-testid=recruitment-end]', '2024-12-15');
      await page.fill('[data-testid=class-start]', '2024-12-16');
      await page.fill('[data-testid=class-end]', '2025-01-13');
      
      // 스케줄 그룹 추가
      await page.click('[data-testid=add-schedule-group]');
      await page.fill('[data-testid=group-name]', '오후반');
      await page.fill('[data-testid=group-capacity]', '12');
      
      // 스케줄 추가
      await page.check('[data-testid=day-monday]');
      await page.check('[data-testid=day-wednesday]');
      await page.fill('[data-testid=start-time]', '14:00');
      await page.fill('[data-testid=end-time]', '15:30');
      
      await page.click('[data-testid=create-class]');
      
      await expect(page.locator('[data-testid=success-message]')).toBeVisible();
    });
    
    // 2. Occurrence 목록 확인
    await test.step('Verify auto-generated occurrences', async () => {
      await page.goto('/instructor/classes');
      await page.click('[data-testid=class-item]:first-child');
      
      // 8개 수업 생성 확인 (4주 × 2일)
      await expect(page.locator('[data-testid=occurrence-item]')).toHaveCount(8);
      
      // 첫 번째 수업 날짜 확인
      await expect(page.locator('[data-testid=occurrence-item]:first-child'))
        .toContainText('2024-12-16');
    });
    
    // 3. 개별 수업 수정
    await test.step('Modify individual occurrence', async () => {
      await page.click('[data-testid=occurrence-item]:first-child');
      await page.click('[data-testid=edit-occurrence]');
      
      // 시간 변경
      await page.fill('[data-testid=occurrence-start-time]', '15:00');
      await page.fill('[data-testid=instructor-notes]', '체험 수업으로 1시간 늦춤');
      
      await page.click('[data-testid=save-occurrence]');
      
      await expect(page.locator('[data-testid=occurrence-time]'))
        .toContainText('15:00');
    });
    
    // 4. 수업 취소 및 보강
    await test.step('Cancel class and create substitute', async () => {
      await page.click('[data-testid=cancel-occurrence]');
      
      await page.fill('[data-testid=cancellation-reason]', '강사 개인사정');
      await page.check('[data-testid=create-substitute]');
      await page.fill('[data-testid=substitute-date]', '2024-12-18');
      await page.fill('[data-testid=substitute-time]', '16:00');
      
      await page.click('[data-testid=confirm-cancellation]');
      
      // 취소 상태 확인
      await expect(page.locator('[data-testid=occurrence-status]'))
        .toContainText('취소됨');
      
      // 보강 수업 생성 확인
      await expect(page.locator('[data-testid=substitute-occurrence]'))
        .toContainText('2024-12-18');
    });
    
    // 5. Dashboard에서 변경사항 확인
    await test.step('Verify changes in dashboard', async () => {
      await page.goto('/instructor/dashboard');
      
      await expect(page.locator('[data-testid=upcoming-occurrences]'))
        .toContainText('4주 요가 마스터 클래스');
      
      await expect(page.locator('[data-testid=total-occurrences]'))
        .toContainText('8'); // 취소 1개 + 보강 1개 = 총 8개
    });
  });
  
  test('attendance checking workflow', async ({ page }) => {
    // 사전 준비: 수강 신청된 수업 생성
    await setupClassWithEnrollments();
    
    await loginAsInstructor(page);
    
    // 1. 오늘 수업 목록 확인
    await test.step('View today\'s classes', async () => {
      await page.goto('/instructor/dashboard');
      
      await expect(page.locator('[data-testid=todays-classes]')).toBeVisible();
      await page.click('[data-testid=class-item]:first-child');
    });
    
    // 2. 출석 체크 페이지 진입
    await test.step('Navigate to attendance check', async () => {
      await page.click('[data-testid=check-attendance]');
      
      await expect(page.locator('[data-testid=attendance-checker]')).toBeVisible();
      await expect(page.locator('[data-testid=student-list]')).toBeVisible();
    });
    
    // 3. 개별 출석 체크
    await test.step('Check individual attendance', async () => {
      const students = page.locator('[data-testid=student-row]');
      const studentCount = await students.count();
      
      for (let i = 0; i < studentCount; i++) {
        const student = students.nth(i);
        const status = i % 3 === 0 ? 'absent' : i % 3 === 1 ? 'late' : 'present';
        
        await student.locator(`[data-testid=status-${status}]`).click();
        
        if (status === 'late') {
          await student.locator('[data-testid=check-in-time]').fill('10:15');
        }
      }
      
      await page.click('[data-testid=save-attendance]');
      
      await expect(page.locator('[data-testid=attendance-saved]')).toBeVisible();
    });
    
    // 4. 출석 통계 확인
    await test.step('Verify attendance statistics', async () => {
      const summary = page.locator('[data-testid=attendance-summary]');
      
      await expect(summary.locator('[data-testid=present-count]'))
        .toHaveText(/\d+/);
      await expect(summary.locator('[data-testid=absent-count]'))
        .toHaveText(/\d+/);
      await expect(summary.locator('[data-testid=late-count]'))
        .toHaveText(/\d+/);
    });
  });
});

// tests/e2e/student-search-enrollment.spec.ts
test.describe('Student Search and Enrollment', () => {
  test('search and enroll in class', async ({ page }) => {
    // 1. 클래스 검색
    await test.step('Search for classes', async () => {
      await page.goto('/classes/search');
      
      // 필터 적용
      await page.selectOption('[data-testid=category-filter]', 'yoga');
      await page.check('[data-testid=day-monday]');
      await page.check('[data-testid=day-wednesday]');
      await page.selectOption('[data-testid=time-filter]', 'afternoon');
      await page.selectOption('[data-testid=level-filter]', 'beginner');
      
      await page.click('[data-testid=search-button]');
      
      await expect(page.locator('[data-testid=search-results]')).toBeVisible();
      await expect(page.locator('[data-testid=class-card]'))
        .toHaveCountGreaterThan(0);
    });
    
    // 2. 클래스 상세 보기
    await test.step('View class details', async () => {
      await page.click('[data-testid=class-card]:first-child');
      
      await expect(page.locator('[data-testid=class-details]')).toBeVisible();
      await expect(page.locator('[data-testid=schedule-groups]')).toBeVisible();
      
      // 실제 수업 일정 확인
      await expect(page.locator('[data-testid=occurrence-calendar]')).toBeVisible();
      await expect(page.locator('[data-testid=occurrence-date]')).toHaveCountGreaterThan(0);
    });
    
    // 3. 시간대 그룹 선택
    await test.step('Select schedule group', async () => {
      await page.click('[data-testid=schedule-group]:first-child');
      
      await expect(page.locator('[data-testid=group-selected]')).toBeVisible();
      await expect(page.locator('[data-testid=enrollment-info]'))
        .toContainText('오후반');
    });
    
    // 4. 신청하기
    await test.step('Enroll in class', async () => {
      await page.click('[data-testid=enroll-button]');
      
      // 로그인 필요한 경우
      if (await page.locator('[data-testid=login-form]').isVisible()) {
        await loginAsStudent(page);
      }
      
      // 결제 정보 입력
      await page.fill('[data-testid=card-number]', '****************');
      await page.fill('[data-testid=expiry]', '12/25');
      await page.fill('[data-testid=cvc]', '123');
      await page.fill('[data-testid=cardholder-name]', '김학생');
      
      await page.click('[data-testid=pay-button]');
      
      await expect(page.locator('[data-testid=enrollment-success]')).toBeVisible();
      await expect(page.locator('[data-testid=enrollment-confirmation]'))
        .toContainText('신청이 완료되었습니다');
    });
    
    // 5. 내 신청 목록 확인
    await test.step('Verify enrollment in my classes', async () => {
      await page.goto('/my/enrollments');
      
      await expect(page.locator('[data-testid=enrollment-list]')).toBeVisible();
      await expect(page.locator('[data-testid=enrollment-item]:first-child'))
        .toContainText('요가');
      
      // 신청 상태 확인
      await expect(page.locator('[data-testid=enrollment-status]'))
        .toContainText('확정');
    });
  });
});
```

### 성능 테스트

```typescript
// tests/e2e/performance.spec.ts
test.describe('Performance Tests', () => {
  test('dashboard loading performance', async ({ page }) => {
    await loginAsInstructor(page);
    
    // 성능 메트릭 수집 시작
    await page.coverage.startJSCoverage();
    
    const navigationPromise = page.waitForNavigation();
    const startTime = Date.now();
    
    await page.goto('/instructor/dashboard');
    await navigationPromise;
    
    // Core Web Vitals 측정
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const vitals = {};
          
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              vitals.fcp = entry.startTime;
            }
            if (entry.name === 'largest-contentful-paint') {
              vitals.lcp = entry.startTime;
            }
          });
          
          resolve(vitals);
        }).observe({ entryTypes: ['paint', 'largest-contentful-paint'] });
      });
    });
    
    const loadTime = Date.now() - startTime;
    
    expect(loadTime).toBeLessThan(3000); // 3초 이내
    expect(metrics.fcp).toBeLessThan(1500); // FCP 1.5초 이내
    expect(metrics.lcp).toBeLessThan(2500); // LCP 2.5초 이내
  });
  
  test('search performance with large dataset', async ({ page }) => {
    await page.goto('/classes/search');
    
    // 무거운 검색 쿼리
    await page.selectOption('[data-testid=category-filter]', 'all');
    await page.fill('[data-testid=search-input]', '요가');
    
    const startTime = Date.now();
    await page.click('[data-testid=search-button]');
    await page.waitForSelector('[data-testid=search-results]');
    const searchTime = Date.now() - startTime;
    
    expect(searchTime).toBeLessThan(2000); // 검색 2초 이내
    
    // 결과 개수 확인
    const resultCount = await page.locator('[data-testid=class-card]').count();
    expect(resultCount).toBeGreaterThan(0);
    
    // 무한 스크롤 성능 테스트
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    
    await page.waitForTimeout(1000);
    const newResultCount = await page.locator('[data-testid=class-card]').count();
    expect(newResultCount).toBeGreaterThan(resultCount);
  });
});
```

## 성능 테스트

### 부하 테스트

```typescript
// tests/performance/load-test.ts
describe('Load Testing', () => {
  test('dashboard API under load', async () => {
    const scenario = {
      name: 'Dashboard Load Test',
      weight: 100,
      exec: async () => {
        const response = await http.get(`${BASE_URL}/api/instructor/dashboard/v2`, {
          headers: { Authorization: `Bearer ${instructorToken}` }
        });
        
        check(response, {
          'status is 200': (r) => r.status === 200,
          'response time < 2s': (r) => r.timings.duration < 2000,
          'has required fields': (r) => {
            const body = JSON.parse(r.body);
            return body.data.stats && body.data.upcoming_occurrences;
          }
        });
      }
    };
    
    const options = {
      stages: [
        { duration: '2m', target: 10 },   // 워밍업
        { duration: '5m', target: 50 },   // 정상 부하
        { duration: '2m', target: 100 },  // 피크 부하
        { duration: '5m', target: 100 },  // 유지
        { duration: '2m', target: 0 }     // 쿨다운
      ],
      thresholds: {
        http_req_duration: ['p(95)<2000'], // 95% 요청이 2초 이내
        http_req_failed: ['rate<0.01'],    // 에러율 1% 미만
      }
    };
    
    await runLoadTest(scenario, options);
  });
  
  test('occurrence search under concurrent load', async () => {
    const searchScenarios = [
      () => searchByCategory('yoga'),
      () => searchByTimeSlot('morning'),
      () => searchByLocation('강남'),
      () => complexSearch({ category: 'pilates', days: ['monday', 'wednesday'] })
    ];
    
    const options = {
      stages: [
        { duration: '1m', target: 20 },
        { duration: '3m', target: 100 },
        { duration: '1m', target: 0 }
      ]
    };
    
    for (const scenario of searchScenarios) {
      await runLoadTest(scenario, options);
    }
  });
});
```

### 메모리 및 리소스 테스트

```typescript
// tests/performance/memory-test.ts
describe('Memory Usage Tests', () => {
  test('occurrence generation memory efficiency', async () => {
    const initialMemory = process.memoryUsage();
    
    // 대량 데이터 처리
    const templates = Array.from({ length: 100 }, () => createMockTemplate());
    const schedules = Array.from({ length: 300 }, () => createMockSchedule());
    
    for (const template of templates) {
      await generateOccurrences(template, schedules);
      
      // 주기적으로 가비지 컬렉션
      if (templates.indexOf(template) % 10 === 0) {
        global.gc && global.gc();
      }
    }
    
    const finalMemory = process.memoryUsage();
    const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
    
    // 메모리 증가가 100MB 이하여야 함
    expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024);
  });
  
  test('database connection pool efficiency', async () => {
    const connectionCount = 50;
    
    const promises = Array.from({ length: connectionCount }, async (_, i) => {
      // 동시 데이터베이스 연결 테스트
      const occurrences = await getOccurrencesByInstructor(testInstructor.id);
      return occurrences.length;
    });
    
    const startTime = Date.now();
    const results = await Promise.all(promises);
    const endTime = Date.now();
    
    // 모든 쿼리가 성공해야 함
    expect(results.every(count => count >= 0)).toBe(true);
    
    // 평균 응답 시간이 합리적이어야 함
    const avgTime = (endTime - startTime) / connectionCount;
    expect(avgTime).toBeLessThan(200); // 평균 200ms 이하
  });
});
```

## 테스트 데이터 관리

### 테스트 픽스처

```typescript
// tests/fixtures/class-lifecycle-fixtures.ts
export class ClassLifecycleFixtures {
  static createMockTemplate(overrides = {}): ClassTemplate {
    return {
      id: 'template-' + Math.random().toString(36).substr(2, 9),
      title: '테스트 요가 클래스',
      description: '초급자를 위한 요가 클래스',
      category: 'yoga',
      specialty: 'YOGA',
      level: 'beginner',
      instructor_id: 'instructor-123',
      studio_id: 'studio-123',
      duration_minutes: 90,
      price_per_session: 20000,
      max_capacity: 10,
      recruitment_start_date: '2024-12-01',
      recruitment_end_date: '2024-12-15',
      class_start_date: '2024-12-16',
      class_end_date: '2025-01-13',
      status: 'recruiting',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }
  
  static createMockScheduleGroup(overrides = {}): ScheduleGroup {
    return {
      id: 'group-' + Math.random().toString(36).substr(2, 9),
      class_template_id: 'template-123',
      group_name: '오후반',
      group_description: '평일 오후 시간대',
      max_participants: 10,
      price_per_session: 20000,
      sessions_per_week: 2,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }
  
  static createMockSchedule(overrides = {}): Schedule {
    return {
      id: 'schedule-' + Math.random().toString(36).substr(2, 9),
      schedule_group_id: 'group-123',
      day_of_week: 'MONDAY',
      start_time: '14:00:00',
      end_time: '15:30:00',
      max_participants: 10,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }
  
  static createMockOccurrence(overrides = {}): Occurrence {
    const baseDate = new Date('2024-12-16T14:00:00Z');
    
    return {
      id: 'occurrence-' + Math.random().toString(36).substr(2, 9),
      class_template_id: 'template-123',
      schedule_group_id: 'group-123',
      class_schedule_id: 'schedule-123',
      occurrence_date: '2024-12-16',
      start_time: '14:00:00',
      end_time: '15:30:00',
      max_participants: 10,
      status: 'scheduled',
      attendance_count: 0,
      confirmed_enrollments: 0,
      instructor_notes: null,
      cancellation_reason: null,
      is_substitute_class: false,
      original_occurrence_id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }
  
  static createCompleteClassSetup(): CompleteClassSetup {
    const template = this.createMockTemplate();
    const scheduleGroup = this.createMockScheduleGroup({
      class_template_id: template.id
    });
    const schedules = [
      this.createMockSchedule({
        schedule_group_id: scheduleGroup.id,
        day_of_week: 'MONDAY'
      }),
      this.createMockSchedule({
        schedule_group_id: scheduleGroup.id,
        day_of_week: 'WEDNESDAY'
      })
    ];
    const occurrences = schedules.flatMap(schedule => 
      Array.from({ length: 4 }, (_, week) => 
        this.createMockOccurrence({
          class_template_id: template.id,
          schedule_group_id: scheduleGroup.id,
          class_schedule_id: schedule.id,
          occurrence_date: this.calculateOccurrenceDate(
            template.class_start_date, 
            week, 
            schedule.day_of_week
          )
        })
      )
    );
    
    return {
      template,
      scheduleGroup,
      schedules,
      occurrences
    };
  }
}
```

### 데이터베이스 시딩

```typescript
// tests/utils/test-database-seeder.ts
export class TestDatabaseSeeder {
  async seedMinimalData(): Promise<TestData> {
    // 기본 필수 데이터만 생성
    const studio = await this.createStudio();
    const instructor = await this.createInstructor();
    const members = await this.createMembers(5);
    
    return { studio, instructor, members };
  }
  
  async seedCompleteClassData(): Promise<CompleteTestData> {
    const basicData = await this.seedMinimalData();
    
    // 완전한 클래스 데이터 생성
    const template = await this.createClassTemplate(basicData.instructor.id, basicData.studio.id);
    const scheduleGroup = await this.createScheduleGroup(template.id);
    const schedules = await this.createSchedules(scheduleGroup.id);
    const occurrences = await this.createOccurrences(template, schedules);
    const enrollments = await this.createEnrollments(basicData.members, template, scheduleGroup);
    const attendances = await this.createAttendances(enrollments, occurrences);
    
    return {
      ...basicData,
      template,
      scheduleGroup,
      schedules,
      occurrences,
      enrollments,
      attendances
    };
  }
  
  async seedLargeDataset(size: 'small' | 'medium' | 'large' = 'small'): Promise<void> {
    const counts = {
      small: { templates: 10, occurrences: 100, enrollments: 500 },
      medium: { templates: 100, occurrences: 1000, enrollments: 5000 },
      large: { templates: 1000, occurrences: 10000, enrollments: 50000 }
    };
    
    const { templates: templateCount, occurrences: occurrenceCount, enrollments: enrollmentCount } = counts[size];
    
    // 배치 처리로 대량 데이터 생성
    await this.batchCreateTemplates(templateCount);
    await this.batchCreateOccurrences(occurrenceCount);
    await this.batchCreateEnrollments(enrollmentCount);
  }
  
  async cleanup(): Promise<void> {
    // 테스트 데이터 정리 (역순으로)
    await this.db.delete(classAttendances);
    await this.db.delete(classEnrollments);
    await this.db.delete(classOccurrences);
    await this.db.delete(classSchedules);
    await this.db.delete(classScheduleGroups);
    await this.db.delete(classTemplates);
    await this.db.delete(instructors);
    await this.db.delete(members);
    await this.db.delete(studios);
  }
}
```

## 테스트 자동화

### CI/CD 파이프라인

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
        env:
          NODE_ENV: test
      
      - name: Generate coverage report
        run: npm run test:coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
  
  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: shallwe_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run database migrations
        run: npm run db:migrate
        env:
          DATABASE_URL: postgres://postgres:test@localhost:5432/shallwe_test
      
      - name: Seed test data
        run: npm run db:seed:test
        env:
          DATABASE_URL: postgres://postgres:test@localhost:5432/shallwe_test
      
      - name: Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgres://postgres:test@localhost:5432/shallwe_test
          REDIS_URL: redis://localhost:6379
  
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Playwright
        run: npx playwright install --with-deps
      
      - name: Build application
        run: npm run build
      
      - name: Start application
        run: npm start &
        env:
          NODE_ENV: test
      
      - name: Wait for application
        run: npx wait-on http://localhost:3000
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/
```

### 테스트 스크립트

```json
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest tests/unit",
    "test:integration": "jest tests/integration --runInBand",
    "test:e2e": "playwright test",
    "test:performance": "k6 run tests/performance/load-test.js",
    "test:coverage": "jest --coverage --coverageReporters=text-lcov",
    "test:watch": "jest --watch",
    "test:ci": "jest --ci --coverage --watchAll=false --runInBand",
    "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand",
    
    "db:test:setup": "npm run db:migrate && npm run db:seed:test",
    "db:test:teardown": "npm run db:reset",
    "db:seed:test": "tsx scripts/seed-test-data.ts",
    
    "pretest:integration": "npm run db:test:setup",
    "posttest:integration": "npm run db:test:teardown"
  }
}
```

## 품질 게이트

### 테스트 통과 기준

```typescript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 95,
      lines: 90,
      statements: 90
    },
    // 핵심 도메인 로직은 더 높은 커버리지 요구
    './src/services/class-lifecycle.service.ts': {
      branches: 95,
      functions: 100,
      lines: 95,
      statements: 95
    },
    './src/services/occurrence-generator.service.ts': {
      branches: 95,
      functions: 100,
      lines: 95,
      statements: 95
    }
  },
  testTimeout: 10000,
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  globalTeardown: '<rootDir>/tests/teardown.ts'
};
```

### 성능 임계값

```typescript
// tests/performance/thresholds.ts
export const performanceThresholds = {
  api: {
    dashboard: {
      p95: 2000, // 95% 요청이 2초 이내
      p99: 5000  // 99% 요청이 5초 이내
    },
    search: {
      p95: 1000,
      p99: 3000
    },
    occurrenceUpdate: {
      p95: 1500,
      p99: 4000
    }
  },
  
  ui: {
    pageLoad: {
      fcp: 1500,  // First Contentful Paint
      lcp: 2500,  // Largest Contentful Paint
      cls: 0.1    // Cumulative Layout Shift
    },
    interaction: {
      inp: 200    // Interaction to Next Paint
    }
  },
  
  memory: {
    maxHeapUsage: 512 * 1024 * 1024, // 512MB
    maxMemoryIncrease: 100 * 1024 * 1024 // 100MB
  }
};
```

이 테스트 전략은 클래스 라이프사이클 시스템의 복잡성을 고려하여 체계적이고 포괄적인 품질 보증을 제공합니다. 특히 Schedule-Occurrence 관계의 정확성과 실시간 운영 요구사항을 만족하는 안정적인 시스템 구축을 목표로 합니다.