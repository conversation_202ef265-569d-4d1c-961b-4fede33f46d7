# 클래스 라이프사이클 시스템 배포 가이드

## 배포 개요

클래스 라이프사이클 관리 시스템의 안전하고 효율적인 배포를 위한 종합 가이드입니다. Blue-Green 배포, 기능 플래그, 모니터링, 롤백 전략을 포함합니다.

## 배포 전략

### Blue-Green 배포

```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Load Balancer]
    end
    
    subgraph "Blue Environment (Current)"
        B1[App Server 1]
        B2[App Server 2]
        BDB[(Blue DB)]
    end
    
    subgraph "Green Environment (New)"
        G1[App Server 1]
        G2[App Server 2]
        GDB[(Green DB)]
    end
    
    subgraph "Shared Services"
        Redis[(Redis)]
        S3[(S3 Storage)]
    end
    
    LB --> B1
    LB --> B2
    B1 --> BDB
    B2 --> BDB
    
    G1 -.-> GDB
    G2 -.-> GDB
    
    B1 --> Redis
    B2 --> Redis
    G1 --> Redis
    G2 --> Redis
```

### 배포 흐름

```yaml
# deployment-pipeline.yml
name: Class Lifecycle Deployment

stages:
  1_preparation:
    - health_check_current
    - backup_database
    - prepare_green_environment
    
  2_deployment:
    - deploy_schema_changes
    - deploy_application_code
    - run_smoke_tests
    
  3_validation:
    - run_integration_tests
    - performance_validation
    - security_scan
    
  4_switch:
    - gradual_traffic_shift
    - monitor_metrics
    - full_cutover
    
  5_cleanup:
    - decommission_blue
    - cleanup_resources
```

## 데이터베이스 마이그레이션

### 마이그레이션 단계별 실행

#### Phase 1: 스키마 확장 (무중단)

```sql
-- migration_001_add_lifecycle_columns.sql
-- 기존 테이블에 새 컬럼 추가 (NULL 허용)
ALTER TABLE class_templates ADD COLUMN recruitment_start_date DATE;
ALTER TABLE class_templates ADD COLUMN recruitment_end_date DATE;
ALTER TABLE class_templates ADD COLUMN class_start_date DATE;
ALTER TABLE class_templates ADD COLUMN class_end_date DATE;
ALTER TABLE class_templates ADD COLUMN status TEXT DEFAULT 'upcoming';

-- 새 테이블 생성
CREATE TABLE class_occurrences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_template_id UUID NOT NULL,
  schedule_group_id UUID NOT NULL,
  class_schedule_id UUID NOT NULL,
  occurrence_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  max_participants INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'scheduled',
  attendance_count INTEGER DEFAULT 0,
  confirmed_enrollments INTEGER DEFAULT 0,
  instructor_notes TEXT,
  cancellation_reason TEXT,
  is_substitute_class BOOLEAN DEFAULT false,
  original_occurrence_id UUID,
  booking_opens_at TIMESTAMP WITH TIME ZONE,
  booking_closes_at TIMESTAMP WITH TIME ZONE,
  cancellation_deadline TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE class_attendances (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_occurrence_id UUID NOT NULL,
  member_id UUID NOT NULL,
  enrollment_id UUID,
  attendance_status TEXT NOT NULL,
  checked_in_at TIMESTAMP WITH TIME ZONE,
  checked_out_at TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Phase 2: 데이터 마이그레이션

```sql
-- migration_002_migrate_data.sql
-- 기존 데이터 변환 및 이전
UPDATE class_templates SET 
  recruitment_start_date = CURRENT_DATE,
  recruitment_end_date = CURRENT_DATE + INTERVAL '7 days',
  class_start_date = CURRENT_DATE + INTERVAL '14 days',
  class_end_date = CURRENT_DATE + INTERVAL '42 days',
  status = 'recruiting'
WHERE recruitment_start_date IS NULL;

-- Enrollment 테이블 업데이트
ALTER TABLE class_enrollments ADD COLUMN schedule_group_id UUID;
UPDATE class_enrollments e
SET schedule_group_id = (
  SELECT sg.id 
  FROM class_schedule_groups sg 
  JOIN class_schedules s ON sg.id = s.schedule_group_id
  WHERE s.id = e.class_schedule_id
  LIMIT 1
);

-- Occurrence 생성 (4주간)
INSERT INTO class_occurrences (
  class_template_id, schedule_group_id, class_schedule_id,
  occurrence_date, start_time, end_time, max_participants, status
)
SELECT 
  sg.class_template_id,
  s.schedule_group_id,
  s.id,
  generate_series(
    date_trunc('week', t.class_start_date) + 
    CASE s.day_of_week 
      WHEN 'MONDAY' THEN INTERVAL '0 days'
      WHEN 'TUESDAY' THEN INTERVAL '1 day'
      WHEN 'WEDNESDAY' THEN INTERVAL '2 days'
      WHEN 'THURSDAY' THEN INTERVAL '3 days'
      WHEN 'FRIDAY' THEN INTERVAL '4 days'
      WHEN 'SATURDAY' THEN INTERVAL '5 days'
      WHEN 'SUNDAY' THEN INTERVAL '6 days'
    END,
    t.class_end_date,
    INTERVAL '1 week'
  )::date,
  s.start_time,
  s.end_time,
  COALESCE(s.max_participants, sg.max_participants, t.max_capacity),
  'scheduled'
FROM class_schedules s
JOIN class_schedule_groups sg ON s.schedule_group_id = sg.id
JOIN class_templates t ON sg.class_template_id = t.id
WHERE t.class_start_date IS NOT NULL;
```

#### Phase 3: 제약 조건 및 인덱스

```sql
-- migration_003_add_constraints.sql
-- 제약 조건 추가
ALTER TABLE class_templates ADD CONSTRAINT check_template_dates 
  CHECK (recruitment_start_date <= recruitment_end_date);
ALTER TABLE class_templates ADD CONSTRAINT check_class_dates
  CHECK (class_start_date <= class_end_date);

-- 인덱스 생성
CREATE INDEX CONCURRENTLY idx_templates_search 
  ON class_templates (category, level, status, recruitment_end_date);
CREATE INDEX CONCURRENTLY idx_occurrences_date_status 
  ON class_occurrences (occurrence_date, status);
CREATE INDEX CONCURRENTLY idx_attendances_occurrence 
  ON class_attendances (class_occurrence_id);
```

### 마이그레이션 스크립트

```bash
#!/bin/bash
# deploy-migrations.sh

set -e

DB_URL=${DATABASE_URL}
BACKUP_DIR="/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "🔄 Starting database migration..."

# 1. 백업 생성
echo "📦 Creating database backup..."
pg_dump $DB_URL > "$BACKUP_DIR/backup_$TIMESTAMP.sql"

# 2. 스키마 검증
echo "🔍 Validating schema..."
psql $DB_URL -f "migrations/validate_schema.sql"

# 3. 단계별 마이그레이션 실행
for migration in migrations/*.sql; do
  echo "⚡ Running $migration..."
  psql $DB_URL -f "$migration"
  
  # 각 단계 후 검증
  psql $DB_URL -c "SELECT 'Migration step completed' as status;"
done

# 4. 최종 검증
echo "✅ Running final validation..."
psql $DB_URL -f "migrations/final_validation.sql"

echo "🎉 Migration completed successfully!"
```

## 기능 플래그 (Feature Flags)

### 플래그 정의

```typescript
// feature-flags.ts
export interface FeatureFlags {
  // 점진적 배포를 위한 플래그들
  ENABLE_CLASS_LIFECYCLE: boolean;          // 전체 기능 활성화
  ENABLE_OCCURRENCE_MANAGEMENT: boolean;    // Occurrence 관리
  ENABLE_ATTENDANCE_TRACKING: boolean;      // 출석 관리
  ENABLE_SCHEDULE_EDITING: boolean;         // 스케줄 편집
  ENABLE_DASHBOARD_V2: boolean;             // 새 대시보드
  
  // 사용자 그룹별 플래그
  ROLLOUT_PERCENTAGE: number;               // 0-100% 점진적 배포
  ENABLED_INSTRUCTORS: string[];            // 특정 강사만 활성화
  ENABLED_STUDIOS: string[];                // 특정 스튜디오만 활성화
  
  // 성능 관련 플래그
  ENABLE_CACHING: boolean;                  // 캐싱 활성화
  ENABLE_SEARCH_OPTIMIZATION: boolean;      // 검색 최적화
  
  // 실험 플래그
  EXPERIMENT_NEW_UI: boolean;               // 새 UI 실험
  EXPERIMENT_BULK_OPERATIONS: boolean;      // 대량 작업 실험
}

// 플래그 체크 함수
export function isFeatureEnabled(
  flag: keyof FeatureFlags, 
  context?: { userId?: string; studioId?: string }
): boolean {
  const flags = getFeatureFlags();
  
  // 기본 플래그 체크
  if (!flags[flag]) return false;
  
  // 점진적 배포 체크
  if (flags.ROLLOUT_PERCENTAGE < 100) {
    const hash = hashString(context?.userId || 'anonymous');
    if ((hash % 100) >= flags.ROLLOUT_PERCENTAGE) {
      return false;
    }
  }
  
  // 특정 그룹 체크
  if (context?.userId && flags.ENABLED_INSTRUCTORS.length > 0) {
    return flags.ENABLED_INSTRUCTORS.includes(context.userId);
  }
  
  if (context?.studioId && flags.ENABLED_STUDIOS.length > 0) {
    return flags.ENABLED_STUDIOS.includes(context.studioId);
  }
  
  return true;
}
```

### 배포 단계별 플래그 설정

```yaml
# feature-flags-stages.yml
development:
  ENABLE_CLASS_LIFECYCLE: true
  ENABLE_OCCURRENCE_MANAGEMENT: true
  ENABLE_ATTENDANCE_TRACKING: true
  ENABLE_SCHEDULE_EDITING: true
  ENABLE_DASHBOARD_V2: true
  ROLLOUT_PERCENTAGE: 100

staging:
  ENABLE_CLASS_LIFECYCLE: true
  ENABLE_OCCURRENCE_MANAGEMENT: true
  ENABLE_ATTENDANCE_TRACKING: true
  ENABLE_SCHEDULE_EDITING: true
  ENABLE_DASHBOARD_V2: true
  ROLLOUT_PERCENTAGE: 100

production_phase1:  # 5% 트래픽
  ENABLE_CLASS_LIFECYCLE: true
  ENABLE_OCCURRENCE_MANAGEMENT: false
  ENABLE_ATTENDANCE_TRACKING: false
  ENABLE_SCHEDULE_EDITING: false
  ENABLE_DASHBOARD_V2: false
  ROLLOUT_PERCENTAGE: 5

production_phase2:  # 25% 트래픽
  ENABLE_CLASS_LIFECYCLE: true
  ENABLE_OCCURRENCE_MANAGEMENT: true
  ENABLE_ATTENDANCE_TRACKING: false
  ENABLE_SCHEDULE_EDITING: true
  ENABLE_DASHBOARD_V2: true
  ROLLOUT_PERCENTAGE: 25

production_phase3:  # 100% 트래픽
  ENABLE_CLASS_LIFECYCLE: true
  ENABLE_OCCURRENCE_MANAGEMENT: true
  ENABLE_ATTENDANCE_TRACKING: true
  ENABLE_SCHEDULE_EDITING: true
  ENABLE_DASHBOARD_V2: true
  ROLLOUT_PERCENTAGE: 100
```

## 모니터링 및 관찰성

### 핵심 메트릭

```typescript
// monitoring-metrics.ts
export const METRICS = {
  // 비즈니스 메트릭
  CLASS_CREATION_RATE: 'class_creation_per_hour',
  ENROLLMENT_RATE: 'enrollments_per_hour', 
  ATTENDANCE_RATE: 'attendance_percentage',
  CANCELLATION_RATE: 'class_cancellation_percentage',
  
  // 성능 메트릭
  API_RESPONSE_TIME: 'api_response_time_ms',
  DATABASE_QUERY_TIME: 'db_query_time_ms',
  SEARCH_PERFORMANCE: 'search_query_time_ms',
  CACHE_HIT_RATE: 'cache_hit_percentage',
  
  // 에러 메트릭
  ERROR_RATE: 'error_rate_percentage',
  FAILED_MIGRATIONS: 'migration_failures',
  DATA_INCONSISTENCY: 'data_inconsistency_count',
  
  // 사용자 경험 메트릭
  PAGE_LOAD_TIME: 'page_load_time_ms',
  USER_SATISFACTION: 'user_satisfaction_score',
  FEATURE_ADOPTION: 'feature_usage_percentage'
};

// 알림 임계값
export const ALERT_THRESHOLDS = {
  [METRICS.API_RESPONSE_TIME]: { warning: 500, critical: 1000 },
  [METRICS.ERROR_RATE]: { warning: 1, critical: 5 },
  [METRICS.ATTENDANCE_RATE]: { warning: 70, critical: 50 },
  [METRICS.CACHE_HIT_RATE]: { warning: 80, critical: 60 }
};
```

### 대시보드 설정

```yaml
# monitoring-dashboard.yml
dashboards:
  business_metrics:
    title: "Class Lifecycle Business Metrics"
    panels:
      - type: stat
        title: "Active Classes"
        query: count(class_templates{status="ongoing"})
        
      - type: graph  
        title: "Enrollment Trends"
        query: rate(enrollments_total[1h])
        
      - type: heatmap
        title: "Class Popularity by Time"
        query: class_occurrences_by_hour
        
  performance_metrics:
    title: "System Performance"
    panels:
      - type: graph
        title: "API Response Times"
        query: histogram_quantile(0.95, api_request_duration_seconds)
        
      - type: graph
        title: "Database Performance" 
        query: postgresql_query_duration_seconds
        
      - type: stat
        title: "Cache Hit Rate"
        query: rate(cache_hits) / rate(cache_total)
        
  error_tracking:
    title: "Error Monitoring"
    panels:
      - type: graph
        title: "Error Rate"
        query: rate(http_requests_total{status=~"5.."}[5m])
        
      - type: table
        title: "Recent Errors"
        query: topk(10, errors_total)
```

### 로그 구조화

```typescript
// structured-logging.ts
export interface LogEntry {
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  service: string;
  trace_id: string;
  user_id?: string;
  instructor_id?: string;
  class_template_id?: string;
  occurrence_id?: string;
  action: string;
  message: string;
  context?: Record<string, any>;
  error?: {
    name: string;
    message: string;
    stack: string;
  };
}

// 로깅 헬퍼
export class ClassLifecycleLogger {
  static logClassCreated(templateId: string, instructorId: string) {
    logger.info({
      action: 'class_created',
      class_template_id: templateId,
      instructor_id: instructorId,
      message: 'New class template created'
    });
  }
  
  static logEnrollment(memberId: string, templateId: string, groupId: string) {
    logger.info({
      action: 'enrollment_created',
      user_id: memberId,
      class_template_id: templateId,
      schedule_group_id: groupId,
      message: 'Student enrolled in class'
    });
  }
  
  static logAttendanceCheck(occurrenceId: string, memberId: string, status: string) {
    logger.info({
      action: 'attendance_checked',
      occurrence_id: occurrenceId,
      user_id: memberId,
      context: { attendance_status: status },
      message: 'Attendance recorded'
    });
  }
  
  static logError(error: Error, context: Record<string, any>) {
    logger.error({
      action: 'system_error',
      message: error.message,
      context,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    });
  }
}
```

## 배포 자동화

### CI/CD 파이프라인

```yaml
# .github/workflows/deploy-class-lifecycle.yml
name: Deploy Class Lifecycle System

on:
  push:
    branches: [main]
    paths: 
      - 'src/app/api/instructor/classes/**'
      - 'src/app/(main)/instructor/classes/**'
      - 'docs/class-lifecycle/**'

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run database migrations
        run: npm run db:migrate
        env:
          DATABASE_URL: postgres://postgres:postgres@localhost:5432/test
          
      - name: Run tests
        run: npm run test:class-lifecycle
        
      - name: Run integration tests
        run: npm run test:integration
        
  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to staging
        run: |
          echo "🚀 Deploying to staging..."
          ./scripts/deploy.sh staging
          
      - name: Run smoke tests
        run: npm run test:smoke
        env:
          BASE_URL: https://staging.shallwe.com
          
  deploy-production:
    needs: deploy-staging
    runs-on: ubuntu-latest
    environment: production
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Blue-Green deployment
        run: |
          echo "🔵 Starting Blue-Green deployment..."
          ./scripts/blue-green-deploy.sh
          
      - name: Health checks
        run: |
          echo "🏥 Running health checks..."
          ./scripts/health-check.sh
          
      - name: Gradual traffic switch
        run: |
          echo "🔀 Switching traffic gradually..."
          ./scripts/traffic-switch.sh 10  # 10% traffic
          sleep 300                       # Wait 5 minutes
          ./scripts/traffic-switch.sh 50  # 50% traffic  
          sleep 300                       # Wait 5 minutes
          ./scripts/traffic-switch.sh 100 # 100% traffic
```

### 배포 스크립트

```bash
#!/bin/bash
# blue-green-deploy.sh

set -e

ENVIRONMENT=${1:-production}
GREEN_CLUSTER="app-cluster-green"
BLUE_CLUSTER="app-cluster-blue"
LOAD_BALANCER="app-lb"

echo "🔵 Starting Blue-Green deployment for $ENVIRONMENT"

# 1. 현재 활성 환경 확인
CURRENT_ENV=$(aws elbv2 describe-target-groups \
  --load-balancer-arn $LOAD_BALANCER \
  --query 'TargetGroups[0].TargetGroupName' \
  --output text)

if [[ $CURRENT_ENV == *"blue"* ]]; then
  ACTIVE_CLUSTER=$BLUE_CLUSTER
  INACTIVE_CLUSTER=$GREEN_CLUSTER
  NEW_ENV="green"
else
  ACTIVE_CLUSTER=$GREEN_CLUSTER  
  INACTIVE_CLUSTER=$BLUE_CLUSTER
  NEW_ENV="blue"
fi

echo "📍 Current active: $ACTIVE_CLUSTER, Deploying to: $INACTIVE_CLUSTER"

# 2. Green 환경에 새 버전 배포
echo "🚀 Deploying new version to $INACTIVE_CLUSTER..."
kubectl apply -f k8s/deployment-$NEW_ENV.yml
kubectl rollout status deployment/app-$NEW_ENV --timeout=600s

# 3. 데이터베이스 마이그레이션
echo "🗄️ Running database migrations..."
kubectl exec deployment/app-$NEW_ENV -- npm run db:migrate

# 4. 헬스 체크
echo "🏥 Health checking new environment..."
./scripts/health-check.sh $INACTIVE_CLUSTER

# 5. 통합 테스트
echo "🧪 Running integration tests on new environment..."
./scripts/integration-test.sh $INACTIVE_CLUSTER

# 6. 트래픽 전환
echo "🔀 Switching traffic to new environment..."
aws elbv2 modify-target-group \
  --target-group-arn $(get_target_group_arn $INACTIVE_CLUSTER) \
  --health-check-path /health

# 7. 구 환경 정리 (5분 후)
echo "⏰ Scheduling cleanup of old environment..."
(sleep 300 && kubectl delete deployment app-$ACTIVE_CLUSTER) &

echo "✅ Blue-Green deployment completed successfully!"
```

## 롤백 전략

### 자동 롤백 트리거

```typescript
// rollback-triggers.ts
export interface RollbackTrigger {
  name: string;
  condition: () => Promise<boolean>;
  severity: 'warning' | 'critical';
  cooldown: number; // 분
}

export const ROLLBACK_TRIGGERS: RollbackTrigger[] = [
  {
    name: 'high_error_rate',
    condition: async () => {
      const errorRate = await getMetric('error_rate_percentage', '5m');
      return errorRate > 5; // 5% 이상 에러
    },
    severity: 'critical',
    cooldown: 10
  },
  
  {
    name: 'slow_response_time',
    condition: async () => {
      const p95 = await getMetric('api_response_time_p95', '5m');
      return p95 > 2000; // 2초 이상
    },
    severity: 'critical', 
    cooldown: 15
  },
  
  {
    name: 'database_errors',
    condition: async () => {
      const dbErrors = await getMetric('database_error_count', '2m');
      return dbErrors > 10;
    },
    severity: 'critical',
    cooldown: 5
  },
  
  {
    name: 'enrollment_drop',
    condition: async () => {
      const currentRate = await getMetric('enrollment_rate', '10m');
      const baselineRate = await getMetric('enrollment_rate', '1d', '1h ago');
      return currentRate < baselineRate * 0.5; // 50% 이상 감소
    },
    severity: 'warning',
    cooldown: 30
  }
];

// 롤백 실행기
export class AutoRollback {
  private triggers = new Map<string, Date>();
  
  async checkTriggers(): Promise<void> {
    for (const trigger of ROLLBACK_TRIGGERS) {
      const lastTriggered = this.triggers.get(trigger.name);
      const cooldownExpired = !lastTriggered || 
        (Date.now() - lastTriggered.getTime()) > trigger.cooldown * 60 * 1000;
        
      if (!cooldownExpired) continue;
      
      try {
        const shouldTrigger = await trigger.condition();
        
        if (shouldTrigger) {
          this.triggers.set(trigger.name, new Date());
          
          if (trigger.severity === 'critical') {
            await this.executeRollback(trigger.name);
          } else {
            await this.sendAlert(trigger.name);
          }
        }
      } catch (error) {
        console.error(`Error checking trigger ${trigger.name}:`, error);
      }
    }
  }
  
  private async executeRollback(triggerName: string): Promise<void> {
    console.log(`🚨 CRITICAL: Executing automatic rollback due to ${triggerName}`);
    
    // 즉시 이전 버전으로 트래픽 전환
    await this.switchToPreviousVersion();
    
    // 알림 발송
    await this.sendCriticalAlert(triggerName);
    
    // 현재 버전 비활성화
    await this.disableCurrentVersion();
  }
  
  private async switchToPreviousVersion(): Promise<void> {
    // Blue-Green 환경 간 즉시 전환
    await execAsync('./scripts/emergency-rollback.sh');
  }
}
```

### 수동 롤백 절차

```bash
#!/bin/bash
# manual-rollback.sh

ROLLBACK_VERSION=${1}
REASON=${2:-"Manual rollback"}

echo "🔄 Starting manual rollback to version $ROLLBACK_VERSION"
echo "📝 Reason: $REASON"

# 1. 롤백 전 백업
echo "📦 Creating pre-rollback backup..."
BACKUP_FILE="rollback_backup_$(date +%Y%m%d_%H%M%S).sql"
pg_dump $DATABASE_URL > "backups/$BACKUP_FILE"

# 2. 트래픽 중단 (점검 모드)
echo "🚧 Enabling maintenance mode..."
kubectl patch deployment app-current -p '{"spec":{"replicas":0}}'
kubectl apply -f k8s/maintenance-mode.yml

# 3. 데이터베이스 롤백
echo "🗄️ Rolling back database..."
if [ -f "migrations/rollback_to_$ROLLBACK_VERSION.sql" ]; then
  psql $DATABASE_URL -f "migrations/rollback_to_$ROLLBACK_VERSION.sql"
else
  echo "⚠️ No database rollback script found, manual intervention may be required"
fi

# 4. 애플리케이션 롤백
echo "🚀 Rolling back application..."
kubectl apply -f "k8s/deployment-$ROLLBACK_VERSION.yml"
kubectl rollout status deployment/app-$ROLLBACK_VERSION --timeout=300s

# 5. 헬스 체크
echo "🏥 Health checking rolled back version..."
./scripts/health-check.sh

# 6. 트래픽 복구
echo "🔀 Restoring traffic..."
kubectl delete -f k8s/maintenance-mode.yml
kubectl patch deployment app-$ROLLBACK_VERSION -p '{"spec":{"replicas":3}}'

# 7. 검증
echo "✅ Running post-rollback validation..."
./scripts/validation-test.sh

echo "🎉 Rollback completed successfully!"
echo "📊 Please monitor metrics closely and update incident log"
```

## 성능 최적화

### 캐싱 전략

```typescript
// caching-strategy.ts
export const CACHE_CONFIG = {
  // Redis 캐시 설정
  redis: {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    ttl: {
      class_search: 300,        // 5분
      class_details: 600,       // 10분  
      instructor_schedule: 120, // 2분
      enrollment_stats: 60,     // 1분
      upcoming_classes: 180     // 3분
    }
  },
  
  // CDN 캐싱
  cdn: {
    static_assets: '1y',        // 1년
    api_responses: '5m',        // 5분
    class_images: '1d'          // 1일
  },
  
  // 브라우저 캐싱
  browser: {
    class_list: '2m',           // 2분
    class_details: '5m',        // 5분
    user_profile: '10m'         // 10분
  }
};

// 캐시 무효화 전략
export class CacheInvalidation {
  static async invalidateClassData(templateId: string): Promise<void> {
    const patterns = [
      `class:${templateId}:*`,
      `search:*:${templateId}:*`,
      `upcoming:*`,
      `instructor:*:classes`
    ];
    
    await Promise.all(
      patterns.map(pattern => redis.del(pattern))
    );
  }
  
  static async invalidateScheduleData(scheduleId: string): Promise<void> {
    const schedule = await getSchedule(scheduleId);
    await this.invalidateClassData(schedule.class_template_id);
    
    // 관련 occurrence 캐시도 무효화
    await redis.del(`schedule:${scheduleId}:occurrences`);
  }
}
```

### 데이터베이스 최적화

```sql
-- 성능 최적화 쿼리
-- 1. 연결 풀 설정
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';

-- 2. 쿼리 최적화
-- 자주 사용되는 복합 인덱스
CREATE INDEX CONCURRENTLY idx_class_search_optimized 
ON class_templates (status, category, recruitment_end_date) 
WHERE is_active = true;

-- 3. 파티셔닝 (대용량 데이터 대비)
CREATE TABLE class_occurrences_partitioned (
  LIKE class_occurrences INCLUDING ALL
) PARTITION BY RANGE (occurrence_date);

-- 월별 파티션 생성
CREATE TABLE class_occurrences_2024_12 PARTITION OF class_occurrences_partitioned
FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

-- 4. 통계 수집 최적화
ALTER TABLE class_templates SET (autovacuum_analyze_scale_factor = 0.02);
ALTER TABLE class_occurrences SET (autovacuum_analyze_scale_factor = 0.05);
```

## 보안 고려사항

### 배포 보안 체크리스트

```yaml
# security-checklist.yml
pre_deployment:
  - name: "Dependencies vulnerability scan"
    command: "npm audit --audit-level moderate"
    required: true
    
  - name: "Secret scanning"
    command: "git-secrets --scan"
    required: true
    
  - name: "Static code analysis"
    command: "npm run lint:security"
    required: true
    
  - name: "Infrastructure scan"
    command: "terraform plan -out=tfplan && tfsec tfplan"
    required: true

post_deployment:
  - name: "Penetration testing"
    command: "./scripts/pentest.sh"
    required: false
    
  - name: "SSL/TLS verification"
    command: "testssl.sh https://api.shallwe.com"
    required: true
    
  - name: "API security test"
    command: "./scripts/api-security-test.sh"
    required: true
```

### 환경 변수 보안

```typescript
// secure-config.ts
export interface SecureConfig {
  database: {
    url: string;          // 암호화된 연결 문자열
    ssl: boolean;
    poolSize: number;
  };
  redis: {
    url: string;          // 암호화된 연결 문자열
    tls: boolean;
  };
  auth: {
    jwtSecret: string;    // JWT 서명 키
    sessionSecret: string; // 세션 암호화 키
  };
  external: {
    paymentApiKey: string;  // 결제 API 키
    smsApiKey: string;      // SMS API 키
  };
}

// 환경별 설정 검증
export function validateConfig(env: string): SecureConfig {
  const requiredVars = [
    'DATABASE_URL',
    'REDIS_URL', 
    'JWT_SECRET',
    'SESSION_SECRET'
  ];
  
  const missing = requiredVars.filter(key => !process.env[key]);
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
  
  // 프로덕션 환경에서 기본값 사용 금지
  if (env === 'production') {
    const defaults = ['changeme', 'default', 'localhost'];
    requiredVars.forEach(key => {
      const value = process.env[key]?.toLowerCase();
      if (value && defaults.some(def => value.includes(def))) {
        throw new Error(`Production environment cannot use default value for ${key}`);
      }
    });
  }
  
  return {
    database: {
      url: process.env.DATABASE_URL!,
      ssl: env === 'production',
      poolSize: parseInt(process.env.DB_POOL_SIZE || '10')
    },
    redis: {
      url: process.env.REDIS_URL!,
      tls: env === 'production'
    },
    auth: {
      jwtSecret: process.env.JWT_SECRET!,
      sessionSecret: process.env.SESSION_SECRET!
    },
    external: {
      paymentApiKey: process.env.PAYMENT_API_KEY!,
      smsApiKey: process.env.SMS_API_KEY!
    }
  };
}
```

## 운영 가이드

### 일상 운영 체크리스트

```yaml
# daily-operations.yml
daily_checks:
  morning:
    - name: "시스템 헬스 체크"
      tasks:
        - "모든 서비스 상태 확인"
        - "데이터베이스 연결 확인"
        - "캐시 서버 상태 확인"
        - "에러 로그 검토"
        
    - name: "비즈니스 메트릭 확인"
      tasks:
        - "전일 신규 수강 신청 수"
        - "취소된 클래스 현황"
        - "출석률 통계"
        - "시스템 사용률"
        
  evening:
    - name: "성능 리뷰"
      tasks:
        - "응답 시간 트렌드 분석"
        - "트래픽 패턴 분석"
        - "데이터베이스 성능 검토"
        - "캐시 히트율 확인"

weekly_checks:
  - name: "용량 계획"
    tasks:
      - "데이터베이스 크기 모니터링"
      - "로그 파일 크기 확인"
      - "서버 리소스 사용률 분석"
      
  - name: "보안 검토"
    tasks:
      - "의심스러운 활동 로그 검토"
      - "접근 권한 검토"
      - "SSL 인증서 만료일 확인"
```

### 장애 대응 플레이북

```markdown
# 장애 대응 플레이북

## 1. 서비스 완전 중단

### 증상
- 모든 API 요청이 실패
- 웹사이트 접근 불가
- 헬스 체크 실패

### 대응 절차
1. **즉시 대응 (0-5분)**
   ```bash
   # 헬스 체크 실행
   ./scripts/health-check.sh
   
   # 서비스 상태 확인
   kubectl get pods
   kubectl get services
   
   # 로드 밸런서 상태 확인
   aws elbv2 describe-target-health --target-group-arn $TARGET_GROUP
   ```

2. **원인 분석 (5-15분)**
   ```bash
   # 최근 배포 이력 확인
   kubectl rollout history deployment/app
   
   # 에러 로그 확인
   kubectl logs -l app=shallwe --tail=100
   
   # 데이터베이스 연결 확인
   psql $DATABASE_URL -c "SELECT 1"
   ```

3. **복구 조치 (15-30분)**
   ```bash
   # 이전 버전으로 롤백
   kubectl rollout undo deployment/app
   
   # 또는 긴급 롤백 실행
   ./scripts/emergency-rollback.sh
   ```

## 2. 데이터베이스 성능 저하

### 증상
- API 응답 시간 급증
- 데이터베이스 연결 타임아웃
- 높은 CPU/메모리 사용률

### 대응 절차
1. **즉시 확인**
   ```sql
   -- 현재 실행 중인 쿼리 확인
   SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
   FROM pg_stat_activity 
   WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';
   
   -- 락 대기 확인
   SELECT * FROM pg_locks WHERE NOT granted;
   ```

2. **임시 조치**
   ```sql
   -- 장시간 실행 쿼리 종료
   SELECT pg_terminate_backend(pid) FROM pg_stat_activity 
   WHERE (now() - pg_stat_activity.query_start) > interval '10 minutes';
   
   -- 캐시 강제 활성화
   redis-cli SET ENABLE_AGGRESSIVE_CACHING true
   ```

## 3. 높은 에러율

### 증상
- 5xx 에러 급증
- 특정 기능 실패율 증가
- 사용자 불만 접수

### 대응 절차
1. **에러 패턴 분석**
   ```bash
   # 에러 로그 그룹핑
   kubectl logs -l app=shallwe | grep ERROR | sort | uniq -c | sort -nr
   
   # 특정 에러 상세 분석
   kubectl logs -l app=shallwe | grep "specific_error_pattern"
   ```

2. **기능별 격리**
   ```typescript
   // 문제 기능 비활성화
   await updateFeatureFlag('PROBLEMATIC_FEATURE', false);
   
   // 서킷 브레이커 활성화
   await enableCircuitBreaker('class_enrollment');
   ```
```

이 배포 가이드는 클래스 라이프사이클 시스템의 안전하고 효율적인 운영을 위한 종합적인 전략을 제공합니다. Blue-Green 배포, 기능 플래그, 모니터링, 롤백 전략을 통해 무중단 서비스 운영을 보장합니다.