# 클래스 라이프사이클 구현 계획

## 구현 개요

클래스 라이프사이클 관리 시스템을 안전하고 체계적으로 구현하기 위한 단계별 계획입니다. 기존 시스템과의 호환성을 유지하면서 점진적으로 새로운 기능을 도입합니다.

## 전체 일정

```mermaid
gantt
    title 클래스 라이프사이클 구현 일정
    dateFormat  YYYY-MM-DD
    section Phase 1: 기반 구축
    스키마 확장               :p1-1, 2024-12-01, 3d
    기본 API 개발            :p1-2, after p1-1, 5d
    Dashboard v2 개발        :p1-3, after p1-2, 4d
    기본 테스트              :p1-4, after p1-3, 3d
    
    section Phase 2: 핵심 기능
    Occurrence 관리          :p2-1, after p1-4, 7d
    출석 체크 시스템         :p2-2, after p2-1, 5d
    검색 기능 구현           :p2-3, after p2-2, 6d
    통합 테스트              :p2-4, after p2-3, 4d
    
    section Phase 3: 고급 기능
    알림 시스템              :p3-1, after p2-4, 5d
    분석 리포팅              :p3-2, after p3-1, 6d
    성능 최적화              :p3-3, after p3-2, 4d
    E2E 테스트               :p3-4, after p3-3, 5d
    
    section Phase 4: 배포
    스테이징 배포            :p4-1, after p3-4, 2d
    QA 테스트                :p4-2, after p4-1, 5d
    프로덕션 배포            :p4-3, after p4-2, 3d
    모니터링 및 최적화       :p4-4, after p4-3, 7d
```

## Phase 1: 기반 구축 (2주)

### 목표
- 기존 시스템 호환성 유지하면서 스키마 확장
- 기본 CRUD API 구현
- 새로운 Dashboard 구조 개발

### 상세 작업

#### 1-1. 데이터베이스 스키마 확장 (3일)

**Day 1: 스키마 설계 및 마이그레이션 스크립트 작성**
```sql
-- 1. 기존 테이블 확장 스크립트
-- src/lib/db/migrations/001_add_class_lifecycle.sql

-- class_templates 확장
ALTER TABLE class_templates ADD COLUMN recruitment_start_date DATE;
ALTER TABLE class_templates ADD COLUMN recruitment_end_date DATE;
ALTER TABLE class_templates ADD COLUMN class_start_date DATE;
ALTER TABLE class_templates ADD COLUMN class_end_date DATE;
ALTER TABLE class_templates ADD COLUMN status TEXT DEFAULT 'upcoming';

-- 🆕 class_schedule_groups 확장 (그룹별 수업 기간 관리)
ALTER TABLE class_schedule_groups ADD COLUMN group_start_date DATE;
ALTER TABLE class_schedule_groups ADD COLUMN group_end_date DATE;

-- 2. 새 테이블 생성
CREATE TABLE class_occurrences (...);
CREATE TABLE class_attendances (...);

-- 3. 기존 enrollments 테이블 수정
ALTER TABLE class_enrollments ADD COLUMN schedule_group_id UUID;
```

**Day 2: 개발 환경 적용 및 데이터 검증**
- 로컬 DB에 마이그레이션 적용
- 기존 데이터 무결성 확인
- 테스트 데이터 생성 스크립트 작성

**Day 3: 인덱스 생성 및 성능 테스트**
```sql
-- 검색 성능 최적화 인덱스
CREATE INDEX idx_templates_search ON class_templates (category, level, status);
CREATE INDEX idx_schedules_search ON class_schedules (day_of_week, start_time);
CREATE INDEX idx_occurrences_date ON class_occurrences (occurrence_date, status);
```

#### 1-2. 기본 API 개발 (5일)

**Day 1-2: 엔티티 및 타입 정의**
```typescript
// src/types/class-lifecycle.ts
export interface ClassTemplate {
  // 기존 필드 + 새 라이프사이클 필드
}

export interface ScheduleGroup {
  // 🆕 그룹별 수업 기간 관리 필드 추가
  group_start_date?: date;
  group_end_date?: date;
}

export interface Occurrence {
  // 새로운 엔티티 정의
}

// src/lib/db/schema.ts 확장
export const classOccurrences = pgTable('class_occurrences', {
  // 테이블 정의
});

// 🆕 class_schedule_groups 테이블 확장
export const classScheduleGroups = pgTable('class_schedule_groups', {
  // 기존 필드들...
  groupStartDate: date('group_start_date'),
  groupEndDate: date('group_end_date'),
});
```

**Day 3-4: 기본 CRUD 서비스 구현 (그룹별 기간 관리 포함)**
```typescript
// src/services/class-lifecycle.service.ts
export class ClassLifecycleService {
  async getClassesWithLifecycle(instructorId: string): Promise<ClassTemplate[]>
  async updateClassDates(classId: string, dates: ClassDates): Promise<ClassTemplate>
  
  // 🆕 그룹별 수업 기간 관리 메서드 추가
  async updateGroupDates(groupId: string, dates: GroupDates): Promise<ScheduleGroup>
  async calculateTemplateClassDates(templateId: string): Promise<TemplateClassDates>
  async generateGroupOccurrences(groupId: string): Promise<Occurrence[]>
  
  async generateOccurrences(classId: string): Promise<Occurrence[]>
  async getUpcomingOccurrences(instructorId: string): Promise<Occurrence[]>
}
```

**Day 5: API 엔드포인트 구현**
```typescript
// src/app/api/instructor/classes/[id]/route.ts
export async function GET(request: Request, { params }: { params: { id: string } }) {
  // 확장된 클래스 정보 조회 (그룹별 수업 기간 정보 포함)
}

export async function PATCH(request: Request, { params }: { params: { id: string } }) {
  // 라이프사이클 정보 업데이트 (템플릿 모집 기간 + 그룹별 수업 기간)
}

// 🆕 그룹별 수업 기간 관리 API
// src/app/api/instructor/schedule-groups/[id]/route.ts
export async function PATCH(request: Request, { params }: { params: { id: string } }) {
  const { group_start_date, group_end_date } = await request.json();
  
  // 그룹별 수업 기간 업데이트
  const updatedGroup = await updateGroupDates(params.id, {
    group_start_date,
    group_end_date
  });
  
  // 템플릿 수업 기간 자동 재계산 (모든 그룹의 범위)
  await calculateAndUpdateTemplateClassDates(updatedGroup.class_template_id);
  
  return NextResponse.json({ data: { schedule_group: updatedGroup } });
}

// src/app/api/instructor/occurrences/route.ts
export async function GET(request: Request) {
  // Occurrence 목록 조회
}
```

#### 1-3. Dashboard v2 개발 (4일)

**Day 1-2: 백엔드 API 구현**
```typescript
// src/app/api/instructor/dashboard/v2/route.ts
export async function GET(request: Request) {
  return {
    stats: {
      // 실제 Occurrence 기반 통계
      upcoming_occurrences: await getUpcomingOccurrences(instructorId),
      this_week_occurrences: await getThisWeekOccurrences(instructorId),
      // ... 기타 통계
    },
    upcoming_occurrences: await getDetailedUpcomingOccurrences(instructorId),
    recent_enrollments: await getRecentEnrollments(instructorId)
  };
}
```

**Day 3-4: 프론트엔드 컴포넌트 개발**
```typescript
// src/app/(main)/instructor/dashboard/page.tsx
export default function DashboardV2() {
  const { data: dashboardData } = useDashboardV2();
  
  return (
    <div className="dashboard-v2">
      <StatsCards stats={dashboardData.stats} />
      <UpcomingOccurrences occurrences={dashboardData.upcoming_occurrences} />
      <RecentEnrollments enrollments={dashboardData.recent_enrollments} />
      <WeeklySchedule schedule={dashboardData.this_week_schedule} />
    </div>
  );
}
```

#### 1-4. 기본 테스트 (3일)

**Day 1: 단위 테스트**
```typescript
// tests/services/class-lifecycle.test.ts
describe('ClassLifecycleService', () => {
  test('should generate occurrences based on schedule pattern', async () => {
    const template = createMockTemplate();
    const schedules = createMockSchedules();
    
    const occurrences = await service.generateOccurrences(template, schedules);
    
    expect(occurrences).toHaveLength(12); // 4주 × 3일
    expect(occurrences[0].occurrence_date).toBe('2024-12-02');
  });
});
```

**Day 2: 통합 테스트**
```typescript
// tests/api/instructor/dashboard.test.ts
describe('Dashboard V2 API', () => {
  test('should return occurrence-based dashboard data', async () => {
    await createTestData();
    
    const response = await request(app)
      .get('/api/instructor/dashboard/v2')
      .set('Authorization', `Bearer ${token}`);
    
    expect(response.body.data.stats.upcoming_occurrences).toBeGreaterThan(0);
    expect(response.body.data.upcoming_occurrences).toBeArray();
  });
});
```

**Day 3: E2E 테스트 (기본)**
```typescript
// e2e/dashboard.spec.ts
test('instructor can view occurrence-based dashboard', async ({ page }) => {
  await page.goto('/instructor/dashboard');
  
  await expect(page.locator('[data-testid=upcoming-occurrences]')).toBeVisible();
  await expect(page.locator('[data-testid=occurrence-item]')).toHaveCount(5);
});
```

## Phase 2: 핵심 기능 (3주)

### 목표
- Occurrence 개별 관리 기능 구현
- 출석 체크 시스템 개발
- 클래스 검색 기능 구현

### 상세 작업

#### 2-1. Occurrence 관리 (7일)

**Day 1-2: Occurrence CRUD API**
```typescript
// src/app/api/instructor/occurrences/[id]/route.ts
export async function PATCH(request: Request, { params }: { params: { id: string } }) {
  const { occurrence_date, start_time, status, cancellation_reason } = await request.json();
  
  // 개별 수업 수정 로직
  const updatedOccurrence = await updateOccurrence(params.id, {
    occurrence_date,
    start_time,
    status,
    cancellation_reason
  });
  
  // 알림 발송
  if (status === 'cancelled') {
    await notifyEnrollees(params.id, cancellation_reason);
  }
  
  return NextResponse.json({ data: { occurrence: updatedOccurrence } });
}
```

**Day 3-4: 보강 수업 기능**
```typescript
// src/services/substitute-class.service.ts
export class SubstituteClassService {
  async createSubstituteClass(
    originalOccurrenceId: string,
    newDate: Date,
    newTime: string
  ): Promise<Occurrence> {
    // 1. 원본 수업 취소
    await this.cancelOccurrence(originalOccurrenceId);
    
    // 2. 보강 수업 생성
    const substituteOccurrence = await this.createOccurrence({
      ...originalOccurrence,
      occurrence_date: newDate,
      start_time: newTime,
      is_substitute_class: true,
      original_occurrence_id: originalOccurrenceId
    });
    
    // 3. 기존 신청자들 자동 등록
    await this.transferEnrollments(originalOccurrenceId, substituteOccurrence.id);
    
    return substituteOccurrence;
  }
}
```

**Day 5-6: 대량 Occurrence 생성**
```typescript
// src/app/api/instructor/occurrences/bulk-generate/route.ts
export async function POST(request: Request) {
  const { class_template_id, date_range, exclude_dates } = await request.json();
  
  const occurrences = await generateOccurrencesBulk({
    classTemplateId: class_template_id,
    startDate: date_range.start_date,
    endDate: date_range.end_date,
    excludeDates: exclude_dates
  });
  
  return NextResponse.json({
    data: {
      generated_occurrences: occurrences,
      summary: {
        total_generated: occurrences.length,
        by_schedule_group: getCountByGroup(occurrences)
      }
    }
  });
}
```

**Day 7: 프론트엔드 UI 개발**
```typescript
// src/components/instructor/OccurrenceManager.tsx
export function OccurrenceManager({ classId }: { classId: string }) {
  const [occurrences, setOccurrences] = useState<Occurrence[]>([]);
  const [selectedOccurrence, setSelectedOccurrence] = useState<Occurrence | null>(null);
  
  return (
    <div className="occurrence-manager">
      <OccurrenceList 
        occurrences={occurrences}
        onSelect={setSelectedOccurrence}
      />
      {selectedOccurrence && (
        <OccurrenceEditor
          occurrence={selectedOccurrence}
          onUpdate={handleUpdate}
          onCancel={handleCancel}
          onCreateSubstitute={handleCreateSubstitute}
        />
      )}
    </div>
  );
}
```

#### 2-2. 출석 체크 시스템 (5일)

**Day 1-2: 출석 API 개발**
```typescript
// src/app/api/instructor/occurrences/[id]/attendances/route.ts
export async function GET(request: Request, { params }: { params: { id: string } }) {
  const occurrenceId = params.id;
  const attendances = await getAttendancesByOccurrence(occurrenceId);
  
  return NextResponse.json({
    data: {
      occurrence: await getOccurrence(occurrenceId),
      attendances,
      summary: calculateAttendanceSummary(attendances)
    }
  });
}

// src/app/api/instructor/attendances/bulk/route.ts
export async function POST(request: Request) {
  const { occurrence_id, attendances } = await request.json();
  
  const results = await Promise.all(
    attendances.map(attendance => 
      upsertAttendance({
        class_occurrence_id: occurrence_id,
        member_id: attendance.member_id,
        attendance_status: attendance.attendance_status,
        checked_in_at: attendance.checked_in_at,
        notes: attendance.notes
      })
    )
  );
  
  // Occurrence 통계 업데이트
  await updateOccurrenceStats(occurrence_id);
  
  return NextResponse.json({
    data: {
      processed_attendances: results.length,
      summary: calculateAttendanceSummary(results)
    }
  });
}
```

**Day 3-4: 출석 체크 UI**
```typescript
// src/components/instructor/AttendanceChecker.tsx
export function AttendanceChecker({ occurrenceId }: { occurrenceId: string }) {
  const { data: attendanceData } = useAttendanceData(occurrenceId);
  const [attendances, setAttendances] = useState<AttendanceRecord[]>([]);
  
  const handleBulkSave = async () => {
    await bulkUpdateAttendances(occurrenceId, attendances);
    toast.success('출석 체크가 완료되었습니다');
  };
  
  return (
    <div className="attendance-checker">
      <OccurrenceHeader occurrence={attendanceData.occurrence} />
      
      <div className="attendance-grid">
        {attendanceData.attendances.map(attendance => (
          <AttendanceRow
            key={attendance.id}
            attendance={attendance}
            onChange={(status) => updateAttendanceStatus(attendance.id, status)}
          />
        ))}
      </div>
      
      <AttendanceSummary summary={attendanceData.summary} />
      
      <div className="actions">
        <Button onClick={handleBulkSave}>출석 체크 완료</Button>
      </div>
    </div>
  );
}
```

**Day 5: 출석 통계 및 리포팅**
```typescript
// src/services/attendance-analytics.service.ts
export class AttendanceAnalyticsService {
  async getAttendanceStats(instructorId: string, period: Period): Promise<AttendanceStats> {
    return {
      overall_attendance_rate: await this.calculateOverallRate(instructorId, period),
      by_class: await this.getStatsByClass(instructorId, period),
      by_time_slot: await this.getStatsByTimeSlot(instructorId, period),
      trends: await this.getAttendanceTrends(instructorId, period)
    };
  }
  
  async getStudentAttendanceHistory(studentId: string, classId: string): Promise<AttendanceHistory> {
    // 학생별 출석 히스토리
  }
}
```

#### 2-3. 검색 기능 구현 (6일)

**Day 1-2: 검색 인덱스 최적화**
```sql
-- 검색 성능 최적화 인덱스
CREATE INDEX idx_classes_search_text ON class_templates 
USING gin(to_tsvector('korean', title || ' ' || coalesce(description, '')));

CREATE INDEX idx_classes_filters ON class_templates (
  category, level, status, recruitment_start_date, recruitment_end_date
);

CREATE INDEX idx_schedules_time_search ON class_schedules (
  day_of_week, start_time, end_time
) WHERE is_active = true;
```

**Day 3-4: 검색 API 개발**
```typescript
// src/app/api/classes/search/route.ts
export async function GET(request: Request) {
  const searchParams = new URL(request.url).searchParams;
  const filters = parseSearchFilters(searchParams);
  
  const results = await searchClasses({
    category: filters.category,
    level: filters.level,
    schedule: {
      days: filters.days,
      time_range: filters.time_range
    },
    location: filters.location,
    period: filters.period,
    search: filters.search,
    sort: filters.sort,
    pagination: {
      limit: filters.limit,
      offset: filters.offset
    }
  });
  
  return NextResponse.json({
    data: {
      classes: results.classes,
      filters: await getAvailableFilters(filters),
      summary: {
        total_found: results.total,
        search_time_ms: results.search_time
      }
    },
    pagination: results.pagination
  });
}

// src/services/class-search.service.ts
export class ClassSearchService {
  async searchClasses(filters: SearchFilters): Promise<SearchResults> {
    const query = this.buildSearchQuery(filters);
    const results = await this.executeSearch(query);
    
    return {
      classes: await this.enrichSearchResults(results),
      total: results.length,
      search_time: performance.now() - startTime
    };
  }
  
  private buildSearchQuery(filters: SearchFilters): QueryBuilder {
    let query = this.db
      .select()
      .from(classTemplates)
      .leftJoin(classScheduleGroups, eq(classTemplates.id, classScheduleGroups.classTemplateId))
      .leftJoin(classSchedules, eq(classScheduleGroups.id, classSchedules.scheduleGroupId))
      .leftJoin(studios, eq(classTemplates.studioId, studios.id))
      .leftJoin(instructors, eq(classTemplates.instructorId, instructors.id));
    
    // 필터 적용
    if (filters.category) {
      query = query.where(eq(classTemplates.category, filters.category));
    }
    
    if (filters.schedule?.days) {
      query = query.where(inArray(classSchedules.dayOfWeek, filters.schedule.days));
    }
    
    if (filters.schedule?.time_range) {
      query = query.where(
        and(
          gte(classSchedules.startTime, filters.schedule.time_range.start_after),
          lte(classSchedules.startTime, filters.schedule.time_range.start_before)
        )
      );
    }
    
    if (filters.search) {
      query = query.where(
        sql`to_tsvector('korean', ${classTemplates.title} || ' ' || coalesce(${classTemplates.description}, '')) 
            @@ plainto_tsquery('korean', ${filters.search})`
      );
    }
    
    return query;
  }
}
```

**Day 5-6: 검색 UI 개발**
```typescript
// src/components/search/ClassSearchForm.tsx
export function ClassSearchForm() {
  const [filters, setFilters] = useState<SearchFilters>({});
  const { data: searchResults, isLoading } = useClassSearch(filters);
  
  return (
    <div className="class-search">
      <SearchFilters
        filters={filters}
        onChange={setFilters}
        availableFilters={searchResults?.filters}
      />
      
      <SearchResults
        results={searchResults?.classes}
        loading={isLoading}
        total={searchResults?.summary.total_found}
      />
      
      <SearchPagination
        pagination={searchResults?.pagination}
        onPageChange={handlePageChange}
      />
    </div>
  );
}

// src/components/search/SearchFilters.tsx
export function SearchFilters({ filters, onChange, availableFilters }: SearchFiltersProps) {
  return (
    <div className="search-filters">
      <CategoryFilter 
        value={filters.category}
        options={availableFilters?.categories}
        onChange={(category) => onChange({ ...filters, category })}
      />
      
      <TimeSlotFilter
        value={filters.schedule?.time_range}
        onChange={(time_range) => onChange({
          ...filters,
          schedule: { ...filters.schedule, time_range }
        })}
      />
      
      <DayOfWeekFilter
        value={filters.schedule?.days}
        onChange={(days) => onChange({
          ...filters,
          schedule: { ...filters.schedule, days }
        })}
      />
      
      <LocationFilter
        value={filters.location}
        onChange={(location) => onChange({ ...filters, location })}
      />
    </div>
  );
}
```

#### 2-4. 통합 테스트 (4일)

**Day 1-2: API 통합 테스트**
```typescript
// tests/integration/class-lifecycle.test.ts
describe('Class Lifecycle Integration', () => {
  test('complete class lifecycle workflow', async () => {
    // 1. 클래스 생성
    const classTemplate = await createTestClass();
    
    // 2. Occurrence 생성 확인
    const occurrences = await getOccurrences(classTemplate.id);
    expect(occurrences).toHaveLength(12);
    
    // 3. 수강 신청
    const enrollment = await enrollStudent(classTemplate.id, testStudent.id);
    expect(enrollment.status).toBe('confirmed');
    
    // 4. 개별 수업 수정
    const updatedOccurrence = await updateOccurrence(occurrences[0].id, {
      start_time: '15:00'
    });
    expect(updatedOccurrence.start_time).toBe('15:00');
    
    // 5. 출석 체크
    const attendance = await checkAttendance(occurrences[0].id, testStudent.id, 'present');
    expect(attendance.attendance_status).toBe('present');
  });
});
```

**Day 3-4: 성능 테스트**
```typescript
// tests/performance/search.test.ts
describe('Search Performance', () => {
  test('should handle search with large dataset', async () => {
    // 10,000개 클래스 데이터 생성
    await createLargeTestDataset();
    
    const startTime = Date.now();
    const results = await searchClasses({
      category: 'yoga',
      schedule: { days: ['MONDAY', 'WEDNESDAY'] }
    });
    const endTime = Date.now();
    
    expect(endTime - startTime).toBeLessThan(1000); // 1초 이내
    expect(results.classes).toBeDefined();
  });
});
```

## Phase 3: 고급 기능 (3주)

### 목표
- 알림 시스템 구현
- 분석 및 리포팅 기능 개발
- 성능 최적화

### 상세 작업

#### 3-1. 알림 시스템 (5일)

**Day 1-2: 알림 인프라 구축**
```typescript
// src/services/notification.service.ts
export class NotificationService {
  async sendOccurrenceUpdate(
    occurrenceId: string,
    type: 'cancellation' | 'time_change' | 'substitute',
    message: string
  ): Promise<void> {
    const enrollments = await getEnrollmentsByOccurrence(occurrenceId);
    
    const notifications = enrollments.map(enrollment => ({
      member_id: enrollment.member_id,
      type,
      title: this.getNotificationTitle(type),
      message,
      related_id: occurrenceId,
      related_type: 'occurrence'
    }));
    
    await this.sendBulkNotifications(notifications);
  }
  
  async sendUpcomingClassReminder(occurrenceId: string): Promise<void> {
    // 수업 2시간 전 알림
  }
  
  async sendAttendanceReminder(occurrenceId: string): Promise<void> {
    // 수업 후 출석 체크 알림
  }
}
```

**Day 3-4: 이메일/푸시 알림 구현**
```typescript
// src/lib/notifications/email.service.ts
export class EmailNotificationService {
  async sendClassCancellationEmail(
    memberEmail: string,
    classInfo: ClassInfo,
    reason: string
  ): Promise<void> {
    const template = await this.getEmailTemplate('class_cancellation');
    const content = this.renderTemplate(template, {
      class_title: classInfo.title,
      occurrence_date: classInfo.date,
      cancellation_reason: reason,
      substitute_info: classInfo.substitute
    });
    
    await this.sendEmail({
      to: memberEmail,
      subject: `[ShallWe] ${classInfo.title} 수업 변경 안내`,
      html: content
    });
  }
}

// src/lib/notifications/push.service.ts
export class PushNotificationService {
  async sendImmediateNotification(
    deviceTokens: string[],
    notification: PushNotification
  ): Promise<void> {
    await this.fcm.sendMulticast({
      tokens: deviceTokens,
      notification: {
        title: notification.title,
        body: notification.message
      },
      data: {
        type: notification.type,
        related_id: notification.related_id,
        action_url: notification.action_url
      }
    });
  }
}
```

**Day 5: 알림 설정 및 프리퍼런스**
```typescript
// src/components/settings/NotificationSettings.tsx
export function NotificationSettings() {
  const [preferences, setPreferences] = useState<NotificationPreferences>();
  
  return (
    <div className="notification-settings">
      <SettingToggle
        label="수업 변경 알림"
        description="수업 취소, 시간 변경 시 알림을 받습니다"
        checked={preferences?.class_changes}
        onChange={(enabled) => updatePreference('class_changes', enabled)}
      />
      
      <SettingToggle
        label="수업 리마인더"
        description="수업 2시간 전 리마인더 알림을 받습니다"
        checked={preferences?.class_reminders}
        onChange={(enabled) => updatePreference('class_reminders', enabled)}
      />
      
      <TimePicker
        label="알림 시간 설정"
        value={preferences?.reminder_time}
        onChange={(time) => updatePreference('reminder_time', time)}
      />
    </div>
  );
}
```

#### 3-2. 분석 리포팅 (6일)

**Day 1-3: 분석 데이터 수집**
```typescript
// src/services/analytics.service.ts
export class AnalyticsService {
  async getInstructorAnalytics(
    instructorId: string,
    period: AnalyticsPeriod
  ): Promise<InstructorAnalytics> {
    const [
      occurrenceMetrics,
      enrollmentMetrics,
      attendanceMetrics,
      revenueMetrics
    ] = await Promise.all([
      this.getOccurrenceMetrics(instructorId, period),
      this.getEnrollmentMetrics(instructorId, period),
      this.getAttendanceMetrics(instructorId, period),
      this.getRevenueMetrics(instructorId, period)
    ]);
    
    return {
      period,
      metrics: {
        occurrence_metrics: occurrenceMetrics,
        enrollment_metrics: enrollmentMetrics,
        attendance_metrics: attendanceMetrics,
        revenue_metrics: revenueMetrics
      },
      time_analysis: await this.getTimeAnalysis(instructorId, period),
      class_performance: await this.getClassPerformance(instructorId, period),
      insights: await this.generateInsights(instructorId, period)
    };
  }
  
  private async getOccurrenceMetrics(
    instructorId: string,
    period: AnalyticsPeriod
  ): Promise<OccurrenceMetrics> {
    const occurrences = await this.db
      .select({
        total: count(),
        completed: countIf(eq(classOccurrences.status, 'completed')),
        cancelled: countIf(eq(classOccurrences.status, 'cancelled')),
        avgOccupancy: avg(
          sql`${classOccurrences.confirmedEnrollments}::float / ${classOccurrences.maxParticipants}`
        )
      })
      .from(classOccurrences)
      .innerJoin(classTemplates, eq(classOccurrences.classTemplateId, classTemplates.id))
      .where(
        and(
          eq(classTemplates.instructorId, instructorId),
          between(classOccurrences.occurrenceDate, period.start, period.end)
        )
      );
    
    return {
      total_occurrences: occurrences[0].total,
      completed_occurrences: occurrences[0].completed,
      cancelled_occurrences: occurrences[0].cancelled,
      completion_rate: occurrences[0].completed / occurrences[0].total,
      cancellation_rate: occurrences[0].cancelled / occurrences[0].total,
      average_occupancy_rate: occurrences[0].avgOccupancy
    };
  }
}
```

**Day 4-5: 리포팅 UI 개발**
```typescript
// src/components/analytics/AnalyticsDashboard.tsx
export function AnalyticsDashboard() {
  const [period, setPeriod] = useState<AnalyticsPeriod>('month');
  const { data: analytics } = useInstructorAnalytics(period);
  
  return (
    <div className="analytics-dashboard">
      <PeriodSelector value={period} onChange={setPeriod} />
      
      <div className="metrics-grid">
        <MetricCard
          title="수업 완료율"
          value={analytics?.metrics.occurrence_metrics.completion_rate}
          format="percentage"
          trend={analytics?.metrics.occurrence_metrics.change_vs_previous}
        />
        
        <MetricCard
          title="평균 정원률"
          value={analytics?.metrics.occurrence_metrics.average_occupancy_rate}
          format="percentage"
        />
        
        <MetricCard
          title="출석률"
          value={analytics?.metrics.attendance_metrics.average_attendance_rate}
          format="percentage"
        />
        
        <MetricCard
          title="월 수익"
          value={analytics?.metrics.revenue_metrics.total_revenue}
          format="currency"
        />
      </div>
      
      <div className="charts-grid">
        <TimeAnalysisChart data={analytics?.time_analysis} />
        <ClassPerformanceChart data={analytics?.class_performance} />
        <RevenueTradeChart data={analytics?.metrics.revenue_metrics.monthly_trend} />
      </div>
      
      <InsightsPanel insights={analytics?.insights} />
    </div>
  );
}
```

**Day 6: 리포트 생성 및 내보내기**
```typescript
// src/services/report-generator.service.ts
export class ReportGeneratorService {
  async generateMonthlyReport(
    instructorId: string,
    month: string
  ): Promise<MonthlyReport> {
    const analytics = await this.analyticsService.getInstructorAnalytics(
      instructorId,
      { type: 'month', value: month }
    );
    
    return {
      period: month,
      summary: this.createSummary(analytics),
      class_performance: analytics.class_performance,
      recommendations: await this.generateRecommendations(analytics),
      generated_at: new Date().toISOString()
    };
  }
  
  async exportToPDF(report: MonthlyReport): Promise<Buffer> {
    const pdf = new PDFGenerator();
    
    pdf.addHeader(`${report.period} 월간 리포트`);
    pdf.addSummarySection(report.summary);
    pdf.addPerformanceChart(report.class_performance);
    pdf.addRecommendations(report.recommendations);
    
    return pdf.generate();
  }
  
  async exportToExcel(analytics: InstructorAnalytics): Promise<Buffer> {
    const workbook = new ExcelGenerator();
    
    workbook.addSheet('요약', analytics.metrics);
    workbook.addSheet('클래스별 성과', analytics.class_performance);
    workbook.addSheet('시간대별 분석', analytics.time_analysis);
    
    return workbook.generate();
  }
}
```

#### 3-3. 성능 최적화 (4일)

**Day 1: 데이터베이스 최적화**
```sql
-- 자주 사용되는 쿼리 최적화
-- 1. 다가오는 수업 조회 최적화
CREATE INDEX idx_upcoming_occurrences ON class_occurrences (
  class_template_id, occurrence_date, status
) WHERE status IN ('scheduled', 'ongoing') AND occurrence_date >= CURRENT_DATE;

-- 2. 검색 쿼리 최적화
CREATE INDEX idx_class_search_composite ON class_templates (
  category, level, status, recruitment_start_date
) WHERE is_active = true;

-- 3. 분석 쿼리 최적화
CREATE INDEX idx_analytics_period ON class_occurrences (
  occurrence_date, status, class_template_id
) INCLUDE (confirmed_enrollments, max_participants, attendance_count);
```

**Day 2: 캐싱 전략 구현**
```typescript
// src/lib/cache/redis-cache.service.ts
export class RedisCacheService {
  private readonly DEFAULT_TTL = 300; // 5분
  
  async getDashboardData(instructorId: string): Promise<DashboardData | null> {
    const key = `dashboard:${instructorId}:${this.getCurrentDateKey()}`;
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }
  
  async setDashboardData(instructorId: string, data: DashboardData): Promise<void> {
    const key = `dashboard:${instructorId}:${this.getCurrentDateKey()}`;
    await this.redis.setex(key, this.DEFAULT_TTL, JSON.stringify(data));
  }
  
  async invalidateInstructorCache(instructorId: string): Promise<void> {
    const pattern = `*:${instructorId}:*`;
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}

// src/services/cached-analytics.service.ts
export class CachedAnalyticsService extends AnalyticsService {
  constructor(
    private cacheService: RedisCacheService,
    private baseAnalyticsService: AnalyticsService
  ) {
    super();
  }
  
  async getInstructorAnalytics(
    instructorId: string,
    period: AnalyticsPeriod
  ): Promise<InstructorAnalytics> {
    const cacheKey = `analytics:${instructorId}:${period.type}:${period.value}`;
    
    // 캐시 확인
    let analytics = await this.cacheService.get<InstructorAnalytics>(cacheKey);
    
    if (!analytics) {
      // 캐시 미스: 실제 계산
      analytics = await this.baseAnalyticsService.getInstructorAnalytics(instructorId, period);
      
      // 캐시 저장 (기간에 따라 TTL 조정)
      const ttl = period.type === 'day' ? 300 : period.type === 'week' ? 1800 : 3600;
      await this.cacheService.set(cacheKey, analytics, ttl);
    }
    
    return analytics;
  }
}
```

**Day 3: 프론트엔드 최적화**
```typescript
// src/hooks/useOptimizedSearch.ts
export function useOptimizedSearch() {
  const [searchResults, setSearchResults] = useState<SearchResults>();
  const [isLoading, setIsLoading] = useState(false);
  
  // 검색어 디바운싱
  const debouncedSearch = useCallback(
    debounce(async (filters: SearchFilters) => {
      setIsLoading(true);
      try {
        const results = await searchClasses(filters);
        setSearchResults(results);
      } finally {
        setIsLoading(false);
      }
    }, 300),
    []
  );
  
  // 무한 스크롤
  const loadMore = useCallback(async () => {
    if (!searchResults || isLoading) return;
    
    const nextPage = await searchClasses({
      ...searchResults.filters,
      offset: searchResults.pagination.offset + searchResults.pagination.limit
    });
    
    setSearchResults(prev => ({
      ...nextPage,
      classes: [...(prev?.classes || []), ...nextPage.classes]
    }));
  }, [searchResults, isLoading]);
  
  return {
    searchResults,
    isLoading,
    search: debouncedSearch,
    loadMore
  };
}

// src/components/VirtualizedOccurrenceList.tsx
export function VirtualizedOccurrenceList({ occurrences }: { occurrences: Occurrence[] }) {
  const rowRenderer = useCallback(({ index, key, style }: ListRowProps) => {
    const occurrence = occurrences[index];
    
    return (
      <div key={key} style={style}>
        <OccurrenceCard occurrence={occurrence} />
      </div>
    );
  }, [occurrences]);
  
  return (
    <AutoSizer>
      {({ height, width }) => (
        <List
          height={height}
          width={width}
          rowCount={occurrences.length}
          rowHeight={120}
          rowRenderer={rowRenderer}
          overscanRowCount={5}
        />
      )}
    </AutoSizer>
  );
}
```

**Day 4: 모니터링 및 메트릭 수집**
```typescript
// src/lib/monitoring/performance-monitor.ts
export class PerformanceMonitor {
  async trackAPICall(
    endpoint: string,
    method: string,
    duration: number,
    statusCode: number
  ): Promise<void> {
    await this.metrics.increment('api_calls_total', {
      endpoint,
      method,
      status: statusCode.toString()
    });
    
    await this.metrics.histogram('api_duration_ms', duration, {
      endpoint,
      method
    });
    
    if (duration > 2000) {
      await this.alerts.sendSlowQueryAlert(endpoint, duration);
    }
  }
  
  async trackSearchPerformance(
    query: SearchFilters,
    resultCount: number,
    duration: number
  ): Promise<void> {
    await this.metrics.histogram('search_duration_ms', duration, {
      has_text_search: !!query.search,
      filter_count: Object.keys(query).length.toString()
    });
    
    await this.metrics.histogram('search_result_count', resultCount);
  }
}
```

#### 3-4. E2E 테스트 (5일)

**Day 1-2: 주요 사용자 플로우 테스트**
```typescript
// e2e/instructor-workflow.spec.ts
test.describe('Instructor Workflow', () => {
  test('complete class management workflow', async ({ page }) => {
    await page.goto('/instructor/login');
    await loginAsInstructor(page);
    
    // 1. 클래스 생성
    await page.goto('/instructor/classes/create');
    await createClass(page, {
      title: '테스트 요가 클래스',
      category: 'yoga',
      recruitment_period: { start: '2024-12-01', end: '2024-12-15' },
      class_period: { start: '2024-12-16', end: '2025-01-16' },
      schedule_groups: [
        {
          name: '오후반',
          schedules: [
            { day: 'MONDAY', time: '14:00-15:00' },
            { day: 'WEDNESDAY', time: '14:00-15:00' }
          ]
        }
      ]
    });
    
    // 2. Occurrence 확인
    await page.goto('/instructor/classes');
    await page.click('[data-testid=class-item]:first-child');
    await expect(page.locator('[data-testid=occurrence-list]')).toBeVisible();
    await expect(page.locator('[data-testid=occurrence-item]')).toHaveCount(8);
    
    // 3. 개별 수업 수정
    await page.click('[data-testid=occurrence-item]:first-child');
    await page.click('[data-testid=edit-occurrence]');
    await page.fill('[data-testid=start-time]', '15:00');
    await page.click('[data-testid=save-occurrence]');
    await expect(page.locator('[data-testid=occurrence-time]')).toContainText('15:00');
    
    // 4. 수업 취소 및 보강
    await page.click('[data-testid=cancel-occurrence]');
    await page.fill('[data-testid=cancellation-reason]', '개인사정');
    await page.check('[data-testid=create-substitute]');
    await page.fill('[data-testid=substitute-date]', '2024-12-20');
    await page.click('[data-testid=confirm-cancellation]');
    
    await expect(page.locator('[data-testid=occurrence-status]')).toContainText('취소');
    await expect(page.locator('[data-testid=substitute-occurrence]')).toBeVisible();
  });
});

// e2e/student-search.spec.ts
test.describe('Student Search', () => {
  test('search and enroll in class', async ({ page }) => {
    await page.goto('/classes/search');
    
    // 1. 필터 적용
    await page.selectOption('[data-testid=category-filter]', 'yoga');
    await page.check('[data-testid=day-filter-monday]');
    await page.check('[data-testid=day-filter-wednesday]');
    await page.selectOption('[data-testid=time-filter]', 'afternoon');
    
    // 2. 검색 결과 확인
    await page.waitForSelector('[data-testid=search-results]');
    await expect(page.locator('[data-testid=class-card]')).toHaveCountGreaterThan(0);
    
    // 3. 클래스 상세 보기
    await page.click('[data-testid=class-card]:first-child');
    await expect(page.locator('[data-testid=class-details]')).toBeVisible();
    await expect(page.locator('[data-testid=schedule-group]')).toHaveCount(1);
    
    // 4. 신청하기
    await page.click('[data-testid=enroll-button]');
    await page.fill('[data-testid=card-number]', '****************');
    await page.fill('[data-testid=expiry]', '12/25');
    await page.fill('[data-testid=cvc]', '123');
    await page.click('[data-testid=pay-button]');
    
    await expect(page.locator('[data-testid=enrollment-success]')).toBeVisible();
  });
});
```

**Day 3-4: 성능 및 안정성 테스트**
```typescript
// e2e/performance.spec.ts
test.describe('Performance Tests', () => {
  test('dashboard loads within acceptable time', async ({ page }) => {
    await loginAsInstructor(page);
    
    const startTime = Date.now();
    await page.goto('/instructor/dashboard');
    await page.waitForSelector('[data-testid=dashboard-loaded]');
    const loadTime = Date.now() - startTime;
    
    expect(loadTime).toBeLessThan(3000); // 3초 이내
  });
  
  test('search handles large result sets', async ({ page }) => {
    await page.goto('/classes/search');
    
    // 광범위한 검색 (많은 결과)
    await page.selectOption('[data-testid=category-filter]', 'all');
    
    const startTime = Date.now();
    await page.waitForSelector('[data-testid=search-results]');
    const searchTime = Date.now() - startTime;
    
    expect(searchTime).toBeLessThan(2000); // 2초 이내
    
    // 무한 스크롤 테스트
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await expect(page.locator('[data-testid=class-card]')).toHaveCountGreaterThan(20);
  });
});

// e2e/error-scenarios.spec.ts
test.describe('Error Scenarios', () => {
  test('handles network failures gracefully', async ({ page }) => {
    await page.route('**/api/instructor/dashboard/v2', route => route.abort());
    
    await page.goto('/instructor/dashboard');
    await expect(page.locator('[data-testid=error-message]')).toBeVisible();
    await expect(page.locator('[data-testid=retry-button]')).toBeVisible();
    
    // 네트워크 복구 시뮬레이션
    await page.unroute('**/api/instructor/dashboard/v2');
    await page.click('[data-testid=retry-button]');
    await expect(page.locator('[data-testid=dashboard-loaded]')).toBeVisible();
  });
});
```

**Day 5: 크로스 브라우저 및 모바일 테스트**
```typescript
// playwright.config.ts
export default defineConfig({
  projects: [
    {
      name: 'Desktop Chrome',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'Desktop Firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'Desktop Safari',
      use: { ...devices['Desktop Safari'] }
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] }
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] }
    }
  ]
});

// e2e/responsive.spec.ts
test.describe('Responsive Design', () => {
  ['Desktop Chrome', 'Mobile Chrome', 'iPhone 12'].forEach(browserName => {
    test(`works on ${browserName}`, async ({ page }) => {
      await page.goto('/instructor/dashboard');
      
      // 모바일에서는 사이드바가 숨겨져야 함
      if (browserName.includes('Mobile') || browserName.includes('iPhone')) {
        await expect(page.locator('[data-testid=sidebar]')).toBeHidden();
        await expect(page.locator('[data-testid=mobile-menu-button]')).toBeVisible();
      } else {
        await expect(page.locator('[data-testid=sidebar]')).toBeVisible();
      }
      
      // 핵심 기능은 모든 디바이스에서 동작해야 함
      await expect(page.locator('[data-testid=upcoming-occurrences]')).toBeVisible();
      await expect(page.locator('[data-testid=stats-cards]')).toBeVisible();
    });
  });
});
```

## Phase 4: 배포 및 안정화 (2주)

### 목표
- 스테이징 환경 배포 및 QA
- 프로덕션 배포
- 모니터링 및 최적화

### 상세 작업

#### 4-1. 스테이징 배포 (2일)

**Day 1: 배포 환경 준비**
```bash
# 배포 스크립트
#!/bin/bash
# deploy-staging.sh

echo "Starting staging deployment..."

# 1. 환경 변수 설정
export NODE_ENV=staging
export DATABASE_URL=$STAGING_DATABASE_URL
export REDIS_URL=$STAGING_REDIS_URL

# 2. 빌드
npm run build

# 3. 데이터베이스 마이그레이션
npm run db:migrate

# 4. 테스트 데이터 시딩
npm run db:seed:staging

# 5. 애플리케이션 배포
docker build -t shallwe-staging .
docker stop shallwe-staging || true
docker run -d --name shallwe-staging \
  -p 3000:3000 \
  -e NODE_ENV=staging \
  -e DATABASE_URL=$STAGING_DATABASE_URL \
  shallwe-staging

echo "Staging deployment completed!"
```

**Day 2: 헬스체크 및 스모크 테스트**
```typescript
// scripts/health-check.ts
async function runHealthCheck() {
  const baseUrl = process.env.STAGING_URL || 'https://staging.shallwe.com';
  
  const checks = [
    { name: 'API Health', url: `${baseUrl}/api/health` },
    { name: 'Database', url: `${baseUrl}/api/health/db` },
    { name: 'Redis', url: `${baseUrl}/api/health/redis` },
    { name: 'Dashboard', url: `${baseUrl}/api/instructor/dashboard/v2` }
  ];
  
  for (const check of checks) {
    try {
      const response = await fetch(check.url);
      if (response.ok) {
        console.log(`✅ ${check.name}: OK`);
      } else {
        console.log(`❌ ${check.name}: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${check.name}: ${error.message}`);
    }
  }
}

// 스모크 테스트
async function runSmokeTests() {
  const tests = [
    () => testClassCreation(),
    () => testOccurrenceGeneration(),
    () => testSearch(),
    () => testDashboard()
  ];
  
  for (const test of tests) {
    try {
      await test();
      console.log(`✅ Test passed`);
    } catch (error) {
      console.log(`❌ Test failed: ${error.message}`);
      throw error;
    }
  }
}
```

#### 4-2. QA 테스트 (5일)

**Day 1-2: 기능 테스트**
```markdown
# QA 테스트 체크리스트

## 클래스 라이프사이클 관리
- [ ] 클래스 생성 시 기간 정보 설정 가능
- [ ] Occurrence 자동 생성 확인
- [ ] 개별 Occurrence 수정 가능
- [ ] 수업 취소 및 보강 기능
- [ ] 상태 전환 정확성

## Dashboard v2
- [ ] 실제 다가오는 수업 표시
- [ ] 통계 정보 정확성
- [ ] 최근 신청 내역 표시
- [ ] 액션 아이템 기능

## 검색 기능
- [ ] 카테고리별 검색
- [ ] 시간대별 필터링
- [ ] 요일별 필터링
- [ ] 위치 기반 검색
- [ ] 검색 성능 (2초 이내)

## 출석 체크
- [ ] 개별 출석 체크
- [ ] 일괄 출석 처리
- [ ] 출석 통계 정확성
- [ ] 출석 기록 수정

## 알림 시스템
- [ ] 수업 변경 알림
- [ ] 수업 리마인더
- [ ] 이메일 발송
- [ ] 푸시 알림 (모바일)
```

**Day 3-4: 성능 및 안정성 테스트**
```typescript
// scripts/load-test.ts
async function runLoadTest() {
  const scenarios = [
    {
      name: 'Dashboard 로드 테스트',
      endpoint: '/api/instructor/dashboard/v2',
      concurrent_users: 100,
      duration: 300 // 5분
    },
    {
      name: '검색 로드 테스트',
      endpoint: '/api/classes/search',
      concurrent_users: 200,
      duration: 300
    },
    {
      name: 'Occurrence 조회 테스트',
      endpoint: '/api/instructor/occurrences',
      concurrent_users: 50,
      duration: 180
    }
  ];
  
  for (const scenario of scenarios) {
    console.log(`Running ${scenario.name}...`);
    
    const result = await artillery.run({
      target: process.env.STAGING_URL,
      phases: [
        {
          duration: scenario.duration,
          arrivalRate: scenario.concurrent_users
        }
      ],
      scenarios: [
        {
          name: scenario.name,
          flow: [
            {
              get: {
                url: scenario.endpoint,
                headers: {
                  Authorization: 'Bearer {{token}}'
                }
              }
            }
          ]
        }
      ]
    });
    
    console.log(`${scenario.name} completed:`, result.summary);
  }
}
```

**Day 5: 보안 테스트**
```bash
# 보안 테스트 스크립트
#!/bin/bash

echo "Running security tests..."

# 1. SQL Injection 테스트
sqlmap -u "https://staging.shallwe.com/api/classes/search?category=yoga" \
  --cookie="session_token=test" \
  --level=3 \
  --risk=2

# 2. XSS 테스트
python xss-scanner.py https://staging.shallwe.com

# 3. 취약점 스캔
nmap -sV -sC staging.shallwe.com

# 4. SSL/TLS 검사
sslyze staging.shallwe.com

echo "Security tests completed!"
```

#### 4-3. 프로덕션 배포 (3일)

**Day 1: 블루-그린 배포 준비**
```bash
# blue-green-deploy.sh
#!/bin/bash

CURRENT_ENV=$(curl -s https://api.shallwe.com/health | jq -r '.environment')
NEW_ENV=$([[ $CURRENT_ENV == "blue" ]] && echo "green" || echo "blue")

echo "Current environment: $CURRENT_ENV"
echo "Deploying to: $NEW_ENV"

# 1. 새 환경에 배포
docker build -t shallwe-$NEW_ENV .
docker stop shallwe-$NEW_ENV || true
docker run -d --name shallwe-$NEW_ENV \
  -p $([ $NEW_ENV == "blue" ] && echo "3001" || echo "3002"):3000 \
  -e NODE_ENV=production \
  -e DATABASE_URL=$PRODUCTION_DATABASE_URL \
  shallwe-$NEW_ENV

# 2. 헬스체크
sleep 30
if ! curl -f http://localhost:$([ $NEW_ENV == "blue" ] && echo "3001" || echo "3002")/api/health; then
  echo "Health check failed!"
  exit 1
fi

# 3. 로드밸런서 전환
update-load-balancer.sh $NEW_ENV

# 4. 기존 환경 중단 (5분 후)
sleep 300
docker stop shallwe-$CURRENT_ENV
```

**Day 2: 프로덕션 배포 실행**
```yaml
# .github/workflows/production-deploy.yml
name: Production Deployment

on:
  push:
    tags:
      - 'v*'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Build application
        run: npm run build
        
      - name: Deploy to production
        run: |
          ssh production-server './blue-green-deploy.sh'
          
      - name: Run post-deployment tests
        run: npm run test:production
        
      - name: Send notification
        if: always()
        uses: slack-notification@v1
        with:
          message: "Production deployment ${{ job.status }}"
```

**Day 3: 배포 검증 및 롤백 준비**
```typescript
// scripts/post-deployment-check.ts
async function runPostDeploymentCheck() {
  const checks = [
    {
      name: '기본 기능 확인',
      test: async () => {
        // 클래스 생성/조회 테스트
        const response = await fetch('/api/instructor/classes');
        return response.ok;
      }
    },
    {
      name: 'Dashboard 기능 확인',
      test: async () => {
        const response = await fetch('/api/instructor/dashboard/v2');
        const data = await response.json();
        return data.data.stats.total_classes !== undefined;
      }
    },
    {
      name: '검색 기능 확인',
      test: async () => {
        const response = await fetch('/api/classes/search?category=yoga');
        const data = await response.json();
        return Array.isArray(data.data.classes);
      }
    },
    {
      name: '성능 확인',
      test: async () => {
        const start = Date.now();
        await fetch('/api/instructor/dashboard/v2');
        const duration = Date.now() - start;
        return duration < 2000; // 2초 이내
      }
    }
  ];
  
  const results = await Promise.all(
    checks.map(async check => {
      try {
        const passed = await check.test();
        return { name: check.name, passed };
      } catch (error) {
        return { name: check.name, passed: false, error: error.message };
      }
    })
  );
  
  const failedChecks = results.filter(r => !r.passed);
  
  if (failedChecks.length > 0) {
    console.error('Post-deployment checks failed:', failedChecks);
    throw new Error('Deployment verification failed');
  }
  
  console.log('✅ All post-deployment checks passed');
}
```

#### 4-4. 모니터링 및 최적화 (7일)

**Day 1-3: 모니터링 시스템 구축**
```typescript
// src/lib/monitoring/metrics.ts
export class MetricsCollector {
  private prometheus = new PrometheusRegistry();
  
  // 카운터 메트릭
  private apiCalls = new Counter({
    name: 'api_calls_total',
    help: 'Total number of API calls',
    labelNames: ['endpoint', 'method', 'status'],
    registers: [this.prometheus]
  });
  
  // 히스토그램 메트릭
  private apiDuration = new Histogram({
    name: 'api_duration_seconds',
    help: 'API call duration',
    labelNames: ['endpoint', 'method'],
    buckets: [0.1, 0.5, 1, 2, 5],
    registers: [this.prometheus]
  });
  
  // 게이지 메트릭
  private activeUsers = new Gauge({
    name: 'active_users',
    help: 'Number of active users',
    registers: [this.prometheus]
  });
  
  trackApiCall(endpoint: string, method: string, duration: number, status: number) {
    this.apiCalls.inc({ endpoint, method, status: status.toString() });
    this.apiDuration.observe({ endpoint, method }, duration / 1000);
  }
  
  updateActiveUsers(count: number) {
    this.activeUsers.set(count);
  }
  
  getMetrics() {
    return this.prometheus.metrics();
  }
}

// src/middleware/monitoring.ts
export function monitoringMiddleware() {
  return async (req: Request, res: Response, next: NextFunction) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      
      metricsCollector.trackApiCall(
        req.route?.path || req.path,
        req.method,
        duration,
        res.statusCode
      );
      
      // 느린 쿼리 알림
      if (duration > 2000) {
        logger.warn('Slow API call detected', {
          endpoint: req.path,
          duration,
          method: req.method
        });
      }
    });
    
    next();
  };
}
```

**Day 4-5: 알림 시스템 구축**
```typescript
// src/lib/alerting/alert-manager.ts
export class AlertManager {
  private alerts = {
    highErrorRate: {
      condition: (metrics: Metrics) => metrics.errorRate > 0.05,
      message: 'Error rate exceeded 5%',
      severity: 'critical'
    },
    slowResponse: {
      condition: (metrics: Metrics) => metrics.avgResponseTime > 2000,
      message: 'Average response time exceeded 2 seconds',
      severity: 'warning'
    },
    highMemoryUsage: {
      condition: (metrics: Metrics) => metrics.memoryUsage > 0.8,
      message: 'Memory usage exceeded 80%',
      severity: 'warning'
    },
    databaseConnectionFailure: {
      condition: (metrics: Metrics) => metrics.dbConnectionErrors > 0,
      message: 'Database connection failures detected',
      severity: 'critical'
    }
  };
  
  async checkAlerts() {
    const metrics = await this.collectMetrics();
    
    for (const [alertName, alert] of Object.entries(this.alerts)) {
      if (alert.condition(metrics)) {
        await this.sendAlert(alertName, alert);
      }
    }
  }
  
  private async sendAlert(alertName: string, alert: Alert) {
    // Slack 알림
    await this.slack.send({
      channel: '#alerts',
      text: `🚨 ${alert.severity.toUpperCase()}: ${alert.message}`,
      attachments: [{
        color: alert.severity === 'critical' ? 'danger' : 'warning',
        fields: [
          { title: 'Alert', value: alertName, short: true },
          { title: 'Time', value: new Date().toISOString(), short: true }
        ]
      }]
    });
    
    // 이메일 알림 (critical인 경우)
    if (alert.severity === 'critical') {
      await this.email.send({
        to: process.env.ALERT_EMAIL,
        subject: `[CRITICAL] ${alertName}`,
        text: alert.message
      });
    }
  }
}
```

**Day 6-7: 성능 최적화 및 튜닝**
```typescript
// scripts/performance-optimization.ts
async function optimizeDatabase() {
  // 1. 슬로우 쿼리 분석
  const slowQueries = await analyzeSlowQueries();
  console.log('Slow queries found:', slowQueries);
  
  // 2. 인덱스 최적화
  for (const query of slowQueries) {
    const suggestedIndex = await suggestIndex(query);
    if (suggestedIndex) {
      console.log(`Suggested index for query: ${suggestedIndex}`);
    }
  }
  
  // 3. 쿼리 계획 분석
  const queryPlans = await analyzeQueryPlans();
  const inefficientPlans = queryPlans.filter(plan => plan.cost > 1000);
  
  console.log('Inefficient query plans:', inefficientPlans);
}

async function optimizeCaching() {
  // 1. 캐시 히트율 분석
  const cacheStats = await redis.info('stats');
  const hitRate = parseFloat(cacheStats.keyspace_hits) / 
    (parseFloat(cacheStats.keyspace_hits) + parseFloat(cacheStats.keyspace_misses));
  
  console.log(`Cache hit rate: ${(hitRate * 100).toFixed(2)}%`);
  
  // 2. 캐시 키 TTL 최적화
  if (hitRate < 0.8) {
    console.log('Cache hit rate is low, consider adjusting TTL values');
    
    // 자주 접근되는 키의 TTL 증가
    const frequentKeys = await getFrequentKeys();
    for (const key of frequentKeys) {
      await redis.expire(key, 3600); // 1시간
    }
  }
}

async function monitorResourceUsage() {
  const usage = {
    cpu: await getCPUUsage(),
    memory: await getMemoryUsage(),
    disk: await getDiskUsage(),
    network: await getNetworkUsage()
  };
  
  console.log('Resource usage:', usage);
  
  // 리소스 사용량이 높으면 알림
  if (usage.cpu > 80 || usage.memory > 80) {
    await sendResourceAlert(usage);
  }
}
```

## 마일스톤 및 체크포인트

### 주요 마일스톤

1. **M1: 기반 구축 완료** (Week 2)
   - 스키마 확장 완료
   - 기본 API 구현
   - Dashboard v2 개발

2. **M2: 핵심 기능 완료** (Week 5)
   - Occurrence 관리 기능
   - 출석 체크 시스템
   - 검색 기능 구현

3. **M3: 고급 기능 완료** (Week 8)
   - 알림 시스템 구현
   - 분석 리포팅 기능
   - 성능 최적화

4. **M4: 프로덕션 배포** (Week 10)
   - 스테이징 QA 완료
   - 프로덕션 배포
   - 모니터링 시스템 구축

### 체크포인트 기준

각 Phase 완료 시 다음 기준을 만족해야 다음 단계로 진행:

- **기능 완성도**: 계획된 기능의 100% 구현
- **테스트 커버리지**: 코드 커버리지 90% 이상
- **성능 기준**: API 응답 시간 2초 이내
- **안정성**: 에러율 1% 미만
- **호환성**: 기존 기능 100% 정상 동작

### 위험 관리

**기술적 위험:**
- 데이터 마이그레이션 실패 → 충분한 백업 및 롤백 계획
- 성능 저하 → 단계별 성능 테스트 및 최적화
- 호환성 문제 → 철저한 하위 호환성 테스트

**일정 위험:**
- 개발 지연 → 핵심 기능 우선 구현, 부가 기능 후순위
- 리소스 부족 → 외부 개발자 투입 검토
- 테스트 시간 부족 → 자동화 테스트 확대

이 구현 계획을 통해 체계적이고 안전한 클래스 라이프사이클 시스템 구축을 목표로 합니다.