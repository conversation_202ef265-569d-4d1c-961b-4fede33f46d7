# 클래스 라이프사이클 API 명세서

## API 설계 원칙

- **RESTful 구조**: 표준 HTTP 메서드와 상태 코드 사용
- **일관성**: 동일한 네이밍 컨벤션과 응답 구조 유지
- **확장성**: 버전 관리와 하위 호환성 고려
- **성능**: 페이지네이션, 캐싱, 인덱스 최적화 지원
- **보안**: 인증/인가, 입력 검증, 에러 정보 노출 최소화

## API 구조 다이어그램

```mermaid
graph TB
    subgraph "Client Layer"
        A[강사 Dashboard]
        B[수강생 Mobile App]
        C[Admin Panel]
    end
    
    subgraph "API Gateway"
        D[Load Balancer]
        E[Authentication]
        F[Rate Limiting]
        G[Request Validation]
    end
    
    subgraph "API Endpoints"
        H[Class Templates API]
        I[Schedule Groups API]
        J[Schedules API]
        K[Occurrences API]
        L[Enrollments API]
        M[Attendances API]
        N[Search API]
        O[Dashboard API]
    end
    
    subgraph "Business Logic"
        P[Class Lifecycle Service]
        Q[Occurrence Generator]
        R[Enrollment Service]
        S[Attendance Service]
        T[Notification Service]
    end
    
    subgraph "Data Layer"
        U[(PostgreSQL)]
        V[(Redis Cache)]
        W[File Storage]
    end
    
    A --> D
    B --> D
    C --> D
    
    D --> E --> F --> G
    
    G --> H
    G --> I
    G --> J
    G --> K
    G --> L
    G --> M
    G --> N
    G --> O
    
    H --> P --> U
    K --> Q --> U
    L --> R --> U
    M --> S --> U
    P --> T
    
    H --> V
    N --> V
    O --> V
```

## 공통 타입 정의

### 기본 타입

```typescript
// 상태 타입들
type ClassStatus = 'upcoming' | 'recruiting' | 'ongoing' | 'completed' | 'cancelled';
type OccurrenceStatus = 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
type EnrollmentStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'refunded';
type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused';

// 메타데이터 타입
interface Pagination {
  total: number;
  limit: number;
  offset: number;
  has_next: boolean;
  has_previous: boolean;
}

interface ApiResponse<T> {
  data: T;
  message?: string;
  pagination?: Pagination;
}

interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  path: string;
}
```

### 엔티티 타입

```typescript
interface ClassTemplate {
  id: string;
  title: string;
  description?: string;
  specialty: string;              // 운동 분야 (category 필드 제거)
  level: string;
  instructor_id: string;
  studio_id: string;
  duration_minutes: number;
  price_per_session: number;
  max_capacity: number;
  curriculum?: object;               // 구조화된 클래스 소개
  
  // 라이프사이클
  recruitment_start_date: string;
  recruitment_end_date: string;
  class_start_date: string;
  class_end_date: string;
  status: ClassStatus;
  
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ScheduleGroup {
  id: string;
  class_template_id: string;
  group_name: string;
  group_description?: string;
  max_participants: number;          // 그룹 최대 인원
  price_per_session?: number;
  sessions_per_week: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface Schedule {
  id: string;
  schedule_group_id: string;
  day_of_week: string;
  start_time: string;
  end_time: string;
  max_participants?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface Occurrence {
  id: string;
  class_template_id: string;
  schedule_group_id: string;
  class_schedule_id: string;
  occurrence_date: string;
  start_time: string;
  end_time: string;
  max_participants?: number;         // NULL 가능 (그룹 설정 상속)
  status: OccurrenceStatus;
  attendance_count: number;
  confirmed_enrollments: number;
  instructor_notes?: string;
  cancellation_reason?: string;
  is_substitute_class: boolean;
  original_occurrence_id?: string;
  created_at: string;
  updated_at: string;
}
```

## Class Templates API

### GET /api/instructor/classes

강사의 클래스 템플릿 목록을 조회합니다.

```typescript
interface GetClassesRequest {
  query?: {
    status?: ClassStatus[];
    specialty?: string;             // 운동 분야
    level?: string;
    search?: string;                 // 제목으로 검색
    recruitment_period?: {           // 모집 기간 필터
      from?: string;                 // ISO date
      to?: string;
    };
    class_period?: {                 // 수업 기간 필터  
      from?: string;
      to?: string;
    };
    limit?: number;                  // default: 20, max: 100
    offset?: number;                 // default: 0
    sort?: 'created_at' | 'title' | 'recruitment_start_date' | 'class_start_date';
    order?: 'asc' | 'desc';         // default: desc
  }
}

interface GetClassesResponse {
  data: {
    classes: Array<ClassTemplate & {
      studio: {
        id: string;
        name: string;
        address: string;
      };
      schedule_groups_count: number;
      total_occurrences: number;
      upcoming_occurrences: number;
      total_enrollments: number;
      confirmed_enrollments: number;
      revenue: number;
    }>;
    summary: {
      total_classes: number;
      by_status: Record<ClassStatus, number>;
      this_month_revenue: number;
    };
  };
  pagination: Pagination;
}
```

**Usage Example:**
```bash
GET /api/instructor/classes?status=recruiting,ongoing&limit=10&sort=recruitment_start_date
```

### POST /api/instructor/classes

새로운 클래스 템플릿을 생성합니다.

```typescript
interface CreateClassRequest {
  body: {
    title: string;
    description?: string;
    specialty: string;              // 운동 분야 (category 필드 제거)
    level: string;
    studio_id: string;
    duration_minutes: number;
    price_per_session: number;
    max_capacity: number;
    curriculum?: object;               // 구조화된 클래스 소개
    
    // 라이프사이클 설정
    recruitment_start_date: string;
    recruitment_end_date: string;
    class_start_date: string;
    class_end_date: string;
    
    // 🆕 스케줄 그룹 설정 (MVP 스펙 - 그룹 내 통일된 시간)
    schedule_groups: Array<{
      group_name: string;
      group_description?: string;
      max_participants: number;      // 그룹 최대 인원 (max_group_size → max_participants)
      price_per_session?: number;
      // 🆕 MVP: 하나의 그룹 내 모든 스케줄은 동일한 시간 사용
      schedules: Array<{
        day_of_week: string;        // 요일만 다름 (예: 'MONDAY', 'WEDNESDAY', 'FRIDAY')
        start_time: string;         // 그룹 내 동일한 시간 (예: '19:00:00')
        end_time: string;           // 그룹 내 동일한 시간 (예: '20:00:00')
        max_participants?: number;
      }>;
    }>;
    
    // 옵션
    auto_generate_occurrences?: boolean;  // default: true
  }
}

interface CreateClassResponse {
  data: {
    class_template: ClassTemplate;
    schedule_groups: ScheduleGroup[];
    schedules: Schedule[];
    generated_occurrences?: Occurrence[];
  };
}
```

### GET /api/instructor/classes/{id}

특정 클래스 템플릿의 상세 정보를 조회합니다.

```typescript
interface GetClassDetailResponse {
  data: {
    class_template: ClassTemplate & {
      studio: {
        id: string;
        name: string;
        address: string;
        studio_type: string;
      };
      instructor: {
        id: string;
        name: string;
        short_bio?: string;
      };
    };
    schedule_groups: Array<ScheduleGroup & {
      schedules: Schedule[];
      enrollments_count: number;
      confirmed_enrollments: number;
      revenue: number;
    }>;
    statistics: {
      total_occurrences: number;
      completed_occurrences: number;
      cancelled_occurrences: number;
      upcoming_occurrences: number;
      total_enrollments: number;
      confirmed_enrollments: number;
      total_revenue: number;
      average_attendance_rate: number;
      cancellation_rate: number;
    };
    recent_activity: Array<{
      type: 'enrollment' | 'cancellation' | 'occurrence_change';
      description: string;
      created_at: string;
    }>;
  };
}
```

### PATCH /api/instructor/classes/{id}

클래스 템플릿 정보를 수정합니다.

```typescript
interface UpdateClassRequest {
  params: { id: string };
  body: {
    title?: string;
    description?: string;
    recruitment_start_date?: string;
    recruitment_end_date?: string;
    class_start_date?: string;
    class_end_date?: string;
    status?: ClassStatus;
    max_capacity?: number;
    price_per_session?: number;
    curriculum?: object;               // 구조화된 클래스 소개
  }
}

interface UpdateClassResponse {
  data: {
    class_template: ClassTemplate;
    affected_occurrences?: number;    // 영향받은 occurrence 수
    notifications_sent?: number;      // 발송된 알림 수
  };
}
```

## Schedule Groups (스케줄 그룹) & Schedules (스케줄) API

### GET /api/instructor/classes/{classId}/schedule-groups

클래스의 스케줄 그룹 목록을 조회합니다.

```typescript
interface GetScheduleGroupsResponse {
  data: {
    schedule_groups: Array<ScheduleGroup & {
      schedules: Schedule[];
      enrollments: {
        total: number;
        confirmed: number;
        pending: number;
        available_spots: number;
      };
      next_occurrence?: {
        id: string;
        occurrence_date: string;
        start_time: string;
        status: OccurrenceStatus;
      };
    }>;
  };
}
```

### POST /api/instructor/classes/{classId}/schedule-groups

새로운 스케줄 그룹을 추가합니다.

**🆕 MVP 스펙 (간소화 버전):**
- 하나의 그룹 내 모든 스케줄은 동일한 시간 사용
- 요일만 다르게 선택 가능 (예: 월/수/금 19:00-20:00)
- 그룹명 자동 생성 (오전반1, 오후반1, 저녁반1 등)

```typescript
interface CreateScheduleGroupRequest {
  params: { classId: string };
  body: {
    group_name: string;
    group_description?: string;
    max_participants: number;
    price_per_session?: number;
    // 🆕 MVP: 그룹 내 모든 스케줄은 동일한 시간 사용
    schedules: Array<{
      day_of_week: string;        // 요일만 다름 (예: 'MONDAY', 'WEDNESDAY', 'FRIDAY')
      start_time: string;         // 그룹 내 동일한 시간 (예: '19:00:00')
      end_time: string;           // 그룹 내 동일한 시간 (예: '20:00:00')
      max_participants?: number;
    }>;
    generate_occurrences?: boolean;   // default: true
  }
}
```

### PATCH /api/instructor/schedule-groups/{id}

스케줄 그룹 정보를 수정합니다.

```typescript
interface UpdateScheduleGroupRequest {
  params: { id: string };
  body: {
    group_name?: string;
    group_description?: string;
    max_participants?: number;
    price_per_session?: number;
    update_future_occurrences?: boolean;  // 미래 occurrence도 업데이트할지
  }
}
```

## Occurrences (실제 수업) API

### GET /api/instructor/occurrences

강사의 수업 일정(occurrence) 목록을 조회합니다.

```typescript
interface GetOccurrencesRequest {
  query?: {
    class_template_id?: string;
    schedule_group_id?: string;
    status?: OccurrenceStatus[];
    date_range?: {
      from: string;                  // ISO date, required
      to: string;                    // ISO date, required
    };
    upcoming_only?: boolean;         // default: false
    include_cancelled?: boolean;     // default: true
    limit?: number;                  // default: 50, max: 200
    offset?: number;
    sort?: 'occurrence_date' | 'created_at' | 'confirmed_enrollments';
    order?: 'asc' | 'desc';         // default: asc for date
  }
}

interface GetOccurrencesResponse {
  data: {
    occurrences: Array<Occurrence & {
      class_template: {
        id: string;
        title: string;
        specialty: string;           // 운동 분야
        level: string;
        duration_minutes: number;
      };
      schedule_group: {
        id: string;
        group_name: string;
      };
      studio: {
        id: string;
        name: string;
        address: string;
      };
      enrollment_stats: {
        confirmed: number;
        pending: number;
        available_spots: number;
        waitlist: number;
        occupancy_rate: number;
      };
      can_modify: boolean;           // 수정 가능 여부
      substitute_info?: {            // 보강 수업인 경우
        original_date: string;
        original_time: string;
        reason: string;
      };
    }>;
    summary: {
      total_occurrences: number;
      by_status: Record<OccurrenceStatus, number>;
      by_date: Array<{
        date: string;
        count: number;
      }>;
    };
  };
  pagination: Pagination;
}
```

### POST /api/instructor/occurrences

새로운 수업 일정을 생성합니다. (주로 보강 수업)

```typescript
interface CreateOccurrenceRequest {
  body: {
    class_template_id: string;
    schedule_group_id: string;
    class_schedule_id?: string;      // 기존 스케줄 참조 (옵션)
    
    occurrence_date: string;
    start_time: string;
    end_time: string;
    max_participants?: number;       // 기본값: 그룹 설정값
    
    instructor_notes?: string;
    is_substitute_class?: boolean;   // default: false
    original_occurrence_id?: string; // 보강 수업인 경우 원본 참조
    
    // 알림 설정
    notify_enrollees?: boolean;      // default: true
    notification_message?: string;
  }
}

interface CreateOccurrenceResponse {
  data: {
    occurrence: Occurrence;
    notifications_sent: number;
    auto_enrolled_members: number;   // 자동 등록된 회원 수
  };
}
```

### PATCH /api/instructor/occurrences/{id}

수업 일정을 수정합니다.

```typescript
interface UpdateOccurrenceRequest {
  params: { id: string };
  body: {
    occurrence_date?: string;
    start_time?: string;
    end_time?: string;
    max_participants?: number;
    status?: OccurrenceStatus;
    instructor_notes?: string;
    cancellation_reason?: string;    // 취소 시 필수
    
    // 알림 설정
    notify_enrollees?: boolean;      // default: true
    notification_message?: string;
    
    // 보강 수업 생성 옵션 (취소 시)
    create_substitute?: boolean;
    substitute_date?: string;
    substitute_start_time?: string;
  }
}

interface UpdateOccurrenceResponse {
  data: {
    occurrence: Occurrence;
    substitute_occurrence?: Occurrence;
    notifications_sent: number;
    affected_enrollments: number;
  };
}
```

### DELETE /api/instructor/occurrences/{id}

수업 일정을 삭제합니다.

```typescript
interface DeleteOccurrenceRequest {
  params: { id: string };
  query?: {
    reason?: string;                 // 삭제 사유
    notify_enrollees?: boolean;      // default: true
    refund_policy?: 'full' | 'partial' | 'none';  // default: full
  }
}

interface DeleteOccurrenceResponse {
  data: {
    deleted_occurrence_id: string;
    affected_enrollments: number;
    refund_amount: number;
    notifications_sent: number;
  };
}
```

### POST /api/instructor/occurrences/bulk-generate

대량 수업 일정 생성 (정기 클래스용)

```typescript
interface BulkGenerateOccurrencesRequest {
  body: {
    class_template_id: string;
    schedule_group_ids?: string[];   // 특정 그룹만, 생략 시 모든 그룹
    date_range: {
      start_date: string;
      end_date: string;
    };
    exclude_dates?: string[];        // 제외할 날짜들 (공휴일 등)
    override_existing?: boolean;     // 기존 occurrence 덮어쓸지
  }
}

interface BulkGenerateOccurrencesResponse {
  data: {
    generated_occurrences: Occurrence[];
    excluded_dates: string[];
    overridden_occurrences: string[];
    summary: {
      total_generated: number;
      by_schedule_group: Record<string, number>;
      by_day_of_week: Record<string, number>;
    };
  };
}
```

## Enrollments (수강 신청) API

### GET /api/instructor/classes/{classId}/enrollments

클래스의 수강 신청 목록을 조회합니다.

```typescript
interface GetEnrollmentsRequest {
  params: { classId: string };
  query?: {
    schedule_group_id?: string;
    status?: EnrollmentStatus[];
    search?: string;                 // 회원 이름/전화번호로 검색
    enrolled_period?: {
      from?: string;
      to?: string;
    };
    include_attendance?: boolean;    // 출석 정보 포함 여부
    limit?: number;
    offset?: number;
  }
}

interface GetEnrollmentsResponse {
  data: {
    enrollments: Array<{
      id: string;
      member: {
        id: string;
        name: string;
        phone?: string;
        email?: string;
      };
      class_template_id: string;
      schedule_group: {
        id: string;
        group_name: string;
      };
      enrollment_status: EnrollmentStatus;
      enrolled_at: string;
      payment_info: {
        payment_id?: string;
        paid_amount: number;
        refund_amount?: number;
        refunded_at?: string;
      };
      attendance_summary?: {         // include_attendance=true 시
        total_classes: number;
        attended_classes: number;
        attendance_rate: number;
        last_attendance: string;
      };
      notes?: string;
      created_at: string;
    }>;
    summary: {
      total_enrollments: number;
      by_status: Record<EnrollmentStatus, number>;
      by_schedule_group: Record<string, number>;
      total_revenue: number;
      refunded_amount: number;
    };
  };
  pagination: Pagination;
}
```

### PATCH /api/instructor/enrollments/{id}

수강 신청 상태를 변경합니다.

```typescript
interface UpdateEnrollmentRequest {
  params: { id: string };
  body: {
    enrollment_status?: EnrollmentStatus;
    notes?: string;
    refund_amount?: number;
    refund_reason?: string;
    notify_member?: boolean;         // default: true
  }
}
```

## Attendances (출석 기록) API

### GET /api/instructor/occurrences/{occurrenceId}/attendances

특정 수업의 출석 현황을 조회합니다.

```typescript
interface GetAttendancesResponse {
  data: {
    occurrence: {
      id: string;
      occurrence_date: string;
      start_time: string;
      end_time: string;
      status: OccurrenceStatus;
      class_template: {
        title: string;
        specialty: string;           // 운동 분야
      };
      schedule_group: {
        group_name: string;
      };
    };
    attendances: Array<{
      id: string;
      member: {
        id: string;
        name: string;
        phone?: string;
      };
      enrollment: {
        id: string;
        enrolled_at: string;
      };
      attendance_status: AttendanceStatus;
      checked_in_at?: string;
      checked_out_at?: string;
      notes?: string;
      created_at: string;
    }>;
    summary: {
      total_expected: number;        // 신청자 수
      total_attended: number;        // 실제 출석자 수
      by_status: Record<AttendanceStatus, number>;
      attendance_rate: number;
    };
  };
}
```

### POST /api/instructor/occurrences/{occurrenceId}/attendances/bulk

출석 체크를 일괄 처리합니다.

```typescript
interface BulkCheckAttendanceRequest {
  params: { occurrenceId: string };
  body: {
    attendances: Array<{
      member_id: string;
      attendance_status: AttendanceStatus;
      notes?: string;
      checked_in_at?: string;        // 지각인 경우 실제 도착 시간
    }>;
    occurrence_notes?: string;       // 수업 전체 메모
  }
}

interface BulkCheckAttendanceResponse {
  data: {
    processed_attendances: number;
    updated_occurrence: Occurrence;
    summary: {
      present: number;
      absent: number;
      late: number;
      excused: number;
    };
  };
}
```

### PATCH /api/instructor/attendances/{id}

개별 출석 기록을 수정합니다.

```typescript
interface UpdateAttendanceRequest {
  params: { id: string };
  body: {
    attendance_status: AttendanceStatus;
    notes?: string;
    checked_in_at?: string;
    checked_out_at?: string;
  }
}
```

## Search API

### GET /api/classes/search

수강생용 클래스 검색 API입니다.

```typescript
interface SearchClassesRequest {
  query: {
    // 기본 필터
    specialty?: string;              // 운동 분야
    level?: string;
    studio_id?: string;
    instructor_id?: string;
    
    // 지역 검색
    location?: {
      near_station?: string;
      district?: string;
      latitude?: number;
      longitude?: number;
      radius_km?: number;
    };
    
    // 시간 필터
    schedule?: {
      days?: string[];              // ['MONDAY', 'WEDNESDAY']
      time_range?: {
        start_after?: string;       // '18:00'
        start_before?: string;      // '21:00'
      };
    };
    
    // 기간 필터
    period?: {
      recruitment_open?: boolean;   // 현재 모집 중인 것만
      starts_after?: string;        // 특정 날짜 이후 시작
      starts_before?: string;       // 특정 날짜 이전 시작
    };
    
    // 기타 필터
    price_range?: {
      min?: number;
      max?: number;
    };
    availability?: {
      has_spots?: boolean;          // 잔여 정원 있는 것만
      min_spots?: number;           // 최소 잔여 정원
    };
    
    // 검색어
    search?: string;                // 제목, 설명, 강사명으로 검색
    
    // 정렬
    sort?: 'relevance' | 'price' | 'rating' | 'start_date' | 'distance';
    order?: 'asc' | 'desc';
    
    // 페이지네이션
    limit?: number;                 // default: 20, max: 50
    offset?: number;
  }
}

interface SearchClassesResponse {
  data: {
    classes: Array<{
      class_template: ClassTemplate & {
        studio: {
          id: string;
          name: string;
          address: string;
          nearest_station?: string;
          distance_km?: number;      // 위치 검색 시
        };
        instructor: {
          id: string;
          name: string;
          short_bio?: string;
          rating?: number;
          review_count?: number;
        };
      };
      schedule_groups: Array<{
        id: string;
        group_name: string;
        max_participants: number;
        available_spots: number;
        price_per_session: number;
        schedules: Array<{
          day_of_week: string;
          start_time: string;
          end_time: string;
        }>;
        next_occurrence?: {
          occurrence_date: string;
          start_time: string;
        };
      }>;
      statistics: {
        total_reviews: number;
        average_rating: number;
        completion_rate: number;
      };
      match_score?: number;          // 검색 관련도 점수
    }>;
    filters: {
      specialties: Array<{ value: string; count: number; }>;
      levels: Array<{ value: string; count: number; }>;
      price_ranges: Array<{ min: number; max: number; count: number; }>;
      locations: Array<{ station: string; count: number; }>;
    };
    summary: {
      total_found: number;
      search_time_ms: number;
    };
  };
  pagination: Pagination;
}
```

## Dashboard API

### GET /api/instructor/dashboard/v2

개선된 강사 Dashboard API입니다.

```typescript
interface DashboardV2Response {
  data: {
    instructor: {
      id: string;
      name: string;
      is_active: boolean;
      onboarding_complete: boolean;
    };
    
    // 📊 주요 통계
    stats: {
      // 클래스 통계
      total_classes: number;
      active_classes: number;
      recruiting_classes: number;
      ongoing_classes: number;
      
      // 수업 통계 (실제 Occurrence 기반)
      total_occurrences: number;
      upcoming_occurrences: number;
      this_week_occurrences: number;
      this_month_occurrences: number;
      completed_occurrences: number;
      cancelled_occurrences: number;
      
      // 수강생 통계
      total_enrollments: number;
      confirmed_enrollments: number;
      this_month_enrollments: number;
      
      // 수익 통계
      total_revenue: number;
      this_month_revenue: number;
      pending_revenue: number;
      
      // 품질 지표
      average_attendance_rate: number;
      cancellation_rate: number;
      average_rating: number;
      total_reviews: number;
    };
    
    // 📅 다가오는 수업 (실제 Occurrence)
    upcoming_occurrences: Array<{
      id: string;
      occurrence_date: string;
      start_time: string;
      end_time: string;
      class_title: string;
      group_name: string;
      studio_name: string;
      confirmed_enrollments: number;
      max_participants: number;
      occupancy_rate: number;
      status: OccurrenceStatus;
      is_substitute_class: boolean;
      can_modify: boolean;
    }>;
    
    // 📋 최근 신청 내역
    recent_enrollments: Array<{
      id: string;
      member_name: string;
      class_title: string;
      group_name: string;
      occurrence_date?: string;       // 다음 수업 날짜
      enrollment_status: EnrollmentStatus;
      enrolled_at: string;
      paid_amount: number;
    }>;
    
    // 📈 이번 주 스케줄
    this_week_schedule: Array<{
      date: string;
      day_of_week: string;
      occurrences: Array<{
        id: string;
        start_time: string;
        end_time: string;
        class_title: string;
        group_name: string;
        confirmed_enrollments: number;
        max_participants: number;
        status: OccurrenceStatus;
        attendance_checked: boolean;
      }>;
    }>;
    
    // 🎯 액션 필요 항목
    action_items: Array<{
      type: 'attendance_check' | 'class_preparation' | 'enrollment_review' | 'rating_review';
      title: string;
      description: string;
      priority: 'high' | 'medium' | 'low';
      due_date?: string;
      action_url?: string;
      count?: number;
    }>;
    
    // 📊 월별 트렌드 (최근 6개월)
    monthly_trends: Array<{
      month: string;                 // '2024-12'
      occurrences: number;
      enrollments: number;
      revenue: number;
      attendance_rate: number;
    }>;
  };
}
```

### GET /api/instructor/dashboard/analytics

강사 분석 데이터 API입니다.

```typescript
interface AnalyticsRequest {
  query?: {
    period?: 'week' | 'month' | 'quarter' | 'year';
    start_date?: string;
    end_date?: string;
    class_template_id?: string;     // 특정 클래스 분석
    include_comparisons?: boolean;   // 이전 기간 비교 포함
  }
}

interface AnalyticsResponse {
  data: {
    period: {
      start_date: string;
      end_date: string;
      previous_period?: {            // 비교 기간
        start_date: string;
        end_date: string;
      };
    };
    
    // 📊 핵심 메트릭
    metrics: {
      occurrence_metrics: {
        total_occurrences: number;
        completed_occurrences: number;
        cancelled_occurrences: number;
        completion_rate: number;
        cancellation_rate: number;
        average_occupancy_rate: number;
        change_vs_previous?: number;  // 이전 기간 대비 변화율
      };
      
      enrollment_metrics: {
        total_enrollments: number;
        confirmed_enrollments: number;
        cancelled_enrollments: number;
        confirmation_rate: number;
        average_enrollment_lead_time_days: number;
        change_vs_previous?: number;
      };
      
      attendance_metrics: {
        total_attendances: number;
        average_attendance_rate: number;
        no_show_rate: number;
        late_arrival_rate: number;
        change_vs_previous?: number;
      };
      
      revenue_metrics: {
        total_revenue: number;
        revenue_per_occurrence: number;
        revenue_per_enrollment: number;
        refunded_amount: number;
        refund_rate: number;
        change_vs_previous?: number;
      };
    };
    
    // 📈 시간별 분석
    time_analysis: {
      // 요일별 성과
      by_day_of_week: Array<{
        day: string;
        occurrences: number;
        average_occupancy: number;
        attendance_rate: number;
        revenue: number;
      }>;
      
      // 시간대별 성과
      by_time_slot: Array<{
        time_slot: string;           // '09:00-12:00'
        occurrences: number;
        average_occupancy: number;
        attendance_rate: number;
        preferred_by_age_group?: string;
      }>;
      
      // 월별 트렌드
      monthly_trend: Array<{
        month: string;
        occurrences: number;
        enrollments: number;
        revenue: number;
        attendance_rate: number;
      }>;
    };
    
    // 🎯 클래스별 성과
    class_performance: Array<{
      class_template: {
        id: string;
        title: string;
        specialty: string;           // 운동 분야
        level: string;
      };
      metrics: {
        total_occurrences: number;
        average_occupancy_rate: number;
        attendance_rate: number;
        revenue: number;
        student_satisfaction: number;
        recommendation_score: number;
      };
      ranking: number;               // 성과 순위
    }>;
    
    // 💡 인사이트 및 추천
    insights: Array<{
      type: 'performance' | 'opportunity' | 'warning' | 'recommendation';
      title: string;
      description: string;
      impact: 'high' | 'medium' | 'low';
      suggested_actions?: string[];
    }>;
  };
}
```

## Error Handling

### 표준 에러 코드

```typescript
enum ErrorCodes {
  // 일반 에러
  INVALID_REQUEST = 'INVALID_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  
  // 클래스 관련
  CLASS_NOT_FOUND = 'CLASS_NOT_FOUND',
  CLASS_NOT_EDITABLE = 'CLASS_NOT_EDITABLE',
  CLASS_ALREADY_STARTED = 'CLASS_ALREADY_STARTED',
  INVALID_CLASS_DATES = 'INVALID_CLASS_DATES',
  
  // Occurrence 관련
  OCCURRENCE_NOT_FOUND = 'OCCURRENCE_NOT_FOUND',
  OCCURRENCE_NOT_MODIFIABLE = 'OCCURRENCE_NOT_MODIFIABLE',
  OCCURRENCE_ALREADY_COMPLETED = 'OCCURRENCE_ALREADY_COMPLETED',
  OCCURRENCE_HAS_ENROLLMENTS = 'OCCURRENCE_HAS_ENROLLMENTS',
  INVALID_OCCURRENCE_TIME = 'INVALID_OCCURRENCE_TIME',
  
  // 신청 관련
  ENROLLMENT_CLOSED = 'ENROLLMENT_CLOSED',
  CLASS_FULL = 'CLASS_FULL',
  DUPLICATE_ENROLLMENT = 'DUPLICATE_ENROLLMENT',
  ENROLLMENT_NOT_FOUND = 'ENROLLMENT_NOT_FOUND',
  INVALID_ENROLLMENT_STATUS = 'INVALID_ENROLLMENT_STATUS',
  
  // 출석 관련
  ATTENDANCE_PERIOD_CLOSED = 'ATTENDANCE_PERIOD_CLOSED',
  ATTENDANCE_ALREADY_RECORDED = 'ATTENDANCE_ALREADY_RECORDED',
  MEMBER_NOT_ENROLLED = 'MEMBER_NOT_ENROLLED',
  
  // 권한 관련
  NOT_CLASS_INSTRUCTOR = 'NOT_CLASS_INSTRUCTOR',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
}
```

### 에러 응답 예시

```typescript
// 400 Bad Request
{
  "error": {
    "code": "INVALID_CLASS_DATES",
    "message": "클래스 종료일은 시작일보다 늦어야 합니다",
    "details": {
      "field": "class_end_date",
      "provided": "2024-11-30",
      "class_start_date": "2024-12-01"
    }
  },
  "timestamp": "2024-12-01T10:30:00Z",
  "path": "/api/instructor/classes"
}

// 409 Conflict  
{
  "error": {
    "code": "CLASS_FULL",
    "message": "해당 시간대 그룹의 정원이 마감되었습니다",
    "details": {
      "schedule_group_id": "sg-123",
      "max_participants": 10,
      "current_enrollments": 10,
      "waitlist_available": true
    }
  },
  "timestamp": "2024-12-01T10:30:00Z",
  "path": "/api/enrollments"
}
```

## Rate Limiting

```typescript
// API별 Rate Limit 설정
const rateLimits = {
  // 조회 API
  'GET /api/instructor/classes': '1000 req/hour',
  'GET /api/instructor/occurrences': '500 req/hour',
  'GET /api/classes/search': '2000 req/hour',
  
  // 생성/수정 API
  'POST /api/instructor/classes': '100 req/hour',
  'PATCH /api/instructor/occurrences/*': '200 req/hour',
  'POST /api/instructor/occurrences/bulk-generate': '10 req/hour',
  
  // 대량 처리 API
  'POST /api/instructor/attendances/bulk': '50 req/hour',
  
  // Dashboard API
  'GET /api/instructor/dashboard/v2': '200 req/hour',
  'GET /api/instructor/dashboard/analytics': '100 req/hour'
};
```

## Caching Strategy

```typescript
// 캐시 전략 설정
const cacheConfig = {
  // 자주 조회되는 정적 데이터
  class_templates: {
    key: 'class:{template_id}',
    ttl: 600,  // 10분
    invalidate_on: ['template_update', 'schedule_change']
  },
  
  // 검색 결과
  search_results: {
    key: 'search:{hash(query)}',
    ttl: 300,  // 5분
    invalidate_on: ['new_class', 'template_update']
  },
  
  // Dashboard 데이터
  dashboard_stats: {
    key: 'dashboard:{instructor_id}:{date}',
    ttl: 120,  // 2분
    invalidate_on: ['enrollment_change', 'occurrence_update']
  },
  
  // 실시간성이 중요한 데이터
  occurrence_enrollments: {
    key: 'enrollments:{occurrence_id}',
    ttl: 60,   // 1분
    invalidate_on: ['enrollment_change']
  }
};
```

이 API 명세서는 클래스 라이프사이클의 모든 단계를 효율적으로 관리할 수 있도록 설계되었으며, 특히 Schedule-Occurrence 관계를 활용한 유연한 클래스 운영을 지원합니다.

## 주요 변경사항

### 필드명 변경
- `category` → `specialty` (운동 분야)
- `max_group_size` → `max_participants` (그룹 최대 인원)

### 구조화된 데이터
- `curriculum` 필드: 구조화된 클래스 소개 정보
  - `overview`: 수업 소개
  - `targetAudience`: 수업 추천 대상 (배열)
  - `curriculum`: 커리큘럼 단계별 정보 (배열)
  - `materials`: 준비물 (배열)

### 엔티티 명칭 (한국어/영어)
- 클래스 템플릿 (Class Template)
- 스케줄 그룹 (Schedule Group)
- 스케줄 (Schedule)
- 실제 수업 (Occurrence)
- 수강 신청 (Enrollment)
- 출석 기록 (Attendance)