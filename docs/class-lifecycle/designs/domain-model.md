# 클래스 라이프사이클 도메인 모델

## 아키텍처 개요

클래스 라이프사이클 관리를 위한 4-Layer 구조로 설계되었습니다. 각 레이어는 명확한 역할과 책임을 가지며, 유연하고 확장 가능한 클래스 운영을 지원합니다.

```mermaid
graph TB
    subgraph Layer1["Layer 1: Template"]
        A["클래스 템플릿<br/>(Class Template)"]
        A --> A1["클래스 기본정보"]
        A --> A2["기간 정보"]
        A --> A3["상태 관리"]
    end
    
    subgraph Layer2["Layer 2: Schedule Pattern"]
        B["스케줄 그룹<br/>(Schedule Group)"]
        C["스케줄<br/>(Schedule)"]
        B --> B1["시간대 그룹"]
        C --> C1["반복 패턴"]
    end
    
    subgraph Layer3["Layer 3: Actual Class"]
        D["실제 수업<br/>(Occurrence)"]
        D --> D1["실제 수업"]
        D --> D2["개별 관리"]
        D --> D3["상태 추적"]
    end
    
    subgraph Layer4["Layer 4: Records"]
        E["수강 신청<br/>(Enrollment)"]
        F["출석 기록<br/>(Attendance)"]
        E --> E1["수강 신청"]
        F --> F1["출석 기록"]
    end
    
    A -->|1:N| B
    B -->|1:N| C  
    C -->|1:N| D
    D -->|1:N| F
    A -->|1:N| E
    B -->|1:1| E
```

## 핵심 개념: Schedule과 Occurrence의 관계

### Template vs Instance 패러다임

**Schedule (스케줄)**: **"패턴 템플릿"** - 반복되는 스케줄 규칙 정의  
**Occurrence (실제 수업)**: **"실제 수업 인스턴스"** - 구체적인 날짜와 시간의 수업

이 관계는 객체지향 프로그래밍의 **Class와 Instance** 관계와 동일합니다.

### 상세한 관계 설명

#### 1. 패턴 정의 (Schedule)

```typescript
// Schedule = 반복 패턴 템플릿
{
  id: "schedule-mon-18",
  schedule_group_id: "evening-group", 
  day_of_week: "MONDAY",
  start_time: "18:00",
  end_time: "19:00"
}
```

#### 2. 인스턴스 생성 (Occurrence)

하나의 Schedule로부터 여러 Occurrence가 생성됩니다.

```typescript
// 4주 과정 → 4개 Occurrence 생성
[
  {
    id: "occ-1",
    class_schedule_id: "schedule-mon-18",  // 어떤 패턴에서 왔는지
    occurrence_date: "2024-12-02",         // 1주차 월요일
    start_time: "18:00",                   // 기본값 (schedule에서 복사)
    end_time: "19:00",
    status: "scheduled"
  },
  {
    id: "occ-2",
    class_schedule_id: "schedule-mon-18", 
    occurrence_date: "2024-12-09",         // 2주차 월요일
    start_time: "18:00", 
    end_time: "19:00",
    status: "scheduled"
  },
  {
    id: "occ-3",
    class_schedule_id: "schedule-mon-18",
    occurrence_date: "2024-12-16",         // 3주차 월요일
    start_time: "17:30",                   // ⭐ 이 날만 30분 앞당김
    end_time: "18:30",
    status: "scheduled"
  },
  {
    id: "occ-4", 
    class_schedule_id: "schedule-mon-18",
    occurrence_date: "2024-12-23",         // 4주차 월요일
    start_time: "18:00",
    end_time: "19:00", 
    status: "cancelled"                    // ⭐ 이 날만 휴강
  }
]
```

#### 3. 핵심 관계 특성

**상속 관계 (Inheritance):**
- Occurrence 생성 시 Schedule의 정보를 기본값으로 복사
- 시간, 정원, 기타 설정값들이 상속됨

**독립성 (Independence):**
- 생성 후에는 각 Occurrence가 독립적으로 수정 가능
- 특정 날짜만 시간 변경, 취소, 정원 조정 등

**추적성 (Traceability):**
- `class_schedule_id`로 어떤 패턴에서 생성되었는지 추적
- 패턴 변경 시 미래 Occurrence에만 영향

### 실제 운영 시나리오

#### 시나리오 1: 정기 클래스 생성

```mermaid
sequenceDiagram
    participant I as 강사
    participant S as 시스템
    participant DB as 데이터베이스
    
    I->>S: 4주 요가 클래스 생성
    S->>DB: Template 생성
    
    I->>S: 오후반(월수금 14시) + 저녁반(월수금 19시) 설정
    S->>DB: 2개 Schedule Group 생성
    S->>DB: 6개 Schedule 생성 (각 그룹당 3개)
    
    I->>S: 수업 기간: 12/2 ~ 12/27 설정
    S->>S: 자동 Occurrence 생성 로직 실행
    S->>DB: 24개 Occurrence 생성 (4주 × 6개 스케줄)
    
    Note over S: 오후반: 12개 Occurrence<br/>저녁반: 12개 Occurrence
```

#### 시나리오 2: 개별 수업 관리

```typescript
// 패턴 수정 → 미래 Occurrence에만 영향
await updateSchedule("schedule-mon-18", { start_time: "19:00" });
// → 12월 30일부터 생성되는 새 Occurrence는 19시 시작

// 개별 수업 수정 → 해당 날짜만 영향
await updateOccurrence("occ-3", { 
  start_time: "17:30",
  instructor_notes: "학회 참석으로 30분 앞당김" 
});
// → 12월 16일만 17:30 시작, 다른 날은 그대로

// 휴강 처리
await updateOccurrence("occ-4", {
  status: "cancelled",
  cancellation_reason: "강사 개인사정"
});

// 보강 수업 생성
await createOccurrence({
  class_schedule_id: "schedule-mon-18",  // 같은 패턴 참조
  occurrence_date: "2024-12-26",        // 보강 날짜
  is_substitute_class: true,
  original_occurrence_id: "occ-4"
});
```

#### 시나리오 3: 다중 시간대 관리 (🆕 MVP 스펙)

```mermaid
graph TD
    A["4주 요가 클래스"] --> B[오후반1 그룹]
    A --> C[저녁반1 그룹]
    
    B --> B1["월/수/금 14:00-15:00<br/>(그룹 내 통일된 시간)"]
    B1 --> D[월요일 14:00 Schedule]
    B1 --> E[수요일 14:00 Schedule]  
    B1 --> F[금요일 14:00 Schedule]
    
    C --> C1["월/수/금 19:00-20:00<br/>(그룹 내 통일된 시간)"]
    C1 --> G[월요일 19:00 Schedule]
    C1 --> H[수요일 19:00 Schedule]
    C1 --> I[금요일 19:00 Schedule]
    
    D --> J[12/2, 12/9, 12/16, 12/23<br/>4개 Occurrence]
    E --> K[12/4, 12/11, 12/18, 12/25<br/>4개 Occurrence]
    F --> L[12/6, 12/13, 12/20, 12/27<br/>4개 Occurrence]
    
    G --> M[12/2, 12/9, 12/16, 12/23<br/>4개 Occurrence]
    H --> N[12/4, 12/11, 12/18, 12/25<br/>4개 Occurrence]
    I --> O[12/6, 12/13, 12/20, 12/27<br/>4개 Occurrence]
    
    style B1 fill:#e1f5fe
    style C1 fill:#e1f5fe
    note1["🆕 MVP 특징:<br/>- 그룹 내 통일된 시간<br/>- 요일만 다중 선택<br/>- 자동 그룹명 생성"]
```

## 도메인 엔티티 설계

### Class Template (클래스 템플릿)

클래스의 기본 정보와 라이프사이클을 관리하는 핵심 엔티티입니다.

```typescript
interface ClassTemplate {
  // 기본 정보
  id: string;
  title: string;
  description?: string;
  specialty: string;              // 운동 분야 (YOGA, PILATES, FITNESS 등)
  level: ClassLevel;             // 난이도 (beginner, intermediate, advanced)
  instructor_id: string;
  studio_id: string;
  
  // 수업 설정
  duration_minutes: number;       // 수업 시간 (분)
  max_capacity: number;           // 최대 정원
  price_per_session: decimal;     // 회당 가격
  curriculum?: object;            // 구조화된 클래스 소개 (overview, targetAudience, curriculum, materials)
  
  // 📅 라이프사이클 관리 (핵심 추가사항)
  recruitment_start_date: date;    // 모집 시작일
  recruitment_end_date: date;      // 모집 종료일
  class_start_date: date;          // 수업 시작일
  class_end_date: date;            // 수업 종료일
  
  // 상태 관리
  status: ClassStatus;             // 클래스 상태
  is_active: boolean;              // 활성화 여부
  
  created_at: timestamp;
  updated_at: timestamp;
}

type ClassStatus = 
  | 'upcoming'     // 모집 시작 전
  | 'recruiting'   // 모집 중
  | 'ongoing'      // 수업 진행 중
  | 'completed'    // 완료
  | 'cancelled';   // 취소됨
```

**🔄 수정된 상태 전환 로직:**
```typescript
// Template 상태는 날짜와 그룹 상태 기준으로 자동 계산
function getClassStatus(template: ClassTemplate, now = new Date()): ClassStatus {
  if (template.status === 'cancelled') return 'cancelled';
  if (now < template.recruitment_start_date) return 'upcoming';
  if (now <= template.recruitment_end_date) return 'recruiting';
  if (now <= template.class_end_date) return 'ongoing';
  return 'completed';
}
```

### Class Schedule Groups (스케줄 그룹)

하나의 클래스 템플릿 내에서 다른 시간대를 가진 그룹을 관리합니다.

```typescript
interface ClassScheduleGroup {
  id: string;
  class_template_id: string;
  
  // 그룹 정보
  group_name: string;              // "오후반", "저녁반", "주말반"
  group_description?: string;       // 그룹 설명
  
  // 그룹별 설정
  max_participants: number;         // 그룹별 정원 (🆕 max_group_size → max_participants)
  price_per_session?: decimal;      // 그룹별 가격 (옵션)
  
  // 메타데이터
  sessions_per_week: number;        // 주당 횟수 (계산값)
  is_active: boolean;
  
  created_at: timestamp;
  updated_at: timestamp;
}
```

### Class Schedules (스케줄 패턴)

검색 최적화와 반복 패턴 관리를 위한 정규화된 스케줄 정보입니다.

**🆕 MVP 스펙 (간소화 버전):**
- 하나의 그룹 내 모든 개별 스케줄은 동일한 시간대 사용
- 요일만 다르게 선택 가능 (예: 월/수/금 19:00-20:00)
- UI에서 요일 다중선택 + 시간 통일 방식 적용

```typescript
interface ClassSchedule {
  id: string;
  schedule_group_id: string;
  
  // 🔍 검색 최적화 필드
  day_of_week: DayOfWeek;         // 'MONDAY', 'TUESDAY', ...
  start_time: time;               // '14:00:00', '19:00:00' - 🆕 그룹 내 동일한 시간
  end_time: time;                 // '15:00:00', '20:00:00' - 🆕 그룹 내 동일한 시간
  
  // 기본 설정 (Occurrence에 상속됨) - 현재 사용하지 않음
  max_participants?: number;       
  
  is_active: boolean;
  created_at: timestamp;
  updated_at: timestamp;
}

type DayOfWeek = 
  | 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' 
  | 'THURSDAY' | 'FRIDAY' | 'SATURDAY' | 'SUNDAY';

// 🆕 MVP 스펙 예시
// 하나의 그룹: "월/수/금 19:00-20:00"
// → 3개의 Schedule 생성 (모두 동일한 시간)
// Schedule 1: { day_of_week: 'MONDAY', start_time: '19:00', end_time: '20:00' }
// Schedule 2: { day_of_week: 'WEDNESDAY', start_time: '19:00', end_time: '20:00' }
// Schedule 3: { day_of_week: 'FRIDAY', start_time: '19:00', end_time: '20:00' }
```

### Class Occurrences (실제 수업)

구체적인 날짜와 시간의 실제 수업 인스턴스입니다.

```typescript
interface ClassOccurrence {
  id: string;
  
  // 📋 참조 관계
  class_template_id: string;      // 어떤 클래스인지
  schedule_group_id: string;      // 어떤 시간대 그룹인지
  class_schedule_id: string;      // 어떤 패턴에서 생성되었는지
  
  // 📅 실제 수업 정보
  occurrence_date: date;          // '2024-12-02'
  start_time: time;               // 개별 조정 가능
  end_time: time;                 // 개별 조정 가능
  max_participants?: number;      // 개별 조정 가능 (옵션)
  
  // 📊 상태 관리
  status: OccurrenceStatus;
  attendance_count: number;       // 실제 출석자 수
  confirmed_enrollments: number;  // 확정 신청자 수
  
  // 📝 운영 정보
  instructor_notes?: string;      // 강사 메모
  cancellation_reason?: string;   // 취소 사유
  
  // 🔄 보강 수업 관리
  is_substitute_class: boolean;
  original_occurrence_id?: string;
  
  // ⏰ 예약 정책 (미구현)
  booking_opens_at?: timestamp;
  booking_closes_at?: timestamp;
  cancellation_deadline?: timestamp;
  
  created_at: timestamp;
  updated_at: timestamp;
}

type OccurrenceStatus = 
  | 'scheduled'    // 예정됨
  | 'ongoing'      // 진행 중
  | 'completed'    // 완료
  | 'cancelled';   // 취소됨
```

### Class Enrollments (수강 신청)

수강생의 클래스 신청 및 결제 정보를 관리합니다.

```typescript
interface ClassEnrollment {
  id: string;
  
  // 👤 신청자 정보
  member_id: string;
  
  // 📋 신청 대상 (클래스 전체 + 특정 시간대)
  class_template_id: string;      // 전체 클래스에 신청
  schedule_group_id: string;      // 특정 시간대 그룹 선택 (🆕 필수)
  
  // 📅 신청 정보
  enrolled_at: timestamp;
  enrollment_status: EnrollmentStatus;
  
  // 💳 결제 정보
  payment_id?: string;
  paid_amount: decimal;
  
  // 💰 환불 관리
  refund_amount?: decimal;
  refunded_at?: timestamp;
  refund_reason?: string;
  
  // 📝 기타
  notes?: string;
  
  created_at: timestamp;
  updated_at: timestamp;
}

type EnrollmentStatus = 
  | 'pending'      // 결제 대기
  | 'confirmed'    // 확정
  | 'cancelled'    // 취소
  | 'completed'    // 수강 완료
  | 'refunded';    // 환불 완료
```

### Class Attendances (출석 기록)

개별 수업에 대한 출석 기록을 관리합니다.

```typescript
interface ClassAttendance {
  id: string;
  
  // 📋 참조 관계
  class_occurrence_id: string;    // 특정 수업
  member_id: string;              // 수강생
  enrollment_id?: string;         // 신청 기록 (옵션)
  
  // ✅ 출석 정보
  attendance_status: AttendanceStatus;
  checked_in_at?: timestamp;
  checked_out_at?: timestamp;
  
  // 📝 기타
  notes?: string;                 // 지각 사유, 조퇴 사유 등
  
  created_at: timestamp;
}

type AttendanceStatus = 
  | 'present'      // 출석
  | 'absent'       // 결석
  | 'late'         // 지각
  | 'excused';     // 사유 결석
```

## 비즈니스 로직

### Occurrence 생성 로직

```typescript
function generateOccurrences(
  template: ClassTemplate,
  scheduleGroups: ClassScheduleGroup[],
  schedules: ClassSchedule[]
): ClassOccurrence[] {
  const occurrences: ClassOccurrence[] = [];
  
  // 수업 기간 동안 모든 날짜 순회
  let currentDate = new Date(template.class_start_date);
  const endDate = new Date(template.class_end_date);
  
  while (currentDate <= endDate) {
    const dayOfWeek = getDayOfWeek(currentDate);
    
    // 해당 요일에 매칭되는 스케줄들 찾기
    const matchingSchedules = schedules.filter(s => s.day_of_week === dayOfWeek);
    
    matchingSchedules.forEach(schedule => {
      const scheduleGroup = scheduleGroups.find(sg => sg.id === schedule.schedule_group_id);
      
      occurrences.push({
        id: generateId(),
        class_template_id: template.id,
        schedule_group_id: schedule.schedule_group_id,
        class_schedule_id: schedule.id,
        
        // Schedule에서 상속
        occurrence_date: formatDate(currentDate),
        start_time: schedule.start_time,
        end_time: schedule.end_time,
        max_participants: scheduleGroup.max_participants,
        
        // 기본값
        status: 'scheduled',
        attendance_count: 0,
        confirmed_enrollments: 0,
        is_substitute_class: false,
        
        created_at: new Date(),
        updated_at: new Date()
      });
    });
    
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return occurrences;
}
```

### 상태 전환 관리

```mermaid
stateDiagram-v2
    [*] --> upcoming : 클래스 생성
    upcoming --> recruiting : 모집 시작일 도래
    recruiting --> ongoing : 수업 시작일 도래
    ongoing --> completed : 수업 종료일 도래
    
    upcoming --> cancelled : 관리자/강사 취소
    recruiting --> cancelled : 관리자/강사 취소
    ongoing --> cancelled : 중도 취소
    
    cancelled --> [*]
    completed --> [*]
    
    note right of recruiting
        신규 신청 가능
        Occurrence 생성 완료
    end note
    
    note right of ongoing  
        수업 진행 중
        출석 체크 가능
    end note
```

### 검색 최적화 전략

```typescript
// 효율적인 클래스 검색
interface ClassSearchQuery {
  specialty?: string;             // 운동 분야
  level?: ClassLevel;            // 난이도
  days?: DayOfWeek[];           // 요일 필터
  time_range?: {                // 시간대 필터
    start_after?: time;         // 18:00 이후
    start_before?: time;        // 12:00 이전
  };
  location?: {
    studio_id?: string;
    near_station?: string;
  };
  status?: ClassStatus[];       // 모집중, 진행중 등
  start_date?: {                // 시작일 필터
    from?: date;
    to?: date;
  };
}

// 검색 쿼리 최적화
function buildSearchQuery(filters: ClassSearchQuery) {
  return `
    SELECT DISTINCT 
      t.id, t.title, t.specialty, t.level,
      t.recruitment_start_date, t.recruitment_end_date,
      t.class_start_date, t.class_end_date,
      s.day_of_week, s.start_time, s.end_time,
      sg.group_name, sg.max_participants
    FROM class_templates t
    JOIN class_schedule_groups sg ON t.id = sg.class_template_id
    JOIN class_schedules s ON sg.id = s.schedule_group_id
    WHERE t.is_active = true
      AND sg.is_active = true  
      AND s.is_active = true
      ${filters.specialty ? `AND t.specialty = '${filters.specialty}'` : ''}
      ${filters.level ? `AND t.level = '${filters.level}'` : ''}
      ${filters.days?.length ? `AND s.day_of_week IN (${filters.days.map(d => `'${d}'`).join(',')})` : ''}
      ${filters.time_range?.start_after ? `AND s.start_time >= '${filters.time_range.start_after}'` : ''}
      ${filters.time_range?.start_before ? `AND s.start_time < '${filters.time_range.start_before}'` : ''}
    ORDER BY t.recruitment_start_date, s.day_of_week, s.start_time
  `;
}
```

### 신청 및 출석 관리

```typescript
// 클래스 신청 프로세스 (그룹 기반)
async function enrollInClass(
  memberId: string, 
  templateId: string, 
  scheduleGroupId: string
): Promise<ClassEnrollment> {
  
  // 1. 신청 가능 여부 확인
  const template = await getClassTemplate(templateId);
  const templateStatus = getClassStatus(template);
  
  if (templateStatus !== 'recruiting') {
    throw new Error(`현재 모집 중이 아닙니다 (상태: ${templateStatus})`);
  }
  
  // 2. 정원 확인
  const scheduleGroup = await getScheduleGroup(scheduleGroupId);
  const currentEnrollments = await countEnrollments(templateId, scheduleGroupId);
  
  if (currentEnrollments >= scheduleGroup.max_participants) {
    throw new Error('정원이 마감되었습니다');
  }
  
  // 3. 중복 신청 확인
  const existingEnrollment = await findEnrollment(memberId, templateId);
  if (existingEnrollment) {
    throw new Error('이미 신청한 클래스입니다');
  }
  
  // 4. 신청 생성
  return await createEnrollment({
    member_id: memberId,
    class_template_id: templateId,
    schedule_group_id: scheduleGroupId,  // 그룹 기반 신청
    enrollment_status: 'pending',
    enrolled_at: new Date()
  });
}

// 출석 체크 프로세스
async function checkAttendance(
  occurrenceId: string, 
  memberId: string, 
  status: AttendanceStatus
): Promise<ClassAttendance> {
  
  // 1. 수업 및 신청 확인
  const occurrence = await getOccurrence(occurrenceId);
  const enrollment = await findEnrollmentByMember(
    memberId, 
    occurrence.class_template_id
  );
  
  if (!enrollment || enrollment.enrollment_status !== 'confirmed') {
    throw new Error('수업 신청이 확인되지 않습니다');
  }
  
  // 2. 수업 상태 확인
  if (occurrence.status !== 'ongoing' && occurrence.status !== 'completed') {
    throw new Error('출석 체크가 불가능한 수업 상태입니다');
  }
  
  // 3. 출석 기록 생성/업데이트
  return await upsertAttendance({
    class_occurrence_id: occurrenceId,
    member_id: memberId,
    enrollment_id: enrollment.id,
    attendance_status: status,
    checked_in_at: status === 'present' ? new Date() : undefined
  });
}
```

## 확장성 고려사항

### 미래 기능 확장

```typescript
// 복잡한 반복 패턴 지원 (향후 확장)
interface AdvancedSchedulePattern {
  pattern_type: 'weekly' | 'biweekly' | 'monthly' | 'custom';
  interval: number;             // 2주마다, 3일마다 등
  custom_dates?: date[];        // 특정 날짜들
  exclude_dates?: date[];       // 제외할 날짜들 (공휴일 등)
  holiday_handling: 'skip' | 'reschedule' | 'proceed';
}

// 다중 강사 지원 (향후 확장)
interface OccurrenceInstructor {
  occurrence_id: string;
  instructor_id: string;
  role: 'primary' | 'assistant' | 'substitute';
  confirmed: boolean;
}

// 동적 가격 정책 (향후 확장)
interface DynamicPricing {
  base_price: decimal;
  time_based_multiplier?: number;    // 시간대별 할증
  demand_based_adjustment?: number;  // 수요 기반 조정
  early_bird_discount?: number;      // 조기 신청 할인
  group_discount?: number;           // 그룹 할인
}
```

### 성능 최적화

```typescript
// 인덱스 전략
const indexes = [
  // 검색 최적화
  'CREATE INDEX idx_schedules_search ON class_schedules (day_of_week, start_time)',
  'CREATE INDEX idx_templates_status ON class_templates (status, recruitment_end_date)',
  'CREATE INDEX idx_occurrences_date ON class_occurrences (occurrence_date, status)',
  
  // 조인 최적화  
  'CREATE INDEX idx_schedules_group ON class_schedules (schedule_group_id)',
  'CREATE INDEX idx_occurrences_template ON class_occurrences (class_template_id)',
  'CREATE INDEX idx_enrollments_member ON class_enrollments (member_id, enrollment_status)',
  
  // 복합 인덱스
  'CREATE INDEX idx_templates_search ON class_templates (specialty, level, status)',
  'CREATE INDEX idx_occurrences_search ON class_occurrences (occurrence_date, status, class_template_id)'
];

// 캐싱 전략
interface CacheStrategy {
  upcoming_classes: {
    key: 'upcoming:{specialty}:{days}';
    ttl: 300; // 5분
  };
  class_details: {
    key: 'class:{template_id}';
    ttl: 600; // 10분
  };
  enrollment_stats: {
    key: 'stats:{template_id}:{group_id}';
    ttl: 60; // 1분
  };
}
```

## 주요 변경사항 요약

### 🆕 현재 구현 상태와의 주요 차이점:

1. **category 필드 제거**: 운동 분야(specialty)로 통합
2. **max_participants**: 그룹 정원 필드명 통일 (max_group_size → max_participants)
3. **schedule_group_id**: 수강 신청시 필수 (그룹 기반 신청)
4. **그룹별 날짜 관리**: group_start_date, group_end_date 필드 제거 (템플릿 레벨에서만 관리)
5. **구조화된 클래스 소개**: curriculum 필드에 JSON 형태로 저장
   - overview: 수업 소개
   - targetAudience: 수업 추천 대상
   - curriculum: 커리큘럼 단계별 정보
   - materials: 준비물

이 도메인 모델은 클래스 라이프사이클의 전체 과정을 체계적으로 관리하면서도, Schedule-Occurrence 관계를 통해 유연하고 확장 가능한 구조를 제공합니다. 특히 **패턴 정의 → 인스턴스 생성 → 개별 관리**의 흐름을 통해 효율적인 클래스 운영을 지원합니다.