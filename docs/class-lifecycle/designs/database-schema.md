# 클래스 라이프사이클 데이터베이스 스키마

## 스키마 개요

클래스 라이프사이클 관리를 위한 데이터베이스 스키마는 기존 테이블을 확장하고 새로운 테이블을 추가하여 완전한 클래스 운영 시스템을 구축합니다.

```mermaid
erDiagram
    CLASS_TEMPLATES {
        uuid id PK
        string title
        string specialty
        string level
        uuid instructor_id FK
        uuid studio_id FK
        integer duration_minutes
        decimal price_per_session
        integer max_capacity
        jsonb curriculum
        date recruitment_start_date
        date recruitment_end_date
        date class_start_date
        date class_end_date
        string status
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    CLASS_SCHEDULE_GROUPS {
        uuid id PK
        uuid class_template_id FK
        string group_name
        string group_description
        integer max_participants
        decimal price_per_session
        integer sessions_per_week
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    CLASS_SCHEDULES {
        uuid id PK
        uuid schedule_group_id FK
        string day_of_week
        time start_time
        time end_time
        integer max_participants
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    CLASS_OCCURRENCES {
        uuid id PK
        uuid class_template_id FK
        uuid schedule_group_id FK
        uuid class_schedule_id FK
        date occurrence_date
        time start_time
        time end_time
        integer max_participants
        string status
        integer attendance_count
        integer confirmed_enrollments
        string instructor_notes
        string cancellation_reason
        boolean is_substitute_class
        uuid original_occurrence_id FK
        timestamp booking_opens_at
        timestamp booking_closes_at
        timestamp cancellation_deadline
        timestamp created_at
        timestamp updated_at
    }
    
    CLASS_ENROLLMENTS {
        uuid id PK
        uuid member_id FK
        uuid class_template_id FK
        uuid schedule_group_id FK
        string enrollment_status
        timestamp enrolled_at
        string payment_id
        decimal paid_amount
        decimal refund_amount
        timestamp refunded_at
        string refund_reason
        string notes
        timestamp created_at
        timestamp updated_at
    }
    
    CLASS_ATTENDANCES {
        uuid id PK
        uuid class_occurrence_id FK
        uuid member_id FK
        uuid enrollment_id FK
        string attendance_status
        timestamp checked_in_at
        timestamp checked_out_at
        string notes
        timestamp created_at
    }
    
    MEMBERS {
        uuid id PK
        string name
        string phone
        string email
        string role
    }
    
    INSTRUCTORS {
        uuid id PK
        uuid member_id FK
        string short_bio
        boolean is_active
    }
    
    STUDIOS {
        uuid id PK
        string name
        string address
        string studio_type
    }

    CLASS_TEMPLATES ||--o{ CLASS_SCHEDULE_GROUPS : contains
    CLASS_SCHEDULE_GROUPS ||--o{ CLASS_SCHEDULES : contains
    CLASS_SCHEDULES ||--o{ CLASS_OCCURRENCES : generates
    CLASS_TEMPLATES ||--o{ CLASS_OCCURRENCES : contains
    CLASS_SCHEDULE_GROUPS ||--o{ CLASS_OCCURRENCES : contains
    CLASS_OCCURRENCES ||--o{ CLASS_ATTENDANCES : tracks
    CLASS_TEMPLATES ||--o{ CLASS_ENROLLMENTS : receives
    CLASS_SCHEDULE_GROUPS ||--o{ CLASS_ENROLLMENTS : receives
    MEMBERS ||--o{ CLASS_ENROLLMENTS : makes
    MEMBERS ||--o{ CLASS_ATTENDANCES : has
    CLASS_ENROLLMENTS ||--o{ CLASS_ATTENDANCES : includes
    CLASS_OCCURRENCES ||--o{ CLASS_OCCURRENCES : substitutes
    INSTRUCTORS ||--o{ CLASS_TEMPLATES : teaches
    STUDIOS ||--o{ CLASS_TEMPLATES : hosts
    MEMBERS ||--o{ INSTRUCTORS : becomes
```

## 테이블 상세 정의

### 1. class_templates (클래스 템플릿)

기존 테이블에 라이프사이클 관리 필드를 추가합니다.

```sql
CREATE TABLE class_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- 기본 정보
  title TEXT NOT NULL,
  description TEXT,
  specialty TEXT NOT NULL,                    -- 운동 분야 (YOGA, PILATES, FITNESS 등)
  level TEXT NOT NULL,                       -- 난이도 (beginner, intermediate, advanced)
  
  -- 참조
  instructor_id UUID NOT NULL,
  studio_id UUID NOT NULL,
  
  -- 수업 설정
  duration_minutes INTEGER NOT NULL,
  price_per_session DECIMAL(10,2) NOT NULL,
  max_capacity INTEGER NOT NULL,
  curriculum JSONB,                          -- 구조화된 클래스 소개 {overview, targetAudience, curriculum, materials}
  
  -- 🆕 라이프사이클 관리
  recruitment_start_date DATE,               -- 모집 시작일
  recruitment_end_date DATE,                 -- 모집 종료일
  class_start_date DATE,                     -- 수업 시작일
  class_end_date DATE,                       -- 수업 종료일
  status TEXT DEFAULT 'upcoming',            -- 상태 (upcoming, recruiting, ongoing, completed, cancelled)
  
  -- 메타데이터
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_template_dates CHECK (recruitment_start_date <= recruitment_end_date),
  CONSTRAINT check_class_dates CHECK (class_start_date <= class_end_date),
  CONSTRAINT check_recruitment_before_class CHECK (recruitment_end_date <= class_start_date),
  CONSTRAINT check_status CHECK (status IN ('upcoming', 'recruiting', 'ongoing', 'completed', 'cancelled'))
);
```

### 2. class_schedule_groups (스케줄 그룹)

하나의 클래스 템플릿 내에서 다른 시간대를 가진 그룹을 관리합니다.

```sql
CREATE TABLE class_schedule_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_template_id UUID NOT NULL,
  
  -- 그룹 정보
  group_name TEXT NOT NULL,                    -- "오전반", "오후반", "저녁반", "주말반"
  group_description TEXT,                      -- "4인 그룹 수업"
  
  -- 그룹별 설정
  max_participants INTEGER NOT NULL,           -- 그룹별 정원 (🆕 max_group_size → max_participants)
  price_per_session DECIMAL(10,2),            -- 그룹별 가격 (옵션)
  
  -- 메타데이터  
  sessions_per_week INTEGER DEFAULT 0,         -- 주당 횟수 (계산값)
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_max_participants CHECK (max_participants > 0),
  CONSTRAINT check_sessions_per_week CHECK (sessions_per_week >= 0)
);
```

### 3. class_schedules (스케줄 패턴)

검색 최적화와 반복 패턴 관리를 위한 정규화된 스케줄 정보입니다.

```sql
CREATE TABLE class_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_group_id UUID NOT NULL,
  
  -- 🔍 검색 최적화 필드
  day_of_week TEXT NOT NULL,                  -- 'MONDAY', 'TUESDAY', ...
  start_time TIME NOT NULL,                   -- '14:00:00'
  end_time TIME NOT NULL,                     -- '15:00:00'
  
  -- 기본 설정 (현재 미사용)
  max_participants INTEGER,                   -- 스케줄별 정원 (옵션)
  
  -- 메타데이터
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_time_order CHECK (start_time < end_time),
  CONSTRAINT check_day_of_week CHECK (day_of_week IN (
    'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 
    'FRIDAY', 'SATURDAY', 'SUNDAY'
  )),
  CONSTRAINT unique_schedule_per_group UNIQUE (schedule_group_id, day_of_week, start_time)
);
```

### 4. class_occurrences (실제 수업)

실제 수업 인스턴스를 관리하는 핵심 테이블입니다.

```sql
CREATE TABLE class_occurrences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- 📋 참조 관계
  class_template_id UUID NOT NULL,
  schedule_group_id UUID NOT NULL, 
  class_schedule_id UUID NOT NULL,
  
  -- 📅 실제 수업 정보
  occurrence_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  max_participants INTEGER,                   -- NULL 가능 (그룹 설정 상속)
  
  -- 📊 상태 및 통계
  status TEXT NOT NULL DEFAULT 'scheduled',   -- scheduled, ongoing, completed, cancelled
  attendance_count INTEGER DEFAULT 0,
  confirmed_enrollments INTEGER DEFAULT 0,
  
  -- 📝 운영 정보
  instructor_notes TEXT,
  cancellation_reason TEXT,
  
  -- 🔄 보강 수업 관리
  is_substitute_class BOOLEAN DEFAULT false,
  original_occurrence_id UUID,                -- 원본 수업 참조
  
  -- ⏰ 예약 정책 (미구현)
  booking_opens_at TIMESTAMP WITH TIME ZONE,
  booking_closes_at TIMESTAMP WITH TIME ZONE,
  cancellation_deadline TIMESTAMP WITH TIME ZONE,
  
  -- 메타데이터
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_occurrence_time CHECK (start_time < end_time),
  CONSTRAINT check_attendance_count CHECK (attendance_count >= 0),
  CONSTRAINT check_confirmed_enrollments CHECK (confirmed_enrollments >= 0),
  CONSTRAINT check_status CHECK (status IN ('scheduled', 'ongoing', 'completed', 'cancelled')),
  CONSTRAINT check_substitute_original CHECK (
    (is_substitute_class = false AND original_occurrence_id IS NULL) OR
    (is_substitute_class = true AND original_occurrence_id IS NOT NULL)
  )
);
```

### 5. class_enrollments (수강 신청)

수강생의 클래스 신청 및 결제 정보를 관리합니다.

```sql
CREATE TABLE class_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- 👤 신청자 정보
  member_id UUID NOT NULL,
  
  -- 📋 신청 대상
  class_template_id UUID NOT NULL,            -- 전체 클래스
  schedule_group_id UUID NOT NULL,            -- 특정 시간대 그룹 (🆕 필수)
  
  -- 📅 신청 정보
  enrollment_status TEXT NOT NULL DEFAULT 'pending',  -- pending, confirmed, cancelled, completed, refunded
  enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 💳 결제 정보
  payment_id TEXT,
  paid_amount DECIMAL(10,2),
  
  -- 💰 환불 정보
  refund_amount DECIMAL(10,2),
  refunded_at TIMESTAMP WITH TIME ZONE,
  refund_reason TEXT,
  
  -- 📝 기타
  notes TEXT,
  
  -- 메타데이터
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_enrollment_status CHECK (enrollment_status IN (
    'pending', 'confirmed', 'cancelled', 'completed', 'refunded'
  )),
  CONSTRAINT check_paid_amount CHECK (paid_amount >= 0),
  CONSTRAINT check_refund_amount CHECK (refund_amount >= 0),
  CONSTRAINT check_refund_vs_paid CHECK (refund_amount <= paid_amount),
  CONSTRAINT unique_member_template UNIQUE (member_id, class_template_id)
);
```

### 6. class_attendances (출석 기록)

개별 수업에 대한 출석 기록을 관리합니다.

```sql
CREATE TABLE class_attendances (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- 📋 참조 관계
  class_occurrence_id UUID NOT NULL,
  member_id UUID NOT NULL,
  enrollment_id UUID,                         -- 신청 기록 참조 (옵션)
  
  -- ✅ 출석 정보
  attendance_status TEXT NOT NULL,            -- present, absent, late, excused
  checked_in_at TIMESTAMP WITH TIME ZONE,
  checked_out_at TIMESTAMP WITH TIME ZONE,
  
  -- 📝 기타
  notes TEXT,
  
  -- 메타데이터
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_attendance_status CHECK (attendance_status IN (
    'present', 'absent', 'late', 'excused'
  )),
  CONSTRAINT check_check_times CHECK (
    checked_out_at IS NULL OR checked_in_at IS NULL OR checked_out_at >= checked_in_at
  ),
  CONSTRAINT unique_occurrence_member UNIQUE (class_occurrence_id, member_id)
);
```

## 인덱스 전략

클래스 검색과 운영 쿼리 최적화를 위한 인덱스 설계입니다.

### 검색 최적화 인덱스

```sql
-- 🔍 클래스 검색 최적화
CREATE INDEX idx_templates_search ON class_templates (
  specialty, level, status, recruitment_end_date
);

CREATE INDEX idx_templates_lifecycle ON class_templates (
  status, recruitment_start_date, recruitment_end_date, 
  class_start_date, class_end_date
);

-- 🔍 스케줄 검색 최적화  
CREATE INDEX idx_schedules_search ON class_schedules (
  day_of_week, start_time, end_time
);

CREATE INDEX idx_schedules_time_range ON class_schedules (
  start_time, end_time, day_of_week
);

-- 🔍 Occurrence 검색 최적화
CREATE INDEX idx_occurrences_date_status ON class_occurrences (
  occurrence_date, status
);

CREATE INDEX idx_occurrences_upcoming ON class_occurrences (
  occurrence_date, start_time
) WHERE status IN ('scheduled', 'ongoing');
```

### 조인 최적화 인덱스

```sql
-- 📋 참조 관계 최적화
CREATE INDEX idx_schedule_groups_template ON class_schedule_groups (class_template_id);
CREATE INDEX idx_schedules_group ON class_schedules (schedule_group_id);
CREATE INDEX idx_occurrences_template ON class_occurrences (class_template_id);
CREATE INDEX idx_occurrences_schedule ON class_occurrences (class_schedule_id);
CREATE INDEX idx_occurrences_group ON class_occurrences (schedule_group_id);

-- 📋 신청 및 출석 최적화
CREATE INDEX idx_enrollments_member ON class_enrollments (member_id, enrollment_status);
CREATE INDEX idx_enrollments_template_group ON class_enrollments (
  class_template_id, schedule_group_id, enrollment_status
);
CREATE INDEX idx_attendances_occurrence ON class_attendances (class_occurrence_id);
CREATE INDEX idx_attendances_member ON class_attendances (member_id, attendance_status);
```

### 성능 최적화 인덱스

```sql
-- 📊 통계 및 집계 최적화
CREATE INDEX idx_enrollments_stats ON class_enrollments (
  class_template_id, schedule_group_id, enrollment_status, enrolled_at
);

CREATE INDEX idx_attendances_stats ON class_attendances (
  class_occurrence_id, attendance_status, created_at
);

-- ⏰ 시간 기반 쿼리 최적화
CREATE INDEX idx_occurrences_instructor_upcoming ON class_occurrences (
  class_template_id, occurrence_date, status
) WHERE status IN ('scheduled', 'ongoing');

CREATE INDEX idx_templates_recruiting ON class_templates (
  recruitment_start_date, recruitment_end_date
) WHERE status = 'recruiting';
```

### 복합 인덱스 (고급 검색)

```sql
-- 🔍 복합 조건 검색 최적화
CREATE INDEX idx_full_class_search ON class_templates (
  specialty, level, status, recruitment_start_date, recruitment_end_date
);

-- 📅 날짜 + 요일 + 시간 복합 검색
CREATE INDEX idx_schedule_datetime ON class_schedules (
  day_of_week, start_time, end_time, schedule_group_id
);

-- 📊 운영 현황 복합 인덱스
CREATE INDEX idx_occurrence_operations ON class_occurrences (
  class_template_id, status, occurrence_date, confirmed_enrollments
);
```

## 뷰 정의

자주 사용되는 복잡한 조인 쿼리를 뷰로 정의하여 성능과 편의성을 향상시킵니다.

### 클래스 전체 정보 뷰

```sql
CREATE VIEW class_full_info AS
SELECT 
  t.id as template_id,
  t.title,
  t.description,
  t.specialty,
  t.level,
  t.instructor_id,
  t.studio_id,
  t.duration_minutes,
  t.price_per_session,
  t.max_capacity,
  
  -- 라이프사이클 정보
  t.recruitment_start_date,
  t.recruitment_end_date,
  t.class_start_date,
  t.class_end_date,
  t.status as class_status,
  
  -- 스케줄 그룹 정보
  sg.id as group_id,
  sg.group_name,
  sg.max_participants as group_max_participants,
  sg.price_per_session as group_price,
  
  -- 스케줄 정보
  s.id as schedule_id,
  s.day_of_week,
  s.start_time,
  s.end_time,
  
  -- 통계 정보
  COUNT(DISTINCT e.id) as total_enrollments,
  COUNT(DISTINCT CASE WHEN e.enrollment_status = 'confirmed' THEN e.id END) as confirmed_enrollments,
  COUNT(DISTINCT o.id) as total_occurrences,
  COUNT(DISTINCT CASE WHEN o.status = 'completed' THEN o.id END) as completed_occurrences
  
FROM class_templates t
LEFT JOIN class_schedule_groups sg ON t.id = sg.class_template_id
LEFT JOIN class_schedules s ON sg.id = s.schedule_group_id
LEFT JOIN class_enrollments e ON t.id = e.class_template_id AND sg.id = e.schedule_group_id
LEFT JOIN class_occurrences o ON t.id = o.class_template_id AND sg.id = o.schedule_group_id

WHERE t.is_active = true 
  AND (sg.is_active = true OR sg.is_active IS NULL)
  AND (s.is_active = true OR s.is_active IS NULL)

GROUP BY 
  t.id, t.title, t.description, t.specialty, t.level, t.instructor_id, t.studio_id,
  t.duration_minutes, t.price_per_session, t.max_capacity,
  t.recruitment_start_date, t.recruitment_end_date, t.class_start_date, t.class_end_date, t.status,
  sg.id, sg.group_name, sg.max_participants, sg.price_per_session,
  s.id, s.day_of_week, s.start_time, s.end_time;
```

### 다가오는 수업 뷰

```sql
CREATE VIEW upcoming_occurrences AS
SELECT 
  o.id as occurrence_id,
  o.occurrence_date,
  o.start_time,
  o.end_time,
  o.status,
  COALESCE(o.max_participants, sg.max_participants) as max_participants,
  o.confirmed_enrollments,
  o.attendance_count,
  
  -- 클래스 정보
  t.id as template_id,
  t.title as class_title,
  t.specialty,
  t.level,
  t.instructor_id,
  
  -- 그룹 정보
  sg.group_name,
  
  -- 스케줄 정보
  s.day_of_week,
  
  -- 계산 필드
  (COALESCE(o.max_participants, sg.max_participants) - o.confirmed_enrollments) as available_spots,
  ROUND(o.confirmed_enrollments::decimal / NULLIF(COALESCE(o.max_participants, sg.max_participants), 0) * 100, 1) as occupancy_rate,
  
  -- 시간 정보
  EXTRACT(dow FROM o.occurrence_date) as day_of_week_num,
  o.occurrence_date || ' ' || o.start_time as occurrence_datetime

FROM class_occurrences o
JOIN class_templates t ON o.class_template_id = t.id
JOIN class_schedule_groups sg ON o.schedule_group_id = sg.id
JOIN class_schedules s ON o.class_schedule_id = s.id

WHERE o.occurrence_date >= CURRENT_DATE
  AND o.status IN ('scheduled', 'ongoing')
  AND t.is_active = true

ORDER BY o.occurrence_date, o.start_time;
```

### 수강생 출석 통계 뷰

```sql
CREATE VIEW member_attendance_stats AS
SELECT 
  e.member_id,
  e.class_template_id,
  e.schedule_group_id,
  
  -- 신청 정보
  e.enrollment_status,
  e.enrolled_at,
  
  -- 출석 통계
  COUNT(DISTINCT o.id) as total_classes,
  COUNT(DISTINCT a.id) as attended_classes,
  COUNT(DISTINCT CASE WHEN a.attendance_status = 'present' THEN a.id END) as present_count,
  COUNT(DISTINCT CASE WHEN a.attendance_status = 'late' THEN a.id END) as late_count,
  COUNT(DISTINCT CASE WHEN a.attendance_status = 'absent' THEN a.id END) as absent_count,
  COUNT(DISTINCT CASE WHEN a.attendance_status = 'excused' THEN a.id END) as excused_count,
  
  -- 비율 계산
  ROUND(
    COUNT(DISTINCT CASE WHEN a.attendance_status = 'present' THEN a.id END)::decimal / 
    NULLIF(COUNT(DISTINCT o.id), 0) * 100, 1
  ) as attendance_rate,
  
  -- 날짜 정보
  MIN(o.occurrence_date) as first_class_date,
  MAX(o.occurrence_date) as last_class_date

FROM class_enrollments e
JOIN class_occurrences o ON e.class_template_id = o.class_template_id 
  AND e.schedule_group_id = o.schedule_group_id
LEFT JOIN class_attendances a ON o.id = a.class_occurrence_id 
  AND e.member_id = a.member_id

WHERE e.enrollment_status = 'confirmed'
  AND o.status = 'completed'

GROUP BY e.member_id, e.class_template_id, e.schedule_group_id, 
         e.enrollment_status, e.enrolled_at;
```

## 마이그레이션 전략

기존 데이터를 새로운 스키마로 안전하게 마이그레이션하는 방법입니다.

### Phase 1: 스키마 확장

```sql
-- 1단계: 기존 테이블에 새 컬럼 추가 (NULL 허용)
ALTER TABLE class_templates ADD COLUMN recruitment_start_date DATE;
ALTER TABLE class_templates ADD COLUMN recruitment_end_date DATE;
ALTER TABLE class_templates ADD COLUMN class_start_date DATE;
ALTER TABLE class_templates ADD COLUMN class_end_date DATE;
ALTER TABLE class_templates ADD COLUMN status TEXT DEFAULT 'upcoming';

-- 2단계: 새 테이블 생성
CREATE TABLE class_occurrences (...);
CREATE TABLE class_attendances (...);

-- 3단계: 기존 enrollments 테이블 수정
ALTER TABLE class_enrollments ADD COLUMN schedule_group_id UUID;
ALTER TABLE class_enrollments ADD COLUMN refund_amount DECIMAL(10,2);
ALTER TABLE class_enrollments ADD COLUMN refunded_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE class_enrollments ADD COLUMN refund_reason TEXT;
```

### Phase 2: 데이터 마이그레이션

```sql
-- 기존 클래스 템플릿에 기본 날짜 설정
UPDATE class_templates SET 
  recruitment_start_date = CURRENT_DATE,
  recruitment_end_date = CURRENT_DATE + INTERVAL '7 days',
  class_start_date = CURRENT_DATE + INTERVAL '14 days',
  class_end_date = CURRENT_DATE + INTERVAL '42 days',
  status = 'recruiting'
WHERE recruitment_start_date IS NULL;

-- 기존 enrollments에 schedule_group_id 설정
UPDATE class_enrollments e
SET schedule_group_id = (
  SELECT sg.id 
  FROM class_schedule_groups sg 
  WHERE sg.class_template_id = e.class_template_id
  LIMIT 1
)
WHERE schedule_group_id IS NULL;

-- 기존 스케줄로부터 Occurrence 생성
INSERT INTO class_occurrences (
  class_template_id, schedule_group_id, class_schedule_id,
  occurrence_date, start_time, end_time, max_participants, status
)
SELECT 
  sg.class_template_id,
  s.schedule_group_id,
  s.id,
  -- 다음 4주간 해당 요일 날짜 생성
  generate_series(
    date_trunc('week', t.class_start_date) + 
    CASE s.day_of_week 
      WHEN 'MONDAY' THEN INTERVAL '0 days'
      WHEN 'TUESDAY' THEN INTERVAL '1 day'
      WHEN 'WEDNESDAY' THEN INTERVAL '2 days'
      WHEN 'THURSDAY' THEN INTERVAL '3 days'
      WHEN 'FRIDAY' THEN INTERVAL '4 days'
      WHEN 'SATURDAY' THEN INTERVAL '5 days'
      WHEN 'SUNDAY' THEN INTERVAL '6 days'
    END,
    t.class_end_date,
    INTERVAL '1 week'
  )::date,
  s.start_time,
  s.end_time,
  sg.max_participants,  -- 그룹 정원 사용
  'scheduled'
FROM class_schedules s
JOIN class_schedule_groups sg ON s.schedule_group_id = sg.id
JOIN class_templates t ON sg.class_template_id = t.id
WHERE t.class_start_date IS NOT NULL 
  AND t.class_end_date IS NOT NULL;
```

### Phase 3: 제약 조건 추가

```sql
-- NULL 제약 조건 추가
ALTER TABLE class_templates ALTER COLUMN recruitment_start_date SET NOT NULL;
ALTER TABLE class_templates ALTER COLUMN recruitment_end_date SET NOT NULL;
ALTER TABLE class_templates ALTER COLUMN class_start_date SET NOT NULL;
ALTER TABLE class_templates ALTER COLUMN class_end_date SET NOT NULL;

-- 체크 제약 조건 추가
ALTER TABLE class_templates ADD CONSTRAINT check_template_dates 
  CHECK (recruitment_start_date <= recruitment_end_date);
ALTER TABLE class_templates ADD CONSTRAINT check_class_dates
  CHECK (class_start_date <= class_end_date);
ALTER TABLE class_templates ADD CONSTRAINT check_recruitment_before_class
  CHECK (recruitment_end_date <= class_start_date);

-- 외래 키 역할 확인 (소프트 레퍼런스 유지)
-- 실제 FK 제약은 성능상 추가하지 않음
```

## 성능 고려사항

### 파티셔닝 전략

대용량 데이터 처리를 위한 테이블 파티셔닝을 고려합니다.

```sql
-- Occurrence 테이블 월별 파티셔닝
CREATE TABLE class_occurrences_partitioned (
  LIKE class_occurrences INCLUDING ALL
) PARTITION BY RANGE (occurrence_date);

-- 월별 파티션 생성
CREATE TABLE class_occurrences_2024_12 PARTITION OF class_occurrences_partitioned
  FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

CREATE TABLE class_occurrences_2025_01 PARTITION OF class_occurrences_partitioned  
  FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- 출석 기록 월별 파티셔닝
CREATE TABLE class_attendances_partitioned (
  LIKE class_attendances INCLUDING ALL
) PARTITION BY RANGE (created_at);
```

### 아카이빙 전략

```sql
-- 완료된 수업 데이터 아카이빙
CREATE TABLE class_occurrences_archive (
  LIKE class_occurrences INCLUDING ALL
);

-- 6개월 이전 완료 수업 아카이빙
INSERT INTO class_occurrences_archive
SELECT * FROM class_occurrences 
WHERE status = 'completed' 
  AND occurrence_date < CURRENT_DATE - INTERVAL '6 months';

DELETE FROM class_occurrences 
WHERE status = 'completed' 
  AND occurrence_date < CURRENT_DATE - INTERVAL '6 months';
```

## 주요 변경사항 요약

### 🆕 현재 구현과의 차이점:

1. **category 필드 제거**: specialty(운동 분야)로 통합
2. **max_participants 필드명 통일**: max_group_size → max_participants
3. **schedule_group_id 필수**: 수강 신청시 그룹 기반 신청 필수
4. **그룹별 날짜 관리 제거**: group_start_date, group_end_date 제거 (템플릿 레벨에서만 관리)
5. **curriculum 필드**: JSON 형태로 구조화된 클래스 소개 저장
   - overview: 수업 소개
   - targetAudience: 수업 추천 대상
   - curriculum: 커리큘럼 단계별 정보
   - materials: 준비물
6. **max_participants NULL 허용**: class_occurrences 테이블에서 NULL 가능 (그룹 설정 상속)

이 데이터베이스 스키마는 클래스 라이프사이클의 모든 단계를 효율적으로 관리하면서도, 검색 성능과 운영 편의성을 최적화하도록 설계되었습니다. 특히 인덱스 전략과 뷰 활용을 통해 복잡한 쿼리도 빠르게 처리할 수 있습니다.