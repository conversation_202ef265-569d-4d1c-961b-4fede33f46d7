# Product Overview

## ShallWe - Fitness Class Booking Platform

ShallWe is a comprehensive fitness class booking platform that connects fitness instructors with students. The platform enables instructors to create and manage fitness classes while providing students with an easy way to discover, book, and pay for classes.

### Key Features

- **Instructor Onboarding**: Multi-step onboarding process for fitness instructors including profile setup, specialties, bio, and payment account configuration
- **Class Management**: Instructors can create, edit, and manage fitness classes with detailed scheduling and capacity management
- **Location-Based Discovery**: Classes are organized by subway stations for easy location-based searching
- **Booking System**: Students can browse, book, and pay for fitness classes with integrated payment processing
- **Center Management**: Support for fitness centers with member roles and permissions
- **User Personalization**: Preference-based recommendations using preferred stations, fitness goals, and schedule preferences

### User Types

- **Students**: Browse and book fitness classes, manage bookings and payment history
- **Instructors**: Create and manage classes, view dashboard analytics, handle student enrollments
- **Center Admins**: Manage fitness center operations and instructor memberships
- **Super Admins**: Platform-wide administration

### Core Business Logic

- Subway station-based location system for Seoul metropolitan area
- Multi-specialty fitness class support (Yoga, Pilates, CrossFit, etc.)
- Instructor specialization and experience tracking
- Class capacity management with enrollment status tracking
- Payment integration with PortOne for secure transactions
- Review and rating system for instructors and classes