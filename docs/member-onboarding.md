# 회원 온보딩 시스템 설계

## Requirements (요구사항)

### 기능 요구사항

#### 기본 온보딩 플로우
- 강사는 `/instructor/onboarding` 경로에서 별도 온보딩 진행 (기존 구현됨)
- 일반 수강생 회원(`member.role=STUDENT`)은 첫 가입 시 온보딩 절차 필요
- 온보딩 완료 전까지는 서비스 주요 기능 이용 제한

#### 수집 정보 목록
1. **기대하는 목표** (필수)
   - 1. 기초 체력
   - 2. 근력 강화  
   - 3. 체형 교정
   - 4. 체중 감량
   - 5. 재활
   - 6. 기타
   - 복수 선택 가능

2. **운동 희망 지역** (필수)
   - 서울시 지하철역 중 선택
   - 검색 기능 필수
   - 자동 완성 기능 필수
   - N개 선택 가능

3. **성별** (필수)
   - 남성/여성/기타 중 선택
   - `members.gender` 필드 업데이트

4. **원하는 운동** (필수)
   - `class_templates.specialty` 기반 선택지
   - 요가, 필라테스, 헬스/웨이트, 크로스핏, 수영, 복싱, 댄스, 러닝, 클라이밍, 무술/격투기 등
   - 복수 선택 가능

5. **원하는 요일/시간대** (필수)
   - 요일: 월~일 복수 선택
   - 시간대: 새벽(05:00-09:00), 오전(09:00-12:00), 오후(12:00-18:00), 저녁(18:00-22:00), 심야(22:00-05:00)
   - 복수 선택 가능

6. **수준** (필수)
   - `class_templates.level` 기반
   - 초급자(beginner), 중급자(intermediate), 고급자(advanced), 모든 레벨(all_levels)
   - 단일 선택

### 비기능 요구사항

#### 사용자 경험
- 모바일 우선 반응형 디자인
- 단계별 진행률 표시
- 직관적인 UI/UX (기존 강사 온보딩과 일관성)
- 이전/다음 단계 네비게이션
- 실시간 유효성 검사

#### 성능
- 지하철역 검색 응답 시간 < 300ms
- 자동완성 디바운싱 적용 (300ms)
- 단계별 데이터 로컬 저장 (새로고침 시 복구)

#### 데이터 정합성
- 온보딩 완료 시 members 테이블 업데이트
- 선호도 데이터는 별도 테이블에 저장
- 트랜잭션 처리로 데이터 일관성 보장

---

## Designs (설계)

### 데이터베이스 설계

#### 새로운 테이블: member_preferences
```sql
CREATE TABLE member_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL, -- members.id 참조
  
  -- 운동 목표
  fitness_goals JSONB NOT NULL, -- ["기초체력", "근력강화"] 형태
  
  -- 선호 지역 (지하철역)
  preferred_stations JSONB NOT NULL, -- [{"id": "222", "name": "강남", "line": "2호선"}] 형태
  
  -- 선호 운동 종목
  preferred_specialties JSONB NOT NULL, -- ["YOGA", "PILATES"] 형태
  
  -- 선호 요일
  preferred_days JSONB NOT NULL, -- ["MONDAY", "WEDNESDAY", "FRIDAY"] 형태
  
  -- 선호 시간대
  preferred_time_slots JSONB NOT NULL, -- ["오전", "저녁"] 형태
  
  -- 운동 수준
  fitness_level TEXT NOT NULL, -- ClassLevel enum
  
  -- 메타데이터
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 온보딩 완료 상태 확인
온보딩 완료 여부는 `member_preferences` 테이블에 레코드 존재 여부로 판단:
- 레코드 존재 = 온보딩 완료
- 레코드 없음 = 온보딩 미완료
- 완료 시점은 `member_preferences.created_at` 사용

### API 설계

#### 온보딩 데이터 저장 API
```typescript
// POST /api/member/onboarding
interface OnboardingSubmission {
  fitnessGoals: string[];
  preferredStations: SubwayStation[];
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  preferredSpecialties: string[];
  preferredDays: string[];
  preferredTimeSlots: string[];
  fitnessLevel: 'beginner' | 'intermediate' | 'advanced' | 'all_levels';
}
```

#### 온보딩 상태 확인 API
```typescript
// GET /api/member/onboarding/status
interface OnboardingStatus {
  isCompleted: boolean;
  completedAt?: string; // member_preferences.created_at
  currentStep?: number;
}
```

### 페이지 구조 설계

#### 라우팅
- `/onboarding` - 학생 온보딩 메인 페이지
- `/onboarding/complete` - 온보딩 완료 페이지

#### 컴포넌트 구조
```
src/app/(main)/onboarding/
├── page.tsx                 # 메인 온보딩 페이지
├── complete/
│   └── page.tsx             # 완료 페이지
└── components/
    ├── OnboardingGoalsStep.tsx      # 운동 목표 선택
    ├── OnboardingLocationStep.tsx   # 지역 선택 (지하철역)
    ├── OnboardingGenderStep.tsx     # 성별 선택
    ├── OnboardingSpecialtyStep.tsx  # 운동 종목 선택
    ├── OnboardingScheduleStep.tsx   # 요일/시간대 선택
    ├── OnboardingLevelStep.tsx      # 수준 선택
    ├── StepProgress.tsx             # 진행률 표시
    └── SubwayStationSearch.tsx      # 지하철역 검색 컴포넌트
```

### UI/UX 설계

#### 단계별 구성 (6단계)
1. **Step 1: 운동 목표** - 그리드 카드 형태로 6가지 목표 선택
2. **Step 2: 희망 지역** - 검색창 + 자동완성 + 선택된 역 목록
3. **Step 3: 성별** - 3개 버튼 형태
4. **Step 4: 운동 종목** - 아이콘과 함께 그리드 형태
5. **Step 5: 요일/시간대** - 요일 선택 + 시간대 선택 (각각 체크박스)
6. **Step 6: 수준** - 4개 라디오 버튼

#### 공통 UI 요소
- 상단: 진행률 바 (6단계)
- 중간: 단계별 컨텐츠
- 하단: 이전/다음 버튼 (첫/마지막 단계는 해당 버튼 숨김)
- 모바일 최적화 (기존 강사 온보딩과 동일한 스타일)

### 데이터 플로우

#### 온보딩 시작
1. 사용자 로그인 후 `member_preferences` 테이블에서 레코드 존재 여부 확인
2. 레코드가 없으면 온보딩 페이지로 리다이렉트
3. 로컬 스토리지에서 이전 진행 상태 복구 (있는 경우)

#### 단계별 진행
1. 각 단계 완료 시 로컬 스토리지에 데이터 저장
2. 유효성 검사 통과 시 다음 단계 활성화
3. 이전 단계로 돌아가기 허용

#### 온보딩 완료
1. 6단계 모든 데이터 수집 완료
2. API 호출하여 `member_preferences` 테이블에 데이터 저장
3. `members.gender` 필드 업데이트
4. 완료 페이지로 이동 후 메인 페이지로 리다이렉트

---

## Tasks (구현 작업)

### Phase 1: 기반 구조 및 데이터 준비

#### 1.1 데이터베이스 스키마 작업
- [ ] `member_preferences` 테이블 스키마 정의 (Drizzle ORM)
- [ ] 테이블 관계 정의 (members ↔ member_preferences)
- [ ] 마이그레이션 스크립트 작성

#### 1.2 지하철역 데이터 준비
- [ ] `src/lib/data/subway-stations.ts` 파일 생성
- [ ] 서울시 전체 지하철역 데이터 정의 (약 300개 역)
- [ ] 검색 및 자동완성 함수 구현
- [ ] 호선별 그룹핑 함수 구현

#### 1.3 상수 및 타입 정의
- [ ] 온보딩 관련 상수 정의 (`src/lib/constants/onboarding.ts`)
- [ ] TypeScript 인터페이스 정의
- [ ] Zod 스키마 정의 (유효성 검사용)

### Phase 2: API 개발

#### 2.1 온보딩 API 엔드포인트
- [ ] `src/app/api/member/onboarding/route.ts` 생성
- [ ] POST: 온보딩 데이터 저장 로직
- [ ] GET: 온보딩 상태 확인 로직
- [ ] 트랜잭션 처리 및 에러 핸들링

#### 2.2 지하철역 검색 API
- [ ] `src/app/api/subway-stations/search/route.ts` 생성
- [ ] 검색어 기반 역 목록 반환
- [ ] 성능 최적화 (캐싱, 인덱싱)

### Phase 3: UI 컴포넌트 개발

#### 3.1 공통 컴포넌트
- [ ] `StepProgress.tsx` - 진행률 표시 컴포넌트
- [ ] `OnboardingLayout.tsx` - 온보딩 전용 레이아웃
- [ ] `SubwayStationSearch.tsx` - 지하철역 검색 컴포넌트

#### 3.2 단계별 컴포넌트
- [ ] `OnboardingGoalsStep.tsx` - 운동 목표 선택
- [ ] `OnboardingLocationStep.tsx` - 지역 선택
- [ ] `OnboardingGenderStep.tsx` - 성별 선택
- [ ] `OnboardingSpecialtyStep.tsx` - 운동 종목 선택
- [ ] `OnboardingScheduleStep.tsx` - 요일/시간대 선택
- [ ] `OnboardingLevelStep.tsx` - 수준 선택

### Phase 4: 페이지 및 라우팅

#### 4.1 온보딩 페이지
- [ ] `src/app/(main)/onboarding/page.tsx` 메인 페이지
- [ ] 단계별 상태 관리 (useState/useReducer)
- [ ] 로컬 스토리지 연동
- [ ] 유효성 검사 로직

#### 4.2 완료 페이지
- [ ] `src/app/(main)/onboarding/complete/page.tsx`
- [ ] 성공 메시지 및 다음 단계 안내
- [ ] 메인 페이지로 자동 리다이렉트

#### 4.3 라우팅 및 미들웨어
- [ ] 온보딩 미완료 사용자 리다이렉트 로직
- [ ] 보호된 라우트 설정
- [ ] 온보딩 완료 후 접근 제한

### Phase 5: 통합 및 최적화

#### 5.1 사용자 경험 개선
- [ ] 반응형 디자인 테스트 및 조정
- [ ] 로딩 상태 및 에러 처리
- [ ] 애니메이션 및 트랜지션 추가

#### 5.2 성능 최적화
- [ ] 지하철역 검색 디바운싱
- [ ] 컴포넌트 메모이제이션
- [ ] 번들 크기 최적화

#### 5.3 테스트 및 QA
- [ ] 단위 테스트 작성
- [ ] 통합 테스트 작성
- [ ] E2E 테스트 작성
- [ ] 접근성 테스트

### Phase 6: 배포 및 모니터링

#### 6.1 배포 준비
- [ ] 환경별 설정 확인
- [ ] 데이터베이스 마이그레이션 실행
- [ ] 프로덕션 배포

#### 6.2 모니터링 및 분석
- [ ] 온보딩 완료율 추적
- [ ] 단계별 이탈률 분석
- [ ] 성능 모니터링 설정

---

### 우선순위 및 일정

#### 높은 우선순위 (1-2주)
- Phase 1: 기반 구조 및 데이터 준비
- Phase 2: API 개발
- Phase 3.1: 공통 컴포넌트

#### 중간 우선순위 (2-3주)
- Phase 3.2: 단계별 컴포넌트
- Phase 4: 페이지 및 라우팅

#### 낮은 우선순위 (3-4주)
- Phase 5: 통합 및 최적화
- Phase 6: 배포 및 모니터링

### 의존성 및 리스크

#### 기술적 의존성
- 기존 강사 온보딩 시스템과의 일관성 유지
- Supabase 인증 시스템과의 연동
- 기존 디자인 시스템 활용

#### 잠재적 리스크
- 지하철역 데이터 정확성 및 최신성
- 온보딩 단계 추가 시 복잡성 증가
- 모바일 환경에서의 성능 이슈

#### 완화 방안
- 지하철역 데이터는 공식 API 연동 검토
- 단계별 모듈화로 확장성 확보
- 성능 테스트 및 최적화 지속 실행