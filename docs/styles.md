# Design System Style Guide

## Overview

This style guide documents the design system built with Tailwind CSS, shadcn/ui components, and custom CSS variables. The system supports both light and dark themes with a cohesive color palette and consistent spacing.

## Color System

### Primary Colors
- **Primary**: `#6114cc` (Purple) - Used for main actions, links, and brand elements
- **Primary Foreground**: Light: `#ffffff`, Dark: `#ffa6ac`

### Semantic Colors
- **Background**: Light: `#fafafa`, Dark: `#18181b`
- **Foreground**: Light: `#09090b`, Dark: `#fafafa`
- **Card**: Light: `#ffffff`, Dark: `#09090b`
- **Secondary**: Light: `#f4f4f5`, Dark: `#27272a`
- **Muted**: Light: `#f1f5f9`, Dark: `#27272a`
- **Accent**: Light: `#f4f4f5`, Dark: `#27272a`
- **Destructive**: Light: `#e7000b`, Dark: `#ff6467`

### Border & Input Colors
- **Border**: Light: `#e4e4e7`, Dark: `#ffffff1a`
- **Input**: Light: `#e4e4e7`, Dark: `#ffffff26`
- **Ring**: `#6114cc` (Focus states)

### Chart Colors
- **Chart 1**: Light: `#f54900`, Dark: `#1447e6`
- **Chart 2**: Light: `#009689`, Dark: `#00bc7d`
- **Chart 3**: Light: `#104e64`, Dark: `#fe9a00`
- **Chart 4**: Light: `#ffb900`, Dark: `#ad46ff`
- **Chart 5**: Light: `#fe9a00`, Dark: `#ff2056`

### Custom Colors
- **Kakao**: `#f9df4a` - For Kakao integration elements
- **Logo Gradient**: `from-[#5530FF] to-[#9D43FF]`

## Typography

### Font Families
- **Sans**: `var(--font-sans)` - Primary font for UI text
- **Mono**: `var(--font-mono)` - Code and monospace text
- **Serif**: `var(--font-serif)` - Decorative text

### Logo Styling
```css
.logo {
  @apply bg-gradient-to-r from-[#5530FF] to-[#9D43FF] bg-clip-text text-transparent;
}
```

## Spacing & Layout

### Border Radius
- **Small**: `calc(var(--radius) - 4px)` (0.125rem)
- **Medium**: `calc(var(--radius) - 2px)` (0.25rem)
- **Large**: `var(--radius)` (0.5rem)
- **Extra Large**: `calc(var(--radius) + 4px)` (0.75rem)

### Shadows
The system includes a comprehensive shadow scale:
- **2xs**: `0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.05)`
- **xs**: `0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.05)`
- **sm**: Multi-layered subtle shadow
- **md**: Medium depth shadow
- **lg**: Large depth shadow
- **xl**: Extra large shadow
- **2xl**: Maximum depth shadow

## Component Guidelines

### Buttons
Use the `Button` component with these variants:
- **Primary**: Default purple background
- **Secondary**: Muted background
- **Destructive**: Red background for dangerous actions
- **Ghost**: Transparent background
- **Link**: Text-only appearance

### Cards
Cards use the card background color and include subtle shadows:
```tsx
<Card className="shadow-sm">
  <CardHeader>
    <CardTitle>Title</CardTitle>
  </CardHeader>
  <CardContent>
    Content here
  </CardContent>
</Card>
```

### Forms
Form elements follow consistent styling:
- **Input**: Uses input background color with border
- **Label**: Uses foreground color
- **Focus**: Purple ring color for focus states

### Navigation
- **Sidebar**: Dedicated sidebar color scheme
- **Navigation Menu**: Uses accent colors for hover states
- **Breadcrumbs**: Muted colors for hierarchy

## Dark Mode

Dark mode is implemented using the `.dark` class and CSS custom properties. All components automatically adapt to the dark theme.

### Implementation
```tsx
// Toggle dark mode
<html className={isDark ? 'dark' : ''}>
```

## Accessibility

### Focus States
All interactive elements include focus rings using the `--ring` color:
```css
* {
  @apply outline-ring/50;
}
```

### Color Contrast
The color system ensures sufficient contrast ratios:
- Light mode: Dark text on light backgrounds
- Dark mode: Light text on dark backgrounds

## Usage Examples

### Basic Layout
```tsx
export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <header className="border-b border-border">
        <div className="container mx-auto px-4">
          <h1 className="logo text-2xl font-bold">Brand Name</h1>
        </div>
      </header>
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
    </div>
  )
}
```

### Card Component
```tsx
<Card className="shadow-md">
  <CardHeader>
    <CardTitle className="text-card-foreground">Card Title</CardTitle>
    <CardDescription className="text-muted-foreground">
      Card description
    </CardDescription>
  </CardHeader>
  <CardContent>
    <p className="text-card-foreground">Card content goes here.</p>
  </CardContent>
  <CardFooter>
    <Button>Primary Action</Button>
    <Button variant="secondary">Secondary Action</Button>
  </CardFooter>
</Card>
```

### Form Example
```tsx
<form className="space-y-4">
  <div>
    <Label htmlFor="email">Email</Label>
    <Input 
      id="email" 
      type="email" 
      placeholder="Enter your email"
      className="mt-1"
    />
  </div>
  <div>
    <Label htmlFor="message">Message</Label>
    <Textarea 
      id="message" 
      placeholder="Your message"
      className="mt-1"
    />
  </div>
  <Button type="submit" className="w-full">
    Submit
  </Button>
</form>
```

## Best Practices

### Color Usage
1. Use semantic color variables instead of hardcoded values
2. Ensure proper contrast in both light and dark modes
3. Use the primary color sparingly for important actions
4. Leverage muted colors for secondary information

### Component Composition
1. Use the `cn()` utility function for conditional classes
2. Compose components using shadcn/ui primitives
3. Maintain consistent spacing using Tailwind's spacing scale
4. Apply shadows consistently across similar components

### Responsive Design
1. Use Tailwind's responsive prefixes (`sm:`, `md:`, `lg:`, `xl:`)
2. Ensure touch targets are at least 44px on mobile
3. Test components in both light and dark modes
4. Verify accessibility across all breakpoints

## Available Components

The following shadcn/ui components are available:
- Accordion, Alert, Alert Dialog, Avatar, Badge
- Breadcrumb, Button, Calendar, Card, Carousel
- Chart, Checkbox, Collapsible, Command, Context Menu
- Dialog, Drawer, Dropdown Menu, Form, Hover Card
- Input, Input OTP, Label, Menubar, Navigation Menu
- Pagination, Popover, Progress, Radio Group, Resizable
- Scroll Area, Select, Separator, Sheet, Sidebar
- Skeleton, Slider, Sonner, Switch, Table
- Tabs, Textarea, Toggle, Toggle Group, Tooltip

Each component follows the design system's color scheme and spacing conventions automatically.