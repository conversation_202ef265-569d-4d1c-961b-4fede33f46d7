## **📄 쉘위 예약금 결제 페이지 정책서** 

---

### **1\. 페이지 기본 정보**

| 항목 | 내용 |
| ----- | ----- |
| 페이지명 | 예약금 결제 페이지 |
| URL | `/payment/deposit` (예정) |
| 접근 방식 | 예약 시간 선택 완료 시 진입 (수업 상세 \> 시간 선택 \> 예약 신청 클릭 시 이동) |
| 로그인 필요 | O (로그인 상태 확인 후 비로그인 시 로그인 플로우 유도) |
| 구성 | 수업/센터/시간 정보 \+ 결제 정보 요약 \+ 프로모션/쿠폰 적용 \+ 약관 동의 \+ 결제 버튼 |

---

### **2\. UI(FE) 구성 정책**

#### **📌 상단 수업 정보 영역**

| 항목 | 내용 |
| ----- | ----- |
| 수업명 | 선택한 수업명 표시 (ex: 초급 체력 클래스) |
| 강사명 | ex: 최하얀 코치님 |
| 센터명 및 위치 | 공덕 쉘위 센터 (마포구 공덕동…) |
| 선택한 시간대 | 예: 매주 화/목 오전 10:30 |
| 정원 정보 | 4명 중 2명 예약됨 등 상태 표시 |

#### **📌 결제 정보 영역**

| 항목 | 내용 |
| ----- | ----- |
| 결제 방식 | 예약금 (총 결제 금액의 15%) |
| 총 수업비 | 200,000원 |
| 예약금 결제 금액 | 30,000원 |
| 포인트 사용 선택 | 사용 가능 포인트 노출 후 입력창 (예: 최대 5,000P 사용 가능) |
| 최종 결제 금액 | 포인트 및 쿠폰 적용 후 최종 금액 실시간 반영 |

#### **📌 할인 코드 / 쿠폰 적용 영역**

| 항목 | 내용 |
| ----- | ----- |
| 프로모션 코드 | 수동 입력창, 유효성 검사 후 적용 (1개만 적용 가능) |
| 쿠폰 | 보유 쿠폰 목록 중 1개 선택 가능, 중복 적용 불가 |
| 적용 시 | 할인 금액 및 적용 내용 UI에 반영 |

#### **📌 결제 수단 선택 영역**

| 항목 | 내용 |
| ----- | ----- |
| 기본 수단 | 마지막 사용 수단 자동 선택 (ex: 네이버페이) |
| 선택 옵션 | 네이버페이, 카드 결제 등 |
| 저장 방식 | 로컬 또는 서버에 마지막 결제 수단 저장 |

#### **📌 약관 영역**

| 항목 | 내용 |
| ----- | ----- |
| 약관 노출 방식 | ‘약관 보기’ 클릭 시 바텀시트로 상세 약관 노출 |
| 약관 체크 | 기본 체크 상태. 단, ‘보기’만 가능하고 동의는 추가로 요구하지 않음 |

#### **📌 CTA**

| 항목 | 내용 |
| ----- | ----- |
| 버튼명 | 결제하기 |
| 동작 | 결제 API 호출 및 처리 후 결과 페이지 이동 또는 완료 토스트 노출 |

---

### **3\. 약관 바텀시트 문구 예시 (초안)**

`[예약금 결제 유의사항]`

`- 예약금은 총 수업비의 15%로, 수업 확정 후 잔여 금액은 센터에서 결제하셔야 합니다.`  
`- 예약 취소는 수업 확정 전까지 자유롭게 가능하며, 결제금액은 100% 환불됩니다.`  
`- 수업 확정 후에는 취소 시 센터 운영 규정에 따라 환불 여부가 달라질 수 있습니다.`  
`- 본 수업은 단체 운동 수업으로, 수업 확정은 정원 모집 기준으로 진행됩니다.`

---

### **4\. BE 및 동작 로직**

| 항목 | 정책 |
| ----- | ----- |
| 결제 처리 | 결제 버튼 클릭 시 PG사 결제 요청 → 결제 성공 시 예약 확정 API 호출 |
| 포인트 처리 | 유효성 체크 후 사용 가능 범위 내 차감 |
| 프로모션/쿠폰 처리 | 유효성 검사 후 할인 금액 적용 및 중복 사용 불가 처리 |
| 예약 기록 | 수업 ID, 시간, 금액, 포인트, 결제 수단, 사용자 ID로 서버에 예약 데이터 저장 |
| 실패 처리 | 결제 실패 시 에러 메시지 및 재시도 버튼 노출 (예시: "결제에 실패했습니다. 다시 시도해주세요.") |

---

### **5\. 공통 및 예외 처리 정책**

| 항목 | 내용 |
| ----- | ----- |
| 로그인 여부 | 미로그인 시 로그인 플로우 유도 후 해당 페이지 복귀 |
| 쿠폰/코드 중복 | 중복 사용 불가, 가장 마지막에 적용한 항목만 유효 |
| 결제 실패 | 토스트 알림 \+ 페이지 유지, 실패 원인 메시지 (ex: 한도 초과, 네트워크 오류 등) |
| 수업 정원 초과 | 결제 직전 정원 상태 확인 → 초과 시 "예약이 마감되었습니다" 팝업 |
| 서버 응답 지연 | 스켈레톤 UI 또는 로딩 스피너 표시로 처리 |

