# PR Description 템플릿

## Summary

[기능에 대한 간단한 요약. 무엇을 구현했는지 한 문장으로 설명]

[더 자세한 구현 내용. 몇 단계에 걸쳐 어떤 기능들을 완료했는지 설명]

## 구현 내용

### Phase 1: [단계명] ✅
- [주요 구현 사항 1]
- [주요 구현 사항 2]

### Phase 2: [단계명] ✅  
- **[컴포넌트명 1]**: [구현 내용]
- **[컴포넌트명 2]**: [구현 내용]
- **[컴포넌트명 3]**: [구현 내용]

### Phase 3: [단계명] ✅
- `[경로 1]`: [구현 내용]
- `[경로 2]`: [구현 내용]

### Phase 4: [단계명] ✅
- `[API 엔드포인트 1]`: [구현 내용]
- `[라이브러리/파일명]`: [구현 내용]
  - [세부 기능 1]
  - [세부 기능 2]
  - [세부 기능 3]

### Phase 5: [단계명] ✅
- [구현 내용 1]
- [구현 내용 2]
- [구현 내용 3]

## 주요 기능

### [핵심 기능 1]
- [기능 설명]
- [기술적 구현 방법]

### [핵심 기능 2] 
- **[세부 기능 1]**: [처리 방법]
- **[세부 기능 2]**: [처리 방법]
- **[세부 기능 3]**: [처리 방법]

### [핵심 기능 3]
- [보안/성능/사용성 관련 개선사항]

### [핵심 기능 4]
- [사용자 경험 관련 기능]
- [구체적인 UX 개선 내용]

## 기술 스택

- **Frontend**: [사용한 프론트엔드 기술]
- **Forms**: [폼 관련 라이브러리]
- **Auth**: [인증 시스템]
- **Database**: [데이터베이스]
- **Routing**: [라우팅 시스템]

## Issue 대비 변경사항

### ⚠️ 요구사항 조정
- **[원래 요구사항]** → **[변경된 요구사항]**
  - [변경 이유 설명]

### ➕ 추가 구현
- [추가로 구현한 기능 1]
- [추가로 구현한 기능 2]
- [추가로 구현한 기능 3]

## Test plan

- [x] [테스트 항목 1]
- [x] [테스트 항목 2]  
- [x] [테스트 항목 3]
- [x] [테스트 항목 4]
- [x] [테스트 항목 5]
- [x] [테스트 항목 6]

## 스크린샷 (선택사항)

### Before/After 비교
[구현 전후 비교 이미지]

### 주요 화면
[주요 기능 화면 캡처]

## 관련 이슈

- Closes #[이슈번호] [이슈 제목]
- Related to #[관련이슈번호] [관련 이슈 제목]

## 추가 고려사항

### 성능
- [성능 관련 고려사항이나 개선점]

### 보안
- [보안 관련 구현 사항]

### 접근성
- [접근성 개선 사항]

### 향후 계획
- [향후 개선하거나 추가할 기능]

---

🤖 Generated with [Claude Code](https://claude.ai/code)