# 개발 체크리스트

## Phase별 완료 체크리스트

### Phase 1: 타입 정의 및 스키마
- [ ] TypeScript 인터페이스 정의 완료
- [ ] Zod 스키마 정의 완료
- [ ] 기본 상수 및 유틸리티 타입 정의
- [ ] 타입 체크 통과 (npx tsc --noEmit)
- [ ] 관련 문서 업데이트

### Phase 2: UI 컴포넌트
- [ ] 모든 계획된 컴포넌트 구현 완료
- [ ] Props 인터페이스 정의
- [ ] 이벤트 핸들러 구현
- [ ] 에러 상태 처리
- [ ] 접근성 속성 추가 (aria-label, role 등)
- [ ] 반응형 스타일 적용
- [ ] 컴포넌트 단위 테스트 가능

### Phase 3: 페이지 구현
- [ ] 레이아웃 컴포넌트 구현
- [ ] 페이지 컴포넌트 구현
- [ ] SEO 메타데이터 설정
- [ ] 라우팅 설정 완료
- [ ] 컴포넌트 조립 및 통합
- [ ] 페이지 레벨 에러 처리

### Phase 4: API 구현
- [ ] 모든 API 엔드포인트 구현
- [ ] 요청/응답 스키마 검증
- [ ] 에러 처리 로직 구현
- [ ] 인증/권한 처리
- [ ] API 문서 작성
- [ ] Postman/Thunder Client 테스트 완료

### Phase 5: 통합 및 미들웨어
- [ ] 미들웨어 로직 구현
- [ ] 라우트 보호 설정
- [ ] 전체 플로우 통합 테스트
- [ ] 상태별 리다이렉트 테스트
- [ ] 에러 시나리오 테스트

## 품질 보증 체크리스트

### 코드 품질
- [ ] TypeScript 컴파일 오류 0개
- [ ] ESLint 경고 해결
- [ ] Prettier 포맷팅 적용
- [ ] 사용하지 않는 import 제거
- [ ] 콘솔 로그 제거 (개발용 제외)
- [ ] TODO/FIXME 주석 처리

### 빌드 및 배포
- [ ] `npm run build` 성공
- [ ] 빌드 사이즈 최적화 확인
- [ ] 환경별 설정 분리
- [ ] 환경 변수 설정 완료
- [ ] 소스맵 생성 확인

### 테스트
- [ ] 주요 기능 수동 테스트 완료
- [ ] 단위 테스트 작성 (가능한 경우)
- [ ] 통합 테스트 시나리오 검증
- [ ] 에러 케이스 테스트
- [ ] 브라우저 호환성 테스트

### 보안
- [ ] 입력값 검증 (클라이언트/서버 양쪽)
- [ ] XSS 방지 처리
- [ ] CSRF 토큰 처리
- [ ] 민감 정보 로깅 방지
- [ ] 적절한 HTTP 상태 코드 사용
- [ ] Rate Limiting 설정 (필요한 경우)

### 성능
- [ ] 불필요한 리렌더링 최소화
- [ ] 이미지 최적화 (있는 경우)
- [ ] 번들 크기 확인
- [ ] 로딩 상태 처리
- [ ] 에러 바운더리 설정

### 사용자 경험
- [ ] 로딩 인디케이터 표시
- [ ] 성공/실패 피드백 제공
- [ ] 적절한 에러 메시지
- [ ] 반응형 디자인 확인
- [ ] 키보드 네비게이션 지원
- [ ] 포커스 관리

## 커밋 체크리스트

### 커밋 전 확인사항
- [ ] 변경된 파일들이 의도한 내용인지 확인
- [ ] 커밋 대상이 아닌 파일 제외 (git add 신중히)
- [ ] 디버깅용 코드 제거
- [ ] 주석 정리
- [ ] 빌드 테스트 통과

### 커밋 메시지
- [ ] 컨벤션 준수 (feat/fix/refactor/docs 등)
- [ ] 구체적이고 명확한 설명
- [ ] 영문 작성 시 첫 글자 대문자
- [ ] 명령형 동사 사용 (Add, Fix, Update 등)
- [ ] 50자 이내 제목
- [ ] 본문에 상세 설명 (필요한 경우)

### 커밋 단위
- [ ] 하나의 논리적 변경사항만 포함
- [ ] 빌드가 깨지지 않는 단위
- [ ] 리뷰하기 적절한 크기
- [ ] 롤백 가능한 단위

## PR 생성 체크리스트

### PR 생성 전
- [ ] 모든 계획된 기능 구현 완료
- [ ] 전체 빌드 테스트 통과
- [ ] 주요 기능 테스트 완료
- [ ] 코드 리뷰 준비 완료
- [ ] 문서 업데이트 완료

### PR 설명
- [ ] 명확한 제목 작성
- [ ] 구현 내용 상세 설명
- [ ] 변경사항 및 이유 명시
- [ ] 테스트 계획 포함
- [ ] 스크린샷 첨부 (UI 변경 시)
- [ ] 관련 이슈 연결

### PR 설정
- [ ] 적절한 리뷰어 지정
- [ ] 라벨 설정
- [ ] 마일스톤 연결 (해당되는 경우)
- [ ] 프로젝트 보드 연결 (해당되는 경우)

## 이슈 관리 체크리스트

### 이슈 업데이트
- [ ] 완료 조건 체크박스 업데이트
- [ ] 구현 내용과 실제 결과 동기화
- [ ] 변경된 요구사항 반영
- [ ] 추가 구현 사항 명시
- [ ] 관련 링크 업데이트

### 이슈 완료
- [ ] 모든 요구사항 충족 확인
- [ ] 완료 조건 모두 체크
- [ ] PR 링크 연결
- [ ] 최종 상태 업데이트
- [ ] 후속 이슈 생성 (필요한 경우)

## 배포 체크리스트

### 배포 전 확인
- [ ] 스테이징 환경 테스트 완료
- [ ] 데이터베이스 마이그레이션 계획
- [ ] 환경 변수 설정 확인
- [ ] 의존성 버전 확인
- [ ] 백업 계획 수립

### 배포 후 확인
- [ ] 프로덕션 환경 정상 동작 확인
- [ ] 모니터링 대시보드 확인
- [ ] 에러 로그 모니터링
- [ ] 사용자 피드백 수집 준비
- [ ] 롤백 계획 준비

## 문서화 체크리스트

### 코드 문서화
- [ ] 복잡한 로직에 주석 추가
- [ ] API 문서 업데이트
- [ ] README 업데이트 (필요한 경우)
- [ ] 컴포넌트 Props 문서화
- [ ] 설정 파일 문서화

### 사용자 문서화
- [ ] 사용자 가이드 작성 (필요한 경우)
- [ ] FAQ 업데이트
- [ ] 변경사항 공지 준비
- [ ] 트러블슈팅 가이드

## 코드 리뷰 체크리스트

### 리뷰 요청 시
- [ ] 리뷰 포인트 명시
- [ ] 중요한 변경사항 하이라이트
- [ ] 질문사항 명시
- [ ] 테스트 방법 안내

### 리뷰 대응
- [ ] 모든 피드백에 응답
- [ ] 변경사항 반영 또는 반박 근거 제시
- [ ] 추가 테스트 수행
- [ ] 문서 업데이트 (필요한 경우)

## 유지보수 체크리스트

### 기술 부채 관리
- [ ] 임시 해결책 정리
- [ ] 성능 개선 포인트 식별
- [ ] 리팩토링 우선순위 설정
- [ ] 테스트 커버리지 개선

### 모니터링
- [ ] 에러 발생 패턴 분석
- [ ] 성능 메트릭 모니터링
- [ ] 사용자 행동 분석
- [ ] 보안 취약점 점검