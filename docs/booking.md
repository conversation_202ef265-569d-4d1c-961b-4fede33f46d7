# **🗂️ 쉘위 회원용 수업 상세 \> 시간 예약 페이지 정책서 (최종안)**

## **1\. 페이지 개요**

| 항목 | 내용 |
| ----- | ----- |
| 페이지명 | 수업 상세 \> 시간 예약 |
| URL | `/classes/{id}` |
| 콘텐츠 단위 | 운동 종류 × 강사 × 클래스 수준별 ID |
| 접근 조건 | 로그인 필요 |
| 페이지 목적 | 회원이 자신에게 맞는 수업 요일/시간을 선택하고, 예약금을 결제하여 신청하는 흐름 제공 |
---

## **2\. 주요 안내 문구 정리**

| 항목 | 문구 |
| ----- | ----- |
| 모집 후 일정 안내 | “수업 모집 완료 후 평균 7일 이내 시작됩니다.” |
| 일정 유동성 안내 | “수업 시작일은 변경될 수 있습니다. 트레이너가 별도 안내드려요.” |
| 하단 안내 고정 문구 | “수업 확정 전까지는 예약금 전액 환불 가능합니다.” |

---

## **3\. 기능 구조**

### **3.1 수업 기본 정보 영역**

| 항목 | 설명 |
| ----- | ----- |
| 수업명 | 예: 운동 경험이 없는 초보자를 위한 기초 수업 |
| 강사명/경력 | 예: 최하얀 코치 / 8년차 |
| 센터 위치 | 예: 턴온 피트니스 센터 (공덕역 1번 출구 5분) |

---

### **3.2 예약 가능한 수업 시간대 목록**

| 항목 | 설명 |
| ----- | ----- |
| 수업 요일/시간 | 예: 월/목, 10:00–11:00 |
| 정원 정보 | 현재 신청자 수 / 정원 (예: 3/4명) |
| 신청자 정보 | 신청자 요약 (예: 45–54세 여성 2명) |
| **수업 시작 예정일 표시** (📌신규) | ● 위치: 각 수업 카드 하단에 “시작 예정일: ○월 ○일 (평균 기준)” 텍스트 노출 ● 계산 방식: 현재 시간 기준 \+ 다다음주 시작일 자동 계산 월/목 수업인 경우,  7/27(일) 기준, 8/4(월) 시작 예정 7/28(월) 기준, 8/11(월) 시작 예정 |
| 선택 UI | 단일 수업 시간 선택만 가능 (멀티 선택 불가) |

---

### **3.3 CTA 정책**

| 항목 | 내용 |
| ----- | ----- |
| 기본 상태 | 수업 시간 선택 전까지 비활성화 (회색, 클릭 불가) |
| 활성 조건 | 수업 시간 1개 선택 시, `예약하기` 버튼 활성화 (보라색) |
| 버튼 텍스트 | 기본: `예약하기` 정원 초과 선택 시: `예약 대기 신청하기 **MVP 미포함` |
| 클릭 시 | 결제 안내 팝업 (`예약금 결제하기`)로 연결 |
| UI 효과 | 버튼 활성화 시 강조 애니메이션(페이드인), 클릭 시 ripple 효과 |

### **3.4 예약금 결제 팝업**

| 항목 | 설명 |
| ----- | ----- |
| 수업 요약 | 선택한 수업 요일, 시간, 강사, 센터 정보 재노출 |
| 전체 수업비 | 월 기준 전체 수업비 표시 (예: 200,000원) |
| 예약금 금액 | 전체 수업비의 15% (예: 30,000원) |
| 추가 안내 문구 | \- 잔여 금액은 센터 현장 결제 \- 모집 실패 시 자동 환불 \- 확정 후 취소 시 환불 불가 |
| CTA | ‘예약금 결제하기’ 버튼 → `/payment` 페이지로 이동 |

---

## **4\. 기능 플로우 요약**

1. 회원은 수업 시간 목록 중 한 개를 선택

2. ‘예약하기’ 버튼 클릭

3. 예약금 안내 팝업 확인

4. ‘예약금 결제하기’ 클릭 시 `/payment`로 이동

5. 결제 성공 시 수업 신청 완료

6. 정원 모집 완료 시 별도 ‘수업 확정 알림톡’ 발송

7. 수업 시작 예정일은 모집일 기준 7일 후로 자동 표시됨

---

## **5\. 예외 및 처리 정책**

| 상황 | 처리 방식 |
| ----- | ----- |
| 정원 초과 | 예약 대기 신청 가능 (취소자 발생 시 우선 안내) |
| 수업 확정 전 취소 | 100% 자동 환불 (회원 마이페이지에서 직접 취소 가능) |
| 수업 확정 후 취소 | 환불 불가 (안내만 제공) |
| 모집 실패 | 예약금 자동 환불 처리 (알림톡 \+ 마이페이지 상태 변경) |
| 수업 시작일 변경 | 실제 시작일과 예상일이 다를 경우, 트레이너가 별도 안내 (자동 메시지 없음) |

### **🧑‍🤝‍🧑 정원 초과 케이스 정책**

| 항목 | 내용 |
| ----- | ----- |
| 선택 가능 여부 | 수업 정원이 초과되어도 선택 가능 |
| CTA 동작 | `예약 대기 신청하기` 버튼 노출 → 예약금 결제 플로우 동일하게 진행 |
| 신청 처리 | 백엔드에서 ‘예약 대기’ 상태로 저장됨 (`status: waiting`) |
| 예약 대기 메시지 | 팝업 메시지 노출: 🔔 “해당 시간대는 정원이 초과되었어요. 예약자 중 취소 발생 시 가장 먼저 안내드릴게요.” |

---

### **📩 예약 대기 완료 시 안내 정책 (MVP 미포함)**

| 트리거 | 예약금 결제 완료 후 수업 상태가 ‘대기’로 저장될 경우 |  
 | 안내 메시지 |

* 메시지 박스 팝업:  
   “예약 대기 신청이 완료되었어요. 취소 인원이 생기면 가장 먼저 알려드릴게요.”  
* 이후 마이페이지 \> 예약 탭에 `예약 대기` 뱃지 노출  
* 카카오 알림톡 별도 발송 예정 (취소자 발생 시 우선순위 확정)

| 확정 시 처리 |

* 취소자 발생 시 대기 중 회원 순서대로 ‘예약 확정’ 처리  
* 확정 시 알림톡 발송:  
   “신청하신 ○○요일 ○○시 수업에 자리가 생겼어요\! 예약이 확정되었으니 확인해보세요 😊”

| 확정 후 변경사항 |

* 마이페이지 예약탭 상태 변경: `예약 대기` → `예약 완료`  
* 회원은 마이페이지에서 수업 취소 가능 (수업 확정 전까진 전액 환불 가능)

