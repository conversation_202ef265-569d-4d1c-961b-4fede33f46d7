# 이미지 업로드 가이드

## FormData 사용 시 주의사항

**중요**: FormData를 사용할 때 `Content-Type` 헤더를 수동으로 설정하지 마세요! 브라우저가 자동으로 올바른 boundary를 포함한 헤더를 설정합니다.

## ✅ 올바른 방법

### JavaScript fetch 사용

```javascript
// ✅ 올바른 방법 - Content-Type 헤더 생략
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('type', 'studio');
formData.append('studioId', 'your-studio-id');
formData.append('prefix', 'featured');

fetch('/api/partner/upload', {
  method: 'POST',
  headers: {
    // Content-Type 헤더를 설정하지 않음 - 브라우저가 자동으로 설정
    'Cookie': 'sb-projectref-auth-token=your-token'
  },
  body: formData
})
.then(response => response.json())
.then(data => console.log(data));
```

### React 컴포넌트 예제

```jsx
import { useState } from 'react';

function ImageUpload({ studioId }) {
  const [uploading, setUploading] = useState(false);
  const [uploadedImages, setUploadedImages] = useState([]);

  const handleFileUpload = async (event) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);

    try {
      const uploadPromises = Array.from(files).map(async (file, index) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'studio');
        formData.append('studioId', studioId);
        formData.append('prefix', index === 0 ? 'featured' : 'gallery');

        const response = await fetch('/api/partner/upload', {
          method: 'POST',
          // Content-Type 헤더 생략 - 중요!
          body: formData
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || '업로드 실패');
        }

        return response.json();
      });

      const results = await Promise.all(uploadPromises);
      setUploadedImages(prev => [...prev, ...results]);
      
    } catch (error) {
      console.error('Upload error:', error);
      alert('업로드 중 오류가 발생했습니다: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <input
        type="file"
        multiple
        accept="image/*"
        onChange={handleFileUpload}
        disabled={uploading}
      />
      {uploading && <p>업로드 중...</p>}
      
      <div>
        {uploadedImages.map((image, index) => (
          <img
            key={index}
            src={image.url}
            alt={`Uploaded ${index}`}
            style={{ width: 100, height: 100, objectFit: 'cover' }}
          />
        ))}
      </div>
    </div>
  );
}
```

## ❌ 잘못된 방법

```javascript
// ❌ 잘못된 방법 - boundary 누락 에러 발생
fetch('/api/partner/upload', {
  method: 'POST',
  headers: {
    'Content-Type': 'multipart/form-data', // 이렇게 하면 에러 발생!
  },
  body: formData
});

// ❌ 잘못된 방법 - boundary를 수동으로 설정해도 문제 발생 가능
fetch('/api/partner/upload', {
  method: 'POST',
  headers: {
    'Content-Type': 'multipart/form-data; boundary=something', // 여전히 문제
  },
  body: formData
});
```

## 업로드 API 명세

### 엔드포인트
```
POST /api/partner/upload
```

### 요청 형식
- **Content-Type**: multipart/form-data (자동 설정)
- **Authentication**: Cookie 기반

### Form Data 필드
| 필드명 | 타입 | 필수 | 설명 |
|--------|------|------|------|
| file | File | ✅ | 이미지 파일 (JPG, PNG, WEBP, 최대 5MB) |
| type | string | ✅ | 업로드 타입 ('studio', 'instructor', 'class') |
| studioId | string | 조건부 | type이 'studio'인 경우 필수 |
| prefix | string | ❌ | 파일명 prefix ('featured', 'gallery' 등) |

### 응답 형식

#### 성공 (200)
```json
{
  "success": true,
  "url": "https://project.supabase.co/storage/v1/object/public/images/studios/partner-id/studio-id/featured-123456789.jpg",
  "path": "studios/partner-id/studio-id/featured-123456789.jpg"
}
```

#### 실패 (400, 401, 422)
```json
{
  "error": "에러 메시지"
}
```

## 파일 제한사항

- **파일 크기**: 최대 5MB
- **허용 형식**: JPG, JPEG, PNG, WEBP
- **최대 개수**: 한 번에 10장 (스튜디오당 최대 10장)
- **보안**: 파트너는 자신의 스튜디오 이미지만 업로드/삭제 가능

## 에러 해결

### "missing boundary in content-type header" 에러
이 에러가 발생하면 Content-Type 헤더를 제거하세요.

```javascript
// Before (에러 발생)
headers: {
  'Content-Type': 'multipart/form-data'
}

// After (해결됨)
headers: {
  // Content-Type 헤더 제거
}
```

### 파일 크기 초과 에러
파일을 5MB 이하로 압축하거나 리사이징하세요.

### 인증 에러
브라우저 개발자 도구에서 올바른 auth token을 확인하세요:
1. F12 → Application → Cookies
2. `sb-projectref-auth-token` 값 복사
3. Cookie 헤더에 설정

## 테스트 도구

### VS Code REST Client
```http
POST http://localhost:3000/api/partner/upload
Cookie: sb-projectref-auth-token=your-token

< ./test-image.jpg
--form type=studio
--form studioId=your-studio-id
--form prefix=featured
```

### curl 명령어
```bash
curl -X POST http://localhost:3000/api/partner/upload \
  -H "Cookie: sb-projectref-auth-token=your-token" \
  -F "file=@test-image.jpg" \
  -F "type=studio" \
  -F "studioId=your-studio-id" \
  -F "prefix=featured"
```

### Postman
1. Method: POST
2. URL: http://localhost:3000/api/partner/upload
3. Headers: Cookie 설정 (Content-Type 설정 안 함)
4. Body → form-data → file 필드에서 파일 선택