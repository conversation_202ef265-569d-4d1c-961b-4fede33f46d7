# ShallWe 페이지 및 사용자 플로우 문서

## 📋 문서 개요

**작성 기준**: 실제 구현된 UI/UX 코드 분석 (`src/app/(main)/instructor/`)  
**마지막 업데이트**: 2025년 1월 17일  
**기술 스택**: Next.js 15, React 19, Tailwind CSS

## 🎨 디자인 시스템

### UI/UX 원칙
- **모바일 우선**: 358px 최소 너비 지원
- **터치 친화적**: 최소 44px 터치 타겟
- **일관된 컬러**: Yellow/Indigo 기반 컬러 팔레트
- **카드 기반**: 둥근 모서리, 그림자 효과

### 컬러 팔레트
```css
/* 주요 컬러 */
--primary-yellow: #D97706;     /* yellow-600 */
--primary-indigo: #4F46E5;     /* indigo-600 */
--background: #F9FAFB;         /* gray-50 */
--card-background: #FFFFFF;    /* white */
--text-primary: #111827;       /* gray-900 */
--text-secondary: #6B7280;     /* gray-500 */
```

### 타이포그래피
- **제목**: text-lg ~ text-2xl, font-bold
- **본문**: text-sm ~ text-base, font-medium
- **보조**: text-xs, text-gray-500

## 📱 구현된 페이지

### 1. 강사 로그인 페이지
**경로**: `/instructor/login`  
**파일**: `src/app/(main)/instructor/login/page.tsx`

#### 기능
- Supabase Auth 기반 로그인
- 이메일/비밀번호 인증
- 자동 온보딩 페이지 리다이렉트

#### UI 구성
```typescript
interface LoginPageProps {
  // 로그인 폼
  emailInput: HTMLInputElement;
  passwordInput: HTMLInputElement;
  loginButton: HTMLButtonElement;
  
  // 상태 관리
  loading: boolean;
  error: string | null;
}
```

### 2. 강사 온보딩 페이지
**경로**: `/instructor/onboarding`  
**파일**: `src/app/(main)/instructor/onboarding/page.tsx`

#### 5단계 온보딩 프로세스
1. **기본 정보**: 이름, 전화번호, 성별
2. **전문분야**: 운동 분야 및 경력
3. **자기소개**: 한줄 소개 및 상세 설명
4. **자격증**: 보유 자격증 정보
5. **계좌 정보**: 정산 계좌 등록

#### UI 구성
```typescript
interface OnboardingState {
  currentStep: 1 | 2 | 3 | 4 | 5;
  basicInfo: {
    name: string;
    phone: string;
    gender: 'MALE' | 'FEMALE' | 'OTHER';
  };
  specialties: Array<{
    specialty: string;
    experienceYears: number;
  }>;
  bio: {
    bio: string;
  };
  account: {
    bankName: string;
    accountNumber: string;
    accountHolder: string;
  };
}
```

#### 사용자 플로우
```mermaid
graph TD
    A[로그인] --> B{온보딩 완료?}
    B -->|No| C[온보딩 시작]
    B -->|Yes| D[대시보드]
    
    C --> E[1단계: 기본정보]
    E --> F[2단계: 전문분야]
    F --> G[3단계: 자기소개]
    G --> H[4단계: 자격증]
    H --> I[5단계: 계좌정보]
    I --> J[온보딩 완료]
    J --> D
```

### 3. 강사 대시보드
**경로**: `/instructor/dashboard`  
**파일**: `src/app/(main)/instructor/dashboard/page.tsx`

#### 주요 섹션
1. **프로필 카드**: 강사 정보 및 통계
2. **빠른 메뉴**: 주요 기능 바로가기
3. **다가오는 수업**: 예정된 스케줄
4. **최근 수강 신청**: 신규 신청 현황
5. **인기 클래스**: 수강생 많은 클래스

#### 대시보드 데이터
```typescript
interface DashboardData {
  instructor: {
    id: string;
    is_active: boolean;
  };
  stats: {
    total_class_templates: number;
    active_class_templates: number;
    total_schedules: number;
    total_enrollments: number;
    total_revenue: number;
    review_count: number;
    average_rating: number;
  };
  upcoming_schedules: UpcomingSchedule[];
  recent_enrollments: RecentEnrollment[];
  top_classes: TopClass[];
  recent_reviews: RecentReview[];
}
```

#### UI 레이아웃
```typescript
// 모바일 우선 그리드 레이아웃
<div className="min-h-screen bg-gray-50">
  {/* 헤더 */}
  <header className="bg-white border-b" />
  
  {/* 프로필 카드 */}
  <section className="px-4 py-6">
    <div className="bg-white rounded-2xl p-6 shadow-sm">
      {/* 프로필 정보 */}
      <div className="flex items-center mb-6">
        <Avatar />
        <ProfileInfo />
      </div>
      
      {/* 통계 그리드 */}
      <div className="grid grid-cols-3 gap-4">
        <StatCard title="총 클래스" value={stats.total_class_templates} />
        <StatCard title="총 수강생" value={stats.total_enrollments} />
        <StatCard title="총 수익" value={stats.total_revenue} />
      </div>
    </div>
  </section>
  
  {/* 빠른 메뉴 */}
  <section className="px-4 mb-6">
    <div className="grid grid-cols-2 gap-4">
      <QuickMenuCard title="내 클래스" href="/instructor/classes" />
      <QuickMenuCard title="클래스 등록" href="/instructor/classes/create" />
      <QuickMenuCard title="스튜디오 목록" href="/instructor/studios" />
      <QuickMenuCard title="내 프로필" href="/instructor/profile" />
    </div>
  </section>
</div>
```

### 4. 클래스 목록 페이지
**경로**: `/instructor/classes`  
**파일**: `src/app/(main)/instructor/classes/page.tsx`

#### 기능
- 강사의 클래스 목록 조회
- 클래스 상태별 필터링
- 클래스 검색 기능
- 클래스 생성 바로가기

#### UI 구성
```typescript
interface ClassListState {
  classes: ClassTemplate[];
  loading: boolean;
  filters: {
    status: 'all' | 'active' | 'inactive';
    category: string;
    search: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}
```

### 5. 클래스 생성 페이지
**경로**: `/instructor/classes/create`  
**파일**: `src/app/(main)/instructor/classes/create/page.tsx`

#### 클래스 생성 폼
```typescript
interface CreateClassForm {
  // 기본 정보
  title: string;
  description: string;
  category: string;
  specialty: string;
  level: string;
  studio_id: string;
  
  // 수업 설정
  duration_minutes: number;
  price_per_session: number;
  max_capacity: number;
  
  // 스케줄 그룹
  scheduleGroups: Array<{
    group_name: string;
    max_participants: number;
    schedules: Array<{
      dayOfWeek: string;
      startTime: string;
      endTime: string;
    }>;
  }>;
}
```

#### 사용자 플로우
```mermaid
graph TD
    A[클래스 생성 페이지] --> B[기본 정보 입력]
    B --> C[스튜디오 선택]
    C --> D[스케줄 그룹 설정]
    D --> E[개별 스케줄 추가]
    E --> F[검증 및 미리보기]
    F --> G[클래스 생성]
    G --> H[클래스 상세 페이지]
```

### 6. 클래스 상세 페이지
**경로**: `/instructor/classes/[id]`  
**파일**: `src/app/(main)/instructor/classes/[id]/page.tsx`

#### 주요 섹션
1. **클래스 정보**: 제목, 설명, 카테고리
2. **스케줄 그룹**: 시간대별 그룹 정보
3. **수강 신청**: 신청자 목록 및 상태
4. **리뷰**: 클래스 후기
5. **통계**: 수강률, 수익, 평점

#### 탭 구조
```typescript
interface ClassDetailTabs {
  overview: {
    classInfo: ClassTemplate;
    scheduleGroups: ScheduleGroup[];
    stats: ClassStats;
  };
  enrollments: {
    enrollments: Enrollment[];
    filters: EnrollmentFilters;
  };
  reviews: {
    reviews: Review[];
    averageRating: number;
  };
  settings: {
    editForm: EditClassForm;
  };
}
```

### 7. 클래스 수정 페이지
**경로**: `/instructor/classes/[id]/edit`  
**파일**: `src/app/(main)/instructor/classes/[id]/edit/page.tsx`

#### 수정 가능 필드
- 기본 정보: 제목, 설명, 카테고리
- 수업 설정: 시간, 가격, 정원
- 스케줄 그룹: 시간대 추가/수정/삭제
- 상태: 활성/비활성 전환

### 8. 프로필 관리 페이지
**경로**: `/instructor/profile`  
**파일**: `src/app/(main)/instructor/profile/page.tsx`

#### 탭 구조
1. **기본 정보**: 이름, 전화번호, 성별
2. **전문분야**: 운동 분야 및 경력 수정
3. **자기소개**: 한줄 소개 및 상세 설명
4. **계좌 정보**: 정산 계좌 변경

### 9. 스튜디오 목록 페이지
**경로**: `/instructor/studios`  
**파일**: `src/app/(main)/instructor/studios/page.tsx`

#### 기능
- 스튜디오 목록 조회
- 지역별 필터링
- 스튜디오 상세 정보
- 클래스 등록 바로가기

## 🎯 사용자 플로우

### 강사 온보딩 플로우
```mermaid
sequenceDiagram
    participant U as 사용자
    participant L as 로그인 페이지
    participant O as 온보딩 페이지
    participant A as API
    participant D as 대시보드
    
    U->>L: 로그인 시도
    L->>A: 인증 요청
    A-->>L: 인증 성공
    L->>O: 온보딩 상태 체크
    O->>A: 온보딩 상태 조회
    A-->>O: 미완료 상태
    O-->>U: 온보딩 폼 표시
    
    U->>O: 1단계 정보 입력
    U->>O: 2단계 정보 입력
    U->>O: 3단계 정보 입력
    U->>O: 4단계 정보 입력
    U->>O: 5단계 정보 입력
    O->>A: 온보딩 완료 요청
    A-->>O: 완료 성공
    O->>D: 대시보드 리다이렉트
```

### 클래스 생성 플로우
```mermaid
sequenceDiagram
    participant U as 강사
    participant D as 대시보드
    participant S as 스튜디오 목록
    participant C as 클래스 생성
    participant A as API
    participant CD as 클래스 상세
    
    U->>D: 대시보드 접속
    D-->>U: 빠른 메뉴 표시
    U->>S: 스튜디오 목록 조회
    S-->>U: 스튜디오 선택
    U->>C: 클래스 생성 페이지
    C-->>U: 생성 폼 표시
    
    U->>C: 기본 정보 입력
    U->>C: 스케줄 그룹 설정
    U->>C: 개별 스케줄 추가
    C->>A: 클래스 생성 요청
    A-->>C: 생성 성공
    C->>CD: 클래스 상세 페이지
```

### 클래스 운영 플로우
```mermaid
graph TD
    A[클래스 생성] --> B[수강생 신청 대기]
    B --> C{신청자 있음?}
    C -->|Yes| D[신청자 관리]
    C -->|No| E[홍보/마케팅]
    E --> B
    
    D --> F[수업 진행]
    F --> G[후기 및 평가]
    G --> H[다음 클래스 계획]
    
    D --> I[수업 취소]
    I --> J[환불 처리]
    J --> K[보강 수업 계획]
    K --> F
```

## 🔧 기술 구현 세부사항

### 상태 관리
```typescript
// React State 패턴
const [loading, setLoading] = useState(false);
const [error, setError] = useState<string | null>(null);
const [data, setData] = useState<Data | null>(null);

// API 호출 패턴
useEffect(() => {
  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/endpoint');
      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  fetchData();
}, [dependency]);
```

### 폼 처리
```typescript
// 폼 상태 관리
const [formData, setFormData] = useState<FormData>(initialState);
const [errors, setErrors] = useState<FormErrors>({});

// 입력 핸들러
const handleChange = (field: string, value: any) => {
  setFormData(prev => ({
    ...prev,
    [field]: value
  }));
  
  // 실시간 검증
  if (errors[field]) {
    setErrors(prev => ({
      ...prev,
      [field]: undefined
    }));
  }
};

// 제출 처리
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  const validationErrors = validateForm(formData);
  if (Object.keys(validationErrors).length > 0) {
    setErrors(validationErrors);
    return;
  }
  
  try {
    await submitForm(formData);
    // 성공 처리
  } catch (error) {
    // 에러 처리
  }
};
```

### 반응형 레이아웃
```css
/* 모바일 우선 (기본) */
.container {
  padding: 1rem;
}

.grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

/* 태블릿 */
@media (min-width: 768px) {
  .container {
    padding: 2rem;
  }
  
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 데스크톱 */
@media (min-width: 1024px) {
  .container {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## 📊 성능 최적화

### 이미지 최적화
```typescript
// Next.js Image 컴포넌트
import Image from 'next/image';

<Image
  src="/studio-image.jpg"
  alt="스튜디오 이미지"
  width={400}
  height={300}
  loading="lazy"
  placeholder="blur"
/>
```

### 코드 스플리팅
```typescript
// 동적 임포트
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <div>로딩 중...</div>
});

// 조건부 로딩
const AdminPanel = dynamic(() => import('./AdminPanel'), {
  ssr: false // 클라이언트 사이드만
});
```

### 메모이제이션
```typescript
// React.memo로 불필요한 리렌더링 방지
const ClassCard = React.memo(({ classData }: { classData: ClassTemplate }) => {
  return (
    <div className="class-card">
      {/* 클래스 카드 내용 */}
    </div>
  );
});

// useMemo로 비싼 계산 캐싱
const expensiveValue = useMemo(() => {
  return calculateComplexValue(data);
}, [data]);
```

## 🔍 접근성 (A11y)

### 키보드 네비게이션
```typescript
// 키보드 이벤트 처리
const handleKeyDown = (e: React.KeyboardEvent) => {
  if (e.key === 'Enter' || e.key === ' ') {
    handleClick();
  }
};

<button
  onClick={handleClick}
  onKeyDown={handleKeyDown}
  aria-label="클래스 생성하기"
>
  클래스 생성
</button>
```

### 스크린 리더 지원
```typescript
// ARIA 속성 사용
<div
  role="tablist"
  aria-label="클래스 정보 탭"
>
  <button
    role="tab"
    aria-selected={activeTab === 'overview'}
    aria-controls="overview-panel"
    id="overview-tab"
  >
    개요
  </button>
</div>

<div
  role="tabpanel"
  id="overview-panel"
  aria-labelledby="overview-tab"
>
  {/* 탭 내용 */}
</div>
```

## 🐛 에러 처리

### 에러 바운더리
```typescript
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('에러 발생:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h2>문제가 발생했습니다</h2>
          <button onClick={() => window.location.reload()}>
            페이지 새로고침
          </button>
        </div>
      );
    }
    
    return this.props.children;
  }
}
```

### 로딩 및 에러 상태
```typescript
const LoadingSpinner = () => (
  <div className="flex justify-center items-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
  </div>
);

const ErrorMessage = ({ message, onRetry }: { message: string; onRetry: () => void }) => (
  <div className="text-center p-8">
    <p className="text-red-600 mb-4">{message}</p>
    <button 
      onClick={onRetry}
      className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
    >
      다시 시도
    </button>
  </div>
);
```

---

**이 문서는 실제 구현된 페이지와 사용자 플로우를 분석하여 작성되었으며, 실제 코드와 일치합니다.**