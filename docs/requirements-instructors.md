## **🧑‍🏫 쉘위 강사 등록 및 목록 페이지 정책서**

---

### **1\. 강사 등록 페이지**

#### **1-1. 기본 정보**

| 필드명 | 필수 여부 | 입력 방식 / UI | 설명 |
| ----- | ----- | ----- | ----- |
| 강사 이름 | ✅ 필수 | 텍스트 입력 | 최대 5자 |
| 성별 | ✅ 필수 | 토글 버튼 (남성/여성) | 기본값: 남성 |
| 강사 연락처 | ❌ 선택 | 텍스트 입력 | 전화번호, 카카오톡 ID 등 |
| 강사 소개 | ✅ 필수 | 텍스트 영역 | 최대 300자, 작성 팁 제공 |
| 강사 소개 링크 | ❌ 선택 | URL 입력 | SNS/블로그 등 (https:// 포함) |
| 강사 대표 사진 | ✅ 필수 | 이미지 업로드 (최대 2장) | 썸네일 표시, 삭제 가능 (X 버튼) |

#### **1-2. 경력 사항**

| 항목 | 설명 |
| ----- | ----- |
| 총 경력 선택 | 드롭다운 선택형 (1년\~20년 등) |
| 전문 분야 선택 | 최소 1개 이상 선택 필수 (요가, 필라테스, 헬스, 수영, 복싱, 러닝, 댄스, 클라이밍, 근력/체중운동 등) |
| 분야별 경력 입력 | 선택한 항목별 개별 경력 설정 (연도 단위: \+ / \- 버튼) |

❗ 최소 1개 분야를 선택하지 않으면 저장 불가

#### **1-3. 자격 사항**

| 필드명 | 필수 여부 | 입력 방식 / UI | 설명 |
| ----- | ----- | ----- | ----- |
| 자격증명 | ✅ 필수 | 텍스트 입력 | 예: 생활스포츠지도사 1급 |
| 발급 기관 | ✅ 필수 | 텍스트 입력 | 예: 대한체육회 |
| 발급일/만료일 | ✅ 필수 | 날짜 선택기 | 미래일 허용 X |
| 자격증 번호 | ❌ 선택 | 텍스트 입력 |  |
| 자격증 등록 버튼 | UI 클릭 | 등록 시 리스트 하단 추가 | 등록 후 편집(✎), 삭제(🗑️) 가능 |

---

### **2\. 강사 등록 프로세스**

| 단계 | 내용 |
| ----- | ----- |
| 1단계 | 모든 필수 필드 입력 및 유효성 검증 |
| 2단계 | '저장' 시 서버에 POST 전송 (`강사 정보`, `전문 분야`, `경력`, `자격증 목록`) |
| 3단계 | 강사 목록 화면으로 이동 (`등록 완료 안내` 표시) |

---

### **3\. 강사 목록 페이지**

| 항목 | 설명 |
| ----- | ----- |
| 기본 구성 | 강사 이미지 \+ 이름 표시 |
| 수정 버튼 | 클릭 시 해당 강사의 등록/수정 페이지로 이동 (기존 데이터 prefill) |
| 삭제 버튼 | 삭제 확인 팝업 노출 후 `강사 삭제 API` 호출 (소프트 삭제 가능성 고려) |

---

### **4\. UI/FE 동작 정의**

| 상황 | 동작 |
| ----- | ----- |
| 성별 선택 | 토글 UI (1개만 선택 가능) |
| 분야 선택 | 멀티 토글 가능, 선택 시 경력 입력 UI 자동 노출 |
| 이미지 업로드 | JPG/PNG 5MB 이하, 썸네일 표시 및 삭제 기능 포함 |
| 저장 버튼 비활성화 조건 | 필수 항목 누락 또는 유효성 실패 시 |
| 자격증 추가 | 1개 추가 시 하단 리스트에 추가 / 수정 및 삭제 UI 포함 |
| 수정 시 기존 정보 불러오기 | 강사 ID 기반 GET 요청으로 prefill 처리 |

---

### **5\. BE 연동 필드 구조 예시**

`{`  
  `"trainerId": "uuid",`  
  `"name": "최인걸",`  
  `"gender": "male",`  
  `"description": "전문 요가 강사입니다.",`  
  `"contact": "010-1234-5678",`  
  `"profileImage": "url",`  
  `"snsLink": "https://instagram.com/...",`  
  `"experienceTotalYears": 5,`  
  `"specialties": [`  
    `{"type": "요가", "years": 4},`  
    `{"type": "헬스", "years": 1}`  
  `],`  
  `"certificates": [`  
    `{`  
      `"name": "스포츠생활체육지도사 1급",`  
      `"issuer": "대한체육회",`  
      `"issueDate": "2021-03-01",`  
      `"expireDate": "2026-03-01",`  
      `"certificateNo": "A123456"`  
    `}`  
  `]`  
`}`

---

### **✅ 추가 고려 사항 (To-be)**

* 자격증 스캔 이미지 업로드 기능 (예: 관리자 검증용)

* 성별 외 다른 분류 태그(시니어 전문, 재활 전문 등) 추가

* 강사 검색/필터 기능 (운영자용 목록 확장 시)

