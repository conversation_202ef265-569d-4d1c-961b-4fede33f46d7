# ShallWe API 문서

## 📋 문서 개요

**작성 기준**: 실제 구현된 API 코드 분석 (`src/app/api/instructor/`)  
**마지막 업데이트**: 2025년 1월 17일  
**버전**: v1.0 (기본 구현) + v2.0 (계획된 확장)

## 🔐 인증 및 권한

### 인증 방식
- **기본 인증**: Supabase Auth + JWT
- **헤더 형식**: `Authorization: Bearer <supabase_access_token>`
- **세션 관리**: Supabase 자동 갱신

### 권한 레벨
- **INSTRUCTOR**: 자신의 클래스만 관리
- **STUDENT**: 클래스 검색 및 신청
- **ADMIN**: 전체 시스템 관리

### RLS 정책
```sql
-- 강사는 자신의 클래스만 접근 가능
CREATE POLICY "instructors_own_classes" ON class_templates
  FOR ALL USING (instructor_id IN (
    SELECT id FROM instructors WHERE member_id = auth.uid()
  ));
```

## 🏫 Instructor APIs

### 온보딩 API

#### `GET /api/instructor/onboarding`
강사 온보딩 상태 확인

**Response:**
```typescript
{
  isInstructor: boolean;
  onboardingCompleted: boolean;
  isActive: boolean;
}
```

**Example:**
```json
{
  "isInstructor": true,
  "onboardingCompleted": false,
  "isActive": true
}
```

#### `POST /api/instructor/onboarding`
강사 온보딩 완료 처리

**Request Body:**
```typescript
{
  basicInfo: {
    name: string;
    phone: string;
    gender: 'MALE' | 'FEMALE' | 'OTHER';
  };
  specialties: Array<{
    specialty: string;
    experienceYears: number;
  }>;
  bio: {
    bio: string;
  };
  account: {
    bankName: string;
    accountNumber: string;
    accountHolder: string;
  };
}
```

**Response:**
```typescript
{
  success: boolean;
  message: string;
  data: {
    instructorId: string;
  };
}
```

### 프로필 API

#### `GET /api/instructor/profile`
강사 프로필 조회

**Response:**
```typescript
{
  instructor: {
    id: string;
    bio: string;
    experienceYears: number;
    rating: number;
    totalReviews: number;
    isActive: boolean;
    onboardingCompleted: boolean;
  };
  profile: {
    name: string;
    phone: string;
    gender: string;
    email: string;
    avatarUrl: string;
  };
  specialties: Array<{
    specialty: string;
    experienceYears: number;
  }>;
  account: {
    bankName: string;
    accountHolder: string;
    isVerified: boolean;
  };
}
```

#### `PUT /api/instructor/profile`
강사 프로필 수정

**Request Body:**
```typescript
{
  basicInfo: {
    name?: string;
    phone?: string;
    bio?: string;
  };
  specialties?: Array<{
    specialty: string;
    experienceYears: number;
  }>;
}
```

### 대시보드 API

#### `GET /api/instructor/dashboard`
강사 대시보드 데이터 조회

**Response:**
```typescript
{
  instructor: {
    id: string;
    is_active: boolean;
  };
  stats: {
    total_class_templates: number;
    active_class_templates: number;
    total_schedules: number;
    total_enrollments: number;
    total_revenue: number;
    review_count: number;
    average_rating: number;
  };
  upcoming_schedules: Array<{
    schedule_id: string;
    group_name: string;
    class_title: string;
    day_of_week: string;
    start_time: string;
    end_time: string;
    studio: {
      name: string;
      address: string;
    };
    capacity_info: {
      max_capacity: number;
      max_group_size: number;
      enrollment_count: number;
      occupancy_rate: number;
    };
    price_per_session: number;
  }>;
  recent_enrollments: Array<{
    id: string;
    status: string;
    enrolled_at: string;
    paid_amount: string;
    class_title: string;
    group_name: string;
    day_of_week: string;
    start_time: string;
    member_name: string;
  }>;
  top_classes: Array<{
    id: string;
    title: string;
    category: string;
    specialty: string;
    price_per_session: number;
    enrollment_count: number;
    total_revenue: number;
  }>;
  recent_reviews: Array<{
    id: string;
    rating: number;
    comment: string;
    is_anonymous: boolean;
    created_at: string;
    class_title: string;
    member_name: string;
  }>;
}
```

### 클래스 관리 API

#### `GET /api/instructor/classes`
강사 클래스 목록 조회

**Query Parameters:**
- `page`: 페이지 번호 (기본값: 1)
- `limit`: 페이지당 항목 수 (기본값: 10)
- `status`: 클래스 상태 필터 (active, inactive)
- `category`: 카테고리 필터
- `search`: 제목 검색

**Response:**
```typescript
{
  classes: Array<{
    id: string;
    title: string;
    description: string;
    specialty: string;
    level: string;
    max_capacity: number;
    price_per_session: number;
    is_active: boolean;
    // 🆕 라이프사이클 정보
    recruitment_start_date?: string;
    recruitment_end_date?: string;
    class_start_date?: string;
    class_end_date?: string;
    status?: string;
    studio: {
      id: string;
      name: string;
    };
    enrollment_count: number;
    total_revenue: number;
  }>;
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}
```

#### `POST /api/instructor/classes`
새 클래스 생성

**Request Body:**
```typescript
{
  title: string;
  description?: string;
  category: string;
  specialty: string;
  level: string;
  studio_id: string;
  duration_minutes: number;
  price_per_session: number;
  max_capacity: number;
  curriculum?: object;
  
  // 🆕 라이프사이클 정보
  recruitment_start_date?: string;
  recruitment_end_date?: string;
  class_start_date?: string;
  class_end_date?: string;
  
  // 스케줄 그룹 설정
  scheduleGroups: Array<{
    group_name: string;
    group_description?: string;
    max_participants: number;
    price_per_session?: number;
    schedules: Array<{
      day_of_week: string;
      start_time: string;
      end_time: string;
      max_participants?: number;
    }>;
  }>;
}
```

**Response:**
```typescript
{
  message: string;
  class_template: ClassTemplate;
  schedule_groups: ScheduleGroup[];
  schedules: Schedule[];
  generated_occurrences?: Occurrence[];  // 미래 기능
}
```

#### `GET /api/instructor/classes/[id]`
클래스 상세 정보 조회

**Response:**
```typescript
{
  class_template: {
    id: string;
    title: string;
    description: string;
    curriculum: object;
    category: string;
    specialty: string;
    level: string;
    duration_minutes: number;
    price_per_session: number;
    max_capacity: number;
    
    // 🆕 라이프사이클 정보
    recruitment_start_date?: string;
    recruitment_end_date?: string;
    class_start_date?: string;
    class_end_date?: string;
    status?: string;
    
    is_active: boolean;
    created_at: string;
    updated_at: string;
    
    studio: {
      id: string;
      name: string;
      address: string;
      studio_type: string;
    };
    
    schedule_groups: Array<{
      id: string;
      group_name: string;
      group_description?: string;
      sessions_per_week: number;
      max_participants: number;
      price_per_session?: number;
      is_active: boolean;
      
      schedules: Array<{
        id: string;
        day_of_week: string;
        start_time: string;
        end_time: string;
        is_active: boolean;
        enrollment_count: number;
        occupancy_rate: number;
      }>;
    }>;
    
    enrollments: Array<{
      id: string;
      status: string;
      paid_amount: number;
      enrolled_at: string;
      notes?: string;
      schedule_group: {
        id: string;
        group_name: string;
      };
      member: {
        name: string;
        phone: string;
      };
    }>;
    
    reviews: Array<{
      id: string;
      rating: number;
      comment: string;
      is_anonymous: boolean;
      is_verified: boolean;
      created_at: string;
      member_name: string;
    }>;
    
    stats: {
      total_enrollments: number;
      confirmed_enrollments: number;
      pending_enrollments: number;
      cancelled_enrollments: number;
      total_revenue: number;
      total_schedule_groups: number;
      total_schedules: number;
      active_schedules: number;
      review_count: number;
      average_rating: number;
    };
  };
}
```

#### `PATCH /api/instructor/classes/[id]`
클래스 정보 수정

**Request Body:**
```typescript
{
  title?: string;
  description?: string;
  curriculum?: object;
  category?: string;
  specialty?: string;
  level?: string;
  duration_minutes?: number;
  price_per_session?: number;
  max_capacity?: number;
  
  // 🆕 라이프사이클 정보
  recruitment_start_date?: string;
  recruitment_end_date?: string;
  class_start_date?: string;
  class_end_date?: string;
  status?: string;
  
  is_active?: boolean;
  
  // 스케줄 그룹 업데이트
  scheduleGroups?: Array<{
    group_name: string;
    max_participants: number;
    schedules: Array<{
      dayOfWeek: string;
      startTime: string;
      endTime: string;
    }>;
  }>;
}
```

**Response:**
```typescript
{
  message: string;
  class_template: ClassTemplate;
}
```

#### `DELETE /api/instructor/classes/[id]`
클래스 삭제 (비활성화)

**Response:**
```typescript
{
  message: string;
  class_template: ClassTemplate;
}
```

**참고**: 실제 삭제 대신 `is_active: false`로 설정

### 🆕 Occurrences API (계획된 확장)

#### `GET /api/instructor/classes/[id]/occurrences`
클래스의 실제 수업 목록 조회

**Query Parameters:**
- `status`: 수업 상태 필터
- `start_date`: 시작 날짜 필터
- `end_date`: 종료 날짜 필터
- `schedule_group_id`: 스케줄 그룹 필터
- `limit`: 페이지 제한 (기본값: 50)
- `offset`: 페이지 오프셋

**Response:**
```typescript
{
  occurrences: Array<{
    id: string;
    occurrence_date: string;
    start_time: string;
    end_time: string;
    max_participants: number;
    status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
    attendance_count: number;
    confirmed_enrollments: number;
    instructor_notes?: string;
    cancellation_reason?: string;
    is_substitute_class: boolean;
    original_occurrence_id?: string;
    
    schedule_group: {
      id: string;
      name: string;
    };
    schedule_info: {
      day_of_week: string;
    };
    occupancy_rate: number;
    
    created_at: string;
    updated_at: string;
  }>;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    has_more: boolean;
  };
}
```

#### `POST /api/instructor/classes/[id]/occurrences`
새 수업 생성 (주로 보강 수업)

**Request Body:**
```typescript
{
  schedule_group_id: string;
  class_schedule_id: string;
  occurrence_date: string;
  start_time?: string;
  end_time?: string;
  max_participants?: number;
  status?: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  instructor_notes?: string;
  is_substitute_class?: boolean;
  original_occurrence_id?: string;
}
```

**Response:**
```typescript
{
  message: string;
  occurrence: Occurrence;
}
```

#### `PATCH /api/instructor/classes/[id]/occurrences/[occurrence_id]`
수업 정보 수정 (시간 변경, 취소 등)

**Request Body:**
```typescript
{
  occurrence_date?: string;
  start_time?: string;
  end_time?: string;
  max_participants?: number;
  status?: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  instructor_notes?: string;
  cancellation_reason?: string;
  
  // 보강 수업 생성 옵션
  create_substitute?: boolean;
  substitute_date?: string;
  substitute_start_time?: string;
}
```

### 스튜디오 API

#### `GET /api/instructor/studios`
스튜디오 목록 조회

**Query Parameters:**
- `search`: 이름 검색
- `type`: 스튜디오 타입 필터
- `near_station`: 지하철역 필터

**Response:**
```typescript
{
  studios: Array<{
    id: string;
    name: string;
    description: string;
    address: string;
    studio_type: string;
    latitude?: number;
    longitude?: number;
    nearest_station?: string;
    amenities?: object;
    links?: object;
    is_active: boolean;
    
    // 통계 정보
    total_classes: number;
    active_instructors: number;
  }>;
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}
```

#### `GET /api/instructor/studios/[id]`
스튜디오 상세 정보

**Response:**
```typescript
{
  studio: {
    id: string;
    name: string;
    description: string;
    address: string;
    studio_type: string;
    latitude?: number;
    longitude?: number;
    nearest_station?: string;
    amenities?: object;
    links?: object;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
  classes: Array<{
    id: string;
    title: string;
    instructor_name: string;
    category: string;
    level: string;
    enrollment_count: number;
  }>;
  stats: {
    total_classes: number;
    total_instructors: number;
    total_enrollments: number;
    average_rating: number;
  };
}
```

### 스케줄 API

#### `GET /api/instructor/class-schedules`
강사의 전체 스케줄 조회

**Query Parameters:**
- `date`: 특정 날짜 필터
- `week_start`: 주간 시작일
- `month`: 월별 필터

**Response:**
```typescript
{
  schedules: Array<{
    id: string;
    class_title: string;
    group_name: string;
    day_of_week: string;
    start_time: string;
    end_time: string;
    max_participants: number;
    enrollment_count: number;
    studio: {
      name: string;
      address: string;
    };
    next_occurrence?: {
      date: string;
      status: string;
    };
  }>;
  summary: {
    total_schedules: number;
    active_schedules: number;
    weekly_hours: number;
    monthly_revenue: number;
  };
}
```

## 📊 에러 처리

### 표준 에러 응답
```typescript
{
  error: string;
  message: string;
  details?: any;
  timestamp: string;
  path: string;
}
```

### 상태 코드
- `200`: 성공
- `201`: 생성 성공
- `400`: 잘못된 요청
- `401`: 인증 필요
- `403`: 권한 없음
- `404`: 리소스 없음
- `409`: 충돌 (중복 데이터)
- `422`: 유효성 검사 실패
- `500`: 서버 오류

### 일반적인 에러 예시

#### 인증 에러
```json
{
  "error": "UNAUTHORIZED",
  "message": "인증이 필요합니다.",
  "timestamp": "2025-01-17T10:30:00Z",
  "path": "/api/instructor/classes"
}
```

#### 권한 에러
```json
{
  "error": "FORBIDDEN",
  "message": "강사 정보를 찾을 수 없습니다.",
  "timestamp": "2025-01-17T10:30:00Z",
  "path": "/api/instructor/classes"
}
```

#### 유효성 검사 에러
```json
{
  "error": "VALIDATION_ERROR",
  "message": "유효하지 않은 카테고리입니다.",
  "details": {
    "field": "category",
    "provided": "invalid_category",
    "allowed": ["fitness", "yoga", "pilates", "dance", "martial_arts", "wellness"]
  },
  "timestamp": "2025-01-17T10:30:00Z",
  "path": "/api/instructor/classes"
}
```

## 🚀 사용 예시

### JavaScript/TypeScript 클라이언트

```typescript
// 클래스 목록 조회
const fetchClasses = async () => {
  const response = await fetch('/api/instructor/classes?limit=10&page=1', {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });
  
  if (!response.ok) {
    throw new Error('클래스 목록 조회 실패');
  }
  
  return response.json();
};

// 클래스 생성
const createClass = async (classData: CreateClassRequest) => {
  const response = await fetch('/api/instructor/classes', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(classData)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message);
  }
  
  return response.json();
};

// 클래스 상세 조회
const fetchClassDetail = async (classId: string) => {
  const response = await fetch(`/api/instructor/classes/${classId}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });
  
  return response.json();
};
```

### React Hook 예시

```typescript
// 커스텀 훅 예시
const useInstructorClasses = () => {
  const [classes, setClasses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    fetchClasses()
      .then(data => {
        setClasses(data.classes);
        setLoading(false);
      })
      .catch(err => {
        setError(err.message);
        setLoading(false);
      });
  }, []);
  
  return { classes, loading, error };
};
```

## 📋 변경 이력

### v1.0 (현재 구현)
- 기본 강사 관리 API
- 클래스 템플릿 CRUD
- 스케줄 그룹 관리
- 수강 신청 시스템

### v2.0 (계획된 확장)
- Occurrence 기반 실제 수업 관리
- 출석 체크 시스템
- 라이프사이클 자동 관리
- 보강 수업 생성

---

**이 API 문서는 실제 구현된 코드를 기반으로 작성되었으며, 계획된 확장 기능은 별도로 표시되어 있습니다.**