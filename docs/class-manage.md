## **✅ 쉘위 파트너 센터 \- 수업 목록 페이지 정책서 (상세)**

### **1\. 페이지 목적**

| 항목 | 내용 |
| ----- | ----- |
| 페이지명 | 내 클래스 |
| 사용자 | 승인된 센터 관리자 (파트너 계정) |
| 목적 | 센터가 개설한 수업 현황을 카드 형태로 확인하고, 수정/노출/상세 진입 가능 |
| 구성 단위 | 수업 (1개 수업 \= 1개 운동종목 × 1명 강사 × 1개 수준) |
| 수업 시간 단위 | 수업 내 요일/시간별 개별 시간 단위 관리 (예: 월/수 10시 \= 1개 수업시간) |

---

### **2\. UI 구성요소**

| UI 구성요소 | 설명 |
| ----- | ----- |
| 수업 카드 리스트 | 한 화면에 등록된 전체 수업을 카드 형태로 나열 |
| 카드 내 표시 항목 | 운동 제목, 담당 강사, 운동 분야, 수준, 수업 정원, 등록된 수업 시간 수, 진행 중 수업 시간 수, 가격 정보, 센터명 및 등록일 |
| 상태 표시 | `노출중 / 미노출` 토글 표시 (ON일 경우 회원 추천 리스트에 노출) |
| 버튼 | 상세보기, 수정 |
| 가격 정보 | 1인 기준 회당 가격 표시 |

---

### **3\. 버튼 및 노출 동작 정책**

#### **(1) 상세보기 버튼**

| 항목 | 내용 |
| ----- | ----- |
| 랜딩 위치 | 해당 수업의 **회원용 수업 상세 페이지**로 랜딩 |
| 목적 | 센터 관리자가 실제 회원에게 보이는 수업 정보를 검토할 수 있도록 제공 |
| 데이터 상태 | 수정 불가 상태, 단순 조회용 |

#### **(2) 수정 버튼**

| 항목 | 내용 |
| ----- | ----- |
| 랜딩 위치 | 수업 등록 페이지 (편집 상태) |
| 수정 가능 조건 | \- 예약자 없는 경우 전체 수업 정보 수정 가능 \- 예약자 있는 경우, 수업 정보 수정 불가(비활성) \- 단, 수업 시간은 추가 가능 |
| 수업 시간 OFF | 예약자 없는 수업 시간만 `OFF` 가능, 예약자 있는 시간은 `OFF` 비활성화 |
| 예외처리 | 수정 버튼 클릭 시 수정 제한 조건 존재 시 안내 팝업 표시 필요: “예약자가 있어 수업 정보를 수정할 수 없습니다” 등 |

---

### **4\. 수업 노출 상태 토글**

| 항목 | 내용 |
| ----- | ----- |
| 위치 | 수업 카드 우측 상단 토글 버튼 |
| 기본값 | `ON` (수업 등록 시 자동 노출 상태로 설정됨) |
| 기능 | ON → 수업 추천 리스트에 노출 / OFF → 비노출 (단, 마이페이지나 직접 URL 접근은 가능) |
| 예외 조건 | 예약자 있는 수업은 노출 여부와 무관하게 접근 가능하나, **예약은 불가** |
| 관련 정책 | 진행중/완료 수업의 경우 마이페이지 접근 허용, 단 신규 예약 차단 |

---

### **5\. 수업 수정 제한 조건 (백엔드 제약)**

| 조건 | 처리 방식 |
| ----- | ----- |
| 예약자가 존재하는 수업 | 수업 기본 정보 수정 불가, 수정 영역 비활성화 처리 |
| 예약자가 없는 수업 시간 | 삭제 또는 OFF 가능 |
| 예약자가 있는 수업 시간 | OFF 불가, 삭제 불가, 상태 표시만 가능 |
| 수업 진행 중 상태 | 전체 수정 불가, 단 수업 시간만 추가 가능 |

---

### **6\. 프론트엔드 및 백엔드 연동 로직**

| 항목 | FE 처리 | BE 처리 |
| ----- | ----- | ----- |
| 수업 목록 불러오기 | 카드 UI 렌더링 | 승인된 센터 계정의 수업 전체 조회 API |
| 상세보기 진입 | 링크 이동 | 수업 상세 페이지 URL 연결 |
| 수정 진입 | 수정 가능 여부 확인 후 폼 이동 | 예약자 존재 여부 확인 → 수정 권한 분기 |
| 노출 토글 | 토글 상태 업데이트 | 수업 노출 여부 업데이트 API (노출 여부 only) |
| 수업 시간 추가/삭제 | 수업 시간 행 UI 추가/삭제 | 추가/삭제 시 수업 시간 단위 업데이트 API 호출 |
| 수정 제한 안내 | 팝업 or 토스트 메시지 노출 | BE에서 수정 제한 조건 명확히 응답 필요 (403 or flag) |

---

### **7\. 향후 고려사항**

* 수업 복제 기능 (예약 없는 수업 기준)

* 수업 내 수업 시간별 출석율 확인 기능 (진행 이후)

* 수업 상태(진행중/완료)별 통계 반영

