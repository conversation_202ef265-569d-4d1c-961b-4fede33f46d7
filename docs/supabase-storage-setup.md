# Supabase Storage Setup for ShallWe

## 1. Storage Bucket 생성

Supabase Dashboard에서 다음 버킷을 생성합니다:

```
Name: images
Public: true (읽기 전용)
File size limit: 5MB
Allowed MIME types: image/jpeg, image/jpg, image/png, image/webp
```

## 2. 폴더 구조

```
images/
├── studios/
│   └── {partnerId}/
│       └── {studioId}/
│           ├── featured-{timestamp}.jpg
│           └── gallery-{timestamp}.jpg
├── instructors/
│   └── {instructorId}/
│       └── profile-{timestamp}.jpg
└── classes/
    └── {classId}/
        └── thumbnail-{timestamp}.jpg
```

## 3. RLS (Row Level Security) 정책

### 3.1 SELECT (공개 읽기)
```sql
-- 모든 이미지는 공개적으로 읽기 가능 (SEO)
CREATE POLICY "Public images are viewable by everyone" ON storage.objects
FOR SELECT USING (bucket_id = 'images');
```

### 3.2 INSERT (인증된 사용자만)
```sql
-- 파트너는 자신의 스튜디오 이미지만 업로드 가능
CREATE POLICY "Partners can upload their studio images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'images' AND
  auth.role() = 'authenticated' AND
  (storage.foldername(name))[1] = 'studios' AND
  (storage.foldername(name))[2] = auth.uid()::text
);
```

### 3.3 UPDATE (소유자만)
```sql
-- 파트너는 자신의 스튜디오 이미지만 수정 가능
CREATE POLICY "Partners can update their studio images" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'images' AND
  auth.role() = 'authenticated' AND
  (storage.foldername(name))[1] = 'studios' AND
  (storage.foldername(name))[2] = auth.uid()::text
);
```

### 3.4 DELETE (소유자만)
```sql
-- 파트너는 자신의 스튜디오 이미지만 삭제 가능
CREATE POLICY "Partners can delete their studio images" ON storage.objects
FOR DELETE USING (
  bucket_id = 'images' AND
  auth.role() = 'authenticated' AND
  (storage.foldername(name))[1] = 'studios' AND
  (storage.foldername(name))[2] = auth.uid()::text
);
```

## 4. 실행 순서

1. Supabase Dashboard > Storage 접속
2. New bucket 클릭하여 'images' 버킷 생성
3. SQL Editor에서 위의 RLS 정책 실행
4. 버킷 설정에서 파일 크기 제한 및 MIME 타입 설정

## 5. 환경 변수 확인

`.env.local` 파일에 다음 변수가 있는지 확인:
```
NEXT_PUBLIC_SUPABASE_URL=https://[PROJECT_ID].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=[YOUR_ANON_KEY]
```

## 6. 이미지 URL 형식

업로드된 이미지는 다음 형식으로 접근 가능:
```
https://[PROJECT_ID].supabase.co/storage/v1/object/public/images/studios/[partnerId]/[studioId]/[filename]
```

## 7. 이미지 변환 (선택사항)

Supabase는 이미지 변환을 지원합니다:
```
https://[PROJECT_ID].supabase.co/storage/v1/render/image/public/images/studios/[path]?width=200&quality=75
```