# 예약/결제 시스템 High Level Design

## 1. 시스템 개요

### 1.1 비즈니스 요구사항
- 회원이 수업을 예약하고 예약금(전체 수업료의 15%)을 결제
- 예약과 결제는 원자적(atomic) 트랜잭션으로 처리
- **정원 초과 시에도 결제 완료 후 순서 대기 (수업 확정 시 초과분 자동 환불)**
- 수업 확정 전까지 100% 환불 가능
- PG사 변경 가능성을 고려한 추상화 설계

### 1.2 핵심 문제와 해결 방향
**문제**: 분산 시스템에서 결제(외부 PG)와 예약(내부 DB)의 트랜잭션 일관성 보장

**해결 방향**:
- **PG-Agnostic Architecture**: Payment Gateway 추상화 계층 구현
- **Saga Pattern**: 각 단계별로 보상 트랜잭션 준비
- **Event Sourcing**: 모든 상태 변화를 이벤트로 기록
- **최종 일관성(Eventual Consistency)**: 실시간 일관성보다는 최종적 일관성 추구

### 1.3 시스템 목표
- 데이터 일관성 보장
- PG사 독립적인 아키텍처
- 장애 상황에서의 복구 가능성
- Backend API 중심 설계
- 확장 가능한 아키텍처

## 2. 아키텍처 설계

### 2.1 전체 시스템 구조
```
┌─────────────┐     ┌──────────────┐     ┌─────────────┐
│   Client    │────▶│   API Layer  │────▶│   Service   │
└─────────────┘     └──────────────┘     │    Layer    │
                                         └──────┬──────┘
                                                │
                    ┌───────────────────────────┼───────────────────────┐
                    │                           │                       │
              ┌─────▼─────┐     ┌──────────────▼──┐     ┌────────────────▼────┐
              │ Enrollment │     │ Payment Gateway │     │    External PG      │
              │ Repository │     │   (Abstract)    │     │  (TossPayments 등)  │
              └─────┬─────┘     └──────────────┬──┘     └────────────────┬────┘
                    │                           │                         │
              ┌─────▼──────────────────────────▼─────────────────────────▼────┐
              │                        Database                              │
              │  ┌────────────┐  ┌────────────┐  ┌────────────────────┐    │
              │  │ enrollments│  │  payments  │  │  payment_events    │    │
              │  └────────────┘  └────────────┘  └────────────────────┘    │
              └──────────────────────────────────────────────────────────────┘
```

### 2.2 Payment Gateway 추상화
```typescript
interface PaymentGateway {
  name: string;
  
  // 결제 준비
  preparePayment(params: PaymentPrepareParams): Promise<PaymentPrepareResult>;
  
  // 결제 승인/확인
  confirmPayment(params: PaymentConfirmParams): Promise<PaymentResult>;
  
  // 결제 취소/환불
  cancelPayment(params: PaymentCancelParams): Promise<PaymentResult>;
  
  // Webhook 검증 및 처리
  verifyWebhook(signature: string, payload: any): boolean;
  processWebhook(payload: any): Promise<WebhookResult>;
}

// TossPayments 구현체 (내부적으로만 사용)
class TossPaymentsGateway implements PaymentGateway {
  name = 'tosspayments';
  // 구현...
}
```

## 3. 데이터 모델

### 3.1 주요 테이블 구조

#### enrollments (수강신청)
```sql
CREATE TABLE enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL,
  class_id UUID NOT NULL,
  schedule_group_id INTEGER NOT NULL,
  
  -- 상태 관리
  status TEXT NOT NULL DEFAULT 'pending',
  -- pending: 결제 대기
  -- payment_processing: 결제 진행중
  -- paid: 결제 완료 (순서 대기)
  -- confirmed: 수업 확정
  -- cancelled: 취소됨
  -- refund_pending: 환불 대기
  -- refunded: 환불 완료
  
  -- 순서 관리 (선착순 확정을 위함)
  enrollment_order INTEGER NOT NULL,
  
  -- 금액 정보
  total_amount INTEGER NOT NULL,
  deposit_amount INTEGER NOT NULL,
  
  -- 메타데이터
  member_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- 인덱스
  UNIQUE(member_id, class_id, schedule_group_id),
  INDEX idx_enrollments_class_order (class_id, schedule_group_id, enrollment_order),
  INDEX idx_enrollments_status (status),
  INDEX idx_enrollments_created (created_at)
);
```

#### payments (결제)
```sql
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  enrollment_id UUID NOT NULL REFERENCES enrollments(id) ON DELETE CASCADE,
  
  -- 결제 정보
  amount INTEGER NOT NULL,
  payment_type TEXT NOT NULL DEFAULT 'deposit', -- deposit, remaining, refund
  
  -- PG사 정보 (추상화)
  provider TEXT NOT NULL, -- 'tosspayments', 'portone' 등
  external_payment_key TEXT UNIQUE, -- PG사 결제 키
  external_order_id TEXT,
  payment_method TEXT, -- 카드, 계좌이체, 가상계좌 등
  payment_data JSONB, -- PG사별 추가 데이터
  
  -- 상태
  status TEXT NOT NULL DEFAULT 'pending',
  -- pending: 결제 준비
  -- ready: 결제 대기
  -- processing: 결제 진행중
  -- paid: 결제 성공
  -- failed: 결제 실패
  -- cancelled: 결제 취소
  -- refunded: 환불 완료
  
  -- 타임스탬프
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  paid_at TIMESTAMPTZ,
  failed_at TIMESTAMPTZ,
  refunded_at TIMESTAMPTZ,
  
  -- 인덱스
  INDEX idx_payments_enrollment (enrollment_id),
  INDEX idx_payments_provider_key (provider, external_payment_key),
  INDEX idx_payments_status (status),
  INDEX idx_payments_created (created_at)
);
```

#### payment_events (결제 이벤트 로그)
```sql
CREATE TABLE payment_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
  enrollment_id UUID NOT NULL REFERENCES enrollments(id) ON DELETE CASCADE,
  
  event_type TEXT NOT NULL,
  -- payment_prepared: 결제 준비됨
  -- payment_requested: 결제 요청됨
  -- payment_processing: 결제 진행중
  -- payment_succeeded: 결제 성공
  -- payment_failed: 결제 실패
  -- payment_cancelled: 결제 취소
  -- refund_requested: 환불 요청됨
  -- refund_completed: 환불 완료됨
  -- webhook_received: 웹훅 수신
  
  event_data JSONB,
  error_message TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- 인덱스
  INDEX idx_payment_events_payment (payment_id),
  INDEX idx_payment_events_enrollment (enrollment_id),
  INDEX idx_payment_events_type (event_type),
  INDEX idx_payment_events_created (created_at)
);
```

#### payment_gateway_configs (PG사 설정)
```sql
CREATE TABLE payment_gateway_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider TEXT NOT NULL UNIQUE, -- 'tosspayments', 'portone' 등
  
  -- 설정 정보
  is_active BOOLEAN DEFAULT true,
  is_default BOOLEAN DEFAULT false,
  config_data JSONB NOT NULL, -- 각 PG사별 설정 (암호화 저장)
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- 인덱스
  INDEX idx_gateway_configs_active (is_active),
  INDEX idx_gateway_configs_default (is_default)
);
```

### 3.2 데이터 관계도
```
members ──┐
          ├──▶ enrollments ──▶ payments ──▶ payment_events
classes ──┘         │
                    └──▶ class_schedule_groups
                    
payment_gateway_configs (독립)
```

## 4. 상태 머신

### 4.1 Enrollment 상태 전이
```
[PENDING] ──────▶ [PAYMENT_PROCESSING] ──────▶ [PAID]
    │                     │                       │
    │                     │                       ├──▶ [CONFIRMED] (선착순)
    │                     │                       │
    │                     ▼                       └──▶ [REFUNDED] (초과분)
    │              [PAYMENT_FAILED]
    │                     │
    └─────────────────────┴───────────────▶ [CANCELLED] ──▶ [REFUNDED]
```

### 4.2 Payment 상태 전이
```
[PENDING] ──▶ [READY] ──▶ [PROCESSING] ──▶ [PAID]
                │              │
                │              └──▶ [FAILED]
                │
                └──────────────────▶ [CANCELLED]
                                         │
                                         ▼
                                    [REFUNDED]
```

## 5. Backend API 설계

### 5.1 주요 엔드포인트

#### 수강신청 + 결제 준비
```typescript
POST /api/enrollments
Request:
{
  "classId": "uuid",
  "scheduleGroupId": 1,
  "memberNotes": "무릎 부상 주의",
  "paymentProvider": "tosspayments" // 선택적 (기본값 사용)
}

Response:
{
  "enrollment": {
    "id": "uuid",
    "status": "pending",
    "enrollmentOrder": 15,
    "totalAmount": 200000,
    "depositAmount": 30000
  },
  "payment": {
    "id": "uuid",
    "paymentKey": "toss_payment_123",
    "amount": 30000,
    "provider": "tosspayments"
  },
  "paymentWidget": {
    "clientKey": "test_ck_...",
    "orderName": "요가 클래스 예약금",
    "customerName": "홍길동",
    "successUrl": "/payments/success",
    "failUrl": "/payments/fail"
  }
}
```

#### 결제 승인
```typescript
POST /api/payments/confirm
Request:
{
  "paymentKey": "toss_payment_123",
  "orderId": "enrollment_uuid",
  "amount": 30000
}

Response:
{
  "success": true,
  "payment": {
    "id": "uuid",
    "status": "paid",
    "paidAt": "2024-01-01T00:00:00Z"
  },
  "enrollment": {
    "id": "uuid",
    "status": "paid",
    "enrollmentOrder": 15,
    "confirmedAt": "2024-01-01T00:00:00Z"
  }
}
```

#### 수업 확정 (관리자용)
```typescript
POST /api/classes/{classId}/schedules/{scheduleGroupId}/confirm
Request:
{
  "maxCapacity": 10,
  "reason": "강사 확정"
}

Response:
{
  "confirmed": [
    {
      "enrollmentId": "uuid1",
      "memberId": "uuid1",
      "enrollmentOrder": 1
    },
    // ... 1~10번째
  ],
  "refunded": [
    {
      "enrollmentId": "uuid11",
      "memberId": "uuid11", 
      "enrollmentOrder": 11,
      "refundAmount": 30000
    },
    // ... 11번째 이후
  ]
}
```

#### PG-Agnostic Webhook
```typescript
POST /api/payments/webhook/{provider}
Headers:
{
  "x-webhook-signature": "signature_value"
}

Request Body: PG사별 webhook payload

Response:
{
  "success": true,
  "processed": true
}
```

### 5.2 내부 Service Layer 구조
```typescript
class EnrollmentService {
  constructor(
    private paymentGateway: PaymentGateway,
    private enrollmentRepo: EnrollmentRepository,
    private paymentRepo: PaymentRepository
  ) {}
  
  async createEnrollment(params: CreateEnrollmentParams) {
    // 1. DB 트랜잭션 시작
    // 2. enrollment 생성 (순서 할당)
    // 3. payment 준비
    // 4. PG사 결제 준비 호출
    // 5. 실패 시 롤백
  }
  
  async confirmPayment(params: ConfirmPaymentParams) {
    // 1. PG사 결제 승인 API 호출
    // 2. DB 상태 업데이트
    // 3. 이벤트 로그 기록
  }
  
  async confirmClass(classId: string, scheduleGroupId: number, maxCapacity: number) {
    // 1. 선착순 기준으로 확정자 선별
    // 2. 초과분 자동 환불 처리
    // 3. 대량 상태 업데이트 (트랜잭션)
    // 4. 알림 발송
  }
}
```

## 6. 트랜잭션 처리 전략

### 6.1 수강신청 + 결제 준비 플로우
```typescript
async function createEnrollmentWithPayment(params) {
  return await db.transaction(async (tx) => {
    try {
      // 1. 순서 할당 (동시성 제어)
      const nextOrder = await getNextEnrollmentOrder(tx, classId, scheduleGroupId);
      
      // 2. enrollment 생성
      const enrollment = await tx.insert(enrollments).values({
        ...params,
        enrollmentOrder: nextOrder,
        status: 'pending'
      });
      
      // 3. payment 레코드 생성
      const payment = await tx.insert(payments).values({
        enrollmentId: enrollment.id,
        amount: depositAmount,
        provider: selectedProvider,
        status: 'pending'
      });
      
      // 4. PG사 결제 준비
      const paymentResult = await paymentGateway.preparePayment({
        amount: depositAmount,
        orderId: enrollment.id,
        orderName: `${className} 예약금`
      });
      
      // 5. payment 업데이트
      await tx.update(payments)
        .set({
          externalPaymentKey: paymentResult.paymentKey,
          status: 'ready'
        })
        .where(eq(payments.id, payment.id));
      
      return { enrollment, payment, paymentWidget: paymentResult };
      
    } catch (error) {
      // 트랜잭션 자동 롤백
      throw error;
    }
  });
}
```

### 6.2 수업 확정 + 대량 환불 플로우
```typescript
async function confirmClassAndRefundExcess(classId: string, scheduleGroupId: number, maxCapacity: number) {
  return await db.transaction(async (tx) => {
    // 1. 결제 완료된 신청자들을 순서대로 조회
    const paidEnrollments = await tx
      .select()
      .from(enrollments)
      .where(and(
        eq(enrollments.classId, classId),
        eq(enrollments.scheduleGroupId, scheduleGroupId),
        eq(enrollments.status, 'paid')
      ))
      .orderBy(enrollments.enrollmentOrder);
    
    const confirmed = paidEnrollments.slice(0, maxCapacity);
    const toRefund = paidEnrollments.slice(maxCapacity);
    
    // 2. 확정자 상태 업데이트
    if (confirmed.length > 0) {
      await tx.update(enrollments)
        .set({ status: 'confirmed', updatedAt: new Date() })
        .where(inArray(enrollments.id, confirmed.map(e => e.id)));
    }
    
    // 3. 초과분 환불 처리
    for (const enrollment of toRefund) {
      await processRefund(tx, enrollment.id, '정원 초과로 인한 자동 환불');
    }
    
    return { confirmed, refunded: toRefund };
  });
}
```

### 6.3 Saga Pattern 적용
```typescript
class PaymentSaga {
  async execute(enrollment: Enrollment, payment: Payment) {
    const steps = [
      { forward: () => this.preparePayment(payment), backward: () => this.cancelPayment(payment) },
      { forward: () => this.confirmPayment(payment), backward: () => this.refundPayment(payment) },
      { forward: () => this.updateEnrollment(enrollment), backward: () => this.revertEnrollment(enrollment) }
    ];
    
    let completedSteps = 0;
    
    try {
      for (const step of steps) {
        await step.forward();
        completedSteps++;
      }
    } catch (error) {
      // 보상 트랜잭션 실행
      for (let i = completedSteps - 1; i >= 0; i--) {
        try {
          await steps[i].backward();
        } catch (rollbackError) {
          // 수동 처리 큐에 등록
          await this.addToManualProcessingQueue({
            enrollmentId: enrollment.id,
            step: i,
            error: rollbackError.message
          });
        }
      }
      throw error;
    }
  }
}
```

## 7. 장애 대응

### 7.1 주요 장애 시나리오

#### 시나리오 1: 결제 승인 후 DB 업데이트 실패
```typescript
async function handlePaymentConfirmFailure(paymentKey: string, error: Error) {
  try {
    // 1. PG사 결제 취소 (TossPayments API)
    await paymentGateway.cancelPayment({
      paymentKey,
      cancelReason: '시스템 오류로 인한 자동 취소'
    });
    
    // 2. DB 상태 롤백
    await db.transaction(async (tx) => {
      await tx.update(payments)
        .set({ status: 'cancelled', updatedAt: new Date() })
        .where(eq(payments.externalPaymentKey, paymentKey));
      
      const payment = await tx.select().from(payments)
        .where(eq(payments.externalPaymentKey, paymentKey)).limit(1);
        
      await tx.update(enrollments)
        .set({ status: 'cancelled', updatedAt: new Date() })
        .where(eq(enrollments.id, payment[0].enrollmentId));
    });
    
  } catch (rollbackError) {
    // 3. 수동 처리 대기열 등록
    await addToManualProcessingQueue({
      type: 'payment_confirm_rollback_failed',
      paymentKey,
      originalError: error.message,
      rollbackError: rollbackError.message
    });
  }
}
```

#### 시나리오 2: Webhook 중복 처리 방지
```typescript
async function processWebhook(provider: string, payload: any, signature: string) {
  // 1. 서명 검증
  const gateway = getPaymentGateway(provider);
  if (!gateway.verifyWebhook(signature, payload)) {
    throw new Error('Invalid webhook signature');
  }
  
  // 2. 멱등성 키 생성
  const idempotencyKey = `webhook_${provider}_${payload.eventType}_${payload.data.paymentKey}`;
  
  // 3. 중복 처리 확인 (Redis 또는 DB)
  const processed = await checkIdempotency(idempotencyKey);
  if (processed) {
    return { success: true, message: 'Already processed' };
  }
  
  try {
    // 4. 웹훅 처리
    const result = await gateway.processWebhook(payload);
    
    // 5. 멱등성 키 저장
    await setIdempotency(idempotencyKey, true, 3600); // 1시간 TTL
    
    return result;
  } catch (error) {
    await setIdempotency(idempotencyKey, false, 3600);
    throw error;
  }
}
```

### 7.2 복구 전략
- **자동 복구**: 재시도 가능한 네트워크 오류, 일시적 DB 오류
- **반자동 복구**: 관리자 확인 후 처리 (결제/환불 불일치)
- **수동 복구**: CS 팀 개입 필요 (복잡한 데이터 불일치)

## 8. 보안 고려사항

### 8.1 PG사 설정 암호화
```typescript
class PaymentGatewayConfigService {
  async saveConfig(provider: string, config: any) {
    const encryptedConfig = await this.encrypt(JSON.stringify(config));
    
    await db.insert(paymentGatewayConfigs).values({
      provider,
      configData: encryptedConfig
    });
  }
  
  async getConfig(provider: string): Promise<any> {
    const config = await db.select()
      .from(paymentGatewayConfigs)
      .where(eq(paymentGatewayConfigs.provider, provider))
      .limit(1);
    
    return JSON.parse(await this.decrypt(config[0].configData));
  }
}
```

### 8.2 Webhook 서명 검증
```typescript
class TossPaymentsGateway implements PaymentGateway {
  verifyWebhook(signature: string, payload: string): boolean {
    const webhookSecret = this.config.webhookSecret;
    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(payload)
      .digest('base64');
    
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  }
}
```

## 9. 모니터링

### 9.1 핵심 메트릭
- 결제 성공률 (PG사별)
- 평균 결제 처리 시간
- 환불 처리 성공률
- API 응답 시간
- Webhook 처리 지연 시간

### 9.2 알림 설정
- 결제 실패율 > 5%
- 환불 처리 실패 발생
- Webhook 처리 지연 > 30초
- 수동 처리 큐 항목 증가

### 9.3 로깅 전략
```typescript
async function logPaymentEvent(eventType: string, data: any) {
  await db.insert(paymentEvents).values({
    paymentId: data.paymentId,
    enrollmentId: data.enrollmentId,
    eventType,
    eventData: data,
    createdAt: new Date()
  });
  
  // 외부 로깅 시스템에도 전송
  logger.info('Payment Event', {
    eventType,
    paymentId: data.paymentId,
    provider: data.provider,
    amount: data.amount
  });
}
```