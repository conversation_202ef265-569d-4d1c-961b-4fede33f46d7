# Next.js Linking and Navigation Guide

This guide covers Next.js linking and navigation patterns for building fast, responsive user experiences in your ShallWe mobile app.

## Table of Contents

1. [How Navigation Works](#how-navigation-works)
2. [The Link Component](#the-link-component)
3. [Programmatic Navigation](#programmatic-navigation)
4. [Performance Optimizations](#performance-optimizations)
5. [Common Navigation Patterns](#common-navigation-patterns)
6. [Troubleshooting Slow Transitions](#troubleshooting-slow-transitions)
7. [ShallWe Mobile App Examples](#shallwe-mobile-app-examples)

## How Navigation Works

Next.js uses server rendering by default but provides several optimizations to make navigation feel instant:

### 1. Server Rendering

- **Static Rendering**: Happens at build time, results are cached
- **Dynamic Rendering**: Happens at request time for dynamic content
- HTML is generated on the server for faster initial loads

### 2. Prefetching

- Routes linked with `<Link>` are automatically prefetched when they enter the viewport
- **Static routes**: Full route is prefetched
- **Dynamic routes**: Partially prefetched if `loading.tsx` exists

### 3. Streaming

- Server sends parts of dynamic routes as they're ready
- Users see content sooner, even while other parts are loading
- Enables partial prefetching for dynamic routes

### 4. Client-side Transitions

- No full page reloads between routes
- Shared layouts remain interactive
- State is preserved where appropriate

## The Link Component

The `<Link>` component is the primary way to navigate between routes in Next.js.

### Basic Usage

```tsx
import Link from 'next/link';

export default function Navigation() {
    return (
        <nav>
            <Link href='/'>Home</Link>
            <Link href='/classes'>Classes</Link>
            <Link href='/profile'>Profile</Link>
        </nav>
    );
}
```

### Dynamic Routes

```tsx
import Link from 'next/link';

export default function ClassCard({ classItem }) {
  return (
    <div className='class-card'>
      <h3>{classItem.title}</h3>
      {/* Dynamic route with parameter */}
      <Link href={`/classes/${classItem.id}`}>View Details</Link>
      {/* Nested dynamic route */}
      <Link href={`/classes/${classItem.id}/booking`}>Book Now</Link>
    </div>
  );
}
```

### Query Parameters

```tsx
import Link from 'next/link';

export default function ClassFilters() {
  return (
    <div>
      {/* Using query object */}
      <Link
        href={{
          pathname: '/classes',
          query: { category: 'yoga', location: 'seoul' },
        }}
      >
        Yoga in Seoul
      </Link>

      {/* Using search params directly */}
      <Link href='/classes?category=fitness&location=gangnam'>
        Fitness in Gangnam
      </Link>
    </div>
  );
}
```

### Link Props

```tsx
import Link from 'next/link';

export default function OptimizedLink() {
  return (
    <div>
      {/* Disable prefetching for performance */}
      <Link href='/heavy-page' prefetch={false}>
        Heavy Page
      </Link>

      {/* Replace current history entry */}
      <Link href='/login' replace>
        Login
      </Link>

      {/* Scroll to top after navigation */}
      <Link href='/classes' scroll={true}>
        Classes (Scroll to Top)
      </Link>
    </div>
  );
}
```

## Programmatic Navigation

Use the `useRouter` hook for programmatic navigation in Client Components.

### Basic Navigation

```tsx
'use client';
import { useRouter } from 'next/navigation';

export default function BookingForm() {
  const router = useRouter();

  const handleBooking = async formData => {
    try {
      await bookClass(formData);
      // Navigate to success page
      router.push('/booking/success');
    } catch (error) {
      // Navigate to error page
      router.push('/booking/error');
    }
  };

  return (
    <form onSubmit={handleBooking}>
      {/* Form content */}
      <button type='submit'>Book Class</button>
      <button type='button' onClick={() => router.back()}>
        Go Back
      </button>
    </form>
  );
}
```

### Navigation with Search Params

```tsx
'use client';
import { useRouter, useSearchParams } from 'next/navigation';

export default function ClassFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const updateFilter = (category: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('category', category);
    router.push(`/classes?${params.toString()}`);
  };

  return (
    <div>
      <button onClick={() => updateFilter('yoga')}>Yoga Classes</button>
      <button onClick={() => updateFilter('fitness')}>Fitness Classes</button>
    </div>
  );
}
```

### Native History API

For more control, use the native History API:

```tsx
'use client';
import { useSearchParams } from 'next/navigation';

export default function SortControls() {
  const searchParams = useSearchParams();

  const updateSorting = (sortOrder: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('sort', sortOrder);
    // Add to history (user can go back)
    window.history.pushState(null, '', `?${params.toString()}`);
  };

  const switchLocale = (locale: string) => {
    const newPath = `/${locale}${window.location.pathname}`;
    // Replace current entry (user cannot go back)
    window.history.replaceState(null, '', newPath);
  };

  return (
    <div>
      <button onClick={() => updateSorting('price-asc')}>
        Price: Low to High
      </button>
      <button onClick={() => updateSorting('price-desc')}>
        Price: High to Low
      </button>
    </div>
  );
}
```

## Performance Optimizations

### Loading States

Create `loading.tsx` files for better UX during navigation:

```tsx
// app/classes/loading.tsx
export default function ClassesLoading() {
  return (
    <div className='loading-container'>
      <div className='skeleton-card'></div>
      <div className='skeleton-card'></div>
      <div className='skeleton-card'></div>
    </div>
  );
}

// app/classes/[id]/loading.tsx
export default function ClassDetailLoading() {
  return (
    <div className='class-detail-loading'>
      <div className='skeleton-header'></div>
      <div className='skeleton-content'></div>
      <div className='skeleton-tabs'></div>
    </div>
  );
}
```

### Static Generation for Dynamic Routes

Use `generateStaticParams` for better performance:

```tsx
// app/classes/[id]/page.tsx
export async function generateStaticParams() {
  const classes = await fetch('https://api.shallwe.com/classes').then(res =>
    res.json()
  );

  return classes.map(classItem => ({
    id: classItem.id.toString(),
  }));
}

export default async function ClassDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const classData = await getClass(id);

  return (
    <div>
      <h1>{classData.title}</h1>
      {/* Class details */}
    </div>
  );
}
```

### Hover-based Prefetching

Optimize prefetching for large lists:

```tsx
'use client';
import Link from 'next/link';
import { useState } from 'react';

export default function HoverPrefetchLink({
  href,
  children,
}: {
  href: string;
  children: React.ReactNode;
}) {
  const [shouldPrefetch, setShouldPrefetch] = useState(false);

  return (
    <Link
      href={href}
      prefetch={shouldPrefetch ? undefined : false}
      onMouseEnter={() => setShouldPrefetch(true)}
    >
      {children}
    </Link>
  );
}
```

### Loading Indicators

Show visual feedback during navigation:

```tsx
'use client';
import { useLinkStatus } from 'next/link';

export default function LoadingIndicator() {
  const { pending } = useLinkStatus();

  return pending ? (
    <div role='status' aria-label='Loading' className='spinner' />
  ) : null;
}

// CSS for smooth loading indicator
/* 
.spinner {
  opacity: 0;
  animation: 
    fadeIn 500ms 100ms forwards,
    rotate 1s linear infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes rotate {
  to { transform: rotate(360deg); }
}
*/
```

## Common Navigation Patterns

### Bottom Navigation (Mobile)

```tsx
'use client';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function BottomNavigation() {
  const pathname = usePathname();

  const isActive = (path: string) => pathname === path;

  return (
    <nav className='bottom-nav'>
      <Link href='/' className={isActive('/') ? 'active' : ''}>
        <HomeIcon />
        Home
      </Link>
      <Link href='/classes' className={isActive('/classes') ? 'active' : ''}>
        <ClassIcon />
        Classes
      </Link>
      <Link href='/bookings' className={isActive('/bookings') ? 'active' : ''}>
        <BookingIcon />
        Bookings
      </Link>
      <Link href='/profile' className={isActive('/profile') ? 'active' : ''}>
        <ProfileIcon />
        Profile
      </Link>
    </nav>
  );
}
```

### Breadcrumb Navigation

```tsx
'use client';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function Breadcrumb() {
  const pathname = usePathname();
  const pathSegments = pathname.split('/').filter(Boolean);

  return (
    <nav className='breadcrumb'>
      <Link href='/'>Home</Link>
      {pathSegments.map((segment, index) => {
        const href = `/${pathSegments.slice(0, index + 1).join('/')}`;
        const isLast = index === pathSegments.length - 1;

        return (
          <span key={href}>
            <span className='separator'>/</span>
            {isLast ? (
              <span className='current'>{segment}</span>
            ) : (
              <Link href={href}>{segment}</Link>
            )}
          </span>
        );
      })}
    </nav>
  );
}
```

### Modal Navigation

```tsx
'use client';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function Modal({ children }: { children: React.ReactNode }) {
  const router = useRouter();

  useEffect(() => {
    // Handle escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        router.back();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [router]);

  return (
    <div className='modal-overlay' onClick={router.back}>
      <div className='modal-content' onClick={e => e.stopPropagation()}>
        {children}
        <button onClick={router.back} className='close-button'>
          ×
        </button>
      </div>
    </div>
  );
}
```

## Troubleshooting Slow Transitions

### Common Issues and Solutions

1. **Dynamic routes without loading.tsx**

   ```tsx
   // Add loading.tsx to dynamic routes
   // app/classes/[id]/loading.tsx
   export default function Loading() {
     return <ClassDetailSkeleton />;
   }
   ```

2. **Missing generateStaticParams**

   ```tsx
   // app/classes/[id]/page.tsx
   export async function generateStaticParams() {
     const classes = await getClasses();
     return classes.map(c => ({ id: c.id }));
   }
   ```

3. **Large JavaScript bundles**

   ```bash
   # Analyze bundle size
   npm install --save-dev @next/bundle-analyzer
   ```

4. **Unnecessary prefetching**
   ```tsx
   // Disable for large lists
   <Link href='/heavy-page' prefetch={false}>
     Heavy Page
   </Link>
   ```

## ShallWe Mobile App Examples

### Class Card with Navigation

```tsx
import Link from 'next/link';
import Image from 'next/image';

interface ClassCardProps {
  classItem: {
    id: string;
    title: string;
    coach: string;
    price: number;
    image: string;
    location: string;
    duration: number;
  };
}

export default function ClassCard({ classItem }: ClassCardProps) {
  return (
    <div className='class-card'>
      <Link href={`/classes/${classItem.id}`}>
        <Image
          src={classItem.image}
          alt={classItem.title}
          width={300}
          height={200}
          className='class-image'
        />
        <div className='class-info'>
          <h3>{classItem.title}</h3>
          <p className='coach'>{classItem.coach}</p>
          <div className='class-meta'>
            <span className='location'>{classItem.location}</span>
            <span className='duration'>{classItem.duration}분</span>
          </div>
          <p className='price'>₩{classItem.price.toLocaleString()}</p>
        </div>
      </Link>

      <div className='card-actions'>
        <Link
          href={`/classes/${classItem.id}/schedule`}
          className='btn btn-outline'
        >
          수업일정 보기
        </Link>
        <Link
          href={`/classes/${classItem.id}/booking`}
          className='btn btn-primary'
        >
          예약하기
        </Link>
      </div>
    </div>
  );
}
```

### Profile Navigation with Tabs

```tsx
'use client';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function ProfileTabs() {
  const pathname = usePathname();

  const tabs = [
    { label: '예약 수업', href: '/profile/bookings' },
    { label: '진행 수업', href: '/profile/active' },
    { label: '완료 수업', href: '/profile/completed' },
  ];

  return (
    <div className='profile-tabs'>
      {tabs.map(tab => (
        <Link
          key={tab.href}
          href={tab.href}
          className={`tab ${pathname === tab.href ? 'active' : ''}`}
        >
          {tab.label}
        </Link>
      ))}
    </div>
  );
}
```

### Search and Filter Navigation

```tsx
'use client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';

export default function ClassSearch() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [query, setQuery] = useState(searchParams.get('q') || '');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams.toString());

    if (query) {
      params.set('q', query);
    } else {
      params.delete('q');
    }

    router.push(`/classes?${params.toString()}`);
  };

  const updateFilter = (key: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(key, value);
    router.push(`/classes?${params.toString()}`);
  };

  return (
    <div className='search-filters'>
      <form onSubmit={handleSearch} className='search-form'>
        <input
          type='search'
          value={query}
          onChange={e => setQuery(e.target.value)}
          placeholder='클래스 검색...'
          className='search-input'
        />
        <button type='submit'>검색</button>
      </form>

      <div className='filter-chips'>
        <button onClick={() => updateFilter('category', 'yoga')}>요가</button>
        <button onClick={() => updateFilter('category', 'fitness')}>
          피트니스
        </button>
        <button onClick={() => updateFilter('location', 'gangnam')}>
          강남
        </button>
        <button onClick={() => updateFilter('location', 'hongdae')}>
          홍대
        </button>
      </div>
    </div>
  );
}
```

## Best Practices Summary

1. **Use `<Link>` for navigation** - Enables prefetching and optimizations
2. **Add loading.tsx files** - Improves perceived performance
3. **Use generateStaticParams** - Pre-generate dynamic routes when possible
4. **Implement proper loading states** - Keep users informed during transitions
5. **Optimize prefetching** - Disable for heavy pages, enable hover-based prefetching
6. **Use route groups** - Organize related routes without affecting URLs
7. **Handle errors gracefully** - Provide fallbacks and error boundaries

For more information, refer to the [official Next.js documentation](https://nextjs.org/docs/app/getting-started/linking-and-navigating).
