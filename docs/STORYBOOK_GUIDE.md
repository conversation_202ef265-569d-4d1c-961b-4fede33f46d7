# Storybook Story Writing Guide

This guide explains how to write Storybook stories based on the patterns used in this project.

## Table of Contents
1. [Basic Story Structure](#basic-story-structure)
2. [Import Requirements](#import-requirements)
3. [Meta Configuration](#meta-configuration)
4. [Story Definition](#story-definition)
5. [Common Patterns](#common-patterns)
6. [Advanced Features](#advanced-features)
7. [Best Practices](#best-practices)

## Basic Story Structure

Every story file follows this basic structure:

```typescript
import type { Meta, StoryObj } from '@storybook/react-vite';
import { ComponentName } from './ComponentName';

const meta = {
  // Meta configuration
} satisfies Meta<typeof ComponentName>;

export default meta;
type Story = StoryObj<typeof meta>;

export const StoryName: Story = {
  // Story configuration
};
```

## Import Requirements

### Essential Imports
```typescript
// Required for all story files
import type { Meta, StoryObj } from '@storybook/react-vite';

// Import your component
import { Button } from './Button';
```

### Optional Imports
```typescript
// For action handlers and testing
import { fn } from 'storybook/test';

// For interaction testing
import { expect, userEvent, within } from 'storybook/test';
```

## Meta Configuration

The meta object defines the story's metadata and default configuration:

```typescript
const meta = {
  title: 'Category/ComponentName',           // Story hierarchy in sidebar
  component: ComponentName,                  // The component being documented
  parameters: {
    layout: 'centered' | 'fullscreen',      // Story layout
  },
  tags: ['autodocs'],                       // Enable automatic documentation
  argTypes: {
    // Control definitions for component props
    backgroundColor: { control: 'color' },
    size: { 
      control: { type: 'select' },
      options: ['small', 'medium', 'large']
    }
  },
  args: {
    // Default args for all stories
    onClick: fn()                           // Mock function for testing
  },
} satisfies Meta<typeof ComponentName>;
```

### Meta Properties Explained

| Property | Purpose | Example Values |
|----------|---------|----------------|
| `title` | Story location in sidebar | `'Example/Button'`, `'Forms/Input'` |
| `component` | React component to document | `Button`, `Header` |
| `parameters.layout` | Story canvas layout | `'centered'`, `'fullscreen'` |
| `tags` | Storybook features | `['autodocs']` |
| `argTypes` | Prop control definitions | Controls for component props |
| `args` | Default arguments | Default prop values |

## Story Definition

Each story represents a different state or variation of your component:

```typescript
export const Primary: Story = {
  args: {
    primary: true,
    label: 'Button',
  },
};

export const Secondary: Story = {
  args: {
    label: 'Button',
  },
};

export const Large: Story = {
  args: {
    size: 'large',
    label: 'Button',
  },
};
```

## Common Patterns

### 1. Button Component Stories
```typescript
// Button.stories.ts
import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import { Button } from './Button';

const meta = {
  title: 'Example/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    backgroundColor: { control: 'color' },
  },
  args: { onClick: fn() },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    primary: true,
    label: 'Button',
  },
};

export const Secondary: Story = {
  args: {
    label: 'Button',
  },
};
```

### 2. Layout Component Stories
```typescript
// Header.stories.ts
import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import { Header } from './Header';

const meta = {
  title: 'Example/Header',
  component: Header,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',  // Use fullscreen for layout components
  },
  args: {
    onLogin: fn(),
    onLogout: fn(),
    onCreateAccount: fn(),
  },
} satisfies Meta<typeof Header>;

export default meta;
type Story = StoryObj<typeof meta>;

export const LoggedIn: Story = {
  args: {
    user: {
      name: 'Jane Doe',
    },
  },
};

export const LoggedOut: Story = {};
```

## Advanced Features

### 1. Interaction Testing
For testing user interactions:

```typescript
export const InteractiveStory: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const loginButton = canvas.getByRole('button', { name: /Log in/i });
    
    await expect(loginButton).toBeInTheDocument();
    await userEvent.click(loginButton);
    
    const logoutButton = canvas.getByRole('button', { name: /Log out/i });
    await expect(logoutButton).toBeInTheDocument();
  },
};
```

### 2. Custom ArgTypes
Define custom controls for complex props:

```typescript
argTypes: {
  size: {
    control: { type: 'select' },
    options: ['small', 'medium', 'large'],
    description: 'Size of the component'
  },
  theme: {
    control: { type: 'radio' },
    options: ['light', 'dark'],
  },
  backgroundColor: { 
    control: 'color',
    description: 'Background color override'
  },
},
```

### 3. Multiple Stories for Different States
Create stories for various component states:

```typescript
export const Default: Story = {};

export const Loading: Story = {
  args: {
    isLoading: true,
  },
};

export const Error: Story = {
  args: {
    error: 'Something went wrong',
  },
};

export const WithData: Story = {
  args: {
    data: mockData,
  },
};
```

## Best Practices

### 1. Naming Conventions
- **File naming**: `ComponentName.stories.ts`
- **Story naming**: Use descriptive names like `Primary`, `Secondary`, `Large`, `LoggedIn`, `WithError`
- **Title hierarchy**: Use categories like `'Example/Button'`, `'Forms/Input'`, `'Layout/Header'`

### 2. Component Props Documentation
Ensure your component has proper TypeScript interfaces:

```typescript
export interface ButtonProps {
  /** Is this the principal call to action on the page? */
  primary?: boolean;
  /** What background color to use */
  backgroundColor?: string;
  /** How large should the button be? */
  size?: 'small' | 'medium' | 'large';
  /** Button contents */
  label: string;
  /** Optional click handler */
  onClick?: () => void;
}
```

### 3. Story Organization
- Group related stories under the same category
- Create stories for all major variations
- Include edge cases and error states
- Use meaningful story names that describe the state

### 4. Mock Functions
Use `fn()` from `storybook/test` for event handlers:

```typescript
args: {
  onClick: fn(),
  onSubmit: fn(),
  onCancel: fn(),
}
```

### 5. Layout Selection
Choose appropriate layouts:
- `'centered'`: For components like buttons, inputs, cards
- `'fullscreen'`: For layouts, pages, headers, navigation


## Running Storybook

```bash
# Development mode
npm run storybook

# Build for production
npm run build-storybook
```

## Additional Resources

- [Storybook Documentation](https://storybook.js.org/docs)
- [Writing Stories Guide](https://storybook.js.org/docs/writing-stories)
- [Interaction Testing](https://storybook.js.org/docs/writing-tests/interaction-testing)
- [Controls Documentation](https://storybook.js.org/docs/essentials/controls)
