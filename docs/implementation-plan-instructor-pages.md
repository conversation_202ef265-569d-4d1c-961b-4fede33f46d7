# 파트너 강사 관리 페이지 구현 계획

## 개요

PR #82에서 완성된 파트너 강사 관리 API를 활용하여 `/partner/instructor` 페이지와 관련 UI 컴포넌트를 구현합니다. 
기존 `/partner/studios/page.tsx`의 패턴을 따라 일관성 있는 사용자 경험을 제공합니다.

## 1. 전체 아키텍처

### 1.1 페이지 구조
```
/partner/instructor/
├── page.tsx                    # 강사 목록 메인 페이지
├── new/
│   └── page.tsx               # 강사 등록 페이지
└── [instructorId]/
    └── edit/
        └── page.tsx           # 강사 수정 페이지
```

### 1.2 컴포넌트 구조
```
/partner/_components/
├── instructor/
│   ├── InstructorCard.tsx          # 강사 카드 컴포넌트
│   ├── InstructorForm.tsx          # 강사 등록/수정 폼
│   ├── InstructorSpecialtySelector.tsx  # 전문분야 선택기
│   ├── InstructorCertificateForm.tsx    # 자격증 폼
│   └── InstructorDeleteDialog.tsx       # 삭제 확인 다이얼로그
└── shared/
    ├── InstructorListItem.tsx      # 기존 목록 아이템 (개선)
    ├── InstructorEmptyState.tsx    # 기존 빈 상태 (개선)
    └── MultipleImageUpload.tsx     # 기존 이미지 업로드
```

### 1.3 서비스 및 스키마
```
/partner/_services/
└── instructor.service.ts           # 강사 API 서비스

/partner/_schemas/
├── instructor-form.ts              # 폼 전용 스키마
└── instructor-transform.ts         # 폼 데이터 변환 유틸
```

## 2. 데이터 구조 및 스키마

### 2.1 API 스키마 (기존)
```typescript
// src/lib/schemas/instructor.ts 사용
interface InstructorData {
  id: string;
  studioId: string;
  partnerId: string;
  name: string;
  gender: 'male' | 'female';
  contact?: string;
  description: string;
  links?: {
    website?: string;
    sns?: string;
  };
  profileImages?: Array<{
    path: string;
    url: string;
  }>;
  experienceTotalYears: number;
  specialties: Array<{
    type: SpecialtyType;
    years: number;
  }>;
  certificates?: Array<{
    name: string;
    issuing_organization: string;
    issue_date: string;
    expiry_date?: string;
    certificate_number?: string;
  }>;
  status: 'active' | 'deleted';
  createdAt: string;
  updatedAt: string;
}
```

### 2.2 폼 스키마 (신규 생성 필요)
```typescript
// src/app/partner/_schemas/instructor-form.ts
interface InstructorFormData {
  // 기본 정보
  name: string;
  gender: 'male' | 'female';
  contact: string;
  description: string;
  
  // 링크 정보
  links: {
    website: string;
    sns: string;
  };
  
  // 이미지 (MultipleImageUpload 패턴 따름)
  images: Array<{
    file?: File;
    url: string;
    path: string;
  }>;
  
  // 경력 정보
  experienceTotalYears: number;
  specialties: Array<{
    type: SpecialtyType;
    years: number;
  }>;
  
  // 자격증 정보
  certificates: Array<{
    name: string;
    issuing_organization: string;
    issue_date: string;
    expiry_date: string;
    certificate_number: string;
  }>;
}
```

## 3. 페이지별 구현 계획

### 3.1 메인 페이지 (`/partner/instructor/page.tsx`)

#### 3.1.1 데이터 페칭 패턴 (스튜디오 페이지 패턴 따름)
```typescript
// Server Component 패턴
export default async function InstructorPage() {
  // 1. 서버 사이드에서 초기 데이터 로드
  const studios = await getPartnerStudios(); // 스튜디오 목록
  const instructors = await getPartnerInstructors(); // 전체 강사 목록
  
  return (
    <InstructorClientPage 
      initialStudios={studios}
      initialInstructors={instructors}
    />
  );
}

// Client Component
'use client';
function InstructorClientPage({ initialStudios, initialInstructors }) {
  // 2. React Query로 클라이언트 데이터 관리
  const { data: studios } = useQuery({
    queryKey: ['partner-studios'],
    queryFn: partnerService.getStudios,
    initialData: initialStudios,
    suspense: true
  });
  
  const { data: instructors } = useQuery({
    queryKey: ['partner-instructors'],
    queryFn: partnerService.getInstructors,
    initialData: initialInstructors,
    suspense: true
  });
}
```

#### 3.1.2 페이지 구성 요소
```typescript
return (
  <div className="p-3">
    {/* 헤더 */}
    <div className="mb-4 flex items-center justify-between">
      <h1 className="text-lg font-semibold text-foreground">강사 관리</h1>
      <Button asChild>
        <Link href="/partner/instructor/new">강사 등록</Link>
      </Button>
    </div>

    {/* 스튜디오 필터 */}
    <StudioFilter 
      studios={studios}
      selectedStudioId={selectedStudioId}
      onStudioChange={setSelectedStudioId}
    />

    {/* 강사 목록 */}
    <div className="space-y-3">
      {filteredInstructors.length === 0 ? (
        <InstructorEmptyState />
      ) : (
        filteredInstructors.map(instructor => (
          <InstructorCard 
            key={instructor.id}
            instructor={instructor}
            onEdit={() => router.push(`/partner/instructor/${instructor.id}/edit`)}
            onDelete={() => setDeleteInstructor(instructor)}
          />
        ))
      )}
    </div>

    {/* 삭제 확인 다이얼로그 */}
    <InstructorDeleteDialog 
      instructor={deleteInstructor}
      open={!!deleteInstructor}
      onClose={() => setDeleteInstructor(null)}
      onConfirm={handleDeleteConfirm}
    />
  </div>
);
```

### 3.2 강사 등록 페이지 (`/partner/instructor/new/page.tsx`)

#### 3.2.1 폼 설정 (스튜디오 폼 패턴 따름)
```typescript
'use client';
export default function NewInstructorPage() {
  const {
    control,
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<InstructorFormData>({
    resolver: zodResolver(instructorFormSchema),
    defaultValues: {
      name: '',
      gender: undefined,
      contact: '',
      description: '',
      links: {
        website: '',
        sns: ''
      },
      images: [],
      experienceTotalYears: 1,
      specialties: [],
      certificates: []
    }
  });

  // 로딩 상태 (스튜디오 페이지 패턴)
  const [isLoading, setIsLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const onSubmit: SubmitHandler<InstructorFormData> = async (data) => {
    try {
      setIsLoading(true);
      setSubmitError(null);
      
      // 폼 데이터를 API 요청 형식으로 변환
      const requestData = await mapInstructorFormDataToRequestData(data);
      
      await instructorService.createInstructor(requestData);
      
      toast.success('강사가 성공적으로 등록되었습니다.');
      router.push('/partner/instructor');
    } catch (error) {
      console.error('강사 등록 실패:', error);
      toast.error('강사 등록에 실패했습니다.');
    } finally {
      setIsLoading(false);
    }
  };
}
```

#### 3.2.2 폼 구조 (다단계 섹션)
```typescript
<form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6">
  {/* 1. 기본 정보 섹션 */}
  <InstructorBasicInfoSection 
    control={control}
    register={register}
    errors={errors}
  />

  {/* 2. 이미지 업로드 섹션 */}
  <InstructorImageSection 
    control={control}
    errors={errors}
  />

  {/* 3. 경력 및 전문분야 섹션 */}
  <InstructorExperienceSection 
    control={control}
    register={register}
    setValue={setValue}
    watch={watch}
    errors={errors}
  />

  {/* 4. 자격증 섹션 */}
  <InstructorCertificateSection 
    control={control}
    setValue={setValue}
    watch={watch}
    errors={errors}
  />

  {/* 제출 버튼 */}
  <div className="pt-6">
    <Button 
      type="submit"
      className="h-12 w-full text-base font-medium"
      size="lg"
      disabled={isLoading}
    >
      강사 등록
    </Button>
  </div>
</form>
```

### 3.3 강사 수정 페이지 (`/partner/instructor/[instructorId]/edit/page.tsx`)

#### 3.3.1 데이터 로딩 패턴
```typescript
// Server Component
export default async function EditInstructorPage({ 
  params 
}: { 
  params: { instructorId: string } 
}) {
  const instructor = await getInstructorById(params.instructorId);
  
  if (!instructor) {
    notFound();
  }
  
  return (
    <EditInstructorClientPage 
      instructor={instructor}
      instructorId={params.instructorId}
    />
  );
}

// Client Component - 등록 페이지와 동일한 폼, 다른 초기값
'use client';
function EditInstructorClientPage({ instructor, instructorId }) {
  const form = useForm({
    resolver: zodResolver(instructorFormSchema),
    defaultValues: transformInstructorToFormData(instructor)
  });

  const onSubmit = async (data) => {
    // PUT 요청으로 업데이트
    await instructorService.updateInstructor(instructorId, requestData);
  };
}
```

## 4. 컴포넌트 설계

### 4.1 InstructorCard 컴포넌트
```typescript
interface InstructorCardProps {
  instructor: InstructorData;
  onEdit: () => void;
  onDelete: () => void;
}

// 카드 레이아웃: 프로필 이미지 + 정보 + 액션 버튼
```

### 4.2 InstructorSpecialtySelector 컴포넌트
```typescript
interface InstructorSpecialtySelectorProps {
  specialties: Array<{ type: SpecialtyType; years: number }>;
  onChange: (specialties: Array<{ type: SpecialtyType; years: number }>) => void;
  error?: string;
}

// 전문분야 타입 선택 + 경력 년수 입력
// 동적 추가/삭제 기능
```

### 4.3 InstructorCertificateForm 컴포넌트
```typescript
interface InstructorCertificateFormProps {
  certificates: Array<Certificate>;
  onChange: (certificates: Array<Certificate>) => void;
  errors?: any;
}

// 자격증 정보 동적 추가/삭제
// 날짜 선택기 포함
```

## 5. 서비스 레이어

### 5.1 InstructorService 클래스
```typescript
// src/app/partner/_services/instructor.service.ts
class InstructorService {
  async getInstructors(studioId?: string): Promise<InstructorData[]> {
    // GET /api/partner/instructors 또는 studio 필터링
  }

  async getInstructorById(instructorId: string): Promise<InstructorData> {
    // GET /api/partner/instructors/{instructorId}
  }

  async createInstructor(data: CreateInstructorRequest): Promise<InstructorData> {
    // POST /api/partner/studios/{studioId}/instructors
  }

  async updateInstructor(instructorId: string, data: UpdateInstructorRequest): Promise<InstructorData> {
    // PUT /api/partner/studios/{studioId}/instructors/{instructorId}
  }

  async deleteInstructor(instructorId: string, studioId: string): Promise<void> {
    // DELETE /api/partner/studios/{studioId}/instructors/{instructorId}
  }
}

export const instructorService = new InstructorService();
```

## 6. 데이터 변환 유틸리티

### 6.1 Form Data 변환
```typescript
// src/app/partner/_utils/instructor-transform.ts

export async function mapInstructorFormDataToRequestData(
  formData: InstructorFormData
): Promise<CreateInstructorRequest> {
  // 1. 이미지 업로드 처리
  const profileImages = await uploadInstructorImages(formData.images);
  
  // 2. 폼 데이터를 API 스키마로 변환
  return {
    name: formData.name,
    gender: formData.gender,
    contact: formData.contact || undefined,
    description: formData.description,
    links: {
      website: formData.links.website || undefined,
      sns: formData.links.sns || undefined
    },
    profileImages: profileImages.length > 0 ? profileImages : undefined,
    experienceTotalYears: formData.experienceTotalYears,
    specialties: formData.specialties,
    certificates: formData.certificates.length > 0 ? formData.certificates : undefined
  };
}

export function transformInstructorToFormData(
  instructor: InstructorData
): InstructorFormData {
  // API 데이터를 폼 데이터로 변환
}
```

## 7. 상태 관리 전략

### 7.1 로컬 상태
- React Hook Form으로 폼 상태 관리
- useState로 UI 상태 관리 (로딩, 모달, 필터)

### 7.2 서버 상태
- React Query (Suspense Query 선호)
- 초기 데이터는 Server Component에서 제공
- 낙관적 업데이트로 UX 개선

### 7.3 글로벌 상태
- 필요시 Zustand 사용 (현재는 불필요할 것으로 예상)

## 8. 에러 처리 및 로딩 상태

### 8.1 로딩 상태
```typescript
// 페이지 레벨 로딩
<Suspense fallback={<InstructorPageSkeleton />}>
  <InstructorPage />
</Suspense>

// 폼 제출 로딩
const [isLoading, setIsLoading] = useState(false);
<Button disabled={isLoading}>
  {isLoading ? <Spinner /> : '강사 등록'}
</Button>
```

### 8.2 에러 처리
```typescript
// 에러 바운더리
<ErrorBoundary fallback={<InstructorErrorPage />}>
  <InstructorPage />
</ErrorBoundary>

// 폼 에러 처리
if (errors.name) {
  <p className="text-destructive text-xs font-semibold mt-1">
    {errors.name.message}
  </p>
}
```

## 9. 접근성 및 사용성

### 9.1 접근성
- 모든 폼 요소에 적절한 라벨 제공
- 키보드 네비게이션 지원
- 스크린 리더 지원

### 9.2 반응형 디자인
- 모바일 우선 설계
- 태블릿/데스크톱 대응

### 9.3 사용자 피드백
- 실시간 폼 검증
- 명확한 에러 메시지
- 성공/실패 토스트 알림

## 10. 구현 우선순위

### Phase 1: 기본 CRUD 구현
1. 강사 목록 페이지 (기존 컴포넌트 개선)
2. 강사 등록 페이지 (기본 폼)
3. 강사 수정 페이지
4. 강사 삭제 기능

### Phase 2: 고급 기능
1. 이미지 업로드 기능
2. 전문분야/자격증 동적 관리
3. 스튜디오별 필터링
4. 검색 기능

### Phase 3: UX 개선
1. 로딩 스켈레톤 UI
2. 에러 바운더리
3. 반응형 디자인 최적화
4. 접근성 개선

## 11. 테스트 전략

### 11.1 단위 테스트
- 폼 검증 로직
- 데이터 변환 유틸리티
- 컴포넌트 렌더링

### 11.2 통합 테스트
- API 호출 흐름
- 폼 제출 프로세스
- 에러 처리 시나리오

### 11.3 E2E 테스트
- 강사 등록/수정/삭제 전체 플로우
- 이미지 업로드 플로우
- 반응형 디자인 검증

이 계획서를 바탕으로 단계적으로 구현하여 일관성 있고 사용자 친화적인 강사 관리 시스템을 완성할 수 있습니다.