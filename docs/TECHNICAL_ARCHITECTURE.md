# ShallWe 기술 아키텍처 문서

## 📋 문서 개요

**작성 기준**: 실제 구현된 기술 스택 및 아키텍처 분석  
**마지막 업데이트**: 2025년 1월 17일  
**대상 독자**: 개발팀, 데브옵스팀, 기술 리더

## 🏗️ 시스템 아키텍처

### 전체 아키텍처 개요
```mermaid
graph TB
    subgraph "Client Layer"
        A[Next.js 15 App]
        B[React 19 Components]
        C[Tailwind CSS]
    end
    
    subgraph "API Layer"
        D[Next.js API Routes]
        E[Authentication Middleware]
        F[Drizzle ORM]
    end
    
    subgraph "Database Layer"
        G[(PostgreSQL)]
        H[Supabase]
        I[Row Level Security]
    end
    
    subgraph "External Services"
        J[Supabase Auth]
        K[File Storage]
        L[Email Service]
    end
    
    A --> D
    B --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    
    E --> J
    D --> K
    D --> L
```

### 기술 스택 상세

#### Frontend
- **Framework**: Next.js 15 (App Router)
- **UI Library**: React 19
- **Styling**: Tailwind CSS v4
- **State Management**: React useState/useEffect
- **Form Handling**: React Hook Form + Zod
- **UI Components**: Radix UI + Custom Components

#### Backend
- **API**: Next.js API Routes
- **Database**: PostgreSQL (Supabase 호스팅)
- **ORM**: Drizzle ORM
- **Authentication**: Supabase Auth
- **Security**: Row Level Security (RLS)

#### DevOps & Tools
- **Build Tool**: Vite 6.3.5
- **Type Checking**: TypeScript 5
- **Code Quality**: ESLint + Prettier
- **Package Manager**: npm
- **Development**: Next.js dev server with Turbopack

## 🗄️ 데이터베이스 아키텍처

### 스키마 설계 원칙

#### 1. 이중 구조 (현재 vs 계획)
```typescript
// 현재 구현 (src/lib/db/schema.ts)
const currentSchema = {
  members: "기본 회원 정보",
  instructors: "강사 정보",
  instructor_specialties: "전문분야",
  instructor_accounts: "정산 계좌",
  class_templates: "클래스 템플릿",
  class_schedule_groups: "스케줄 그룹",
  class_schedules: "개별 스케줄",
  class_enrollments: "수강 신청",
  class_reviews: "후기"
};

// 계획된 확장 (schema.sql)
const plannedSchema = {
  ...currentSchema,
  class_occurrences: "실제 수업 인스턴스",
  class_attendances: "출석 기록",
  // 라이프사이클 필드 확장
};
```

#### 2. 정규화 전략
- **3NF 준수**: 중복 제거 및 무결성 보장
- **소프트 레퍼런스**: 성능을 위한 FK 제약 조건 생략
- **인덱스 최적화**: 검색 성능 향상
- **DB Level enum 제거**: 코드 레벨에서만 관리

#### 3. 보안 정책 (RLS)
```sql
-- 회원 데이터 접근 제어
CREATE POLICY "members_own_data" ON members
  FOR ALL USING (auth.uid() = id);

-- 강사 클래스 접근 제어
CREATE POLICY "instructors_own_classes" ON class_templates
  FOR ALL USING (instructor_id IN (
    SELECT id FROM instructors WHERE member_id = auth.uid()
  ));

-- 수강생 신청 접근 제어
CREATE POLICY "students_own_enrollments" ON class_enrollments
  FOR ALL USING (member_id = auth.uid());
```

### 데이터 흐름

#### 1. 사용자 인증 흐름
```mermaid
sequenceDiagram
    participant C as Client
    participant A as API Route
    participant S as Supabase
    participant D as Database
    
    C->>A: 로그인 요청
    A->>S: Auth 검증
    S-->>A: JWT 토큰
    A->>D: 사용자 정보 조회
    D-->>A: 사용자 데이터
    A-->>C: 인증 성공 + 사용자 정보
```

#### 2. 클래스 생성 흐름
```mermaid
sequenceDiagram
    participant U as User
    participant API as API Route
    participant ORM as Drizzle ORM
    participant DB as PostgreSQL
    
    U->>API: 클래스 생성 요청
    API->>API: 권한 검증
    API->>ORM: 트랜잭션 시작
    ORM->>DB: class_templates 생성
    ORM->>DB: class_schedule_groups 생성
    ORM->>DB: class_schedules 생성
    ORM->>DB: 트랜잭션 커밋
    DB-->>ORM: 성공 응답
    ORM-->>API: 생성된 데이터
    API-->>U: 클래스 생성 완료
```

### 성능 최적화 전략

#### 1. 인덱스 설계
```sql
-- 클래스 검색 최적화
CREATE INDEX idx_templates_search ON class_templates (
  category, level, status, recruitment_end_date
);

-- 스케줄 검색 최적화
CREATE INDEX idx_schedules_search ON class_schedules (
  day_of_week, start_time, end_time
);

-- 조인 최적화
CREATE INDEX idx_schedule_groups_template ON class_schedule_groups (class_template_id);
CREATE INDEX idx_schedules_group ON class_schedules (schedule_group_id);
```

#### 2. 쿼리 최적화
```typescript
// 효율적인 조인 쿼리
const classesWithStats = await db
  .select({
    ...classFields,
    enrollmentCount: count(classEnrollments.id)
  })
  .from(class_templates)
  .leftJoin(classEnrollments, eq(class_templates.id, classEnrollments.class_template_id))
  .groupBy(class_templates.id)
  .where(eq(class_templates.instructor_id, instructorId));
```

#### 3. 캐싱 전략
```typescript
// 메모리 기반 캐싱 (미구현)
const cacheStrategy = {
  classTemplates: {
    ttl: 300, // 5분
    key: (id: string) => `class:${id}`
  },
  instructorStats: {
    ttl: 600, // 10분
    key: (id: string) => `instructor:${id}:stats`
  }
};
```

## 🔐 보안 아키텍처

### 인증 및 권한 관리

#### 1. Supabase Auth 통합
```typescript
// 클라이언트 인증
const { data: { user }, error } = await supabase.auth.getUser();

// 서버 사이드 인증
const { supabase } = createClient(request);
const { data: { user }, error } = await supabase.auth.getUser();
```

#### 2. API 권한 검증
```typescript
// 공통 인증 미들웨어
export async function authenticateInstructor(request: NextRequest) {
  const { supabase } = createClient(request);
  
  // 사용자 인증 확인
  const { data: { user }, error } = await supabase.auth.getUser();
  if (!user) {
    return { error: "UNAUTHORIZED", status: 401 };
  }
  
  // 강사 권한 확인
  const instructor = await db
    .select()
    .from(instructors)
    .where(eq(instructors.member_id, user.id))
    .limit(1);
    
  if (!instructor.length) {
    return { error: "FORBIDDEN", status: 403 };
  }
  
  return { user, instructor: instructor[0] };
}
```

#### 3. 데이터 접근 제어
```typescript
// 리소스 소유권 검증
const verifyClassOwnership = async (classId: string, instructorId: string) => {
  const classTemplate = await db
    .select()
    .from(class_templates)
    .where(and(
      eq(class_templates.id, classId),
      eq(class_templates.instructor_id, instructorId)
    ))
    .limit(1);
    
  return classTemplate.length > 0;
};
```

### 보안 위협 대응

#### 1. SQL 인젝션 방지
```typescript
// Drizzle ORM 사용으로 자동 방지
const classes = await db
  .select()
  .from(class_templates)
  .where(eq(class_templates.title, userInput)); // 자동 이스케이핑
```

#### 2. XSS 방지
```typescript
// 입력 검증
const sanitizeInput = (input: string) => {
  return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
};

// HTML 출력 시 이스케이핑 (React가 기본 제공)
const ClassTitle = ({ title }: { title: string }) => (
  <h1>{title}</h1> // 자동 이스케이핑
);
```

#### 3. CSRF 방지
```typescript
// Next.js API Routes는 기본적으로 CSRF 보호
// 추가 보안을 위한 Origin 검증
const verifyOrigin = (request: NextRequest) => {
  const origin = request.headers.get('origin');
  const allowedOrigins = [process.env.NEXT_PUBLIC_SITE_URL];
  return allowedOrigins.includes(origin);
};
```

## 🚀 성능 및 확장성

### 프론트엔드 성능

#### 1. 코드 스플리팅
```typescript
// 동적 임포트
const InstructorDashboard = dynamic(() => import('./InstructorDashboard'), {
  loading: () => <LoadingSpinner />,
  ssr: false
});

// 조건부 로딩
const AdminPanel = dynamic(() => import('./AdminPanel'), {
  loading: () => <div>관리자 패널 로딩 중...</div>
});
```

#### 2. 이미지 최적화
```typescript
// Next.js Image 컴포넌트 활용
import Image from 'next/image';

<Image
  src="/studio-image.jpg"
  alt="스튜디오 이미지"
  width={600}
  height={400}
  loading="lazy"
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
/>
```

#### 3. 번들 최적화
```javascript
// next.config.js
const nextConfig = {
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons']
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production'
  }
};
```

### 백엔드 성능

#### 1. 데이터베이스 연결 최적화
```typescript
// 연결 풀 설정
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};
```

#### 2. API 응답 최적화
```typescript
// 페이지네이션 구현
const getClassesPaginated = async (
  instructorId: string,
  page: number = 1,
  limit: number = 10
) => {
  const offset = (page - 1) * limit;
  
  const classes = await db
    .select()
    .from(class_templates)
    .where(eq(class_templates.instructor_id, instructorId))
    .limit(limit)
    .offset(offset);
    
  const total = await db
    .select({ count: count() })
    .from(class_templates)
    .where(eq(class_templates.instructor_id, instructorId));
    
  return {
    classes,
    pagination: {
      page,
      limit,
      total: total[0].count,
      pages: Math.ceil(total[0].count / limit)
    }
  };
};
```

### 확장성 고려사항

#### 1. 마이크로서비스 준비
```typescript
// 도메인별 API 분리
const apiStructure = {
  '/api/auth/*': 'Authentication Service',
  '/api/instructor/*': 'Instructor Service',
  '/api/classes/*': 'Class Management Service',
  '/api/payments/*': 'Payment Service (미래)',
  '/api/notifications/*': 'Notification Service (미래)'
};
```

#### 2. 데이터베이스 샤딩 준비
```typescript
// 테넌트 기반 분할 준비
const getShardKey = (instructorId: string) => {
  return instructorId.slice(-1); // 마지막 문자로 샤드 결정
};

const getDatabase = (shardKey: string) => {
  return databases[`shard_${shardKey}`] || databases.default;
};
```

## 📦 배포 및 운영

### 배포 전략

#### 1. Vercel 배포
```typescript
// vercel.json
{
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        { "key": "Access-Control-Allow-Origin", "value": "*" },
        { "key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS" },
        { "key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization" }
      ]
    }
  ]
}
```

#### 2. 환경 변수 관리
```typescript
// .env.local
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
DATABASE_URL=postgresql://user:password@host:port/database
```

### 모니터링 및 로깅

#### 1. 에러 추적
```typescript
// 에러 로깅 유틸리티
const logError = (error: Error, context: string) => {
  console.error(`[${context}] ${error.message}`, {
    stack: error.stack,
    timestamp: new Date().toISOString(),
    context
  });
  
  // 프로덕션에서는 외부 서비스로 전송
  if (process.env.NODE_ENV === 'production') {
    // Sentry, LogRocket 등으로 전송
  }
};
```

#### 2. 성능 모니터링
```typescript
// API 응답 시간 측정
const withPerformanceLogging = (handler: NextApiHandler) => {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const startTime = Date.now();
    
    try {
      await handler(req, res);
    } finally {
      const duration = Date.now() - startTime;
      console.log(`API ${req.url} took ${duration}ms`);
    }
  };
};
```

## 🔄 마이그레이션 계획

### Phase 1: 현재 시스템 안정화
```typescript
// 라이프사이클 필드 활용
const enableLifecycleFields = async () => {
  await db.execute(sql`
    UPDATE class_templates 
    SET 
      recruitment_start_date = CURRENT_DATE,
      recruitment_end_date = CURRENT_DATE + INTERVAL '7 days',
      class_start_date = CURRENT_DATE + INTERVAL '14 days',
      class_end_date = CURRENT_DATE + INTERVAL '28 days'
    WHERE recruitment_start_date IS NULL;
  `);
};
```

### Phase 2: Occurrence 시스템 도입
```typescript
// 점진적 Occurrence 생성
const migrateToOccurrences = async () => {
  const templates = await db
    .select()
    .from(class_templates)
    .where(eq(class_templates.is_active, true));
    
  for (const template of templates) {
    await generateOccurrencesForTemplate(template.id);
  }
};
```

### Phase 3: 고도화 기능 추가
```typescript
// 출석 시스템 도입
const enableAttendanceSystem = async () => {
  // 테이블 생성
  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS class_attendances (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      class_occurrence_id UUID NOT NULL,
      member_id UUID NOT NULL,
      attendance_status TEXT NOT NULL,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `);
  
  // 인덱스 생성
  await db.execute(sql`
    CREATE INDEX idx_attendances_occurrence 
    ON class_attendances (class_occurrence_id);
  `);
};
```

## 📊 기술 부채 및 개선 사항

### 현재 기술 부채
1. **타입 안전성**: 일부 API 응답에서 타입 불일치
2. **에러 처리**: 통일된 에러 핸들링 부재
3. **테스트**: 단위 테스트 및 통합 테스트 부족
4. **성능**: 데이터베이스 쿼리 최적화 여지
5. **보안**: API Rate Limiting 미구현

### 개선 계획
```typescript
// 1. 타입 안전성 개선
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

// 2. 통일된 에러 핸들링
class ApiError extends Error {
  constructor(
    public statusCode: number,
    public code: string,
    message: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 3. 테스트 프레임워크 도입
// Jest + React Testing Library 설정 예정

// 4. 성능 모니터링 도구 도입
// Vercel Analytics, 커스텀 메트릭 수집

// 5. 보안 강화
// Rate limiting, API key 관리, 입력 검증 강화
```

---

**이 기술 아키텍처 문서는 실제 구현된 시스템을 기반으로 작성되었으며, 지속적으로 업데이트됩니다.**