# ShallWe 플랫폼 종합 도메인 명세서

## 📋 문서 개요

**문서 목적**: ShallWe 플랫폼의 전체 도메인 구조와 비즈니스 정책을 종합적으로 정리  
**작성 기준**: 실제 구현 코드 분석 + 계획된 확장 기능  
**대상 독자**: 기획팀, 개발팀, 운영팀  
**최종 업데이트**: 2025년 1월 17일

## 🏗️ 플랫폼 아키텍처 개요

### 현재 구현 상태
- **기반 시스템**: 강사 중심 클래스 관리 시스템
- **기술 스택**: Next.js 15, React 19, Drizzle ORM, Supabase
- **데이터베이스**: PostgreSQL (Supabase 호스팅)
- **인증**: Supabase Auth + JWT

### 계획된 확장 (클래스 라이프사이클 시스템)
- **Schedule-Occurrence 구조**: 패턴 기반 실제 수업 관리
- **다중 시간대 지원**: 하나의 클래스 템플릿에 여러 시간대 그룹
- **출석 관리**: 개별 수업별 출석 체크 시스템
- **라이프사이클 관리**: 모집→진행→완료 단계별 상태 관리

## 👥 Members 도메인

### 도메인 개요
회원 관리의 핵심 도메인으로, 학생과 강사 모두를 포괄하는 사용자 관리 시스템입니다.

### 핵심 엔티티

#### 1. Members (회원)
```typescript
interface Member {
  id: string;              // auth.users.id와 동일
  nickname?: string;
  name: string;           // 실명 (온보딩 1단계)
  phone: string;          // 전화번호 (온보딩 1단계)
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  birth_date?: Date;
  role: 'STUDENT' | 'INSTRUCTOR' | 'ADMIN';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  created_at: Date;
  updated_at: Date;
}
```

**비즈니스 규칙**:
- `id`는 Supabase auth.users.id와 1:1 매핑
- `role`에 따라 추가 정보 테이블 연결 (강사 → instructors)
- `status`가 'ACTIVE'인 경우만 서비스 이용 가능

#### 2. 회원 상태 관리
```mermaid
stateDiagram-v2
    [*] --> ACTIVE : 회원가입 완료
    ACTIVE --> INACTIVE : 비활성화
    INACTIVE --> ACTIVE : 재활성화
    ACTIVE --> SUSPENDED : 정지
    SUSPENDED --> ACTIVE : 정지 해제
```

### 온보딩 프로세스

#### 공통 온보딩 (1단계)
1. **기본 정보 입력**
   - 실명, 전화번호, 성별
   - 필수 정보로 모든 회원 공통

#### 강사 온보딩 (2-5단계)
2. **전문분야 선택** → `instructor_specialties` 테이블
3. **자기소개 작성** → `instructors` 테이블
4. **자격증 등록** → `instructor_certificates` 테이블
5. **계좌 정보** → `instructor_accounts` 테이블

### 권한 시스템

#### 역할별 권한
- **STUDENT**: 클래스 검색, 수강신청, 출석 확인
- **INSTRUCTOR**: 클래스 등록, 수강생 관리, 출석 체크
- **ADMIN**: 전체 시스템 관리

#### RLS (Row Level Security) 정책
```sql
-- 회원은 자신의 정보만 접근 가능
CREATE POLICY "members_own_data" ON members
  FOR ALL USING (auth.uid() = id);

-- 강사는 자신의 클래스 정보만 접근 가능
CREATE POLICY "instructors_own_classes" ON class_templates
  FOR ALL USING (instructor_id IN (
    SELECT id FROM instructors WHERE member_id = auth.uid()
  ));
```

## 🎓 Instructors 도메인

### 도메인 개요
강사 전용 정보와 클래스 운영에 필요한 모든 데이터를 관리하는 도메인입니다.

### 핵심 엔티티

#### 1. Instructors (강사 정보)
```typescript
interface Instructor {
  id: string;
  member_id: string;        // members.id 참조
  short_bio?: string;       // 한줄 소개
  detailed_bio?: string;    // 상세 소개
  is_active: boolean;       // 강사 활동 상태
  created_at: Date;
  updated_at: Date;
}
```

#### 2. Instructor Specialties (전문분야)
```typescript
interface InstructorSpecialty {
  id: string;
  instructor_id: string;
  specialty: 'YOGA' | 'PILATES' | 'FITNESS' | 'CROSSFIT' | 'SWIMMING' | 
           'BOXING' | 'DANCE' | 'RUNNING' | 'CLIMBING' | 'MARTIAL_ARTS' |
           'MEDITATION' | 'STRETCHING' | 'BARRE' | 'SPINNING' | 'ZUMBA' |
           'KICKBOXING' | 'THERAPEUTIC';
  experience_years: number;
  created_at: Date;
}
```

#### 3. Instructor Certificates (자격증)
```typescript
interface InstructorCertificate {
  id: string;
  instructor_id: string;
  certificate_name: string;
  issuing_organization: string;
  issue_date: Date;
  expiry_date?: Date;
  certificate_number?: string;
  image_url?: string;
  is_verified: boolean;
  created_at: Date;
  updated_at: Date;
}
```

#### 4. Instructor Accounts (정산 계좌)
```typescript
interface InstructorAccount {
  id: string;
  instructor_id: string;
  bank_name: string;
  account_number: string;    // 암호화 저장 권장
  account_holder: string;
  is_verified: boolean;
  created_at: Date;
  updated_at: Date;
}
```

### 강사 온보딩 플로우

```mermaid
graph TD
    A[회원가입] --> B[role=INSTRUCTOR 선택]
    B --> C[1단계: 기본정보<br/>name, phone, gender]
    C --> D[2단계: 전문분야<br/>specialty, experience]
    D --> E[3단계: 자기소개<br/>bio]
    E --> F[4단계: 자격증<br/>certificates]
    F --> G[5단계: 계좌정보<br/>bank account]
    G --> H[온보딩 완료]
    H --> I[강사 활동 시작]
```

### 비즈니스 정책

#### 온보딩 완료 조건
- 모든 5단계 정보 입력 완료
- 계좌 정보 검증 완료 (권장)
- 관리자 승인 (선택사항)

#### 활동 정지 조건
- 신고 접수 시 일시 정지
- 규정 위반 시 계정 정지
- 장기간 비활성 시 자동 비활성화

## 🏫 Classes 도메인

### 도메인 개요
클래스 생성부터 운영까지 전체 라이프사이클을 관리하는 핵심 도메인입니다.

### 현재 구현 구조

#### 1. Class Templates (클래스 템플릿)
```typescript
interface ClassTemplate {
  id: string;
  studio_id: string;
  instructor_id: string;
  
  // 기본 정보
  title: string;
  description?: string;
  curriculum?: object;
  category: 'fitness' | 'yoga' | 'pilates' | 'dance' | 'martial_arts' | 'wellness';
  specialty: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'all_levels';
  
  // 수업 설정
  duration_minutes: number;
  price_per_session: number;
  max_capacity: number;
  
  // 🆕 라이프사이클 관리 (부분 구현)
  recruitment_start_date?: Date;
  recruitment_end_date?: Date;
  class_start_date?: Date;
  class_end_date?: Date;
  status?: 'upcoming' | 'recruiting' | 'ongoing' | 'completed' | 'cancelled';
  
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}
```

#### 2. Class Schedule Groups (스케줄 그룹)
```typescript
interface ClassScheduleGroup {
  id: string;
  class_template_id: string;
  group_name: string;         // "오후반", "저녁반"
  group_description?: string;
  max_participants: number;   // 그룹별 정원
  price_per_session?: number; // 그룹별 가격
  sessions_per_week: number;  // 주당 횟수
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}
```

#### 3. Class Schedules (개별 스케줄)
```typescript
interface ClassSchedule {
  id: string;
  schedule_group_id: string;
  day_of_week: 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY' | 'SATURDAY' | 'SUNDAY';
  start_time: string;         // '14:00:00'
  end_time: string;           // '15:00:00'
  max_participants?: number;  // 개별 정원 (옵션)
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}
```

### 계획된 확장 구조

#### 4. Class Occurrences (실제 수업) - 미구현
```typescript
interface ClassOccurrence {
  id: string;
  class_template_id: string;
  schedule_group_id: string;
  class_schedule_id: string;
  
  // 실제 수업 정보
  occurrence_date: Date;      // '2024-12-02'
  start_time: string;         // 개별 조정 가능
  end_time: string;
  max_participants: number;
  
  // 상태 관리
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  attendance_count: number;
  confirmed_enrollments: number;
  
  // 운영 정보
  instructor_notes?: string;
  cancellation_reason?: string;
  
  // 보강 수업
  is_substitute_class: boolean;
  original_occurrence_id?: string;
  
  created_at: Date;
  updated_at: Date;
}
```

### 클래스 라이프사이클

#### 현재 구현 (기본)
```mermaid
graph TD
    A[클래스 생성] --> B[스케줄 그룹 설정]
    B --> C[개별 스케줄 등록]
    C --> D[수강생 신청]
    D --> E[클래스 진행]
    E --> F[완료/평가]
```

#### 계획된 확장 (고도화)
```mermaid
graph TD
    A[클래스 템플릿 생성] --> B[모집 기간 설정]
    B --> C[모집 시작]
    C --> D[수강생 신청]
    D --> E[모집 종료]
    E --> F[Occurrence 생성]
    F --> G[수업 진행]
    G --> H[출석 체크]
    H --> I[수업 완료]
    I --> J[전체 클래스 완료]
    
    G --> K[휴강 처리]
    K --> L[보강 수업 생성]
    L --> G
```

#### 상태 전환 정책
```typescript
function getClassStatus(template: ClassTemplate, now = new Date()) {
  if (template.status === 'cancelled') return 'cancelled';
  if (!template.recruitment_start_date) return 'upcoming';
  
  if (now < template.recruitment_start_date) return 'upcoming';
  if (now <= template.recruitment_end_date) return 'recruiting';
  if (now <= template.class_end_date) return 'ongoing';
  return 'completed';
}
```

### 수강 신청 시스템

#### 1. Class Enrollments (수강 신청)
```typescript
interface ClassEnrollment {
  id: string;
  member_id: string;
  class_template_id: string;
  schedule_group_id: string;    // 🆕 특정 시간대 그룹 선택
  
  enrollment_status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'refunded';
  enrolled_at: Date;
  
  // 결제 정보
  payment_id?: string;
  paid_amount?: number;
  
  // 환불 정보
  refund_amount?: number;
  refunded_at?: Date;
  refund_reason?: string;
  
  notes?: string;
  created_at: Date;
  updated_at: Date;
}
```

#### 신청 프로세스
```mermaid
sequenceDiagram
    participant U as 사용자
    participant S as 시스템
    participant P as 결제 시스템
    
    U->>S: 클래스 검색
    S-->>U: 클래스 목록 (시간대별)
    U->>S: 특정 시간대 그룹 선택
    S-->>U: 신청 확인
    U->>S: 신청 제출
    S->>P: 결제 요청
    P-->>S: 결제 완료
    S->>S: 신청 상태 confirmed
    S-->>U: 신청 완료 알림
```

### 계획된 출석 시스템

#### 2. Class Attendances (출석 기록) - 미구현
```typescript
interface ClassAttendance {
  id: string;
  class_occurrence_id: string;
  member_id: string;
  enrollment_id?: string;
  
  attendance_status: 'present' | 'absent' | 'late' | 'excused';
  checked_in_at?: Date;
  checked_out_at?: Date;
  notes?: string;
  
  created_at: Date;
}
```

### 비즈니스 정책

#### 정원 관리
- 클래스 템플릿 레벨: 전체 최대 정원
- 스케줄 그룹 레벨: 시간대별 정원
- 개별 스케줄 레벨: 특정 요일 정원 (옵션)

#### 가격 정책
- 기본 가격: 클래스 템플릿 레벨
- 그룹별 가격: 시간대별 차등 가격 (옵션)
- 동적 가격: 수요에 따른 가격 조정 (미래 계획)

#### 환불 정책
- 모집 기간 내: 전액 환불
- 클래스 시작 전: 부분 환불
- 클래스 시작 후: 환불 불가 (예외 상황 제외)

## 🏢 Studios 도메인

### 도메인 개요
클래스가 진행되는 물리적 공간을 관리하는 도메인입니다.

### 핵심 엔티티

#### Studios (스튜디오)
```typescript
interface Studio {
  id: string;
  name: string;
  description?: string;
  address: string;
  studio_type: 'fitness' | 'yoga' | 'pilates' | 'dance' | 'martial_arts' | 'wellness';
  
  // 위치 정보
  latitude?: number;
  longitude?: number;
  nearest_station?: string;  // "강남역 2호선"
  
  // 부가 정보
  amenities?: object;        // 편의시설
  links?: object;           // 관련 링크
  
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}
```

### 비즈니스 정책

#### 스튜디오 등록
- 관리자 승인 필요
- 위치 정보 필수
- 편의시설 정보 권장

#### 클래스 연결
- 활성 스튜디오에만 클래스 등록 가능
- 스튜디오 타입과 클래스 카테고리 일치 권장

## 📊 Analytics & Reviews 도메인

### 리뷰 시스템

#### Class Reviews (클래스 후기)
```typescript
interface ClassReview {
  id: string;
  member_id: string;
  class_template_id: string;
  instructor_id: string;
  
  rating: number;           // 1-5 별점
  comment?: string;
  is_anonymous: boolean;
  is_verified: boolean;
  helpful_count: number;
  
  created_at: Date;
}
```

### 비즈니스 정책

#### 리뷰 작성 조건
- 수강 완료한 클래스만 리뷰 가능
- 회원당 클래스별 1개 리뷰만 가능
- 익명 리뷰 지원

#### 평점 계산
- 강사 평점: 모든 클래스 리뷰 평균
- 클래스 평점: 해당 클래스 리뷰 평균
- 검증된 리뷰 가중치 적용

## 🔄 마이그레이션 계획

### Phase 1: 현재 구현 완성
1. 라이프사이클 필드 활용
2. 스케줄 그룹 기능 완성
3. 시간대별 신청 구현

### Phase 2: 출석 시스템 추가
1. Occurrence 테이블 생성
2. 출석 체크 기능 구현
3. 출석률 통계 제공

### Phase 3: 고도화 기능
1. 동적 가격 정책
2. 자동 알림 시스템
3. 고급 분석 기능

## 📈 성장 전략

### 확장 방향
1. **다중 지역 지원**: 지역별 스튜디오 확장
2. **다양한 클래스 형태**: 온라인, 하이브리드, 캠프
3. **커뮤니티 기능**: 수강생 간 소통 플랫폼
4. **파트너십**: 기업 복지, 헬스케어 연계

### 비즈니스 모델
- **수수료 모델**: 클래스 수강료의 일정 비율
- **구독 모델**: 월 정액 무제한 수강
- **프리미엄 서비스**: 1:1 레슨, 전담 관리

---

**이 문서는 ShallWe 플랫폼의 현재 구현과 미래 계획을 종합적으로 다루며, 실제 코드 분석을 바탕으로 작성되었습니다.**