{"info": {"name": "ShallWe Partner Upload API", "description": "파트너 이미지 업로드 API 테스트 컬렉션", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "projectRef", "value": "dxodiiizyfzpueyvoaqr", "type": "string"}, {"key": "authToken", "value": "your-auth-token-here", "type": "string", "description": "브라우저 개발자 도구에서 sb-{{projectRef}}-auth-token 쿠키 값을 복사하여 입력하세요"}, {"key": "testStudioId", "value": "550e8400-e29b-41d4-a716-446655440000", "type": "string"}], "item": [{"name": "🔴 이미지 업로드 - 인증 없음 (401 에러)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "이미지 파일을 선택하세요 (JPG, PNG, WEBP)"}, {"key": "type", "value": "studio", "type": "text"}, {"key": "studioId", "value": "{{testStudioId}}", "type": "text"}, {"key": "prefix", "value": "featured", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/partner/upload", "host": ["{{baseUrl}}"], "path": ["api", "partner", "upload"]}}, "response": []}, {"name": "✅ 스튜디오 이미지 업로드 (인증 포함)", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "sb-{{projectRef}}-auth-token={{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "이미지 파일을 선택하세요 (JPG, PNG, WEBP, 최대 5MB)"}, {"key": "type", "value": "studio", "type": "text"}, {"key": "studioId", "value": "{{testStudioId}}", "type": "text"}, {"key": "prefix", "value": "featured", "type": "text", "description": "featured 또는 gallery"}]}, "url": {"raw": "{{baseUrl}}/api/partner/upload", "host": ["{{baseUrl}}"], "path": ["api", "partner", "upload"]}}, "response": []}, {"name": "✅ 갤러리 이미지 업로드", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "sb-{{projectRef}}-auth-token={{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "갤러리 이미지 파일을 선택하세요"}, {"key": "type", "value": "studio", "type": "text"}, {"key": "studioId", "value": "{{testStudioId}}", "type": "text"}, {"key": "prefix", "value": "gallery", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/partner/upload", "host": ["{{baseUrl}}"], "path": ["api", "partner", "upload"]}}, "response": []}, {"name": "🔴 잘못된 파일 형식 (422 에러)", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "sb-{{projectRef}}-auth-token={{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "PDF 또는 TXT 파일 등 허용되지 않는 형식의 파일을 선택하세요"}, {"key": "type", "value": "studio", "type": "text"}, {"key": "studioId", "value": "{{testStudioId}}", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/partner/upload", "host": ["{{baseUrl}}"], "path": ["api", "partner", "upload"]}}, "response": []}, {"name": "🔴 스튜디오 ID 누락 (400 에러)", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "sb-{{projectRef}}-auth-token={{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "이미지 파일을 선택하세요"}, {"key": "type", "value": "studio", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/partner/upload", "host": ["{{baseUrl}}"], "path": ["api", "partner", "upload"]}}, "response": []}, {"name": "🔴 파일 없음 (400 에러)", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "sb-{{projectRef}}-auth-token={{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "type", "value": "studio", "type": "text"}, {"key": "studioId", "value": "{{testStudioId}}", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/partner/upload", "host": ["{{baseUrl}}"], "path": ["api", "partner", "upload"]}}, "response": []}, {"name": "✅ 이미지 삭제 (단일)", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "sb-{{projectRef}}-auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"path\": \"studios/your-partner-id/your-studio-id/featured-1234567890.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/partner/upload", "host": ["{{baseUrl}}"], "path": ["api", "partner", "upload"]}}, "response": []}, {"name": "🔴 다른 파트너 이미지 삭제 시도 (403 에러)", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "sb-{{projectRef}}-auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"path\": \"studios/other-partner-id/other-studio-id/image.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/partner/upload", "host": ["{{baseUrl}}"], "path": ["api", "partner", "upload"]}}, "response": []}, {"name": "🔴 경로 누락 (400 에러)", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "sb-{{projectRef}}-auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/partner/upload", "host": ["{{baseUrl}}"], "path": ["api", "partner", "upload"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 인증 토큰 확인", "if (!pm.collectionVariables.get('authToken') || pm.collectionVariables.get('authToken') === 'your-auth-token-here') {", "    console.warn('❌ authToken이 설정되지 않았습니다. 브라우저에서 로그인 후 쿠키 값을 복사하여 설정하세요.');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 공통 응답 검증", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// 성공 응답 검증", "if (pm.response.code === 200) {", "    pm.test('Success response has required fields', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('success');", "        if (jsonData.url) {", "            pm.expect(jsonData.url).to.include('supabase.co/storage');", "        }", "    });", "}", "", "// 에러 응답 검증", "if (pm.response.code >= 400) {", "    pm.test('Error response has error field', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('error');", "    });", "}"]}}]}