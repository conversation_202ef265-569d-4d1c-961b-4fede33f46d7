### Partner Classes API 테스트 (실제 구현된 API)

# 변수 설정
@baseUrl = http://localhost:3000
@contentType = application/json
@projectRef = dxodiiizyfzpueyvoaqr

# 파트너 인증을 위한 변수 설정
# 실제 테스트 시에는 브라우저에서 로그인 후 개발자 도구의 Application > Cookies에서
# sb-{{projectRef}}-auth-token 쿠키 값을 복사해서 사용하세요
@authToken = ...

# 테스트용 ID들 (실제 테스트 시 적절한 값으로 변경)
@studioId = aa881146-755b-4311-a961-252d4abe4163
@instructorId = d0e666ed-a07a-4bdf-b49e-42a310686f3d
@classId = d0e666ed-a07a-4bdf-b49e-42a310686f3d

###############################################################################
# 인증 테스트 가이드
###############################################################################
# 
# 1. 브라우저에서 http://localhost:3000/partner/login 접속
# 2. 파트너 계정으로 로그인
# 3. 개발자 도구 (F12) 열기
# 4. Application > Storage > Cookies > localhost:3000
# 5. sb-{{projectRef}}-auth-token 쿠키 값 복사
# 6. 위의 @authToken 변수에 붙여넣기
# 7. 테스트 실행
#
# 주의사항:
# - 토큰은 보통 1시간 후 만료됩니다
# - 파트너 상태가 'ACTIVE'여야 API 사용 가능합니다
# - 다른 파트너의 클래스는 접근할 수 없습니다
# - 스튜디오와 강사가 파트너 소유여야 합니다
###############################################################################

###############################################################################
# 클래스 목록 조회 API
###############################################################################

###
# 🔴 파트너 클래스 목록 조회 - 인증 없음 (401 에러)
GET {{baseUrl}}/api/partner/classes
Accept: {{contentType}}

###
# ✅ 파트너 클래스 목록 조회 (인증 포함) - 전체 목록
GET {{baseUrl}}/api/partner/classes
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 파트너 클래스 목록 조회 - 스튜디오 필터링
GET {{baseUrl}}/api/partner/classes?studioId={{studioId}}
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 파트너 클래스 목록 조회 - 페이지네이션
GET {{baseUrl}}/api/partner/classes?page=1&limit=5
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 파트너 클래스 목록 조회 - 스튜디오 필터 + 페이지네이션
GET {{baseUrl}}/api/partner/classes?studioId={{studioId}}&page=1&limit=3
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###############################################################################
# 클래스 생성 API
###############################################################################

###
# 🔴 파트너 클래스 생성 - 인증 없음 (401 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "인증 없는 클래스",
  "description": "인증 없이 생성 시도",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 8,
  "pricePerSession": 15000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 4,
  "sessionsPerWeek": 2,
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "10:00",
          "endTime": "11:00"
        }
      ]
    }
  ]
}

#### ✅ 파트너 클래스 생성 - 단일 스케줄 그룹 (월수금 필라테스)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "고급 필라테스 클래스",
  "description": "경험자를 위한 고급 필라테스 수업입니다. 코어 강화와 전신 밸런스 향상에 중점을 둡니다.",
  "category": "pilates",
  "level": "advanced",
  "target": "women_only",
  "maxParticipants": 6,
  "pricePerSession": 25000,
  "sessionDurationMinutes": 75,
  "durationWeeks": 8,
  "sessionsPerWeek": 3,
  "images": [
    {
      "url": "https://example.com/pilates-class1.jpg",
      "path": "/uploads/classes/pilates-class1.jpg"
    },
    {
      "url": "https://example.com/pilates-class2.jpg",
      "path": "/uploads/classes/pilates-class2.jpg"
    }
  ],
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "19:00",
          "endTime": "20:15"
        },
        {
          "dayOfWeek": "wed",
          "startTime": "19:00",
          "endTime": "20:15"
        },
        {
          "dayOfWeek": "fri",
          "startTime": "19:00",
          "endTime": "20:15"
        }
      ]
    }
  ]
}

###
#### ✅ 파트너 클래스 생성 - 복수 스케줄 그룹 (오전반 + 저녁반)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "다시간대 요가 클래스",
  "description": "오전반과 저녁반으로 나누어 진행하는 요가 클래스입니다. 원하는 시간대를 선택할 수 있습니다.",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 8,
  "pricePerSession": 20000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 4,
  "sessionsPerWeek": 4,
  "images": [
    {
      "url": "https://example.com/multi-time-yoga.jpg",
      "path": "/uploads/classes/multi-time-yoga.jpg"
    }
  ],
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "10:00",
          "endTime": "11:00"
        },
        {
          "dayOfWeek": "fri",
          "startTime": "10:00",
          "endTime": "11:00"
        }
      ]
    },
    {
      "schedules": [
        {
          "dayOfWeek": "tue",
          "startTime": "19:00",
          "endTime": "20:00"
        },
        {
          "dayOfWeek": "thu",
          "startTime": "19:00",
          "endTime": "20:00"
        }
      ]
    }
  ]
}

###############################################################################
# 클래스 상세 조회 API
###############################################################################

###
# 🔴 파트너 클래스 상세 조회 - 인증 없음 (401 에러)
GET {{baseUrl}}/api/partner/classes/{{classId}}
Accept: {{contentType}}

###
# ✅ 파트너 클래스 상세 조회 (인증 포함)
# 위에서 생성된 클래스 ID로 변경 필요
GET {{baseUrl}}/api/partner/classes/{{classId}}
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###############################################################################
# 클래스 수정 API
###############################################################################

###
# 🔴 파트너 클래스 수정 - 인증 없음 (401 에러)
PUT {{baseUrl}}/api/partner/classes/{{classId}}
Content-Type: {{contentType}}

{
  "title": "인증 없는 수정"
}

###
# ✅ 파트너 클래스 수정 (인증 포함) - 부분 수정
PUT {{baseUrl}}/api/partner/classes/{{classId}}
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "title": "초급 요가 클래스 (수정됨)",
  "description": "수정된 설명입니다. 더욱 자세한 수업 내용을 안내합니다.",
  "pricePerSession": 18000,
  "maxParticipants": 10
}

###
# ✅ 파트너 클래스 수정 - 스케줄 변경 포함
PUT {{baseUrl}}/api/partner/classes/{{classId}}
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "title": "저녁 요가 클래스",
  "sessionDurationMinutes": 90,
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "tue",
          "startTime": "19:30",
          "endTime": "21:00"
        },
        {
          "dayOfWeek": "thu",
          "startTime": "19:30",
          "endTime": "21:00"
        }
      ]
    }
  ]
}

###
# ✅ 파트너 클래스 수정 - 이미지 업데이트
PUT {{baseUrl}}/api/partner/classes/{{classId}}
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "images": [
    {
      "url": "https://example.com/updated-class1.jpg",
      "path": "/uploads/classes/updated-class1.jpg"
    },
    {
      "url": "https://example.com/updated-class2.jpg",
      "path": "/uploads/classes/updated-class2.jpg"
    },
    {
      "url": "https://example.com/updated-class3.jpg",
      "path": "/uploads/classes/updated-class3.jpg"
    }
  ]
}

###
# ✅ 파트너 클래스 수정 - 상태 변경
PUT {{baseUrl}}/api/partner/classes/{{classId}}
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "status": "inactive",
  "visible": false
}

###############################################################################
# 클래스 삭제 API
###############################################################################

###
# 🔴 파트너 클래스 삭제 - 인증 없음 (401 에러)
DELETE {{baseUrl}}/api/partner/classes/{{classId}}
Cookie: sb-{{projectRef}}-auth-token=invalid

###
# ✅ 파트너 클래스 삭제 (인증 포함) - Soft Delete
DELETE {{baseUrl}}/api/partner/classes/{{classId}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###############################################################################
# 에러 케이스 테스트
###############################################################################

###
# 🔴 유효하지 않은 클래스 ID로 조회 (400 에러)
GET {{baseUrl}}/api/partner/classes/invalid-uuid
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# 🔴 존재하지 않는 클래스 조회 (404 에러)
GET {{baseUrl}}/api/partner/classes/00000000-0000-0000-0000-000000000000
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# 🔴 다른 파트너의 클래스 접근 시도 (403 에러)
GET {{baseUrl}}/api/partner/classes/11111111-1111-1111-1111-111111111111
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# 🔴 잘못된 스튜디오 ID로 클래스 생성 (422 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "00000000-0000-0000-0000-000000000000",
  "instructorId": "{{instructorId}}",
  "title": "존재하지 않는 스튜디오",
  "description": "잘못된 스튜디오 ID 테스트",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 8,
  "pricePerSession": 15000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 4,
  "sessionsPerWeek": 2,
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "10:00",
          "endTime": "11:00"
        }
      ]
    }
  ]
}

###
# 🔴 잘못된 강사 ID로 클래스 생성 (422 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "00000000-0000-0000-0000-000000000000",
  "title": "존재하지 않는 강사",
  "description": "잘못된 강사 ID 테스트",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 8,
  "pricePerSession": 15000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 4,
  "sessionsPerWeek": 2,
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "10:00",
          "endTime": "11:00"
        }
      ]
    }
  ]
}

###
# 🔴 필수 필드 누락으로 클래스 생성 (400 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "title": "필수 필드 누락 테스트"
}

###
# 🔴 잘못된 데이터 형식으로 클래스 생성 (400 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "",
  "description": "a",
  "category": "invalid_category",
  "level": "invalid_level",
  "target": "invalid_target",
  "maxParticipants": 1,
  "pricePerSession": 3000,
  "sessionDurationMinutes": 20,
  "durationWeeks": 3,
  "sessionsPerWeek": 8,
  "schedules": []
}

###
# 🔴 참가자 수 범위 초과 (400 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "참가자 수 초과 테스트",
  "description": "참가자 수가 최대 10명을 초과하는 테스트",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 15,
  "pricePerSession": 15000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 4,
  "sessionsPerWeek": 2,
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "10:00",
          "endTime": "11:00"
        }
      ]
    }
  ]
}

###
# 🔴 가격 범위 초과 (400 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "가격 범위 초과 테스트",
  "description": "가격이 최대 40,000원을 초과하는 테스트",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 8,
  "pricePerSession": 50000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 4,
  "sessionsPerWeek": 2,
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "10:00",
          "endTime": "11:00"
        }
      ]
    }
  ]
}

###
# 🔴 잘못된 수업 기간 (400 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "잘못된 수업 기간 테스트",
  "description": "4주 또는 8주가 아닌 기간 설정 테스트",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 8,
  "pricePerSession": 15000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 6,
  "sessionsPerWeek": 2,
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "10:00",
          "endTime": "11:00"
        }
      ]
    }
  ]
}

###
# 🔴 중복된 요일 스케줄 (400 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "중복 요일 테스트",
  "description": "같은 요일에 여러 시간대 설정 테스트",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 8,
  "pricePerSession": 15000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 4,
  "sessionsPerWeek": 2,
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "10:00",
          "endTime": "11:00"
        },
        {
          "dayOfWeek": "mon",
          "startTime": "14:00",
          "endTime": "15:00"
        }
      ]
    }
  ]
}

###
# 🔴 잘못된 시간 순서 (400 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "잘못된 시간 순서 테스트",
  "description": "시작 시간이 종료 시간보다 늦은 테스트",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 8,
  "pricePerSession": 15000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 4,
  "sessionsPerWeek": 2,
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "15:00",
          "endTime": "14:00"
        }
      ]
    }
  ]
}

###
# 🔴 이미지 개수 초과 (400 에러)
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "이미지 개수 초과 테스트",
  "description": "최대 5개를 초과하는 이미지 업로드 테스트",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 8,
  "pricePerSession": 15000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 4,
  "sessionsPerWeek": 2,
  "images": [
    {"url": "https://example.com/image1.jpg", "path": "/uploads/classes/image1.jpg"},
    {"url": "https://example.com/image2.jpg", "path": "/uploads/classes/image2.jpg"},
    {"url": "https://example.com/image3.jpg", "path": "/uploads/classes/image3.jpg"},
    {"url": "https://example.com/image4.jpg", "path": "/uploads/classes/image4.jpg"},
    {"url": "https://example.com/image5.jpg", "path": "/uploads/classes/image5.jpg"},
    {"url": "https://example.com/image6.jpg", "path": "/uploads/classes/image6.jpg"}
  ],
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "10:00",
          "endTime": "11:00"
        }
      ]
    }
  ]
}

###############################################################################
# 성공 케이스 추가 테스트
###############################################################################

###
# ✅ 다양한 카테고리 클래스 생성 - 피트니스
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "근력 강화 피트니스",
  "description": "전신 근력 강화에 중점을 둔 피트니스 클래스입니다.",
  "category": "fitness",
  "level": "intermediate",
  "target": "mixed",
  "maxParticipants": 10,
  "pricePerSession": 20000,
  "sessionDurationMinutes": 50,
  "durationWeeks": 4,
  "sessionsPerWeek": 3,
  "images": [
    {
      "url": "https://example.com/fitness-class1.jpg",
      "path": "/uploads/classes/fitness-class1.jpg"
    }
  ],
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "18:00",
          "endTime": "18:50"
        },
        {
          "dayOfWeek": "wed",
          "startTime": "18:00",
          "endTime": "18:50"
        },
        {
          "dayOfWeek": "fri",
          "startTime": "18:00",
          "endTime": "18:50"
        }
      ]
    }
  ]
}

###
# ✅ 남성 전용 클래스 생성
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "남성 전용 복싱 클래스",
  "description": "남성들을 위한 전문 복싱 수업입니다.",
  "category": "boxing",
  "level": "beginner",
  "target": "men_only",
  "maxParticipants": 8,
  "pricePerSession": 30000,
  "sessionDurationMinutes": 90,
  "durationWeeks": 8,
  "sessionsPerWeek": 2,
  "images": [
    {
      "url": "https://example.com/boxing-class1.jpg",
      "path": "/uploads/classes/boxing-class1.jpg"
    }
  ],
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "tue",
          "startTime": "20:00",
          "endTime": "21:30"
        },
        {
          "dayOfWeek": "thu",
          "startTime": "20:00",
          "endTime": "21:30"
        }
      ]
    }
  ]
}

###
# ✅ 8주 과정 장기 클래스 생성
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "8주 완성 수영 마스터",
  "description": "8주 동안 수영 기초부터 고급까지 완전 마스터하는 과정입니다.",
  "category": "swimming",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 6,
  "pricePerSession": 35000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 8,
  "sessionsPerWeek": 2,
  "images": [
    {
      "url": "https://example.com/swimming-class1.jpg",
      "path": "/uploads/classes/swimming-class1.jpg"
    }
  ],
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "07:00",
          "endTime": "08:00"
        },
        {
          "dayOfWeek": "fri",
          "startTime": "07:00",
          "endTime": "08:00"
        }
      ]
    }
  ]
}

###
# ✅ 최대 참가자 수와 최고 가격 클래스
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "프리미엄 클라이밍 클래스",
  "description": "최고급 클라이밍 시설에서 진행하는 프리미엄 수업입니다.",
  "category": "climbing",
  "level": "advanced",
  "target": "mixed",
  "maxParticipants": 10,
  "pricePerSession": 40000,
  "sessionDurationMinutes": 120,
  "durationWeeks": 4,
  "sessionsPerWeek": 1,
  "images": [
    {
      "url": "https://example.com/climbing-class1.jpg",
      "path": "/uploads/classes/climbing-class1.jpg"
    }
  ],
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "sat",
          "startTime": "14:00",
          "endTime": "16:00"
        }
      ]
    }
  ]
}

###############################################################################
# 필터링 및 페이지네이션 테스트
###############################################################################

###
# ✅ 잘못된 스튜디오 ID 필터링 (403 에러 - 다른 파트너 스튜디오)
GET {{baseUrl}}/api/partner/classes?studioId=11111111-1111-1111-1111-111111111111
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 존재하지 않는 스튜디오 ID 필터링 (422 에러)
GET {{baseUrl}}/api/partner/classes?studioId=00000000-0000-0000-0000-000000000000
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 잘못된 페이지 번호 (400 에러)
GET {{baseUrl}}/api/partner/classes?page=0
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 잘못된 limit 크기 (400 에러)
GET {{baseUrl}}/api/partner/classes?limit=101
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 대량 데이터 페이지네이션
GET {{baseUrl}}/api/partner/classes?page=1&limit=50
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###############################################################################
# 활성 수강신청이 있는 클래스 삭제 테스트
###############################################################################

###
# 🔴 활성 수강신청이 있는 클래스 삭제 시도 (422 에러)
# 주의: 실제 수강신청이 있는 클래스 ID로 변경 필요
DELETE {{baseUrl}}/api/partner/classes/class-with-enrollments
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###############################################################################
# 권한 테스트
###############################################################################

###
# 🔴 다른 파트너의 클래스 수정 시도 (403 에러)
PUT {{baseUrl}}/api/partner/classes/other-partner-class-id
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "title": "다른 파트너 클래스 수정 시도"
}

###
# 🔴 다른 파트너의 클래스 삭제 시도 (403 에러)
DELETE {{baseUrl}}/api/partner/classes/other-partner-class-id
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###############################################################################
# 실제 사용 시나리오
###############################################################################

###
# ✅ 실제 시나리오 - 클래스 생성부터 삭제까지
# 1단계: 클래스 생성
POST {{baseUrl}}/api/partner/classes
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "studioId": "{{studioId}}",
  "instructorId": "{{instructorId}}",
  "title": "시나리오 테스트 클래스",
  "description": "실제 사용 시나리오를 위한 테스트 클래스입니다.",
  "category": "yoga",
  "level": "beginner",
  "target": "mixed",
  "maxParticipants": 8,
  "pricePerSession": 15000,
  "sessionDurationMinutes": 60,
  "durationWeeks": 4,
  "sessionsPerWeek": 2,
  "images": [
    {
      "url": "https://example.com/test-class1.jpg",
      "path": "/uploads/classes/test-class1.jpg"
    }
  ],
  "scheduleGroups": [
    {
      "schedules": [
        {
          "dayOfWeek": "mon",
          "startTime": "10:00",
          "endTime": "11:00"
        },
        {
          "dayOfWeek": "wed",
          "startTime": "10:00",
          "endTime": "11:00"
        }
      ]
    }
  ]
}

# 2단계: 생성된 클래스 ID를 위의 @classId 변수에 설정 후 다음 테스트들 실행

###
# ✅ 2단계: 생성된 클래스 조회
GET {{baseUrl}}/api/partner/classes/{{classId}}
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 3단계: 클래스 정보 수정
PUT {{baseUrl}}/api/partner/classes/{{classId}}
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "title": "시나리오 테스트 클래스 (수정됨)",
  "description": "수정된 설명입니다.",
  "pricePerSession": 18000
}

###
# ✅ 4단계: 수정된 클래스 재조회
GET {{baseUrl}}/api/partner/classes/{{classId}}
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 5단계: 클래스 삭제
DELETE {{baseUrl}}/api/partner/classes/{{classId}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 6단계: 삭제된 클래스 조회 시도 (404 에러 예상)
GET {{baseUrl}}/api/partner/classes/{{classId}}
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}