### Member Class Schedules API 테스트

# 변수 설정
@baseUrl = http://localhost:3000
@contentType = application/json

# 테스트용 클래스 ID (실제 테스트 시 적절한 값으로 변경)
@classId = a54820fc-bf7e-48f7-aa50-d0044facea38
@invalidClassId = 00000000-0000-0000-0000-000000000000

###############################################################################
# 회원용 클래스 스케줄 조회 API
###############################################################################

###
# ✅ 클래스 스케줄 조회 - 정상 케이스
GET {{baseUrl}}/api/classes/{{classId}}/schedules
Accept: {{contentType}}

###
# 🔴 존재하지 않는 클래스 조회 (404 에러)
GET {{baseUrl}}/api/classes/{{invalidClassId}}/schedules
Accept: {{contentType}}

###
# 🔴 잘못된 UUID 형식 (400 에러)
GET {{baseUrl}}/api/classes/invalid-uuid/schedules
Accept: {{contentType}}

###############################################################################
# 응답 구조 검증용 예시
###############################################################################

# 예상 응답 구조:
# {
#   "class": {
#     "id": "class-uuid",
#     "title": "초급 요가 클래스",
#     "description": "요가 기초를 배우는 클래스입니다",
#     "category": "yoga",
#     "level": "beginner",
#     "target": "mixed",
#     "pricePerSession": 15000,
#     "maxParticipants": 8,
#     "sessionDurationMinutes": 60,
#     "durationWeeks": 4,
#     "sessionsPerWeek": 2,
#     "images": [...],
#     "studio": {
#       "id": "studio-uuid",
#       "name": "강남 요가 스튜디오",
#       "address": "서울시 강남구 테헤란로 123",
#       "phone": "02-1234-5678"
#     },
#     "instructor": {
#       "id": "instructor-uuid",
#       "name": "김강사",
#       "profileImages": [...]
#     }
#   },
#   "scheduleGroups": [
#     {
#       "id": 1,
#       "status": "confirmed",
#       "schedules": [
#         {
#           "id": 1,
#           "dayOfWeek": "mon",
#           "startTime": "10:00",
#           "endTime": "11:00"
#         },
#         {
#           "id": 2,
#           "dayOfWeek": "wed", 
#           "startTime": "10:00",
#           "endTime": "11:00"
#         }
#       ]
#     }
#   ]
# }

###############################################################################
# 테스트 시나리오
###############################################################################

# 1. 정상적인 클래스 조회
# 2. 존재하지 않는 클래스
# 3. 비활성화된 클래스 (접근 불가)
# 4. 비공개 클래스 (접근 불가)
# 5. 잘못된 UUID 형식

###############################################################################
# 사용법
###############################################################################

# 1. 실제 클래스 ID를 @classId 변수에 설정
# 2. 서버가 localhost:3000에서 실행 중인지 확인
# 3. 각 테스트 케이스를 순차적으로 실행
# 4. 응답 구조가 예상과 일치하는지 확인

###############################################################################
# 예약 Flow 연계
###############################################################################

# 이 API는 다음 단계에서 사용됩니다:
# 1. 회원이 클래스 목록에서 특정 클래스 선택
# 2. 이 API로 클래스 상세 정보 및 스케줄 그룹 조회
# 3. 회원이 원하는 스케줄 그룹 선택
# 4. 예약 생성 API 호출 (POST /api/bookings)
# 5. 결제 처리 API 호출 (POST /api/payments)