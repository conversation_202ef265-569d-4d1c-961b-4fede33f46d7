### Partner Studios API 테스트 (실제 구현된 API)

# 변수 설정
@baseUrl = http://localhost:3000
@contentType = application/json
@projectRef = dxodiiizyfzpueyvoaqr

# 파트너 인증을 위한 변수 설정
# 실제 테스트 시에는 브라우저에서 로그인 후 개발자 도구의 Application > Cookies에서
# sb-{{projectRef}}-auth-token 쿠키 값을 복사해서 사용하세요
@authToken = ...

###############################################################################
# 인증 테스트 가이드
###############################################################################
# 
# 1. 브라우저에서 http://localhost:3000/partner/login 접속
# 2. 파트너 계정으로 로그인
# 3. 개발자 도구 (F12) 열기
# 4. Application > Storage > Cookies > localhost:3000
# 5. sb-{{projectRef}}-auth-token 쿠키 값 복사
# 6. 위의 @authToken 변수에 붙여넣기
# 7. 테스트 실행
#
# 주의사항:
# - 토큰은 보통 1시간 후 만료됩니다
# - 파트너 상태가 'ACTIVE'여야 API 사용 가능합니다
# - 다른 파트너의 스튜디오는 접근할 수 없습니다
###############################################################################

###
# 🔴 파트너 스튜디오 생성 - 인증 없음 (401 에러)
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}

{
  "name": "인증 없는 스튜디오",
  "phone": "02-1234-5678",
  "address": "서울시 강남구 테헤란로 123",
  "latitude": 37.5665,
  "longitude": 126.9780
}

###
# ✅ 파트너 스튜디오 생성 (인증 포함)
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "헬퍼 피트니스 센터 파트너점",
  "phone": "02-1234-5678",
  "description": "파트너 API 테스트용 스튜디오",
  "address": "서울시 강남구 테헤란로 123",
  "addressDetail": "1층",
  "postalCode": "06234",
  "latitude": 37.5665,
  "longitude": 126.9780,
  "nearestStation": "강남역",
  "stationDistance": 200,
  "amenities": {
    "parking": [{"type": "free", "description": "무료 주차장 이용 가능"}],
    "shower": [{"available": true, "description": "샤워실 완비"}],
    "locker": [{"type": "paid", "price": 1000, "description": "유료 라커 이용"}]
  },
  "operatingHours": {
    "monday": { "open": "06:00", "close": "23:00" },
    "tuesday": { "open": "06:00", "close": "23:00" },
    "wednesday": { "open": "06:00", "close": "23:00" },
    "thursday": { "open": "06:00", "close": "23:00" },
    "friday": { "open": "06:00", "close": "23:00" },
    "saturday": { "open": "07:00", "close": "22:00" },
    "sunday": { "open": "08:00", "close": "21:00" }
  },
  "links": {
    "website": "https://helperfitness.com",
    "sns": "https://instagram.com/helper_fitness"
  },
  "images": [
    {
      "path": "studios/partner-id/studio-id/featured-12345.jpg",
      "url": "https://example.com/studio-featured.jpg"
    },
    {
      "path": "studios/partner-id/studio-id/gallery-12346.jpg", 
      "url": "https://example.com/studio-image1.jpg"
    },
    {
      "path": "studios/partner-id/studio-id/gallery-12347.jpg",
      "url": "https://example.com/studio-image2.jpg"
    }
  ]
}

###
# 🔴 파트너 스튜디오 단건 조회 - 인증 없음 (401 에러)
GET {{baseUrl}}/api/partner/studios/550e8400-e29b-41d4-a716-446655440000
Accept: {{contentType}}

###
# ✅ 파트너 스튜디오 단건 조회 (인증 포함)
# 위에서 생성된 스튜디오 ID로 변경 필요
GET {{baseUrl}}/api/partner/studios/d1a1e198-9541-45f5-bb2c-f3327b2adb95
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# 🔴 파트너 스튜디오 수정 - 인증 없음 (401 에러)
PUT {{baseUrl}}/api/partner/studios/550e8400-e29b-41d4-a716-446655440000
Content-Type: {{contentType}}

{
  "name": "수정된 스튜디오 이름"
}

###
# ✅ 파트너 스튜디오 수정 (인증 포함)
PUT {{baseUrl}}/api/partner/studios/d1a1e198-9541-45f5-bb2c-f3327b2adb95
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "헬퍼 피트니스 센터 파트너점 (수정됨)",
  "description": "수정된 설명",
  "addressDetail": "지하 1층",
  "nearestStation": "역삼역",
  "stationDistance": 150,
  "amenities": {
    "parking": [{"type": "paid", "price": 2000, "description": "유료 주차장"}],
    "shower": [{"available": true, "description": "프리미엄 샤워실"}]
  },
  "operatingHours": {
    "monday": { "open": "07:00", "close": "22:00" },
    "tuesday": { "open": "07:00", "close": "22:00" },
    "wednesday": { "open": "07:00", "close": "22:00" },
    "thursday": { "open": "07:00", "close": "22:00" },
    "friday": { "open": "07:00", "close": "22:00" },
    "saturday": { "open": "08:00", "close": "21:00" },
    "sunday": { "open": "09:00", "close": "20:00" }
  },
  "links": {
    "website": "https://helperfitness-updated.com",
    "sns": "https://instagram.com/helper_fitness_updated"
  }
}

###############################################################################
# 에러 케이스 테스트
###############################################################################

###
# 🔴 유효하지 않은 스튜디오 ID로 조회 (422 에러)
GET {{baseUrl}}/api/partner/studios/invalid-uuid
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# 🔴 존재하지 않는 스튜디오 조회 (404 에러)
GET {{baseUrl}}/api/partner/studios/00000000-0000-0000-0000-000000000000
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# 🔴 잘못된 데이터로 스튜디오 생성 (400 에러)
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "",
  "phone": "invalid-phone",
  "address": "서울",
  "latitude": 91,
  "longitude": 181
}

###
# 🔴 필수 필드 누락으로 스튜디오 생성 (400 에러)
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "테스트 스튜디오"
}

###
# 🔴 잘못된 전화번호 형식 (400 에러)
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "전화번호 테스트 스튜디오",
  "phone": "************",
  "address": "서울시 강남구 테헤란로 123",
  "latitude": 37.5665,
  "longitude": 126.9780
}

###
# 🔴 범위를 벗어난 좌표 (400 에러)
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "좌표 테스트 스튜디오",
  "phone": "02-1234-5678",
  "address": "서울시 강남구 테헤란로 123",
  "latitude": 95.0,
  "longitude": 185.0
}

###############################################################################
# 성공 케이스 추가 테스트
###############################################################################

###
# ✅ 최소 필수 정보만으로 스튜디오 생성
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "미니멀 스튜디오",
  "phone": "02-9999-8888",
  "address": "서울시 서초구 서초대로 456",
  "latitude": 37.4979,
  "longitude": 127.0276
}

###
# ✅ 부분 정보만 수정
PUT {{baseUrl}}/api/partner/studios/550e8400-e29b-41d4-a716-446655440000
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "description": "부분 수정 테스트",
  "nearestStation": "교대역",
  "links": {
    "website": "https://updated-studio.com",
    "sns": "https://instagram.com/updated-channel"
  },
  "images": [
    {
      "path": "studios/partner-id/studio-id/featured-67890.jpg",
      "url": "https://example.com/updated-featured.jpg"
    },
    {
      "path": "studios/partner-id/studio-id/gallery-67891.jpg",
      "url": "https://example.com/updated-image1.jpg"
    },
    {
      "path": "studios/partner-id/studio-id/gallery-67892.jpg",
      "url": "https://example.com/updated-image2.jpg"
    },
    {
      "path": "studios/partner-id/studio-id/gallery-67893.jpg",
      "url": "https://example.com/updated-image3.jpg"
    }
  ]
}

###############################################################################
# 이미지 업로드 테스트 & JSONB 구조 가이드
###############################################################################
#
# 🎯 새로운 이미지 관리 워크플로우 (JSONB 구조)
#
# 1단계: 이미지 파일 업로드 (upload API 사용)
#   - POST /api/partner/upload로 실제 파일 업로드
#   - 응답에서 path와 url 획득
#
# 2단계: 스튜디오 생성/수정 시 이미지 정보 저장
#   - 1단계에서 받은 path, url을 사용하여 JSONB 구조로 저장
#   - images 배열에 이미지 메타데이터 포함
#
# 📋 최소한의 JSONB 이미지 구조:
# {
#   "path": "studios/.../image.jpg", // 필수: 스토리지 경로 (고유 식별자)
#   "url": "https://..."            // 필수: 접근 가능한 URL
# }
#
# 🤖 자동 처리:
# - 배열 순서로 자동 결정: 첫 번째 = 대표 이미지, 나머지 = 갤러리
#
# 📝 제약사항:
# - 최대 10개 이미지
# - 중복 경로 불허
# - 올바른 URL 형식 필수
# - 배열 순서 = 표시 순서
#
###############################################################################

###
# 🚫 VS Code REST Client multipart 형식 (Next.js 15 호환 이슈)
# 
# 주의: 아래 방식은 Next.js 15의 FormData 파서와 호환되지 않아 "expected CRLF" 에러 발생
# 대신 하단의 curl 명령어를 사용하거나 Postman을 이용하세요
#
# POST {{baseUrl}}/api/partner/upload
# Cookie: sb-{{projectRef}}-auth-token={{authToken}}
# 
# (이 형식은 현재 작동하지 않음 - CRLF 파싱 에러)

###
# ✅ curl 명령어 사용 (터미널에서 실행) - 권장 방법
# 
# 이 방법이 가장 안정적입니다. 토큰 값을 실제 값으로 변경한 후 터미널에서 실행하세요:
#
# 1. 스튜디오 메인 이미지 업로드:
# curl -X POST http://localhost:3000/api/partner/upload \
#   -H "Cookie: sb-{{projectRef}}-auth-token={{authToken}}" \
#   -F "file=@test-image.jpg" \
#   -F "type=studio" \
#   -F "studioId=8d5cadcd-be18-4134-ab70-5a28002e1ee1" \
#   -F "prefix=featured"
#
# 2. 스튜디오 갤러리 이미지 업로드:  
# curl -X POST http://localhost:3000/api/partner/upload \
#   -H "Cookie: sb-{{projectRef}}-auth-token={{authToken}}" \
#   -F "file=@test-image.jpg" \
#   -F "type=studio" \
#   -F "studioId=8d5cadcd-be18-4134-ab70-5a28002e1ee1" \
#   -F "prefix=gallery"
#
# 성공 시 응답:
# {
#   "success": true,
#   "url": "https://dxodiiizyfzpueyvoaqr.supabase.co/storage/v1/object/public/images/studios/.../featured-....jpg",
#   "path": "studios/.../featured-....jpg"
# }
#
# 실제 토큰 값은 다음 방법으로 확인:
# 1. 브라우저에서 http://localhost:3000/partner/login 접속하여 로그인
# 2. 개발자 도구(F12) → Application → Cookies → localhost:3000
# 3. sb-{{projectRef}}-auth-token 쿠키 값 복사

###
# ✅ Postman 사용 - 대안 방법
# 
# 1. Postman에서 다음 컬렉션 import: tests/api/postman-upload-collection.json
# 2. Collection Variables에서 authToken 설정
# 3. Body → form-data에서 파일 선택하여 업로드
# 
# 또는 웹 브라우저에서 직접 테스트:
# - 파트너 로그인 후 스튜디오 관리 페이지에서 이미지 업로드 기능 사용

###
# ✅ 업로드된 이미지 삭제
DELETE {{baseUrl}}/api/partner/upload
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "path": "studios/d5d16063-706e-4506-9a74-64ddd5365bc7/8d5cadcd-be18-4134-ab70-5a28002e1ee1/featured-1234567890.jpg"
}

###
# 🔴 대표 이미지 2개 이상 설정 (400 에러)
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "대표 이미지 중복 테스트",
  "phone": "02-1234-5678",
  "address": "서울시 강남구 테헤란로 123",
  "latitude": 37.5665,
  "longitude": 126.9780,
  "images": [
    {
      "path": "studios/partner-id/studio-id/featured-1.jpg",
      "url": "https://example.com/featured1.jpg"
    },
    {
      "path": "studios/partner-id/studio-id/featured-2.jpg", 
      "url": "https://example.com/featured2.jpg"
    }
  ]
}

###
# 🔴 잘못된 이미지 구조 (400 에러)
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "잘못된 이미지 구조 테스트",
  "phone": "02-1234-5678", 
  "address": "서울시 강남구 테헤란로 123",
  "latitude": 37.5665,
  "longitude": 126.9780,
  "images": [
    {
      "path": "test.jpg",
      "url": "invalid-url"
    }
  ]
}

###
# 🔴 이미지 개수 초과 (400 에러)
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "이미지 개수 초과 테스트",
  "phone": "02-1234-5678",
  "address": "서울시 강남구 테헤란로 123",
  "latitude": 37.5665,
  "longitude": 126.9780,
  "images": [
    {"path": "test1.jpg", "url": "https://example.com/image1.jpg"},
    {"path": "test2.jpg", "url": "https://example.com/image2.jpg"},
    {"path": "test3.jpg", "url": "https://example.com/image3.jpg"},
    {"path": "test4.jpg", "url": "https://example.com/image4.jpg"},
    {"path": "test5.jpg", "url": "https://example.com/image5.jpg"},
    {"path": "test6.jpg", "url": "https://example.com/image6.jpg"},
    {"path": "test7.jpg", "url": "https://example.com/image7.jpg"},
    {"path": "test8.jpg", "url": "https://example.com/image8.jpg"},
    {"path": "test9.jpg", "url": "https://example.com/image9.jpg"},
    {"path": "test10.jpg", "url": "https://example.com/image10.jpg"},
    {"path": "test11.jpg", "url": "https://example.com/image11.jpg"}
  ]
}

###
# ✅ JSONB 이미지 구조 올바른 사용 예시
POST {{baseUrl}}/api/partner/studios
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "JSONB 이미지 테스트 스튜디오",
  "phone": "02-1234-5678",
  "description": "새로운 JSONB 이미지 구조를 사용한 스튜디오",
  "address": "서울시 강남구 테헤란로 789",
  "latitude": 37.5665,
  "longitude": 126.9780,
  "images": [
    {
      "path": "studios/d5d16063-706e-4506-9a74-64ddd5365bc7/test-studio-id/featured-timestamp.jpg",
      "url": "https://dxodiiizyfzpueyvoaqr.supabase.co/storage/v1/object/public/images/studios/d5d16063-706e-4506-9a74-64ddd5365bc7/test-studio-id/featured-timestamp.jpg"
    },
    {
      "path": "studios/d5d16063-706e-4506-9a74-64ddd5365bc7/test-studio-id/gallery-timestamp1.jpg",
      "url": "https://dxodiiizyfzpueyvoaqr.supabase.co/storage/v1/object/public/images/studios/d5d16063-706e-4506-9a74-64ddd5365bc7/test-studio-id/gallery-timestamp1.jpg"
    },
    {
      "path": "studios/d5d16063-706e-4506-9a74-64ddd5365bc7/test-studio-id/gallery-timestamp2.jpg",
      "url": "https://dxodiiizyfzpueyvoaqr.supabase.co/storage/v1/object/public/images/studios/d5d16063-706e-4506-9a74-64ddd5365bc7/test-studio-id/gallery-timestamp2.jpg"
    }
  ]
}