-- Migration script to add group_start_date and group_end_date columns to class_schedule_groups
-- Execute this script after updating the schema.ts file

-- 1. Add the new columns to class_schedule_groups table
ALTER TABLE class_schedule_groups 
ADD COLUMN IF NOT EXISTS group_start_date DATE,
ADD COLUMN IF NOT EXISTS group_end_date DATE;

-- 2. Update existing groups with reasonable default dates
-- This sets group dates based on template dates where they exist
UPDATE class_schedule_groups 
SET 
    group_start_date = (
        SELECT class_start_date 
        FROM class_templates 
        WHERE id = class_schedule_groups.class_template_id
    ),
    group_end_date = (
        SELECT class_end_date 
        FROM class_templates 
        WHERE id = class_schedule_groups.class_template_id
    )
WHERE group_start_date IS NULL OR group_end_date IS NULL;

-- 3. For templates without dates, set reasonable defaults (optional)
-- You may want to customize these dates based on your business needs
UPDATE class_schedule_groups 
SET 
    group_start_date = CURRENT_DATE + INTERVAL '7 days',
    group_end_date = CURRENT_DATE + INTERVAL '3 months'
WHERE group_start_date IS NULL OR group_end_date IS NULL;

-- 4. Verify the migration
SELECT 
    csg.id,
    csg.group_name,
    csg.group_start_date,
    csg.group_end_date,
    ct.title as template_title,
    ct.class_start_date as template_start,
    ct.class_end_date as template_end
FROM class_schedule_groups csg
LEFT JOIN class_templates ct ON csg.class_template_id = ct.id
ORDER BY ct.title, csg.group_name;

-- 5. Update template class dates based on all groups (optional)
-- This recalculates template dates to be the min/max of all group dates
UPDATE class_templates
SET 
    class_start_date = (
        SELECT MIN(group_start_date) 
        FROM class_schedule_groups 
        WHERE class_template_id = class_templates.id 
        AND group_start_date IS NOT NULL
        AND is_active = true
    ),
    class_end_date = (
        SELECT MAX(group_end_date) 
        FROM class_schedule_groups 
        WHERE class_template_id = class_templates.id 
        AND group_end_date IS NOT NULL
        AND is_active = true
    )
WHERE EXISTS (
    SELECT 1 
    FROM class_schedule_groups 
    WHERE class_template_id = class_templates.id
);

COMMIT;