CREATE TABLE "class_attendances" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"class_occurrence_id" uuid NOT NULL,
	"member_id" uuid NOT NULL,
	"enrollment_id" uuid,
	"attendance_status" text NOT NULL,
	"checked_in_at" timestamp with time zone,
	"checked_out_at" timestamp with time zone,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "class_enrollments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"member_id" uuid NOT NULL,
	"class_template_id" uuid NOT NULL,
	"schedule_group_id" uuid NOT NULL,
	"enrollment_status" text DEFAULT 'pending' NOT NULL,
	"enrolled_at" timestamp with time zone DEFAULT now(),
	"payment_id" text,
	"paid_amount" numeric(10, 2),
	"refund_amount" numeric(10, 2),
	"refunded_at" timestamp with time zone,
	"refund_reason" text,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "class_occurrences" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"class_template_id" uuid NOT NULL,
	"schedule_group_id" uuid NOT NULL,
	"class_schedule_id" uuid NOT NULL,
	"occurrence_date" date NOT NULL,
	"start_time" time NOT NULL,
	"end_time" time NOT NULL,
	"max_participants" integer NOT NULL,
	"status" text DEFAULT 'scheduled' NOT NULL,
	"attendance_count" integer DEFAULT 0,
	"confirmed_enrollments" integer DEFAULT 0,
	"instructor_notes" text,
	"cancellation_reason" text,
	"is_substitute_class" boolean DEFAULT false,
	"original_occurrence_id" uuid,
	"booking_opens_at" timestamp with time zone,
	"booking_closes_at" timestamp with time zone,
	"cancellation_deadline" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "class_reviews" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"member_id" uuid NOT NULL,
	"class_template_id" uuid NOT NULL,
	"instructor_id" uuid NOT NULL,
	"rating" integer NOT NULL,
	"comment" text,
	"is_anonymous" boolean DEFAULT false,
	"is_verified" boolean DEFAULT false,
	"helpful_count" integer DEFAULT 0,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "class_schedule_groups" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"class_template_id" uuid NOT NULL,
	"group_name" text NOT NULL,
	"group_description" text,
	"group_start_date" date,
	"group_end_date" date,
	"max_participants" integer NOT NULL,
	"price_per_session" numeric(10, 2),
	"sessions_per_week" integer DEFAULT 0,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "class_schedules" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"schedule_group_id" uuid NOT NULL,
	"day_of_week" text NOT NULL,
	"start_time" time NOT NULL,
	"end_time" time NOT NULL,
	"max_participants" integer,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "class_templates" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"studio_id" uuid NOT NULL,
	"instructor_id" uuid NOT NULL,
	"title" text NOT NULL,
	"description" text,
	"curriculum" jsonb,
	"category" text NOT NULL,
	"specialty" text NOT NULL,
	"level" text NOT NULL,
	"duration_minutes" integer NOT NULL,
	"price_per_session" numeric(10, 2) NOT NULL,
	"max_capacity" integer NOT NULL,
	"recruitment_start_date" date,
	"recruitment_end_date" date,
	"class_start_date" date,
	"class_end_date" date,
	"status" text DEFAULT 'upcoming' NOT NULL,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "instructor_accounts" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"instructor_id" uuid NOT NULL,
	"bank_name" text NOT NULL,
	"account_number" text NOT NULL,
	"account_holder" text NOT NULL,
	"is_verified" boolean DEFAULT false,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "instructor_accounts_instructor_id_unique" UNIQUE("instructor_id")
);
--> statement-breakpoint
CREATE TABLE "instructor_certificates" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"instructor_id" uuid NOT NULL,
	"certificate_name" text NOT NULL,
	"issuing_organization" text NOT NULL,
	"issue_date" date NOT NULL,
	"expiry_date" date,
	"certificate_number" text,
	"image_url" text,
	"is_verified" boolean DEFAULT false,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "instructor_specialties" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"instructor_id" uuid NOT NULL,
	"specialty" text NOT NULL,
	"experience_years" integer NOT NULL,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "instructors" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"member_id" uuid NOT NULL,
	"short_bio" text,
	"detailed_bio" text,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "instructors_member_id_unique" UNIQUE("member_id")
);
--> statement-breakpoint
CREATE TABLE "member_preferences" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"member_id" uuid NOT NULL,
	"fitness_goals" jsonb NOT NULL,
	"preferred_stations" jsonb NOT NULL,
	"preferred_specialties" jsonb NOT NULL,
	"preferred_days" jsonb NOT NULL,
	"preferred_time_slots" jsonb NOT NULL,
	"fitness_level" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "member_preferences_member_id_unique" UNIQUE("member_id")
);
--> statement-breakpoint
CREATE TABLE "members" (
	"id" uuid PRIMARY KEY NOT NULL,
	"nickname" text,
	"name" text,
	"phone" text,
	"gender" text,
	"birth_date" date,
	"role" text DEFAULT 'STUDENT' NOT NULL,
	"status" text DEFAULT 'ACTIVE',
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "partners" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"contact_name" text NOT NULL,
	"contact_phone" text NOT NULL,
	"status" text DEFAULT 'PENDING' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "partners_user_id_unique" UNIQUE("user_id")
);
--> statement-breakpoint
CREATE TABLE "studios" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"address" text NOT NULL,
	"studio_type" text NOT NULL,
	"latitude" numeric(10, 8),
	"longitude" numeric(11, 8),
	"nearest_station" text,
	"amenities" jsonb,
	"links" jsonb,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
