{"id": "8e3b21bc-839f-4bfc-bfe9-ceb509e0764d", "prevId": "b2ab0cd8-242b-4639-af15-66e7d795a636", "version": "7", "dialect": "postgresql", "tables": {"public.class_attendances": {"name": "class_attendances", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "class_occurrence_id": {"name": "class_occurrence_id", "type": "uuid", "primaryKey": false, "notNull": true}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "enrollment_id": {"name": "enrollment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "attendance_status": {"name": "attendance_status", "type": "text", "primaryKey": false, "notNull": true}, "checked_in_at": {"name": "checked_in_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "checked_out_at": {"name": "checked_out_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_enrollments": {"name": "class_enrollments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_template_id": {"name": "class_template_id", "type": "uuid", "primaryKey": false, "notNull": true}, "schedule_group_id": {"name": "schedule_group_id", "type": "uuid", "primaryKey": false, "notNull": true}, "enrollment_status": {"name": "enrollment_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "enrolled_at": {"name": "enrolled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "payment_id": {"name": "payment_id", "type": "text", "primaryKey": false, "notNull": false}, "paid_amount": {"name": "paid_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "refund_amount": {"name": "refund_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "refunded_at": {"name": "refunded_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "refund_reason": {"name": "refund_reason", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_occurrences": {"name": "class_occurrences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "class_template_id": {"name": "class_template_id", "type": "uuid", "primaryKey": false, "notNull": true}, "schedule_group_id": {"name": "schedule_group_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_schedule_id": {"name": "class_schedule_id", "type": "uuid", "primaryKey": false, "notNull": true}, "occurrence_date": {"name": "occurrence_date", "type": "date", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "time", "primaryKey": false, "notNull": true}, "max_participants": {"name": "max_participants", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "attendance_count": {"name": "attendance_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "confirmed_enrollments": {"name": "confirmed_enrollments", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "instructor_notes": {"name": "instructor_notes", "type": "text", "primaryKey": false, "notNull": false}, "cancellation_reason": {"name": "cancellation_reason", "type": "text", "primaryKey": false, "notNull": false}, "is_substitute_class": {"name": "is_substitute_class", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "original_occurrence_id": {"name": "original_occurrence_id", "type": "uuid", "primaryKey": false, "notNull": false}, "booking_opens_at": {"name": "booking_opens_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "booking_closes_at": {"name": "booking_closes_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "cancellation_deadline": {"name": "cancellation_deadline", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_reviews": {"name": "class_reviews", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_template_id": {"name": "class_template_id", "type": "uuid", "primaryKey": false, "notNull": true}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "is_anonymous": {"name": "is_anonymous", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "helpful_count": {"name": "helpful_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_schedule_groups": {"name": "class_schedule_groups", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "class_template_id": {"name": "class_template_id", "type": "uuid", "primaryKey": false, "notNull": true}, "group_name": {"name": "group_name", "type": "text", "primaryKey": false, "notNull": true}, "group_description": {"name": "group_description", "type": "text", "primaryKey": false, "notNull": false}, "group_start_date": {"name": "group_start_date", "type": "date", "primaryKey": false, "notNull": false}, "group_end_date": {"name": "group_end_date", "type": "date", "primaryKey": false, "notNull": false}, "max_participants": {"name": "max_participants", "type": "integer", "primaryKey": false, "notNull": true}, "price_per_session": {"name": "price_per_session", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "sessions_per_week": {"name": "sessions_per_week", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_schedules": {"name": "class_schedules", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "schedule_group_id": {"name": "schedule_group_id", "type": "uuid", "primaryKey": false, "notNull": true}, "day_of_week": {"name": "day_of_week", "type": "text", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "time", "primaryKey": false, "notNull": true}, "max_participants": {"name": "max_participants", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_templates": {"name": "class_templates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "studio_id": {"name": "studio_id", "type": "uuid", "primaryKey": false, "notNull": true}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "curriculum": {"name": "curriculum", "type": "jsonb", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "specialty": {"name": "specialty", "type": "text", "primaryKey": false, "notNull": true}, "level": {"name": "level", "type": "text", "primaryKey": false, "notNull": true}, "duration_minutes": {"name": "duration_minutes", "type": "integer", "primaryKey": false, "notNull": true}, "price_per_session": {"name": "price_per_session", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "max_capacity": {"name": "max_capacity", "type": "integer", "primaryKey": false, "notNull": true}, "recruitment_start_date": {"name": "recruitment_start_date", "type": "date", "primaryKey": false, "notNull": false}, "recruitment_end_date": {"name": "recruitment_end_date", "type": "date", "primaryKey": false, "notNull": false}, "class_start_date": {"name": "class_start_date", "type": "date", "primaryKey": false, "notNull": false}, "class_end_date": {"name": "class_end_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'upcoming'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructor_accounts": {"name": "instructor_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "bank_name": {"name": "bank_name", "type": "text", "primaryKey": false, "notNull": true}, "account_number": {"name": "account_number", "type": "text", "primaryKey": false, "notNull": true}, "account_holder": {"name": "account_holder", "type": "text", "primaryKey": false, "notNull": true}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"instructor_accounts_instructor_id_unique": {"name": "instructor_accounts_instructor_id_unique", "nullsNotDistinct": false, "columns": ["instructor_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructor_certificates": {"name": "instructor_certificates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "certificate_name": {"name": "certificate_name", "type": "text", "primaryKey": false, "notNull": true}, "issuing_organization": {"name": "issuing_organization", "type": "text", "primaryKey": false, "notNull": true}, "issue_date": {"name": "issue_date", "type": "date", "primaryKey": false, "notNull": true}, "expiry_date": {"name": "expiry_date", "type": "date", "primaryKey": false, "notNull": false}, "certificate_number": {"name": "certificate_number", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructor_specialties": {"name": "instructor_specialties", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "specialty": {"name": "specialty", "type": "text", "primaryKey": false, "notNull": true}, "experience_years": {"name": "experience_years", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructors": {"name": "instructors", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "short_bio": {"name": "short_bio", "type": "text", "primaryKey": false, "notNull": false}, "detailed_bio": {"name": "detailed_bio", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"instructors_member_id_unique": {"name": "instructors_member_id_unique", "nullsNotDistinct": false, "columns": ["member_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.member_preferences": {"name": "member_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "fitness_goals": {"name": "fitness_goals", "type": "jsonb", "primaryKey": false, "notNull": true}, "preferred_stations": {"name": "preferred_stations", "type": "jsonb", "primaryKey": false, "notNull": true}, "preferred_specialties": {"name": "preferred_specialties", "type": "jsonb", "primaryKey": false, "notNull": true}, "preferred_days": {"name": "preferred_days", "type": "jsonb", "primaryKey": false, "notNull": true}, "preferred_time_slots": {"name": "preferred_time_slots", "type": "jsonb", "primaryKey": false, "notNull": true}, "fitness_level": {"name": "fitness_level", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"member_preferences_member_id_unique": {"name": "member_preferences_member_id_unique", "nullsNotDistinct": false, "columns": ["member_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.members": {"name": "members", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "nickname": {"name": "nickname", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "birth_date": {"name": "birth_date", "type": "date", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'STUDENT'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partners": {"name": "partners", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "contact_name": {"name": "contact_name", "type": "text", "primaryKey": false, "notNull": true}, "contact_phone": {"name": "contact_phone", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"partners_user_id_unique": {"name": "partners_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}, "partners_email_unique": {"name": "partners_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.studios": {"name": "studios", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "studio_type": {"name": "studio_type", "type": "text", "primaryKey": false, "notNull": true}, "latitude": {"name": "latitude", "type": "numeric(10, 8)", "primaryKey": false, "notNull": false}, "longitude": {"name": "longitude", "type": "numeric(11, 8)", "primaryKey": false, "notNull": false}, "nearest_station": {"name": "nearest_station", "type": "text", "primaryKey": false, "notNull": false}, "amenities": {"name": "amenities", "type": "jsonb", "primaryKey": false, "notNull": false}, "links": {"name": "links", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}