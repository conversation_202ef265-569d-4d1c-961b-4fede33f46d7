-- Migration: Add email column to partners table
-- Created: 2024
-- Description: Add email column for Supabase independence

-- 1. Add email column to partners table
ALTER TABLE "partners" ADD COLUMN "email" text NOT NULL DEFAULT '';

-- 2. Add unique constraint to email column
ALTER TABLE "partners" ADD CONSTRAINT "partners_email_unique" UNIQUE("email");

-- 3. Update existing records with email from auth.users (if any exist)
-- Note: This assumes the auth schema is accessible
-- UPDATE partners 
-- SET email = (
--   SELECT email 
--   FROM auth.users 
--   WHERE auth.users.id = partners.user_id
-- );

-- 4. Remove default value after data migration (uncomment after running above update)
-- ALTER TABLE "partners" ALTER COLUMN "email" DROP DEFAULT;