import { Skeleton } from '@/components/ui/skeleton';

function InstructorListItemSkeleton() {
  return (
    <div className='rounded-md border border-gray-200'>
      <div className='flex items-center justify-between p-2'>
        {/* 프로필 이미지 & 이름 스켈레톤 */}
        <div className='flex items-center gap-4'>
          <Skeleton className='h-12 w-12 rounded-full' />
          <Skeleton className='h-5 w-24' />
        </div>

        {/* 버튼 스켈레톤 */}
        <div className='flex items-center gap-2'>
          <Skeleton className='h-9 w-12' />
          <Skeleton className='h-9 w-12' />
        </div>
      </div>
    </div>
  );
}

export default function InstructorPageSkeleton() {
  return (
    <div className='container mx-auto px-4 py-8'>
      {/* 헤더 스켈레톤 */}
      <div className='mb-8 flex items-center justify-between'>
        <div>
          <Skeleton className='mb-2 h-8 w-32' />
          <Skeleton className='h-4 w-48' />
        </div>

        <div className='flex items-center gap-3'>
          <Skeleton className='h-10 w-24' />
        </div>
      </div>

      {/* 강사 목록 스켈레톤 */}
      <div className='space-y-4'>
        {Array.from({ length: 3 }).map((_, index) => (
          <InstructorListItemSkeleton key={index} />
        ))}
      </div>
    </div>
  );
}
