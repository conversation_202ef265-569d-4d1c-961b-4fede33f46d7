import { InstructorListItem } from '@/app/partner/_types/client.type';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface DeleteInstructorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  instructor: InstructorListItem | null;
  isLoading?: boolean;
}

export default function DeleteInstructorModal({
  isOpen,
  onClose,
  onConfirm,
  instructor,
  isLoading = false,
}: DeleteInstructorModalProps) {
  if (!instructor) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className='max-w-sm'>
        <AlertDialogHeader>
          <AlertDialogTitle>삭제 확인</AlertDialogTitle>
          <AlertDialogDescription>
            <span className='font-medium'>{instructor.name}</span> 강사를
            삭제하시겠습니까?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className='gap-2'>
          <AlertDialogCancel disabled={isLoading} className='flex-1'>
            취소
          </AlertDialogCancel>
          <AlertDialogAction
            disabled={isLoading}
            onClick={onConfirm}
            className='flex-1 bg-red-600 text-white hover:bg-red-700'
          >
            {isLoading ? '삭제 중...' : '삭제'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
