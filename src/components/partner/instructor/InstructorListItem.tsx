import { InstructorListItem } from '@/app/partner/_types/client.type';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';

interface InstructorListItemProps {
  instructor: InstructorListItem;
  onEdit: (instructor: InstructorListItem) => void;
  onDelete: (instructor: InstructorListItem) => void;
}

export default function InstructorListItemComponent({
  instructor,
  onEdit,
  onDelete,
}: InstructorListItemProps) {
  const getInitials = (name: string): string =>
    name.length > 1 ? name.slice(0, 2) : name;

  return (
    <div className='rounded-md border border-gray-200'>
      <div className='flex items-center justify-between p-2'>
        {/* 프로필 이미지 & 이름 */}
        <div className='flex items-center gap-4'>
          <Avatar className='h-12 w-12'>
            <AvatarImage
              src={instructor.profileImageUrl}
              alt={instructor.name}
            />
            <AvatarFallback className='text-sm font-medium'>
              {getInitials(instructor.name)}
            </AvatarFallback>
          </Avatar>
          <span className='text-base font-medium text-gray-900'>
            {instructor.name}
          </span>
        </div>

        {/* 수정/삭제 버튼 */}
        <div className='flex items-center gap-2'>
          <Button
            variant='ghost'
            size='icon'
            aria-label='강사 수정'
            onClick={() => onEdit(instructor)}
          >
            수정
          </Button>
          <Button
            variant='ghost'
            size='icon'
            aria-label='강사 삭제'
            onClick={() => onDelete(instructor)}
            className='text-red-600 hover:bg-red-50'
          >
            삭제
          </Button>
        </div>
      </div>
    </div>
  );
}
