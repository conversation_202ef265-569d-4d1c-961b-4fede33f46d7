'use client';

import { But<PERSON> } from '@/components/ui/button';
import { UserPlus, Users } from 'lucide-react';
import Link from 'next/link';

interface InstructorEmptyStateProps {
  onAddInstructor?: () => void;
}

export default function InstructorEmptyState({
  onAddInstructor,
}: InstructorEmptyStateProps) {
  return (
    <div className='px-4 py-16 text-center'>
      <div className='mb-6 flex justify-center'>
        <div className='rounded-full bg-gray-100 p-6'>
          <Users className='h-12 w-12 text-gray-400' />
        </div>
      </div>

      <h3 className='mb-3 text-xl font-semibold text-gray-900'>
        등록된 강사가 없습니다
      </h3>

      <p className='mx-auto mb-8 max-w-md text-sm leading-relaxed text-gray-600'>
        첫 번째 강사를 등록하여 클래스를 시작해보세요.
        <br />
        강사가 등록되면 클래스 운영이 가능합니다.
      </p>

      <div className='space-y-3'>
        <Button
          onClick={onAddInstructor}
          className='inline-flex items-center gap-2'
          size='lg'
        >
          <UserPlus className='h-4 w-4' />
          강사 등록하기
        </Button>

        <div className='text-xs text-gray-500'>
          또는{' '}
          <Link
            href='/partner/instructor/new'
            className='text-blue-600 underline hover:text-blue-700'
          >
            강사 초대 링크 보내기
          </Link>
        </div>
      </div>
    </div>
  );
}
