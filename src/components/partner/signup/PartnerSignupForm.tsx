'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { PhoneInput } from '@/components/partner/common/PhoneInput';
import { 
  PartnerSignupFormSchema, 
  type PartnerSignupFormData,
  formatZodErrors 
} from '@/schemas/partner';
import type { PartnerRegisterResponse } from '@/types/partner';
import { cn } from '@/lib/utils';

/**
 * PartnerSignupForm 컴포넌트 Props
 */
interface PartnerSignupFormProps {
  onSubmit: (data: PartnerSignupFormData) => Promise<void>;
  loading?: boolean;
  className?: string;
}

/**
 * 비밀번호 강도 체크 함수
 */
function getPasswordStrength(password: string): {
  score: number;
  label: string;
  color: string;
} {
  let score = 0;
  
  if (password.length >= 8) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/\d/.test(password)) score++;
  if (/[@$!%*?&]/.test(password)) score++;
  
  const strengthMap = {
    0: { label: '매우 약함', color: 'bg-red-500' },
    1: { label: '약함', color: 'bg-red-400' },
    2: { label: '보통', color: 'bg-yellow-500' },
    3: { label: '강함', color: 'bg-blue-500' },
    4: { label: '매우 강함', color: 'bg-green-500' },
    5: { label: '최강', color: 'bg-green-600' },
  };
  
  return {
    score,
    ...strengthMap[score as keyof typeof strengthMap],
  };
}

/**
 * PartnerSignupForm 컴포넌트
 * 
 * @description
 * - 파트너 회원가입을 위한 통합 폼
 * - React Hook Form + Zod 유효성 검사
 * - 실시간 비밀번호 강도 표시
 * - 로딩 상태 처리
 */
export function PartnerSignupForm({ 
  onSubmit, 
  loading = false,
  className 
}: PartnerSignupFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const form = useForm<PartnerSignupFormData>({
    resolver: zodResolver(PartnerSignupFormSchema),
    defaultValues: {
      email: '',
      password: '',
      passwordConfirm: '',
      contactInfo: {
        name: '',
        phone: '',
      },
    },
  });

  const watchedPassword = form.watch('password');
  const passwordStrength = getPasswordStrength(watchedPassword || '');

  const handleSubmit = async (data: PartnerSignupFormData) => {
    try {
      setSubmitError(null);
      await onSubmit(data);
    } catch (error) {
      console.error('회원가입 에러:', error);
      setSubmitError('회원가입 중 오류가 발생했습니다. 다시 시도해주세요.');
    }
  };

  const togglePasswordVisibility = () => setShowPassword(!showPassword);
  const togglePasswordConfirmVisibility = () => setShowPasswordConfirm(!showPasswordConfirm);

  return (
    <div className={cn('w-full max-w-md mx-auto bg-white p-6', className)}>
      {/* 헤더 영역 */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-purple-600 mb-2">SHALLWE</h1>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">파트너 회원가입</h2>
        <p className="text-sm text-gray-500">
          쉘위 운영팀에서 빠르게 확인 후 연락드리겠습니다.
        </p>
      </div>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* 이메일 */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">이메일</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="예, <EMAIL>"
                      disabled={loading}
                      className="mt-1 border-gray-300 focus:border-purple-500 focus:ring-purple-500 placeholder:text-gray-400"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 비밀번호 */}
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">비밀번호</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        placeholder="8자 이상, 영문+숫자+특수문자"
                        disabled={loading}
                        className="mt-1 border-gray-300 focus:border-purple-500 focus:ring-purple-500 placeholder:text-gray-400"
                        {...field}
                      />
                      <button
                        type="button"
                        onClick={togglePasswordVisibility}
                        className="absolute inset-y-0 right-0 flex items-center pr-3"
                        disabled={loading}
                      >
                        {showPassword ? (
                          <svg className="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </FormControl>
                  
                  {/* 비밀번호 강도 표시 */}
                  {watchedPassword && (
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className={cn(
                              'h-2 rounded-full transition-all duration-300',
                              passwordStrength.color
                            )}
                            style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                          />
                        </div>
                        <span className="text-xs text-gray-600">
                          {passwordStrength.label}
                        </span>
                      </div>
                    </div>
                  )}
                  
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 비밀번호 확인 */}
            <FormField
              control={form.control}
              name="passwordConfirm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">비밀번호 확인</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showPasswordConfirm ? 'text' : 'password'}
                        placeholder="비밀번호를 다시 입력해주세요"
                        disabled={loading}
                        className="mt-1 border-gray-300 focus:border-purple-500 focus:ring-purple-500 placeholder:text-gray-400"
                        {...field}
                      />
                      <button
                        type="button"
                        onClick={togglePasswordConfirmVisibility}
                        className="absolute inset-y-0 right-0 flex items-center pr-3"
                        disabled={loading}
                      >
                        {showPasswordConfirm ? (
                          <svg className="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 담당자 정보 섹션 라벨 */}
            <div className="pt-4">
              <h3 className="text-sm font-medium text-gray-700 mb-4">담당자 정보</h3>
            </div>

            {/* 담당자명 */}
            <FormField
              control={form.control}
              name="contactInfo.name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">성함</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="예, 이해원"
                      disabled={loading}
                      className="mt-1 border-gray-300 focus:border-purple-500 focus:ring-purple-500 placeholder:text-gray-400"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 담당자 연락처 */}
            <FormField
              control={form.control}
              name="contactInfo.phone"
              render={({ field }) => (
                <FormItem>
                  <PhoneInput
                    label="연락처"
                    disabled={loading}
                    error={form.formState.errors.contactInfo?.phone?.message}
                    className="mt-1 border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                    {...field}
                  />
                </FormItem>
              )}
            />

            {/* 전체 에러 메시지 */}
            {submitError && (
              <Alert variant="destructive">
                <AlertDescription>{submitError}</AlertDescription>
              </Alert>
            )}

            {/* 제출 버튼 */}
            <Button 
              type="submit" 
              className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 mt-6"
              disabled={loading}
            >
              {loading ? (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 8v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  파트너 등록 중...
                </>
              ) : (
                '파트너 등록 문의하기'
              )}
            </Button>

            {/* 이용약관 및 안내사항 */}
            <div className="text-xs text-gray-500 text-center space-y-1 mt-4">
              <p>파트너 등록 시 <span className="text-purple-600 underline">이용약관</span> 및 <span className="text-purple-600 underline">개인정보처리방침</span>에 동의합니다.</p>
              <p>승인 완료 후 서비스를 이용하실 수 있습니다.</p>
            </div>
          </form>
        </Form>
    </div>
  );
}

export default PartnerSignupForm;