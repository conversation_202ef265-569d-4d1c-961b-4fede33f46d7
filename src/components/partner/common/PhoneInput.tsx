'use client';

import { forwardRef, useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { PARTNER_PHONE_REGEX } from '@/types/partner';

/**
 * PhoneInput 컴포넌트 Props
 */
interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  placeholder?: string;
  label?: string;
  id?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

/**
 * 전화번호 자동 포맷팅 함수
 * 입력값을 010-0000-0000 형태로 변환
 */
function formatPhoneNumber(value: string): string {
  // 숫자만 추출
  const numbers = value.replace(/\D/g, '');
  
  // 010으로 시작하지 않으면 010 강제 추가
  let formatted = numbers;
  if (numbers.length > 0 && !numbers.startsWith('010')) {
    formatted = '010' + numbers;
  }
  
  // 11자리 제한
  if (formatted.length > 11) {
    formatted = formatted.substring(0, 11);
  }
  
  // 하이픈 추가
  if (formatted.length > 6) {
    return `${formatted.substring(0, 3)}-${formatted.substring(3, 7)}-${formatted.substring(7)}`;
  } else if (formatted.length > 3) {
    return `${formatted.substring(0, 3)}-${formatted.substring(3)}`;
  }
  
  return formatted;
}

/**
 * 전화번호 유효성 검사
 */
function validatePhoneNumber(value: string): boolean {
  return PARTNER_PHONE_REGEX.test(value);
}

/**
 * PhoneInput 컴포넌트
 * 
 * @description
 * - 010-0000-0000 형식으로 자동 포맷팅
 * - 숫자만 입력 허용
 * - 실시간 유효성 검사
 */
export const PhoneInput = forwardRef<HTMLInputElement, PhoneInputProps>(
  ({
    value,
    onChange,
    error,
    placeholder = '010-0000-0000',
    label,
    id,
    disabled = false,
    required = false,
    className,
    ...props
  }, ref) => {
    const [internalValue, setInternalValue] = useState(value);
    const [isValid, setIsValid] = useState<boolean | null>(null);

    // 외부 value 변경 시 내부 상태 동기화
    useEffect(() => {
      setInternalValue(value);
      if (value) {
        setIsValid(validatePhoneNumber(value));
      } else {
        setIsValid(null);
      }
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      const formattedValue = formatPhoneNumber(inputValue);
      
      setInternalValue(formattedValue);
      onChange(formattedValue);
      
      // 유효성 검사 (빈 값이 아닌 경우에만)
      if (formattedValue) {
        setIsValid(validatePhoneNumber(formattedValue));
      } else {
        setIsValid(null);
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // 숫자, 백스페이스, 삭제, 탭, 화살표 키만 허용
      const allowedKeys = [
        'Backspace', 'Delete', 'Tab', 'Escape', 'Enter',
        'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
        'Home', 'End'
      ];
      
      const isNumber = /^[0-9]$/.test(e.key);
      const isAllowedKey = allowedKeys.includes(e.key);
      const isCtrlA = e.ctrlKey && e.key === 'a';
      const isCtrlC = e.ctrlKey && e.key === 'c';
      const isCtrlV = e.ctrlKey && e.key === 'v';
      const isCtrlX = e.ctrlKey && e.key === 'x';
      
      if (!isNumber && !isAllowedKey && !isCtrlA && !isCtrlC && !isCtrlV && !isCtrlX) {
        e.preventDefault();
      }
    };

    const inputId = id || 'phone-input';
    const hasError = !!error;
    const showValidation = isValid !== null && internalValue.length > 0;

    return (
      <div className={cn('space-y-2', className)}>
        {label && (
          <Label 
            htmlFor={inputId}
            className={cn(
              'text-sm font-medium text-gray-700',
              hasError && 'text-red-600'
            )}
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Label>
        )}
        
        <div className="relative">
          <Input
            ref={ref}
            id={inputId}
            type="tel"
            value={internalValue}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            maxLength={13} // 010-0000-0000
            className={cn(
              'mt-1 border-gray-300 focus:border-purple-500 focus:ring-purple-500 transition-colors placeholder:text-gray-400',
              hasError && 'border-red-500 focus:ring-red-500',
              showValidation && isValid && 'border-green-500 focus:ring-green-500',
              showValidation && !isValid && 'border-red-500 focus:ring-red-500'
            )}
            {...props}
          />
          
          {/* 유효성 검사 아이콘 */}
          {showValidation && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              {isValid ? (
                <svg
                  className="h-4 w-4 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              ) : (
                <svg
                  className="h-4 w-4 text-red-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              )}
            </div>
          )}
        </div>

        {/* 에러 메시지 또는 도움말 */}
        {error ? (
          <p className="text-sm text-red-600 flex items-center gap-1">
            <svg
              className="h-4 w-4 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            {error}
          </p>
        ) : (
          !showValidation && (
            <p className="text-sm text-gray-500">
              휴대폰 번호를 010-0000-0000 형식으로 입력해주세요.
            </p>
          )
        )}
        
        {/* 성공 메시지 */}
        {showValidation && isValid && !error && (
          <p className="text-sm text-green-600 flex items-center gap-1">
            <svg
              className="h-4 w-4 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
            올바른 연락처 형식입니다.
          </p>
        )}
      </div>
    );
  }
);

PhoneInput.displayName = 'PhoneInput';

export default PhoneInput;