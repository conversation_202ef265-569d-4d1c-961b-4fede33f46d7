'use client';

import { cn } from '@/lib/utils';
import { PartnerStatusType } from '@/types/partner';

interface StatusMessageProps {
  status: Extract<PartnerStatusType, 'PENDING' | 'SUSPENDED' | 'REJECTED'>;
  message?: string;
  className?: string;
}

const statusConfig = {
  PENDING: {
    icon: (
      <svg
        className="h-5 w-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    ),
    title: '승인 대기 중',
    defaultMessage: '파트너 승인이 진행중입니다. 승인 완료 시 이메일로 안내드립니다.',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-300',
    textColor: 'text-yellow-800',
    iconColor: 'text-yellow-600'
  },
  SUSPENDED: {
    icon: (
      <svg
        className="h-5 w-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
        />
      </svg>
    ),
    title: '계정 정지',
    defaultMessage: '계정이 일시 정지되었습니다. 자세한 사항은 고객센터로 문의해주세요.',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-300',
    textColor: 'text-red-800',
    iconColor: 'text-red-600'
  },
  REJECTED: {
    icon: (
      <svg
        className="h-5 w-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    ),
    title: '승인 거부',
    defaultMessage: '파트너 승인이 거부되었습니다. 자세한 사항은 고객센터로 문의해주세요.',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-300',
    textColor: 'text-gray-800',
    iconColor: 'text-gray-600'
  }
};

export const StatusMessage: React.FC<StatusMessageProps> = ({
  status,
  message,
  className
}) => {
  const config = statusConfig[status];
  
  return (
    <div
      className={cn(
        'rounded-lg border p-4',
        config.bgColor,
        config.borderColor,
        className
      )}
      role="alert"
    >
      <div className="flex">
        <div className={cn('flex-shrink-0', config.iconColor)}>
          {config.icon}
        </div>
        <div className="ml-3">
          <h3 className={cn('text-sm font-medium', config.textColor)}>
            {config.title}
          </h3>
          <div className={cn('mt-2 text-sm', config.textColor)}>
            <p>{message || config.defaultMessage}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatusMessage;