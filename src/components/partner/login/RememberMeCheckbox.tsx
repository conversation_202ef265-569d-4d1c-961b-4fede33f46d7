'use client';

import { forwardRef } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface RememberMeCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  label?: string;
  className?: string;
}

export const RememberMeCheckbox = forwardRef<HTMLButtonElement, RememberMeCheckboxProps>(
  ({
    checked,
    onChange,
    disabled = false,
    label = '로그인 상태 유지',
    className,
    ...props
  }, ref) => {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <Checkbox
          ref={ref}
          id="rememberMe"
          checked={checked}
          onCheckedChange={onChange}
          disabled={disabled}
          className="border-gray-300 text-purple-600 focus:ring-purple-500"
          aria-label={label}
          {...props}
        />
        <Label 
          htmlFor="rememberMe" 
          className={cn(
            'text-sm font-normal text-gray-700 cursor-pointer select-none',
            disabled && 'opacity-50 cursor-not-allowed'
          )}
        >
          {label}
        </Label>
      </div>
    );
  }
);

RememberMeCheckbox.displayName = 'RememberMeCheckbox';

export default RememberMeCheckbox;