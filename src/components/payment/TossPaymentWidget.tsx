'use client';

import { useEffect, useRef, useState } from 'react';
import { loadTossPayments } from '@tosspayments/tosspayments-sdk';
import getEnv from '@/lib/config/get-env';

interface TossPaymentWidgetProps {
  customerKey: string;
  amount: number;
  orderId: string;
  orderName: string;
  successUrl: string;
  failUrl: string;
  customerEmail?: string;
  customerName?: string;
  onPaymentReady?: () => void;
  onPaymentMethodSelect?: (paymentMethod: any) => void;
}

export default function TossPaymentWidget({
  customerKey,
  amount,
  orderId,
  orderName,
  successUrl,
  failUrl,
  customerEmail,
  customerName,
  onPaymentReady,
  onPaymentMethodSelect,
}: TossPaymentWidgetProps) {
  const paymentMethodRef = useRef<HTMLDivElement>(null);
  const agreementRef = useRef<HTMLDivElement>(null);
  const [widgets, setWidgets] = useState<any>(null);
  const [paymentMethodWidget, setPaymentMethodWidget] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeTossPayments = async () => {
      try {
        const clientKey = getEnv('NEXT_PUBLIC_TOSS_CLIENT_KEY');

        if (!clientKey) {
          throw new Error('토스페이먼츠 클라이언트 키가 설정되지 않았습니다.');
        }

        // 토스페이먼츠 SDK 로드
        const tossPayments = await loadTossPayments(clientKey);

        // 결제위젯 초기화
        const widgetsInstance = tossPayments.widgets({ customerKey });
        setWidgets(widgetsInstance);

        // 결제 금액 설정
        await widgetsInstance.setAmount({
          currency: 'KRW',
          value: amount,
        });

        // 결제 UI 렌더링
        const paymentMethodInstance =
          await widgetsInstance.renderPaymentMethods({
            selector: '#payment-method',
            variantKey: 'DEFAULT',
          });

        setPaymentMethodWidget(paymentMethodInstance);

        // 약관 UI 렌더링
        await widgetsInstance.renderAgreement({
          selector: '#agreement',
          variantKey: 'AGREEMENT',
        });

        // 결제수단 선택 이벤트 구독
        paymentMethodInstance.on(
          'paymentMethodSelect',
          (selectedPaymentMethod: any) => {
            onPaymentMethodSelect?.(selectedPaymentMethod);
          }
        );

        setIsLoading(false);
        onPaymentReady?.();
      } catch (err) {
        console.error('토스페이먼츠 초기화 실패:', err);
        setError(
          err instanceof Error ? err.message : '결제 위젯 로드에 실패했습니다.'
        );
        setIsLoading(false);
      }
    };

    initializeTossPayments();

    // 컴포넌트 언마운트 시 정리
    return () => {
      if (paymentMethodWidget) {
        paymentMethodWidget.destroy?.();
      }
    };
  }, [customerKey, amount, onPaymentReady, onPaymentMethodSelect]);

  // 금액이 변경될 때 업데이트
  useEffect(() => {
    if (widgets && !isLoading) {
      widgets.setAmount({
        currency: 'KRW',
        value: amount,
      });
    }
  }, [widgets, amount, isLoading]);

  const handlePayment = async () => {
    if (!widgets) {
      alert('결제 위젯이 준비되지 않았습니다.');
      return;
    }

    try {
      await widgets.requestPayment({
        orderId,
        orderName,
        successUrl,
        failUrl,
        customerEmail,
        customerName,
      });
    } catch (error) {
      console.error('결제 요청 실패:', error);
      alert('결제 요청에 실패했습니다. 다시 시도해주세요.');
    }
  };

  if (error) {
    return (
      <div className='rounded-lg border border-red-200 bg-red-50 p-4'>
        <div className='flex'>
          <div className='ml-3'>
            <h3 className='text-sm font-medium text-red-800'>
              결제 위젯 로드 오류
            </h3>
            <div className='mt-2 text-sm text-red-700'>
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* 결제수단 선택 영역 */}
      <div>
        <h3 className='mb-3 text-xl font-semibold text-black'>결제수단</h3>
        <div
          id='payment-method'
          ref={paymentMethodRef}
          className={`min-h-[200px] rounded-lg border ${
            isLoading ? 'animate-pulse bg-gray-100' : 'bg-white'
          }`}
        >
          {isLoading && (
            <div className='flex h-[200px] items-center justify-center'>
              <div className='text-center'>
                <div className='border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-t-transparent'></div>
                <p className='text-sm text-gray-600'>
                  결제 위젯을 로드하는 중...
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 약관 동의 영역 */}
      <div>
        <div
          id='agreement'
          ref={agreementRef}
          className={`min-h-[100px] rounded-lg border ${
            isLoading ? 'animate-pulse bg-gray-100' : 'bg-white'
          }`}
        >
          {isLoading && (
            <div className='flex h-[100px] items-center justify-center'>
              <div className='text-center'>
                <div className='border-primary mx-auto mb-2 h-4 w-4 animate-spin rounded-full border-2 border-t-transparent'></div>
                <p className='text-xs text-gray-600'>약관을 로드하는 중...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 결제 버튼 */}
      <button
        onClick={handlePayment}
        disabled={isLoading}
        className='bg-primary hover:bg-primary/90 w-full rounded-lg px-4 py-3 font-semibold text-white transition-colors disabled:cursor-not-allowed disabled:opacity-50'
      >
        {isLoading ? '로딩 중...' : `${amount.toLocaleString()}원 결제하기`}
      </button>
    </div>
  );
}
