'use client';

import { ClassListItemType } from '@/app/api/classes/schema';
import ClassFilter, {
  type ClassFilters,
} from '@/components/shared/class/ClassFilter';
import ClassListItem from '@/components/shared/class/ClassListItem';
import { getActiveClasses } from '@/lib/api/classes/api';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';

interface HomePageContentProps {
  initialData: ClassListItemType[];
}

export default function HomePageContent({ initialData }: HomePageContentProps) {
  const [filters, setFilters] = useState<ClassFilters>({});
  const { data: classData = initialData, isLoading } = useQuery({
    queryKey: ['classes', filters],
    queryFn: () => getActiveClasses({ filters }),
    enabled: Object.keys(filters).length > 0,
  });

  return (
    <div className='space-y-6 px-3'>
      <div className='pt-3'>
        <h2 className='mb-4 text-xl font-bold'>
          쉘위 맞춤 추천 운동을 확인해보세요.
        </h2>
        <ClassFilter filters={filters} onFiltersChange={setFilters} />
      </div>

      <section className='pt-4'>
        {isLoading ? (
          <div className='flex justify-center py-8'>
            <div className='text-muted-foreground'>로딩 중...</div>
          </div>
        ) : (
          <div className='flex flex-col gap-4'>
            {classData.map(item => (
              <ClassListItem
                key={item.classTemplate.id}
                classInfo={{
                  ...item.classTemplate,
                }}
                instructor={{
                  name: item.instructor.name,
                  specialties: item.instructor.specialties[0].specialty,
                  experienceYears:
                    item.instructor.specialties[0].experienceYears,
                }}
                studio={{
                  nearestStation: item.studio.nearestStation ?? '',
                }}
              />
            ))}
          </div>
        )}
      </section>
    </div>
  );
}
