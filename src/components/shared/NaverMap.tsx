'use client';

import getEnv from '@/lib/config/get-env';
import Script from 'next/script';
import React, { useEffect, useRef } from 'react';

interface NaverMapProps {
  lat: number;
  lng: number;
  zoom?: number;
}

const NaverMap = ({ lat, lng, zoom = 15 }: NaverMapProps) => {
  const mapRef = useRef<any>(null);

  useEffect(() => {
    const { naver } = window;
    if (!naver) {
      console.error('Naver Maps API가 로드되지 않았습니다.');
      return;
    }

    const mapOptions = {
      center: new naver.maps.LatLng(lat, lng),
      zoom: zoom,
    };
    const map = new naver.maps.Map(mapRef.current, mapOptions);

    new naver.maps.Marker({
      position: new naver.maps.LatLng(lat, lng),
      map: map,
    });
  }, [lat, lng, zoom]);

  return (
    <>
      <div ref={mapRef} style={{ width: '100%', height: '300px' }} />
    </>
  );
};

export default NaverMap;
