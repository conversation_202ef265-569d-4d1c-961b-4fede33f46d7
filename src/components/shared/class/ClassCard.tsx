import Image from 'next/image';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { MapPin, Clock, Users, Star, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CLASSES_NAVIGATION } from '@/lib/config/navigation';
import { DEFAULT_CLASS_IMAGE } from '@/lib/config/defaults';

interface ClassInfo {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  variant?: 'default' | 'primary';
}

interface ClassData {
  id: string;
  title: string;
  image?: string;
  coach: {
    name: string;
    avatar?: string;
    bio: string;
    tags: string[];
  };
  location: string;
  duration: number;
  maxStudents: number;
  price: number;
  tags: string[];
  badges?: string[];
  classInfo?: ClassInfo[];
}

interface ClassCardProps {
  classData: ClassData;
  className?: string;
}

export function ClassCard({ classData, className }: ClassCardProps) {
  const {
    id,
    title,
    image = DEFAULT_CLASS_IMAGE,
    coach,
    location,
    duration,
    maxStudents,
    price,
    // tags,
    badges,
    classInfo,
  } = classData;

  return (
    <Card className={cn('min-w-[320px] overflow-hidden sm:min-w-0', className)}>
      {badges && badges.length > 0 && (
        <div className='relative'>
          <div className='absolute top-4 left-4 z-10 flex flex-wrap gap-2'>
            {badges.map((badge, index) => (
              <Badge
                key={index}
                className='bg-primary text-primary-foreground border-none'
              >
                {badge}
              </Badge>
            ))}
          </div>
        </div>
      )}

      <Link href={CLASSES_NAVIGATION.DETAILS(id)} className='block'>
        <div className='relative h-[210px] w-full overflow-hidden'>
          <Image
            src={image}
            alt={title}
            fill
            className='object-cover transition-transform hover:scale-105'
          />
        </div>
      </Link>

      <CardContent className='space-y-4 pb-5'>
        <Link href={`/classes/${id}`}>
          <h3 className='my-3 text-lg font-semibold text-gray-900 transition-colors'>
            {title}
          </h3>
        </Link>

        <div className='flex items-center gap-3'>
          <Avatar className='h-11 w-11'>
            <AvatarImage src={coach.avatar} />
            <AvatarFallback>{coach.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div className='min-w-0 flex-1'>
            <p className='font-medium text-gray-900'>{coach.name}</p>
            <p className='truncate text-sm text-gray-600'>{coach.bio}</p>
          </div>
        </div>

        <div className='flex flex-wrap gap-1'>
          {coach.tags.map((tag, index) => (
            <Badge key={index} variant='secondary' className='text-xs'>
              #{tag}
            </Badge>
          ))}
        </div>

        {classInfo && classInfo.length > 0 ? (
          <div className='grid grid-cols-2 gap-3 text-sm'>
            {classInfo.map((info, index) => {
              const IconComponent = info.icon;
              return (
                <div
                  key={index}
                  className={cn('flex items-center gap-1 text-gray-600')}
                >
                  <IconComponent className='h-4 w-4' />
                  <span>{info.label}</span>
                </div>
              );
            })}
          </div>
        ) : (
          <div className='grid grid-cols-2 gap-3 text-sm'>
            <div className='flex items-center gap-1 text-gray-600'>
              <MapPin className='h-4 w-4' />
              <span>{location}</span>
            </div>
            <div className='flex items-center gap-1 text-gray-600'>
              <Clock className='h-4 w-4' />
              <span>{duration}분 수업</span>
            </div>
            <div className='flex items-center gap-1 text-gray-600'>
              <Star className='h-4 w-4 fill-current' />
              <span>초급 클래스</span>
            </div>
            <div className='flex items-center gap-1 text-gray-600'>
              <Users className='h-4 w-4' />
              <span>{maxStudents}인 그룹 수업</span>
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className='flex items-center justify-between border-t pb-5'>
        <div className='text-right'>
          <div className='flex items-center gap-1'>
            <span className='text-primary'>회당</span>
            <span className='text-primary text-xl font-bold'>
              {price.toLocaleString()}원
            </span>
          </div>
        </div>
        <Link href={CLASSES_NAVIGATION.BOOKING(id)}>
          <Button className='px-6 font-bold'>
            <Calendar />
            수업 일정 보기
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
