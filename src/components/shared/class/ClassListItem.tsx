import { Badge } from '@/components/ui/badge';
import { MapPin, Users } from 'lucide-react';
import Link from 'next/link';

interface ClassListItemProps {
  // data: {
  //   'class.id': string;
  //   'class.title': string;
  //   'class.level': string;
  //   'class.specialty': string;
  //   'studio.nearestStation': string;
  //   'class.maxCapacity': number;
  //   'class.pricePerSession': number;
  //   'instructor.name': string;
  //   'instructor.specialties': string;
  //   'instructor.experienceYears': number;
  // };

  classInfo: {
    id: string;
    title: string;
    level: string;
    specialty: string;
    maxCapacity: number;
    pricePerSession: number;
  };
  instructor: {
    name: string;
    specialties: string;
    experienceYears: number;
  };
  studio: {
    nearestStation: string;
  };
}

export default function ClassListItem({
  classInfo,
  instructor,
  studio,
}: ClassListItemProps) {
  const formatPrice = (price: number) => {
    return price.toLocaleString('ko-KR');
  };
  return (
    <Link href={`/classes/${classInfo.id}`}>
      <div className='flex h-40'>
        {/* Image Section */}
        <div className='relative w-40 flex-shrink-0'>
          <img
            src='/mock.jpg'
            alt={classInfo.title}
            className='h-full w-full object-cover'
          />
          {/* Category Badge */}
          <Badge
            variant='secondary'
            className='bg-primary text-primary-foreground absolute top-0 left-0 w-fit rounded-none'
          >
            {classInfo.level}
          </Badge>
        </div>

        {/* Content Section */}
        <div className='flex flex-1 flex-col justify-between px-4'>
          {/* Class Type, Location, Group Size */}
          <div className='flex flex-col gap-2'>
            <div className='flex items-center gap-2'>
              <div className='bg-neutral-200 px-1 text-sm font-medium'>
                {classInfo.specialty}
              </div>
              <div className='flex items-center gap-1 text-sm text-gray-600'>
                <MapPin className='h-4 w-4' />
                <span>{studio.nearestStation}</span>
              </div>
              <div className='flex items-center gap-1 text-sm text-gray-600'>
                <Users className='h-4 w-4' />
                <span>{classInfo.maxCapacity}인 수업</span>
              </div>
            </div>

            {/* Title */}
            <h3 className='text-foreground text-lg leading-tight font-bold md:text-xl'>
              {classInfo.title}
            </h3>

            {/* Instructor Info */}
            <div className='text-muted-foreground text-sm'>
              {instructor.name} 강사 / {instructor.experienceYears}년차{' '}
              {instructor.specialties}
            </div>
          </div>

          {/* Bottom Section */}
          <div className='flex items-end justify-end gap-2'>
            <span className='text-primary text-sm'>회당</span>
            <span className='text-primary text-2xl font-bold'>
              {formatPrice(classInfo.pricePerSession)}원
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
}
