import { useQuery } from '@tanstack/react-query';
import { getFilterOptions } from '@/lib/actions/filter-options';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export interface ClassFilters {
  level?: string;
  gender?: string;
  nearestStation?: string;
}

interface ClassFilterProps {
  filters: ClassFilters;
  onFiltersChange: (filters: ClassFilters) => void;
}

export default function ClassFilter({
  filters,
  onFiltersChange,
}: ClassFilterProps) {
  const {
    data: filterOptions = { levels: [], nearestStations: [], genders: [] },
    isLoading: isLoadingOptions,
  } = useQuery({
    queryKey: ['filterOptions'],
    queryFn: async () => {
      const response = await getFilterOptions();

      return response.success
        ? response.data
        : { levels: [], nearestStations: [], genders: [] };
    },
  });

  const handleFilterChange = (key: keyof ClassFilters, value: string) => {
    const newFilters = {
      ...filters,
      [key]: value === 'all' ? undefined : value,
    };

    // undefined 값 제거
    Object.keys(newFilters).forEach(k => {
      if (newFilters[k as keyof ClassFilters] === undefined) {
        delete newFilters[k as keyof ClassFilters];
      }
    });

    onFiltersChange(newFilters);
  };

  return (
    <div className='mb-6 flex gap-4'>
      <div className='flex flex-col gap-2'>
        <Select
          value={filters.level || 'all'}
          onValueChange={value => handleFilterChange('level', value)}
          disabled={isLoadingOptions}
        >
          <SelectTrigger className='w-32 rounded-full'>
            <SelectValue placeholder='레벨 선택' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>모든 레벨</SelectItem>
            {filterOptions.levels.map(level => (
              <SelectItem key={level.value} value={level.value}>
                {level.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className='flex flex-col gap-2'>
        <Select
          value={filters.gender || 'all'}
          onValueChange={value => handleFilterChange('gender', value)}
          disabled={isLoadingOptions}
        >
          <SelectTrigger className='w-32 rounded-full'>
            <SelectValue placeholder='성별 선택' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>전체</SelectItem>
            {filterOptions.genders.map(gender => (
              <SelectItem key={gender.value} value={gender.value}>
                {gender.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className='flex flex-col gap-2'>
        <Select
          value={filters.nearestStation || 'all'}
          onValueChange={value => handleFilterChange('nearestStation', value)}
          disabled={isLoadingOptions}
        >
          <SelectTrigger className='w-40 rounded-full'>
            <SelectValue placeholder='역 선택' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>모든 역</SelectItem>
            {filterOptions.nearestStations.map(station => (
              <SelectItem key={station.value} value={station.value}>
                {station.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
