import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { CheckCircle, Clock, Users } from 'lucide-react';

interface StudentDemographics {
  ageRange: string;
  gender: string;
  count: number;
}

interface ScheduleData {
  id: string;
  title: string;
  frequency: string;
  times: Array<{
    day: string;
    time: string;
  }>;
  spotsLeft: number;
  studentDemographics: StudentDemographics;
  isSelected?: boolean;
}

interface BookingCardProps {
  schedule: ScheduleData;
  onSelect: (scheduleId: string) => void;
  className?: string;
}

export function BookingCard({
  schedule,
  onSelect,
  className,
}: BookingCardProps) {
  const { id, frequency, times, spotsLeft, studentDemographics, isSelected } =
    schedule;

  return (
    <Card
      className={cn(
        'min-w-[320px] cursor-pointer p-3 transition-all hover:shadow-md sm:min-w-0',
        isSelected && 'border-primary ring-primary bg-primary/5 ring-2',
        className
      )}
      onClick={() => onSelect(id)}
    >
      <CardHeader className='px-2'>
        <CardTitle className='text-primary'>{frequency}</CardTitle>

        {isSelected ? (
          <CardAction>
            <CheckCircle className='text-primary' />
          </CardAction>
        ) : (
          <CardAction>
            <CheckCircle className='text-black/20' />
          </CardAction>
        )}
      </CardHeader>

      <CardContent className='space-y-2 p-2'>
        <div className='space-y-2'>
          {times.map((timeSlot, index) => (
            <div key={index} className='flex items-center gap-2 text-sm'>
              <Clock className='h-4 w-4 text-gray-500' />
              <span className='text-gray-700'>
                {timeSlot.day} {timeSlot.time}
              </span>
            </div>
          ))}
        </div>

        <div className='flex items-center gap-2'>
          <Users className='text-primary h-4 w-4' />
          <span className='text-primary font-medium'>
            {spotsLeft}자리 남았어요
          </span>
        </div>
      </CardContent>
      <CardFooter className='bg-primary/10 rounded-md py-2'>
        <div className='w-full'>
          <div className='text-sm text-gray-700'>
            수업 신청자 정보: {studentDemographics.ageRange}{' '}
            {studentDemographics.gender} {studentDemographics.count}명
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
