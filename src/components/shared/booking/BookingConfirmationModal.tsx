import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { CheckCircle } from 'lucide-react';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  bookingData: {
    title: string;
    schedule: string;
    successMessage?: string;
    description?: string;
    detailMessage?: string;
    buttonText?: string;
    footerMessage?: string;
  };
}

export function BookingConfirmationModal({
  isOpen,
  onClose,
  bookingData,
}: ConfirmationModalProps) {
  const {
    title,
    schedule,
    successMessage = '예약이 완료되었습니다!',
    description = '정원이 모집되면 확정 알림을 드릴게요.',
    detailMessage = '수업 모집 확정 시 48시간 이내 예약금 15%를 결제하시면 참여가 완료됩니다.',
    buttonText = '확인했어요.',
    footerMessage = '예약 완료 상태는 마이페이지에서 확인할 수 있어요.',
  } = bookingData;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-[400px]'>
        <div className='space-y-4'>
          {/* Check Icon */}
          <div className='mb-3 text-center'>
            <div className='mb-5 flex justify-center'>
              <div className='bg-primary flex size-[100px] items-center justify-center rounded-full'>
                <CheckCircle className='size-[60px] text-white' />
              </div>
            </div>

            <div>
              <h2 className='mb-1 text-base font-bold text-gray-900'>
                {successMessage}
              </h2>
              <p className='text-sm text-neutral-400'>{description}</p>
            </div>
          </div>
          <div className='flex flex-col gap-3 rounded-sm bg-neutral-100 p-4'>
            <p className='text-center text-xl font-semibold text-gray-800'>
              {title}
            </p>
            <p className='text-center text-sm text-neutral-500'>{schedule}</p>
            <p className='text-center text-sm text-neutral-400'>
              {detailMessage}
            </p>
          </div>

          <Button size='lg' onClick={onClose} className='w-full'>
            {buttonText}
          </Button>

          <p className='text-center text-sm text-gray-500'>{footerMessage}</p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
