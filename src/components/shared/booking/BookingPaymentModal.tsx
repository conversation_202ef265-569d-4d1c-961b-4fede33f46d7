import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { CreditCard, Info } from 'lucide-react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useBooking } from '@/contexts/BookingContext';

export function BookingPaymentModal() {
  const { selectedBooking, isPaymentModalOpen, closePaymentModal } =
    useBooking();
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();

  if (!selectedBooking) return null;

  const classData = {
    title: selectedBooking.classTitle,
    coach: selectedBooking.coachName,
    location: selectedBooking.location || '위치 정보 없음',
    schedule: `${selectedBooking.date} ${selectedBooking.time}`,
    totalPrice: selectedBooking.price,
    depositAmount: Math.round(selectedBooking.price * 0.15),
    depositRate: 15,
  };

  const handlePayment = async () => {
    setIsProcessing(true);

    // Close the modal first
    closePaymentModal();

    // Navigate to payment page with class data
    const searchParams = new URLSearchParams({
      title: classData.title,
      coach: classData.coach,
      location: classData.location,
      schedule: classData.schedule,
      totalPrice: classData.totalPrice.toString(),
      depositAmount: classData.depositAmount.toString(),
      depositRate: classData.depositRate.toString(),
    });

    router.push(`/payment?${searchParams.toString()}`);
  };

  return (
    <Dialog open={isPaymentModalOpen} onOpenChange={closePaymentModal}>
      <DialogContent className='max-h-[90vh] overflow-y-auto sm:max-w-[400px]'>
        <DialogHeader className='space-y-3'>
          <div className='flex items-center justify-between'>
            <DialogTitle className='text-xl font-semibold'>
              수업 예약 확정하기
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className='space-y-4'>
          <Card className='bg-gray-50'>
            <CardContent className='space-y-3 p-4'>
              <h3 className='line-clamp-1 min-w-0 font-medium text-black'>
                {classData.title}
              </h3>
              <div className='space-y-1 text-sm text-gray-600'>
                <p>{classData.coach}</p>
                <p>{classData.location}</p>
                <p>{classData.schedule}</p>
              </div>
              <p className='bg-primary/10 w-full rounded-md p-2 text-center'>
                운동 초급 45-54 여성 클래스
              </p>
            </CardContent>
          </Card>

          <div className='rounded-md bg-neutral-200/50 p-3'>
            <div className='text-primary mb-3 flex items-center gap-2'>
              <Info className='fill-primary text-white' />
              <span className='text-lg font-bold text-black'>
                예약금 결제 안내
              </span>
            </div>

            <div className='mb-3 flex flex-col gap-2'>
              <div className='flex items-center justify-between rounded-md bg-white p-3 text-sm'>
                <span className='text-black'>월 수업료</span>
                <span className='font-medium'>
                  {classData.totalPrice.toLocaleString()}원
                </span>
              </div>

              <div className='bg-primary flex items-center justify-between rounded-md p-3 text-sm text-white'>
                <span className=''>
                  예약금 결제 (수업료 {classData.depositRate}%)
                </span>
                <span className='text-primary font-bold'>
                  {classData.depositAmount.toLocaleString()}원
                </span>
              </div>
            </div>

            <div className='space-y-1 text-xs text-gray-600'>
              <div className='flex items-start gap-2'>
                <div className='bg-primary mt-2 h-1 w-1 flex-shrink-0 rounded-full' />
                <span>
                  예약금 15%를 제외한 잔액 수업료는 센터에서 결제해주시면
                  됩니다.
                </span>
              </div>
              <div className='flex items-start gap-2'>
                <div className='bg-primary mt-2 h-1 w-1 flex-shrink-0 rounded-full' />
                <span>
                  첫월 수업료 수업의 취소는 전액 2주 이내로 완불되어야 합니다.
                </span>
              </div>
              <div className='flex items-start gap-2'>
                <div className='bg-primary mt-2 h-1 w-1 flex-shrink-0 rounded-full' />
                <span>수업 확정 후 취소 시 완불 정책에 따라 처리됩니다.</span>
              </div>
            </div>
          </div>

          <div className='flex gap-3 pt-4'>
            <Button
              size='lg'
              variant='outline'
              onClick={closePaymentModal}
              className='flex-1'
              disabled={isProcessing}
            >
              취소
            </Button>
            <Button
              size='lg'
              onClick={handlePayment}
              className='flex-1'
              disabled={isProcessing}
            >
              {isProcessing ? (
                '결제 중...'
              ) : (
                <>
                  <CreditCard className='mr-2 h-4 w-4' />
                  예약금 결제하기
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
