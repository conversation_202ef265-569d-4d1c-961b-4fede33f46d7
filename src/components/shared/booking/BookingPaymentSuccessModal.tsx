import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { CheckCircle } from 'lucide-react';

interface PaymentConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  paymentData: {
    title: string;
    schedule: string;
    successMessage?: string;
    description?: string;
    detailMessage?: string;
    buttonText?: string;
    footerMessage?: string;
  };
}

export function BookingPaymentSuccessModal({
  isOpen,
  onClose,
  paymentData,
}: PaymentConfirmationModalProps) {
  const {
    title,
    schedule,
    successMessage = '수업 예약 결제가 완료되었습니다!',
    description = '다른 회원분들의 결제 확정 후 수업 진행 알림을 드릴 예정입니다.\n잠시만 기다려주세요.',
    detailMessage,
    buttonText = '확인했어요.',
    footerMessage = '예약 완료 상태는 마이페이지에서 확인할 수 있어요.',
  } = paymentData;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-[400px]'>
        <div className='space-y-4'>
          {/* Check Icon */}
          <div className='mb-3 text-center'>
            <div className='mb-5 flex justify-center'>
              <div className='bg-primary flex size-[100px] items-center justify-center rounded-full'>
                <CheckCircle className='size-[60px] text-white' />
              </div>
            </div>

            <div>
              <h2 className='mb-1 text-base font-bold text-gray-900'>
                {successMessage}
              </h2>
              <p className='text-sm whitespace-pre-line text-neutral-400'>
                {description}
              </p>
            </div>
          </div>
          <div className='flex flex-col gap-3 rounded-sm bg-neutral-100 p-4'>
            <p className='text-center text-xl font-semibold text-gray-800'>
              {title}
            </p>
            <p className='text-center text-sm text-neutral-500'>{schedule}</p>
            {detailMessage && (
              <p className='text-center text-sm text-neutral-400'>
                {detailMessage}
              </p>
            )}
          </div>

          <Button size='lg' onClick={onClose} className='w-full'>
            {buttonText}
          </Button>

          <p className='text-center text-sm text-gray-500'>{footerMessage}</p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
