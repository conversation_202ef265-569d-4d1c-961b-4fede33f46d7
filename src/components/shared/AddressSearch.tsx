import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MapPin, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import type {
  AddressSearchProps,
  DaumAddressData,
  AddressWithCoordinates,
} from '@/types/address';
import { geocodeAddress } from '@/lib/actions/geocoding';

/**
 * 다음 우편번호 서비스를 사용한 주소 검색 컴포넌트
 *
 * @example
 * ```tsx
 * <AddressSearch
 *   onComplete={(data) => {
 *     console.log('선택된 주소:', data.address);
 *     console.log('우편번호:', data.zonecode);
 *   }}
 *   placeholder="주소를 검색해주세요"
 *   buttonText="주소 찾기"
 * />
 * ```
 */
export function AddressSearch({
  onComplete,
  placeholder = '주소를 검색해주세요',
  buttonText = '주소 찾기',
  error,
  value,
  disabled = false,
  size = 'md',
  includeCoordinates = false,
  theme,
}: AddressSearchProps) {
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const scriptRef = useRef<HTMLScriptElement | null>(null);

  // 다음 우편번호 스크립트 로드
  useEffect(() => {
    const loadDaumScript = () => {
      // 이미 로드된 경우
      if (window.daum?.Postcode) {
        setIsScriptLoaded(true);
        return;
      }

      // 이미 스크립트가 추가된 경우
      if (scriptRef.current) {
        return;
      }

      const script = document.createElement('script');
      script.src =
        '//t1.daumcdn.net/mapjsapi/bundle/postcode/prod/postcode.v2.js';
      script.async = true;
      script.onload = () => {
        setIsScriptLoaded(true);
      };
      script.onerror = () => {
        console.error('다음 우편번호 스크립트 로드 실패');
        setIsScriptLoaded(false);
      };

      document.head.appendChild(script);
      scriptRef.current = script;
    };

    loadDaumScript();

    // 컴포넌트 언마운트 시 스크립트 제거
    return () => {
      if (scriptRef.current) {
        document.head.removeChild(scriptRef.current);
        scriptRef.current = null;
      }
    };
  }, []);

  const handleAddressSearch = () => {
    if (!isScriptLoaded || !window.daum?.Postcode) {
      alert('주소 검색 서비스를 불러오는 중입니다. 잠시 후 다시 시도해주세요.');
      return;
    }

    setIsSearching(true);

    const postcodeConfig = {
      oncomplete: async (data: DaumAddressData) => {
        let result: AddressWithCoordinates = { ...data };

        // 좌표 정보가 필요한 경우 지오코딩 수행
        if (includeCoordinates) {
          try {
            const coordinates = await geocodeAddress(data.address);
            console.log('coordinates', coordinates);
            if (coordinates) {
              result.coordinates = coordinates;
            }
          } catch (error) {
            console.warn('좌표 변환 실패:', error);
          }
        }

        setIsSearching(false);
        onComplete(result);
      },
      onclose: () => {
        setIsSearching(false);
      },
      width: 500,
      height: 600,
      animation: true,
      focusInput: true,
      theme: theme || {
        searchBgColor: '#0B65C8',
        queryTextColor: '#FFFFFF',
      },
    };

    const postcode = new window.daum.Postcode(postcodeConfig);

    // 화면 중앙에 팝업 띄우기
    const width = 500;
    const height = 600;
    const left = window.screen.width / 2 - width / 2;
    const top = window.screen.height / 2 - height / 2;

    postcode.open({
      left,
      top,
      popupTitle: '주소 검색',
      popupKey: 'addressSearch',
      autoClose: true,
    });
  };

  const getSizeClasses = () => {
    const sizeMap = {
      sm: 'h-8 text-sm',
      md: 'h-10 text-sm',
      lg: 'h-12 text-base',
    };
    return sizeMap[size];
  };

  const getButtonSizeClasses = () => {
    const sizeMap = {
      sm: 'h-8 px-3 text-sm',
      md: 'h-10 px-4 text-sm',
      lg: 'h-12 px-6 text-base',
    };
    return sizeMap[size];
  };

  return (
    <div className='space-y-2'>
      <div className='flex gap-2'>
        <div className='relative flex-1'>
          <Input
            value={value || ''}
            placeholder={placeholder}
            readOnly
            disabled={disabled}
            className={cn(
              getSizeClasses(),
              'cursor-pointer pr-10',
              error && 'border-red-500 focus-visible:ring-red-500',
              disabled && 'cursor-not-allowed'
            )}
            onClick={!disabled ? handleAddressSearch : undefined}
          />
          <MapPin
            size={size === 'sm' ? 16 : size === 'lg' ? 20 : 18}
            className='absolute top-1/2 right-3 -translate-y-1/2 text-gray-400'
          />
        </div>
        <Button
          type='button'
          variant='outline'
          onClick={handleAddressSearch}
          disabled={disabled || isSearching || !isScriptLoaded}
          className={cn(getButtonSizeClasses(), 'flex-shrink-0')}
        >
          {isSearching ? (
            <>
              <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent' />
              검색중...
            </>
          ) : (
            <>
              <Search
                size={size === 'sm' ? 14 : size === 'lg' ? 18 : 16}
                className='mr-2'
              />
              {buttonText}
            </>
          )}
        </Button>
      </div>

      {error && <p className='mt-1 text-sm text-red-500'>{error}</p>}

      {!isScriptLoaded && (
        <p className='mt-1 text-xs text-gray-500'>
          주소 검색 서비스를 불러오는 중...
        </p>
      )}
    </div>
  );
}

/**
 * 임베드 모드로 사용할 수 있는 주소 검색 컴포넌트
 */
interface AddressSearchEmbedProps
  extends Omit<AddressSearchProps, 'buttonText'> {
  /** 임베드할 컨테이너의 높이 */
  height?: number;
  /** 자동 닫힘 여부 */
  autoClose?: boolean;
}

export function AddressSearchEmbed({
  onComplete,
  height = 400,
  autoClose = true,
  includeCoordinates = false,
  theme,
  disabled = false,
}: AddressSearchEmbedProps) {
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const scriptRef = useRef<HTMLScriptElement | null>(null);

  // 다음 우편번호 스크립트 로드
  useEffect(() => {
    const loadDaumScript = () => {
      if (window.daum?.Postcode) {
        setIsScriptLoaded(true);
        return;
      }

      if (scriptRef.current) {
        return;
      }

      const script = document.createElement('script');
      script.src =
        '//t1.daumcdn.net/mapjsapi/bundle/postcode/prod/postcode.v2.js';
      script.async = true;
      script.onload = () => {
        setIsScriptLoaded(true);
      };
      script.onerror = () => {
        console.error('다음 우편번호 스크립트 로드 실패');
        setIsScriptLoaded(false);
      };

      document.head.appendChild(script);
      scriptRef.current = script;
    };

    loadDaumScript();

    return () => {
      if (scriptRef.current) {
        document.head.removeChild(scriptRef.current);
        scriptRef.current = null;
      }
    };
  }, []);

  const handleShowSearch = () => {
    if (!isScriptLoaded || !window.daum?.Postcode || !containerRef.current) {
      alert('주소 검색 서비스를 불러오는 중입니다. 잠시 후 다시 시도해주세요.');
      return;
    }

    setIsVisible(true);

    const postcodeConfig = {
      oncomplete: async (data: DaumAddressData) => {
        let result: AddressWithCoordinates = { ...data };

        // 좌표 정보가 필요한 경우 지오코딩 수행
        if (includeCoordinates) {
          try {
            const coordinates = await geocodeAddress(data.address);
            if (coordinates) {
              result.coordinates = coordinates;
            }
          } catch (error) {
            console.warn('좌표 변환 실패:', error);
          }
        }

        onComplete(result);
        if (autoClose) {
          setIsVisible(false);
        }
      },
      onclose: () => {
        setIsVisible(false);
      },
      width: 500,
      height: 600,
      theme: theme || {
        searchBgColor: '#0B65C8',
        queryTextColor: '#FFFFFF',
      },
    };

    const postcode = new window.daum.Postcode(postcodeConfig);
    postcode.embed(containerRef.current, {
      autoClose,
    });
  };

  const handleClose = () => {
    setIsVisible(false);
    if (containerRef.current) {
      containerRef.current.innerHTML = '';
    }
  };

  if (!isVisible) {
    return (
      <div className='space-y-2'>
        <Button
          type='button'
          variant='outline'
          onClick={handleShowSearch}
          disabled={disabled || !isScriptLoaded}
          className='w-full'
        >
          <Search size={16} className='mr-2' />
          주소 검색
        </Button>

        {!isScriptLoaded && (
          <p className='text-center text-xs text-gray-500'>
            주소 검색 서비스를 불러오는 중...
          </p>
        )}
      </div>
    );
  }

  return (
    <div className='overflow-hidden rounded-lg border'>
      <div className='flex items-center justify-between border-b bg-gray-50 p-3'>
        <h3 className='text-sm font-medium'>주소 검색</h3>
        <Button
          type='button'
          variant='ghost'
          size='sm'
          onClick={handleClose}
          className='h-6 w-6 p-0'
        >
          <Search size={12} />
        </Button>
      </div>
      <div ref={containerRef} style={{ height }} className='w-full' />
    </div>
  );
}
