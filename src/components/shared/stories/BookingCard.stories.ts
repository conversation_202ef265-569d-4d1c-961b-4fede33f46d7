import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import { BookingCard } from '../booking/BookingCard';

const meta = {
  title: 'Shared/Booking/BookingCard',
  component: BookingCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onSelect: { action: 'selected' },
  },
  args: {
    onSelect: fn(),
  },
} satisfies Meta<typeof BookingCard>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockSchedule = {
  id: '1',
  title: '요가 기초 클래스',
  frequency: '월/목 (주 2회)',
  times: [
    { day: '월요일', time: '10:00 ~ 11:00' },
    { day: '목요일', time: '10:00 ~ 11:00' },
  ],
  spotsLeft: 3,
  studentDemographics: {
    ageRange: '45-54세',
    gender: '여성',
    count: 2,
  },
};

export const Default: Story = {
  args: {
    schedule: mockSchedule,
  },
};

export const Selected: Story = {
  args: {
    schedule: {
      ...mockSchedule,
      isSelected: true,
    },
  },
};

export const SingleSpotLeft: Story = {
  args: {
    schedule: {
      ...mockSchedule,
      spotsLeft: 1,
    },
  },
};

export const NoSpotsLeft: Story = {
  args: {
    schedule: {
      ...mockSchedule,
      spotsLeft: 0,
    },
  },
};

export const MaleClass: Story = {
  args: {
    schedule: {
      ...mockSchedule,
      title: '남성 전용 헬스 클래스',
      frequency: '월/수/금 (주 3회)',
      times: [
        { day: '월요일', time: '19:00 ~ 20:00' },
        { day: '수요일', time: '19:00 ~ 20:00' },
        { day: '금요일', time: '19:00 ~ 20:00' },
      ],
      studentDemographics: {
        ageRange: '35-44세',
        gender: '남성',
        count: 6,
      },
      spotsLeft: 4,
    },
  },
};

export const MixedGender: Story = {
  args: {
    schedule: {
      ...mockSchedule,
      title: '프리미엄 요가',
      frequency: '토 (주 1회)',
      times: [
        { day: '토요일', time: '14:00 ~ 15:30' },
      ],
      studentDemographics: {
        ageRange: '25-34세',
        gender: '혼성',
        count: 5,
      },
    },
  },
};

export const SeniorClass: Story = {
  args: {
    schedule: {
      ...mockSchedule,
      title: '실버 요가',
      frequency: '화/금 (주 2회)',
      times: [
        { day: '화요일', time: '09:00 ~ 10:00' },
        { day: '금요일', time: '09:00 ~ 10:00' },
      ],
      studentDemographics: {
        ageRange: '55-64세',
        gender: '여성',
        count: 3,
      },
      spotsLeft: 5,
    },
  },
};