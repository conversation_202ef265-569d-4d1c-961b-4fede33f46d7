import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import { BookingPaymentSuccessModal } from '../booking/BookingPaymentSuccessModal';

const meta = {
  title: 'Shared/Booking/PaymentConfirmationModal',
  component: BookingPaymentSuccessModal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isOpen: { control: 'boolean' },
    onClose: { action: 'closed' },
  },
  args: {
    onClose: fn(),
  },
} satisfies Meta<typeof BookingPaymentSuccessModal>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPaymentData = {
  title: '주 2회 화/목 그룹PT 확정 완료',
  schedule: '화요일 10:00 ~ 11:00, 목요일 10:00 ~ 11:00',
  successMessage: '수업 예약 결제가 완료되었습니다!',
  description:
    '다른 회원분들의 결제 확정 후 수업 진행 알림을 드릴 예정입니다.\n잠시만 기다려주세요.',
  buttonText: '확인했어요.',
  footerMessage: '예약 완료 상태는 마이페이지에서 확인할 수 있어요.',
};

export const Default: Story = {
  args: {
    isOpen: true,
    paymentData: mockPaymentData,
  },
};

export const Closed: Story = {
  args: {
    isOpen: false,
    paymentData: mockPaymentData,
  },
};

export const YogaClass: Story = {
  args: {
    isOpen: true,
    paymentData: {
      title: '요가 기초 클래스 결제 완료',
      schedule: '월요일 10:00 ~ 11:00, 목요일 10:00 ~ 11:00',
      successMessage: '수업 예약 결제가 완료되었습니다!',
      description:
        '다른 회원분들의 결제 확정 후 수업 진행 알림을 드릴 예정입니다.\n잠시만 기다려주세요.',
      buttonText: '확인했어요.',
      footerMessage: '예약 완료 상태는 마이페이지에서 확인할 수 있어요.',
    },
  },
};

export const PilatesClass: Story = {
  args: {
    isOpen: true,
    paymentData: {
      title: '필라테스 중급 클래스 결제 완료',
      schedule: '화요일 14:00 ~ 15:00, 금요일 14:00 ~ 15:00',
      successMessage: '수업 예약 결제가 완료되었습니다!',
      description:
        '다른 회원분들의 결제 확정 후 수업 진행 알림을 드릴 예정입니다.\n잠시만 기다려주세요.',
      buttonText: '확인했어요.',
      footerMessage: '예약 완료 상태는 마이페이지에서 확인할 수 있어요.',
    },
  },
};

export const PersonalTraining: Story = {
  args: {
    isOpen: true,
    paymentData: {
      title: '개인 PT 세션 결제 완료',
      schedule: '매주 수요일 16:00 ~ 17:00',
      successMessage: '개인 PT 결제가 완료되었습니다!',
      description:
        '트레이너 확정 후 첫 수업 일정을 알려드릴게요.\n잠시만 기다려주세요.',
      buttonText: '네, 알겠습니다.',
      footerMessage: '결제 내역은 마이페이지에서 확인할 수 있어요.',
    },
  },
};

export const WithDetailMessage: Story = {
  args: {
    isOpen: true,
    paymentData: {
      title: '주 2회 화/목 그룹PT 확정 완료',
      schedule: '화요일 10:00 ~ 11:00, 목요일 10:00 ~ 11:00',
      successMessage: '수업 예약 결제가 완료되었습니다!',
      description:
        '다른 회원분들의 결제 확정 후 수업 진행 알림을 드릴 예정입니다.\n잠시만 기다려주세요.',
      detailMessage: '수업 시작 전 운동복과 수건을 준비해 주세요.',
      buttonText: '확인했어요.',
      footerMessage: '예약 완료 상태는 마이페이지에서 확인할 수 있어요.',
    },
  },
};

export const MinimalData: Story = {
  args: {
    isOpen: true,
    paymentData: {
      title: '수업 결제 완료',
      schedule: '일정 미정',
    },
  },
};
