import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import { BookingConfirmationModal } from '../booking/BookingConfirmationModal';

const meta = {
  title: 'Shared/Booking/ConfirmationModal',
  component: BookingConfirmationModal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isOpen: { control: 'boolean' },
    onClose: { action: 'closed' },
  },
  args: {
    onClose: fn(),
  },
} satisfies Meta<typeof BookingConfirmationModal>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockBookingData = {
  title: '요가 기초 클래스 예약 완료',
  schedule: '월요일 10:00 ~ 11:00, 목요일 10:00 ~ 11:00',
  successMessage: '예약이 완료되었습니다!',
  description: '정원이 모집되면 확정 알림을 드릴게요.',
  detailMessage:
    '수업 모집 확정 시 48시간 이내 예약금 15%를 결제하시면 참여가 완료됩니다.',
  buttonText: '확인했어요.',
  footerMessage: '예약 완료 상태는 마이페이지에서 확인할 수 있어요.',
};

export const Default: Story = {
  args: {
    isOpen: true,
    bookingData: mockBookingData,
  },
};

export const Closed: Story = {
  args: {
    isOpen: false,
    bookingData: mockBookingData,
  },
};

export const PilatesClass: Story = {
  args: {
    isOpen: true,
    bookingData: {
      title: '필라테스 중급 클래스 예약 완료',
      schedule: '화요일 14:00 ~ 15:00, 금요일 14:00 ~ 15:00',
      successMessage: '예약이 완료되었습니다!',
      description: '정원이 모집되면 확정 알림을 드릴게요.',
      detailMessage:
        '수업 모집 확정 시 48시간 이내 예약금 15%를 결제하시면 참여가 완료됩니다.',
      buttonText: '확인했어요.',
      footerMessage: '예약 완료 상태는 마이페이지에서 확인할 수 있어요.',
    },
  },
};

export const WeekendClass: Story = {
  args: {
    isOpen: true,
    bookingData: {
      title: '주말 스트레칭 클래스 예약 완료',
      schedule: '토요일 09:00 ~ 10:30',
      successMessage: '예약이 완료되었습니다!',
      description: '정원이 모집되면 확정 알림을 드릴게요.',
      detailMessage:
        '수업 모집 확정 시 48시간 이내 예약금 15%를 결제하시면 참여가 완료됩니다.',
      buttonText: '확인했어요.',
      footerMessage: '예약 완료 상태는 마이페이지에서 확인할 수 있어요.',
    },
  },
};

export const CustomMessages: Story = {
  args: {
    isOpen: true,
    bookingData: {
      title: '개인 PT 세션 예약 완료',
      schedule: '매주 수요일 16:00 ~ 17:00',
      successMessage: '개인 PT 예약이 성공적으로 완료되었습니다!',
      description: '트레이너가 확인 후 24시간 이내 연락드릴게요.',
      detailMessage: '첫 수업 전날까지 수업료 전액을 결제해주시면 됩니다.',
      buttonText: '네, 알겠습니다.',
      footerMessage: '예약 내역은 마이페이지 > 예약 관리에서 확인하세요.',
    },
  },
};

export const MinimalData: Story = {
  args: {
    isOpen: true,
    bookingData: {
      title: '간단한 예약 완료',
      schedule: '일정 미정',
    },
  },
};
