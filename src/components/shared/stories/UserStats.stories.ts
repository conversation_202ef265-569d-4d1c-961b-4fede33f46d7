import type { Meta, StoryObj } from '@storybook/react-vite';
import { UserStats } from '../profile/UserStats';

const meta = {
  title: 'Shared/Profile/UserStats',
  component: UserStats,
  parameters: {
    layout: 'fullscreen',
  },
} satisfies Meta<typeof UserStats>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockUser = {
  nickname: '닉네임',
  totalClasses: 15,
  attendedClasses: 12,
  points: 5000,
};

export const Default: Story = {
  args: {
    user: {
      ...mockUser,
      nextClass: {
        title: '기초 체력 수업',
        date: '2025-01-15',
        time: '10:00 ~ 11:00',
      },
    },
    attendancePoints: 500,
  },
};

export const NoNextClasses: Story = {
  args: {
    user: {
      ...mockUser,
      // no nextClass
    },
  },
};

export const NewUser: Story = {
  args: {
    user: {
      nickname: '새로운사용자',
      totalClasses: 0,
      attendedClasses: 0,
      points: 0,
      // no nextClass
    },
  },
};

export const OneNextClass: Story = {
  args: {
    user: {
      ...mockUser,
      nickname: '하나만예약',
      nextClass: {
        title: '필라테스 기초반',
        date: '2025-01-16',
        time: '14:00 ~ 15:00',
      },
    },
  },
};
