import type { Meta, StoryObj } from '@storybook/react-vite';
import { BookingHistoryCard } from '../profile/BookingHistoryCard';

const meta = {
  title: 'Shared/Profile/BookingHistoryCard',
  component: BookingHistoryCard,
} satisfies Meta<typeof BookingHistoryCard>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockBooking = {
  id: '1',
  classTitle: '요가 기초 클래스',
  coachName: '김지영 코치',
  date: '2024년 1월 15일',
  time: '10:00 ~ 11:00',
  price: 35000,
  location: '강남센터 A스튜디오',
};

export const Reserved: Story = {
  args: {
    booking: {
      ...mockBooking,
      status: 'reserved',
    },
  },
};

export const PaymentPending: Story = {
  args: {
    booking: {
      ...mockBooking,
      status: 'payment_pending',
    },
  },
};

export const InProgress: Story = {
  args: {
    booking: {
      ...mockBooking,
      status: 'in_progress',
    },
  },
};

export const Completed: Story = {
  args: {
    booking: {
      ...mockBooking,
      status: 'completed',
      date: '2023년 12월 20일',
    },
  },
};

export const Cancelled: Story = {
  args: {
    booking: {
      ...mockBooking,
      status: 'cancelled',
      date: '2023년 12월 10일',
    },
  },
};

export const HighPriceClass: Story = {
  args: {
    booking: {
      ...mockBooking,
      classTitle: '프리미엄 필라테스 클래스',
      price: 80000,
      status: 'reserved',
    },
  },
};
