import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import { BookingPaymentModal } from '../booking/BookingPaymentModal';

const meta = {
  title: 'Shared/Booking/PaymentModal',
  component: BookingPaymentModal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isOpen: { control: 'boolean' },
    onClose: { action: 'closed' },
    onConfirm: { action: 'confirmed' },
  },
  args: {
    onClose: fn(),
    onConfirm: fn(),
  },
} satisfies Meta<typeof BookingPaymentModal>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockClassData = {
  title: '요가 기초 클래스',
  coach: '김지영 코치',
  location: '강남센터 A스튜디오',
  schedule: '월요일 10:00 ~ 11:00, 목요일 10:00 ~ 11:00',
  totalPrice: 120000,
  depositAmount: 18000,
  depositRate: 15,
};

export const Default: Story = {
  args: {
    isOpen: true,
    classData: mockClassData,
  },
};

export const Closed: Story = {
  args: {
    isOpen: false,
    classData: mockClassData,
  },
};

export const HighPriceClass: Story = {
  args: {
    isOpen: true,
    classData: {
      ...mockClassData,
      title: '프리미엄 필라테스 클래스',
      totalPrice: 200000,
      depositAmount: 30000,
    },
  },
};

export const LowPriceClass: Story = {
  args: {
    isOpen: true,
    classData: {
      ...mockClassData,
      title: '그룹 스트레칭 클래스',
      totalPrice: 80000,
      depositAmount: 12000,
    },
  },
};

export const WeekendClass: Story = {
  args: {
    isOpen: true,
    classData: {
      ...mockClassData,
      title: '주말 요가 클래스',
      schedule: '토요일 09:00 ~ 10:30',
      location: '홍대센터 B스튜디오',
    },
  },
};
