import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { ClassCard } from '../class/ClassCard';
import { MapPin, Clock, Users, Star, Calendar, Award } from 'lucide-react';

const meta = {
  title: 'Shared/Class/ClassCard',
  component: ClassCard,
} satisfies Meta<typeof ClassCard>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockClassData = {
  id: '1',
  title: '요가 기초 클래스',
  image: '/mock.jpg',
  coach: {
    name: '김지영',
    avatar: '/coach1.jpg',
    bio: '10년 경력의 요가 전문 강사',
    tags: ['요가', '스트레칭', '명상'],
  },
  location: '강남센터',
  duration: 60,
  maxStudents: 8,
  price: 35000,
  tags: ['초급', '여성전용', '45-54세'],
  classInfo: [
    { icon: MapPin, label: '강남센터', variant: 'default' as const },
    { icon: Clock, label: '60분 수업', variant: 'default' as const },
    { icon: Star, label: '초급 클래스', variant: 'primary' as const },
    { icon: Users, label: '8인 그룹 수업', variant: 'primary' as const },
  ],
};

export const Default: Story = {
  args: {
    classData: mockClassData,
  },
};

export const Recommended: Story = {
  args: {
    classData: {
      ...mockClassData,
      badges: ['초급자 추천'],
    },
  },
};

export const PilatesClass: Story = {
  args: {
    classData: {
      ...mockClassData,
      id: '2',
      title: '필라테스 중급 클래스',
      coach: {
        name: '박민수',
        avatar: '/coach2.jpg',
        bio: '필라테스 자격증 보유 전문 강사',
        tags: ['필라테스', '코어운동', '재활'],
      },
      duration: 75,
      maxStudents: 6,
      price: 45000,
      tags: ['중급', '혼성', '30-45세'],
      classInfo: [
        { icon: MapPin, label: '홍대센터', variant: 'default' as const },
        { icon: Clock, label: '75분 수업', variant: 'default' as const },
        { icon: Award, label: '중급 클래스', variant: 'primary' as const },
        { icon: Users, label: '6인 소그룹', variant: 'primary' as const },
      ],
    },
  },
};

export const HighIntensityClass: Story = {
  args: {
    classData: {
      ...mockClassData,
      id: '3',
      title: 'HIIT 고강도 운동',
      coach: {
        name: '이철수',
        avatar: '/coach3.jpg',
        bio: '크로스핏 및 HIIT 전문 트레이너',
        tags: ['HIIT', '크로스핏', '다이어트'],
      },
      duration: 45,
      maxStudents: 12,
      price: 40000,
      tags: ['고급', '다이어트', '25-40세'],
      badges: ['인기클래스', '체험가능'],
      classInfo: [
        { icon: MapPin, label: '선릉센터', variant: 'default' as const },
        { icon: Clock, label: '45분 수업', variant: 'default' as const },
        { icon: Calendar, label: '고강도 클래스', variant: 'primary' as const },
        { icon: Users, label: '12인 그룹', variant: 'primary' as const },
      ],
    },
  },
};

export const LongDurationClass: Story = {
  args: {
    classData: {
      ...mockClassData,
      id: '4',
      title: '주말 특별 요가 클래스',
      duration: 90,
      maxStudents: 15,
      price: 50000,
      tags: ['초급', '휴식', '모든연령'],
    },
  },
};

export const SmallGroupClass: Story = {
  args: {
    classData: {
      ...mockClassData,
      id: '5',
      title: '1:1 개인 레슨',
      maxStudents: 1,
      price: 80000,
      tags: ['개인레슨', '맞춤형', '모든연령'],
    },
  },
};

export const MultipleBadges: Story = {
  args: {
    classData: {
      ...mockClassData,
      badges: ['초급자 추천', '인기클래스', '얼리버드'],
    },
  },
};

export const CustomClassInfo: Story = {
  args: {
    classData: {
      ...mockClassData,
      id: '6',
      title: '맞춤형 개인 트레이닝',
      classInfo: [
        { icon: MapPin, label: '강남 프리미엄점', variant: 'default' as const },
        { icon: Clock, label: '90분 집중', variant: 'default' as const },
        { icon: Award, label: 'VIP 전용', variant: 'primary' as const },
        { icon: Calendar, label: '1:1 개인레슨', variant: 'primary' as const },
      ],
    },
  },
};
