'use client';

import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Target, Clock, Edit, LogOut } from 'lucide-react';
import { signOut } from '@/lib/supabase/auth';

interface UserStatsProps {
  user: {
    nickname: string;
    totalClasses: number;
    attendedClasses: number;
    points: number;
    avatar?: string;
    nextClasses: {
      title: string;
      date: string;
      time: string;
    }[];
  };
  attendancePoints?: number;
}

export function UserStats({ user, attendancePoints }: UserStatsProps) {
  const { nickname, totalClasses, attendedClasses, points, nextClasses } = user;

  const handleLogout = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('UserStats: logout error', error);
    }
  };

  return (
    <div className='space-y-6 bg-gray-50 p-4'>
      <div className='flex items-center justify-between'>
        <h1 className='text-2xl font-bold'>{nickname}</h1>
        <div className='flex gap-2'>
          <Button>
            <Edit className='mr-2 h-4 w-4' /> 쉘위에 문의하기
          </Button>
          <Button variant='outline' onClick={handleLogout}>
            <LogOut className='mr-2 h-4 w-4' /> 로그아웃
          </Button>
        </div>
      </div>

      <div className='grid grid-cols-3 gap-2'>
        <Card>
          <CardContent className='flex flex-col items-center justify-center px-4 py-6'>
            <p className='text-3xl font-bold text-gray-900'>
              {totalClasses.toLocaleString()}
            </p>
            <p className='text-sm text-gray-600'>총 수업 일수</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='flex flex-col items-center justify-center px-4 py-6'>
            <p className='text-3xl font-bold text-gray-900'>
              {attendedClasses.toLocaleString()}
            </p>
            <p className='text-sm text-gray-600'>출석 일수</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='flex flex-col items-center justify-center px-4 py-6'>
            <p className='text-3xl font-bold text-gray-900'>
              {points.toLocaleString()}
            </p>
            <p className='text-sm text-gray-600'>포인트</p>
          </CardContent>
        </Card>
      </div>

      {nextClasses.length > 0 && (
        <Card>
          <CardContent className='p-4'>
            <div className='mb-4 flex items-center gap-2'>
              <Target className='text-primary h-6 w-6' />
              <h3 className='text-lg font-bold'>다음 수업 일정</h3>
            </div>
            <div className='space-y-2'>
              <div className='flex items-center gap-2 rounded-md bg-gray-100 p-3'>
                <Clock className='h-5 w-5 text-gray-500' />
                <div className='text-sm text-gray-800'>
                  <p className='font-medium'>{nextClasses[0].title}</p>
                  <p>
                    {nextClasses[0].date} {nextClasses[0].time}
                  </p>
                </div>
              </div>
            </div>
            {attendancePoints && (
              <Button className='mt-4 w-full' size='lg'>
                오늘 수업 출석체크하고 500 포인트 받기!
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
