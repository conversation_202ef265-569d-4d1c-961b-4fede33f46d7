'use client';

import { useState } from 'react';
import { BookingHistoryCard } from '@/components/shared/profile/BookingHistoryCard';
import { BookingPaymentModal } from '@/components/shared/booking/BookingPaymentModal';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BookingProvider, useBooking } from '@/contexts/BookingContext';
import type { Booking } from '@/schemas/profile';

interface BookingTabsProps {
  bookings: {
    reserved: Booking[];
    inProgress: Booking[];
    completed: Booking[];
  };
}

function BookingTabsContent({ bookings }: BookingTabsProps) {
  const [activeTab, setActiveTab] = useState('reserved');
  const { openPaymentModal, handleCancelBooking, handleViewDetails } = useBooking();

  const handlePaymentClick = (booking: Booking) => {
    openPaymentModal(booking);
  };

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab}>
      <TabsList className='grid w-full grid-cols-3'>
        <TabsTrigger value='reserved'>
          예약 수업 ({bookings.reserved.length})
        </TabsTrigger>
        <TabsTrigger value='inProgress'>
          진행 수업 ({bookings.inProgress.length})
        </TabsTrigger>
        <TabsTrigger value='completed'>
          완료 수업 ({bookings.completed.length})
        </TabsTrigger>
      </TabsList>

      <TabsContent value='reserved' className='mt-6'>
        <div className='space-y-4'>
          {bookings.reserved.length > 0 ? (
            bookings.reserved.map(booking => (
              <BookingHistoryCard
                key={booking.id}
                booking={booking}
                onPaymentClick={handlePaymentClick}
                onCancelBooking={handleCancelBooking}
                onViewDetails={handleViewDetails}
              />
            ))
          ) : (
            <div className='py-8 text-center text-gray-500'>
              예약된 수업이 없습니다
            </div>
          )}
        </div>
      </TabsContent>

      <TabsContent value='inProgress' className='mt-6'>
        <div className='space-y-4'>
          {bookings.inProgress.length > 0 ? (
            bookings.inProgress.map(booking => (
              <BookingHistoryCard
                key={booking.id}
                booking={booking}
                onViewDetails={handleViewDetails}
              />
            ))
          ) : (
            <div className='py-8 text-center text-gray-500'>
              진행 중인 수업이 없습니다
            </div>
          )}
        </div>
      </TabsContent>

      <TabsContent value='completed' className='mt-6'>
        <div className='space-y-4'>
          {bookings.completed.length > 0 ? (
            bookings.completed.map(booking => (
              <BookingHistoryCard
                key={booking.id}
                booking={booking}
                onViewDetails={handleViewDetails}
              />
            ))
          ) : (
            <div className='py-8 text-center text-gray-500'>
              완료된 수업이 없습니다
            </div>
          )}
        </div>
      </TabsContent>
      <BookingPaymentModal />
    </Tabs>
  );
}

export function BookingTabs({ bookings: initialBookings }: BookingTabsProps) {
  return (
    <BookingProvider>
      <BookingTabsContent bookings={initialBookings} />
    </BookingProvider>
  );
}
