import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, MapPin, Star, Users } from 'lucide-react';
import { cn } from '@/lib/utils';

import type { Booking } from '@/schemas/profile';

interface BookingHistoryCardProps {
  booking: Booking;
  onPaymentClick?: (booking: Booking) => void;
  onCancelBooking?: (bookingId: string) => void;
  onViewDetails?: (bookingId: string) => void;
  className?: string;
}

export function BookingHistoryCard({
  booking,
  onPaymentClick,
  onCancelBooking,
  onViewDetails,
  className,
}: BookingHistoryCardProps) {
  const handlePaymentClick = () => {
    onPaymentClick?.(booking);
  };

  const getStatusBadge = (status: Booking['status'], paymentStatus?: string) => {
    // payment_pending일 때는 실제 결제 상태를 확인
    if (status === 'payment_pending' && paymentStatus) {
      switch (paymentStatus) {
        case 'pending':
          return (
            <Badge
              variant='default'
              className='bg-orange-500 text-white hover:bg-orange-600'
            >
              결제 대기
            </Badge>
          );
        case 'processing':
          return (
            <Badge
              variant='default'
              className='bg-blue-500 text-white hover:bg-blue-600'
            >
              결제 처리 중
            </Badge>
          );
        case 'completed':
          return (
            <Badge className='border-green-200 bg-green-100 text-green-800'>
              결제 완료
            </Badge>
          );
        case 'failed':
          return (
            <Badge className='border-red-200 bg-red-100 text-red-800'>
              결제 실패
            </Badge>
          );
        case 'cancelled':
          return (
            <Badge className='border-gray-200 bg-gray-100 text-gray-800'>
              결제 취소
            </Badge>
          );
      }
    }

    switch (status) {
      case 'payment_pending':
        return (
          <Badge
            variant='default'
            className='bg-primary text-primary-foreground hover:bg-primary/90'
          >
            결제 대기
          </Badge>
        );
      case 'recruiting':
        return (
          <Badge
            variant='secondary'
            className='border-purple-200 bg-purple-100 text-purple-700'
          >
            모집중
          </Badge>
        );
      case 'reserved':
        return (
          <Badge className='border-green-200 bg-green-100 text-green-800'>
            예약 완료
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge className='border-blue-200 bg-blue-100 text-blue-800'>
            진행 중
          </Badge>
        );
      case 'completed':
        return (
          <Badge className='border-gray-200 bg-gray-100 text-gray-800'>
            완료
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge className='border-red-200 bg-red-100 text-red-800'>취소</Badge>
        );
      default:
        return null;
    }
  };

  const getActionButtons = () => {
    // payment_pending일 때는 결제 상태에 따라 다른 버튼 표시
    if (booking.status === 'payment_pending') {
      const paymentStatus = booking.payment?.status;
      
      if (paymentStatus === 'pending' || paymentStatus === 'failed') {
        return (
          <Button
            onClick={handlePaymentClick}
            className='flex h-auto w-full flex-col px-2 py-1.5'
          >
            {booking.paymentDueDate && (
              <span className='text-xs font-normal'>
                {booking.paymentDueDate}까지
              </span>
            )}
            <span className='font-semibold'>
              {paymentStatus === 'failed' ? '다시 결제하기' : '예약금 결제하기'}
            </span>
          </Button>
        );
      } else if (paymentStatus === 'processing') {
        return (
          <Button disabled className='w-full'>
            결제 처리 중...
          </Button>
        );
      } else if (paymentStatus === 'completed') {
        return (
          <Button
            onClick={() => onViewDetails?.(booking.id)}
            className='w-full'
          >
            수업 상세
          </Button>
        );
      }
      
      // fallback
      return (
        <Button
          onClick={handlePaymentClick}
          className='flex h-auto w-full flex-col px-2 py-1.5'
        >
          {booking.paymentDueDate && (
            <span className='text-xs font-normal'>
              {booking.paymentDueDate}까지
            </span>
          )}
          <span className='font-semibold'>예약금 결제하기</span>
        </Button>
      );
    }
    
    switch (booking.status) {
      case 'reserved':
      case 'recruiting':
        return (
          <div className='flex gap-2'>
            <Button
              variant='outline'
              onClick={() => onCancelBooking?.(booking.id)}
              className='flex-1'
            >
              예약 취소
            </Button>
            <Button
              onClick={() => onViewDetails?.(booking.id)}
              className='flex-1'
            >
              수업 상세
            </Button>
          </div>
        );
      case 'completed':
        return (
          <Button
            variant='outline'
            onClick={() => onViewDetails?.(booking.id)}
            className='w-full'
          >
            수업 후기 작성
          </Button>
        );
      default:
        return null;
    }
  };

  return (
    <Card className={cn('w-full max-w-md', className)}>
      <CardContent className='space-y-4 p-4'>
        <div className='flex items-start justify-between'>
          <div>
            <h3 className='text-lg font-bold'>{booking.classTitle}</h3>
            <p className='text-sm text-gray-500'>{booking.coachName}</p>
          </div>
          {getStatusBadge(booking.status, booking.payment?.status)}
        </div>

        <div className='grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-gray-600'>
          {booking.location && (
            <div className='flex items-center gap-2'>
              <MapPin className='h-4 w-4' />
              <span>{booking.location}</span>
            </div>
          )}
          {booking.schedule && (
            <div className='flex items-center gap-2'>
              <Clock className='h-4 w-4' />
              <span>{booking.schedule}</span>
            </div>
          )}
          {booking.level && (
            <div className='flex items-center gap-2'>
              <Star className='h-4 w-4' />
              <span>{booking.level}</span>
            </div>
          )}
          {booking.groupSize && (
            <div className='flex items-center gap-2'>
              <Users className='h-4 w-4' />
              <span>{booking.groupSize}인 그룹 수업</span>
            </div>
          )}
        </div>

        <div className='flex items-center justify-between gap-4'>
          <div>
            <p className='text-primary text-xl font-bold'>
              월 {booking.price.toLocaleString()}원
            </p>
            <p className='text-xs text-gray-500'>
              회당 {(booking.price / 8).toLocaleString()}원 · 8회 기준
            </p>
          </div>
          <div className='flex-shrink-0'>{getActionButtons()}</div>
        </div>
      </CardContent>
    </Card>
  );
}
