import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import { Slider } from '../slider';
import { useState } from 'react';

const meta = {
  title: 'UI/Slider',
  component: Slider,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    defaultValue: {
      control: 'object',
      description: 'Default value(s) for the slider',
    },
    min: {
      control: 'number',
      description: 'Minimum value',
    },
    max: {
      control: 'number',
      description: 'Maximum value',
    },
    step: {
      control: 'number',
      description: 'Step size',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the slider is disabled',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
  args: {
    onValueChange: fn(),
  },
} satisfies Meta<typeof Slider>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultValue: [50],
    max: 100,
    step: 1,
    className: 'w-64',
  },
};

export const Range: Story = {
  args: {
    defaultValue: [20, 80],
    max: 100,
    step: 1,
    className: 'w-64',
  },
};

export const WithSteps: Story = {
  args: {
    defaultValue: [25],
    max: 100,
    step: 25,
    className: 'w-64',
  },
};

export const SmallRange: Story = {
  args: {
    defaultValue: [5],
    min: 0,
    max: 10,
    step: 1,
    className: 'w-64',
  },
};

export const Disabled: Story = {
  args: {
    defaultValue: [60],
    max: 100,
    step: 1,
    disabled: true,
    className: 'w-64',
  },
};

export const WithLabel: Story = {
  render: () => {
    const [value, setValue] = useState([50]);

    return (
      <div className='w-64 space-y-2'>
        <div className='flex justify-between text-sm'>
          <span>Volume</span>
          <span>{value[0]}%</span>
        </div>
        <Slider value={value} onValueChange={setValue} max={100} step={1} />
      </div>
    );
  },
};

export const PriceRange: Story = {
  render: () => {
    const [value, setValue] = useState([200, 800]);

    return (
      <div className='w-80 space-y-3'>
        <div className='flex justify-between'>
          <span className='text-sm font-medium'>Price Range</span>
          <span className='text-muted-foreground text-sm'>
            ${value[0]} - ${value[1]}
          </span>
        </div>
        <Slider
          value={value}
          onValueChange={setValue}
          max={1000}
          min={0}
          step={10}
        />
        <div className='text-muted-foreground flex justify-between text-xs'>
          <span>$0</span>
          <span>$1,000</span>
        </div>
      </div>
    );
  },
};

export const Temperature: Story = {
  render: () => {
    const [value, setValue] = useState([22]);

    return (
      <div className='w-64 space-y-2'>
        <div className='flex justify-between text-sm'>
          <span>Temperature</span>
          <span>{value[0]}°C</span>
        </div>
        <Slider
          value={value}
          onValueChange={setValue}
          max={35}
          min={10}
          step={0.5}
        />
        <div className='text-muted-foreground flex justify-between text-xs'>
          <span>10°C</span>
          <span>35°C</span>
        </div>
      </div>
    );
  },
};
