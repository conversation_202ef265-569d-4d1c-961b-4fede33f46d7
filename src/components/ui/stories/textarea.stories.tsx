import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import { Textarea } from '../textarea';

const meta = {
  title: 'UI/Textarea',
  component: Textarea,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    placeholder: {
      control: 'text',
      description: 'Placeholder text',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the textarea is disabled',
    },
    rows: {
      control: 'number',
      description: 'Number of visible text lines',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
  args: {
    onChange: fn(),
    onFocus: fn(),
    onBlur: fn(),
  },
} satisfies Meta<typeof Textarea>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Type your message here...',
  },
};

export const WithValue: Story = {
  args: {
    defaultValue: 'This is some default text in the textarea.',
  },
};

export const Disabled: Story = {
  args: {
    placeholder: 'This textarea is disabled',
    disabled: true,
  },
};

export const WithRows: Story = {
  args: {
    placeholder: 'This textarea has 6 rows',
    rows: 6,
  },
};

export const Invalid: Story = {
  args: {
    placeholder: 'This textarea has an error',
    'aria-invalid': true,
  },
};

export const WithLabel: Story = {
  render: args => (
    <div className='grid w-full gap-1.5'>
      <label htmlFor='message' className='text-sm font-medium'>
        Your message
      </label>
      <Textarea id='message' placeholder='Type your message here.' {...args} />
    </div>
  ),
};

export const WithDescription: Story = {
  render: args => (
    <div className='grid w-full gap-1.5'>
      <label htmlFor='message-2' className='text-sm font-medium'>
        Your message
      </label>
      <Textarea
        id='message-2'
        placeholder='Type your message here.'
        {...args}
      />
      <p className='text-muted-foreground text-sm'>
        Your message will be copied to the support team.
      </p>
    </div>
  ),
};

export const WithErrorMessage: Story = {
  render: args => (
    <div className='grid w-full gap-1.5'>
      <label htmlFor='message-3' className='text-sm font-medium'>
        Your message
      </label>
      <Textarea
        id='message-3'
        placeholder='Type your message here.'
        aria-invalid={true}
        {...args}
      />
      <p className='text-sm text-red-500'>This field is required.</p>
    </div>
  ),
};
