import type { Meta, StoryObj } from '@storybook/react-vite';
import { Label } from '../label';
import { Input } from '../input';
import { Checkbox } from '../checkbox';

const meta = {
  title: 'UI/Label',
  component: Label,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
    children: {
      control: 'text',
      description: 'Label content',
    },
  },
  args: {
    children: 'Label',
  },
} satisfies Meta<typeof Label>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Default Label',
  },
};

export const WithInput: Story = {
  render: () => (
    <div className='grid w-full max-w-sm items-center gap-1.5'>
      <Label htmlFor='email'>Email</Label>
      <Input type='email' id='email' placeholder='Email' />
    </div>
  ),
};

export const WithCheckbox: Story = {
  render: () => (
    <div className='flex items-center space-x-2'>
      <Checkbox id='terms' />
      <Label htmlFor='terms'>Accept terms and conditions</Label>
    </div>
  ),
};

export const Required: Story = {
  render: () => (
    <div className='grid w-full max-w-sm items-center gap-1.5'>
      <Label htmlFor='name'>
        Name <span className='text-red-500'>*</span>
      </Label>
      <Input type='text' id='name' placeholder='Enter your name' />
    </div>
  ),
};

export const WithDescription: Story = {
  render: () => (
    <div className='grid w-full max-w-sm items-center gap-1.5'>
      <Label htmlFor='username'>Username</Label>
      <Input type='text' id='username' placeholder='Username' />
      <p className='text-muted-foreground text-sm'>
        This is your public display name.
      </p>
    </div>
  ),
};

export const Disabled: Story = {
  render: () => (
    <div className='grid w-full max-w-sm items-center gap-1.5'>
      <Label htmlFor='disabled-input'>Disabled Field</Label>
      <Input
        type='text'
        id='disabled-input'
        placeholder='Disabled input'
        disabled
      />
    </div>
  ),
};
