import type { Meta, StoryObj } from '@storybook/react-vite';
import { ToggleGroup, ToggleGroupItem } from '../toggle-group';

const meta = {
  title: 'UI/ToggleGroup',
  component: ToggleGroup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: { type: 'select' },
      options: ['single', 'multiple'],
      description: 'Selection type',
    },
    size: {
      control: { type: 'select' },
      options: ['default', 'sm', 'lg'],
      description: 'Size of toggle items',
    },
    variant: {
      control: { type: 'select' },
      options: ['default', 'outline'],
      description: 'Visual style variant',
    },
  },
} satisfies Meta<typeof ToggleGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Single: Story = {
  args: {
    type: 'single',
  },
  render: args => (
    <ToggleGroup {...args}>
      <ToggleGroupItem value='left'>Left</ToggleGroupItem>
      <ToggleGroupItem value='center'>Center</ToggleGroupItem>
      <ToggleGroupItem value='right'>Right</ToggleGroupItem>
    </ToggleGroup>
  ),
};

export const Multiple: Story = {
  args: {
    type: 'multiple',
  },
  render: args => (
    <ToggleGroup {...args}>
      <ToggleGroupItem value='bold'>B</ToggleGroupItem>
      <ToggleGroupItem value='italic'>I</ToggleGroupItem>
      <ToggleGroupItem value='underline'>U</ToggleGroupItem>
    </ToggleGroup>
  ),
};

export const Outline: Story = {
  args: {
    type: 'single',
    variant: 'outline',
  },
  render: args => (
    <ToggleGroup {...args}>
      <ToggleGroupItem value='left'>Left</ToggleGroupItem>
      <ToggleGroupItem value='center'>Center</ToggleGroupItem>
      <ToggleGroupItem value='right'>Right</ToggleGroupItem>
    </ToggleGroup>
  ),
};
