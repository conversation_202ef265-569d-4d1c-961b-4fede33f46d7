import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import { Switch } from '../switch';

const meta = {
  title: 'UI/Switch',
  component: Switch,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    checked: {
      control: 'boolean',
      description: 'Whether the switch is checked',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the switch is disabled',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
  args: {
    onCheckedChange: fn(),
  },
} satisfies Meta<typeof Switch>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const Checked: Story = {
  args: {
    checked: true,
  },
};

export const Unchecked: Story = {
  args: {
    checked: false,
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
};

export const DisabledChecked: Story = {
  args: {
    checked: true,
    disabled: true,
  },
};

export const WithLabel: Story = {
  render: args => (
    <div className='flex items-center space-x-2'>
      <Switch id='airplane-mode' {...args} />
      <label htmlFor='airplane-mode' className='text-sm font-medium'>
        Airplane mode
      </label>
    </div>
  ),
};

export const WithDescription: Story = {
  render: args => (
    <div className='flex items-center justify-between space-x-2'>
      <div className='space-y-0.5'>
        <label htmlFor='notifications' className='text-sm font-medium'>
          Push Notifications
        </label>
        <p className='text-muted-foreground text-sm'>
          Receive notifications about updates and new features.
        </p>
      </div>
      <Switch id='notifications' {...args} />
    </div>
  ),
};
