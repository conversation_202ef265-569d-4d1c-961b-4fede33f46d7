import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from '../accordion';

// Create a wrapper component to handle the different accordion types
const AccordionWrapper = ({ children, ...props }: any) => {
  return <div className='w-96'>{children}</div>;
};

const meta = {
  title: 'UI/Accordion',
  component: AccordionWrapper,
} satisfies Meta<typeof AccordionWrapper>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Single: Story = {
  render: () => (
    <Accordion type='single' collapsible className='w-96'>
      <AccordionItem value='item-1'>
        <AccordionTrigger>Is it accessible?</AccordionTrigger>
        <AccordionContent>
          Yes. It adheres to the WAI-ARIA design pattern.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value='item-2'>
        <AccordionTrigger>Is it styled?</AccordionTrigger>
        <AccordionContent>
          Yes. It comes with default styles that matches the other
          components&apos; aesthetic.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value='item-3'>
        <AccordionTrigger>Is it animated?</AccordionTrigger>
        <AccordionContent>
          Yes. It&apos;s animated by default, but you can disable it if you
          prefer.
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};

export const Multiple: Story = {
  render: () => (
    <Accordion type='multiple' className='w-96'>
      <AccordionItem value='item-1'>
        <AccordionTrigger>Can I open multiple items?</AccordionTrigger>
        <AccordionContent>
          Yes! In multiple mode, you can open several accordion items at the
          same time.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value='item-2'>
        <AccordionTrigger>How does it work?</AccordionTrigger>
        <AccordionContent>
          Each item can be independently opened or closed without affecting
          others.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value='item-3'>
        <AccordionTrigger>What are the benefits?</AccordionTrigger>
        <AccordionContent>
          This allows users to compare content from different sections
          simultaneously.
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};

export const FAQ: Story = {
  render: () => (
    <Accordion type='single' collapsible className='w-96'>
      <AccordionItem value='shipping'>
        <AccordionTrigger>What are your shipping options?</AccordionTrigger>
        <AccordionContent>
          We offer standard shipping (5-7 business days) and express shipping
          (2-3 business days). Free shipping is available on orders over $50.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value='returns'>
        <AccordionTrigger>What is your return policy?</AccordionTrigger>
        <AccordionContent>
          You can return items within 30 days of purchase for a full refund.
          Items must be in their original condition and packaging.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value='warranty'>
        <AccordionTrigger>Do you offer warranties?</AccordionTrigger>
        <AccordionContent>
          Yes, all our products come with a 1-year manufacturer warranty.
          Extended warranties are available for purchase.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value='support'>
        <AccordionTrigger>How can I contact customer support?</AccordionTrigger>
        <AccordionContent>
          You can reach us via <NAME_EMAIL>, phone at
          **************, or through our live chat feature available 24/7.
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};

export const WithLongContent: Story = {
  render: () => (
    <Accordion type='single' collapsible className='w-96'>
      <AccordionItem value='terms'>
        <AccordionTrigger>Terms and Conditions</AccordionTrigger>
        <AccordionContent>
          <div className='space-y-2'>
            <p>
              By using our service, you agree to the following terms and
              conditions:
            </p>
            <ul className='list-inside list-disc space-y-1'>
              <li>You must be at least 18 years old to use this service</li>
              <li>
                You are responsible for maintaining the security of your account
              </li>
              <li>
                We reserve the right to terminate accounts that violate our
                policies
              </li>
              <li>
                Content you upload must not infringe on others&apos; rights
              </li>
            </ul>
            <p>
              These terms may be updated from time to time. Continued use of the
              service constitutes acceptance of any changes.
            </p>
          </div>
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value='privacy'>
        <AccordionTrigger>Privacy Policy</AccordionTrigger>
        <AccordionContent>
          We respect your privacy and are committed to protecting your personal
          data. This policy explains how we collect, use, and safeguard your
          information when you use our services.
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};

export const DefaultExpanded: Story = {
  render: () => (
    <Accordion type='single' collapsible defaultValue='item-1' className='w-96'>
      <AccordionItem value='item-1'>
        <AccordionTrigger>This item is expanded by default</AccordionTrigger>
        <AccordionContent>
          This content is visible when the component first loads because we set
          defaultValue=&quot;item-1&quot; on the Accordion component.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value='item-2'>
        <AccordionTrigger>This item starts collapsed</AccordionTrigger>
        <AccordionContent>
          This content is hidden by default and will be shown when the user
          clicks the trigger.
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};
