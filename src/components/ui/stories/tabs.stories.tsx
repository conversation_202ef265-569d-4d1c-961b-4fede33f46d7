import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from '../tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../card';
import { Input } from '../input';
import { Label } from '../label';

const meta = {
  title: 'UI/Tabs',
  component: Tabs,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    defaultValue: {
      control: 'text',
      description: 'The default active tab',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof Tabs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: args => (
    <Tabs defaultValue='account' className='w-[400px]' {...args}>
      <TabsList>
        <TabsTrigger value='account'>Account</TabsTrigger>
        <TabsTrigger value='password'>Password</TabsTrigger>
      </TabsList>
      <TabsContent value='account'>
        <p>Make changes to your account here.</p>
      </TabsContent>
      <TabsContent value='password'>
        <p>Change your password here.</p>
      </TabsContent>
    </Tabs>
  ),
};

export const WithCards: Story = {
  render: args => (
    <Tabs defaultValue='account' className='w-[400px]' {...args}>
      <TabsList className='grid w-full grid-cols-2'>
        <TabsTrigger value='account'>Account</TabsTrigger>
        <TabsTrigger value='password'>Password</TabsTrigger>
      </TabsList>
      <TabsContent value='account'>
        <Card>
          <CardHeader>
            <CardTitle>Account</CardTitle>
            <CardDescription>
              Make changes to your account here. Click save when you&apos;re
              done.
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-2'>
            <div className='space-y-1'>
              <Label htmlFor='name'>Name</Label>
              <Input id='name' defaultValue='Pedro Duarte' />
            </div>
            <div className='space-y-1'>
              <Label htmlFor='username'>Username</Label>
              <Input id='username' defaultValue='@peduarte' />
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value='password'>
        <Card>
          <CardHeader>
            <CardTitle>Password</CardTitle>
            <CardDescription>
              Change your password here. After saving, you&apos;ll be logged
              out.
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-2'>
            <div className='space-y-1'>
              <Label htmlFor='current'>Current password</Label>
              <Input id='current' type='password' />
            </div>
            <div className='space-y-1'>
              <Label htmlFor='new'>New password</Label>
              <Input id='new' type='password' />
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  ),
};

export const Multiple: Story = {
  render: args => (
    <Tabs defaultValue='overview' className='w-[500px]' {...args}>
      <TabsList>
        <TabsTrigger value='overview'>Overview</TabsTrigger>
        <TabsTrigger value='analytics'>Analytics</TabsTrigger>
        <TabsTrigger value='reports'>Reports</TabsTrigger>
        <TabsTrigger value='notifications'>Notifications</TabsTrigger>
      </TabsList>
      <TabsContent value='overview' className='space-y-4'>
        <h3 className='text-lg font-semibold'>Overview</h3>
        <p>View your account overview and recent activity.</p>
      </TabsContent>
      <TabsContent value='analytics' className='space-y-4'>
        <h3 className='text-lg font-semibold'>Analytics</h3>
        <p>Detailed analytics and insights about your account.</p>
      </TabsContent>
      <TabsContent value='reports' className='space-y-4'>
        <h3 className='text-lg font-semibold'>Reports</h3>
        <p>Generate and download various reports.</p>
      </TabsContent>
      <TabsContent value='notifications' className='space-y-4'>
        <h3 className='text-lg font-semibold'>Notifications</h3>
        <p>Manage your notification preferences.</p>
      </TabsContent>
    </Tabs>
  ),
};

export const WithIcons: Story = {
  render: args => (
    <Tabs defaultValue='profile' className='w-[400px]' {...args}>
      <TabsList>
        <TabsTrigger value='profile'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='16'
            height='16'
            viewBox='0 0 24 24'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          >
            <path d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2' />
            <circle cx='12' cy='7' r='4' />
          </svg>
          Profile
        </TabsTrigger>
        <TabsTrigger value='settings'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='16'
            height='16'
            viewBox='0 0 24 24'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          >
            <path d='M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z' />
            <circle cx='12' cy='12' r='3' />
          </svg>
          Settings
        </TabsTrigger>
      </TabsList>
      <TabsContent value='profile'>
        <p>Edit your profile information here.</p>
      </TabsContent>
      <TabsContent value='settings'>
        <p>Manage your account settings here.</p>
      </TabsContent>
    </Tabs>
  ),
};

export const Vertical: Story = {
  render: args => (
    <Tabs
      defaultValue='general'
      className='flex w-[600px]'
      orientation='vertical'
      {...args}
    >
      <TabsList className='h-fit w-48 flex-col'>
        <TabsTrigger value='general' className='w-full justify-start'>
          General
        </TabsTrigger>
        <TabsTrigger value='security' className='w-full justify-start'>
          Security
        </TabsTrigger>
        <TabsTrigger value='integrations' className='w-full justify-start'>
          Integrations
        </TabsTrigger>
        <TabsTrigger value='support' className='w-full justify-start'>
          Support
        </TabsTrigger>
      </TabsList>
      <div className='flex-1 pl-6'>
        <TabsContent value='general'>
          <h3 className='mb-2 text-lg font-semibold'>General Settings</h3>
          <p>Manage your general account settings and preferences.</p>
        </TabsContent>
        <TabsContent value='security'>
          <h3 className='mb-2 text-lg font-semibold'>Security Settings</h3>
          <p>Configure security settings and two-factor authentication.</p>
        </TabsContent>
        <TabsContent value='integrations'>
          <h3 className='mb-2 text-lg font-semibold'>Integrations</h3>
          <p>Connect and manage third-party integrations.</p>
        </TabsContent>
        <TabsContent value='support'>
          <h3 className='mb-2 text-lg font-semibold'>Support</h3>
          <p>Get help and contact our support team.</p>
        </TabsContent>
      </div>
    </Tabs>
  ),
};
