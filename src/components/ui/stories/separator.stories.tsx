import type { Meta, StoryObj } from '@storybook/react-vite';
import { Separator } from '../separator';

const meta = {
  title: 'UI/Separator',
  component: Separator,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
      description: 'The orientation of the separator',
    },
    decorative: {
      control: 'boolean',
      description: 'Whether the separator is decorative',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof Separator>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Horizontal: Story = {
  args: {
    orientation: 'horizontal',
  },
  render: args => (
    <div className='w-64'>
      <div className='space-y-1'>
        <h4 className='text-sm font-medium'>Above separator</h4>
        <p className='text-muted-foreground text-sm'>Content above</p>
      </div>
      <Separator className='my-4' {...args} />
      <div className='space-y-1'>
        <h4 className='text-sm font-medium'>Below separator</h4>
        <p className='text-muted-foreground text-sm'>Content below</p>
      </div>
    </div>
  ),
};

export const Vertical: Story = {
  args: {
    orientation: 'vertical',
  },
  render: args => (
    <div className='flex h-20 items-center space-x-4'>
      <div className='text-sm'>
        <div className='font-medium'>Left content</div>
        <div className='text-muted-foreground'>Some text</div>
      </div>
      <Separator {...args} />
      <div className='text-sm'>
        <div className='font-medium'>Right content</div>
        <div className='text-muted-foreground'>Some text</div>
      </div>
    </div>
  ),
};

export const InMenu: Story = {
  render: () => (
    <div className='bg-popover text-popover-foreground w-48 rounded-md border p-1 shadow-md'>
      <div className='px-2 py-1.5 text-sm font-semibold'>Account</div>
      <Separator />
      <div className='hover:bg-accent cursor-pointer px-2 py-1.5 text-sm'>
        Profile
      </div>
      <div className='hover:bg-accent cursor-pointer px-2 py-1.5 text-sm'>
        Settings
      </div>
      <Separator />
      <div className='hover:bg-accent cursor-pointer px-2 py-1.5 text-sm'>
        Logout
      </div>
    </div>
  ),
};

export const InText: Story = {
  render: () => (
    <div className='text-center'>
      <p className='text-muted-foreground text-sm'>Already have an account?</p>
      <div className='relative'>
        <div className='absolute inset-0 flex items-center'>
          <Separator />
        </div>
        <div className='relative flex justify-center text-xs uppercase'>
          <span className='bg-background text-muted-foreground px-2'>
            Or continue with
          </span>
        </div>
      </div>
      <p className='text-muted-foreground text-sm'>Sign up with your email</p>
    </div>
  ),
};
