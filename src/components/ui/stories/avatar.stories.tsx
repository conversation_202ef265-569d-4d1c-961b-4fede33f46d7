import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { Avatar, AvatarImage, AvatarFallback } from '../avatar';

const meta = {
  title: 'UI/Avatar',
  component: Avatar,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof Avatar>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <Avatar>
      <AvatarImage src='https://github.com/shadcn.png' alt='@shadcn' />
      <AvatarFallback>CN</AvatarFallback>
    </Avatar>
  ),
};

export const Fallback: Story = {
  render: () => (
    <Avatar>
      <AvatarImage src='https://invalid-url.com/image.jpg' alt='Invalid' />
      <AvatarFallback>JD</AvatarFallback>
    </Avatar>
  ),
};

export const WithoutImage: Story = {
  render: () => (
    <Avatar>
      <AvatarFallback>AB</AvatarFallback>
    </Avatar>
  ),
};

export const Large: Story = {
  render: () => (
    <Avatar className='h-16 w-16'>
      <AvatarImage src='https://github.com/shadcn.png' alt='@shadcn' />
      <AvatarFallback className='text-lg'>CN</AvatarFallback>
    </Avatar>
  ),
};

export const Small: Story = {
  render: () => (
    <Avatar className='h-6 w-6'>
      <AvatarImage src='https://github.com/shadcn.png' alt='@shadcn' />
      <AvatarFallback className='text-xs'>CN</AvatarFallback>
    </Avatar>
  ),
};

export const Group: Story = {
  render: () => (
    <div className='flex -space-x-2'>
      <Avatar className='border-background border-2'>
        <AvatarImage src='https://github.com/shadcn.png' alt='User 1' />
        <AvatarFallback>U1</AvatarFallback>
      </Avatar>
      <Avatar className='border-background border-2'>
        <AvatarImage src='https://github.com/vercel.png' alt='User 2' />
        <AvatarFallback>U2</AvatarFallback>
      </Avatar>
      <Avatar className='border-background border-2'>
        <AvatarFallback>U3</AvatarFallback>
      </Avatar>
      <Avatar className='border-background border-2'>
        <AvatarFallback>+2</AvatarFallback>
      </Avatar>
    </div>
  ),
};

export const WithBadge: Story = {
  render: () => (
    <div className='relative'>
      <Avatar>
        <AvatarImage src='https://github.com/shadcn.png' alt='@shadcn' />
        <AvatarFallback>CN</AvatarFallback>
      </Avatar>
      <div className='ring-background absolute -right-0 -bottom-0 h-3 w-3 rounded-full bg-green-500 ring-2'></div>
    </div>
  ),
};

export const Square: Story = {
  render: () => (
    <Avatar className='rounded-md'>
      <AvatarImage src='https://github.com/shadcn.png' alt='@shadcn' />
      <AvatarFallback className='rounded-md'>CN</AvatarFallback>
    </Avatar>
  ),
};
