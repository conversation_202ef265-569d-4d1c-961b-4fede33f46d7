import type { Meta, StoryObj } from '@storybook/react-vite';
import { AspectRatio } from '../aspect-ratio';

const meta = {
  title: 'UI/AspectRatio',
  component: AspectRatio,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    ratio: {
      control: 'number',
      description: 'The desired aspect ratio (width / height)',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof AspectRatio>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Square: Story = {
  render: () => (
    <div className='w-64'>
      <AspectRatio ratio={1 / 1}>
        <div className='flex h-full items-center justify-center rounded-md bg-slate-100'>
          <span className='text-slate-600'>1:1 Square</span>
        </div>
      </AspectRatio>
    </div>
  ),
};

export const Video: Story = {
  render: () => (
    <div className='w-96'>
      <AspectRatio ratio={16 / 9}>
        <div className='flex h-full items-center justify-center rounded-md bg-slate-100'>
          <span className='text-slate-600'>16:9 Video</span>
        </div>
      </AspectRatio>
    </div>
  ),
};

export const Photo: Story = {
  render: () => (
    <div className='w-64'>
      <AspectRatio ratio={4 / 3}>
        <div className='flex h-full items-center justify-center rounded-md bg-slate-100'>
          <span className='text-slate-600'>4:3 Photo</span>
        </div>
      </AspectRatio>
    </div>
  ),
};

export const Portrait: Story = {
  render: () => (
    <div className='w-48'>
      <AspectRatio ratio={3 / 4}>
        <div className='flex h-full items-center justify-center rounded-md bg-slate-100'>
          <span className='text-slate-600'>3:4 Portrait</span>
        </div>
      </AspectRatio>
    </div>
  ),
};

export const WithImage: Story = {
  render: () => (
    <div className='w-96'>
      <AspectRatio ratio={16 / 9}>
        <img
          src='https://images.unsplash.com/photo-1588345921523-c2dcdb7f1dcd?w=800&dpr=2&q=80'
          alt='Photo by Drew Beamer'
          className='h-full w-full rounded-md object-cover'
        />
      </AspectRatio>
    </div>
  ),
};

export const WithVideo: Story = {
  render: () => (
    <div className='w-96'>
      <AspectRatio ratio={16 / 9}>
        <iframe
          src='https://www.youtube.com/embed/dQw4w9WgXcQ'
          title='YouTube video player'
          className='h-full w-full rounded-md'
          allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
        />
      </AspectRatio>
    </div>
  ),
};

export const UltraWide: Story = {
  render: () => (
    <div className='w-96'>
      <AspectRatio ratio={21 / 9}>
        <div className='flex h-full items-center justify-center rounded-md bg-slate-100'>
          <span className='text-slate-600'>21:9 Ultra Wide</span>
        </div>
      </AspectRatio>
    </div>
  ),
};

export const Golden: Story = {
  render: () => (
    <div className='w-64'>
      <AspectRatio ratio={1.618 / 1}>
        <div className='flex h-full items-center justify-center rounded-md bg-slate-100'>
          <span className='text-slate-600'>Golden Ratio</span>
        </div>
      </AspectRatio>
    </div>
  ),
};
