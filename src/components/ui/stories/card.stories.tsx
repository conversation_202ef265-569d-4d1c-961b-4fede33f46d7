import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import {
  <PERSON>,
  CardHeader,
  CardFooter,
  CardTitle,
  CardAction,
  CardDescription,
  CardContent,
} from '../card';
import { Button } from '../button';

const meta = {
  title: 'UI/Card',
  component: Card,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <Card className='w-[350px]'>
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>Card description goes here.</CardDescription>
      </CardHeader>
      <CardContent>
        <p>This is the card content area.</p>
      </CardContent>
    </Card>
  ),
};

export const WithFooter: Story = {
  render: () => (
    <Card className='w-[350px]'>
      <CardHeader>
        <CardTitle>Notifications</CardTitle>
        <CardDescription>You have 3 unread messages.</CardDescription>
      </CardHeader>
      <CardContent>
        <div className='space-y-2'>
          <p>Message 1: Hello there!</p>
          <p>Message 2: How are you?</p>
          <p>Message 3: Let&apos;s meet tomorrow.</p>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={fn()}>Mark all as read</Button>
      </CardFooter>
    </Card>
  ),
};

export const WithAction: Story = {
  render: () => (
    <Card className='w-[350px]'>
      <CardHeader>
        <CardTitle>Project Settings</CardTitle>
        <CardDescription>Manage your project configuration.</CardDescription>
        <CardAction>
          <Button variant='outline' size='sm' onClick={fn()}>
            Edit
          </Button>
        </CardAction>
      </CardHeader>
      <CardContent>
        <div className='space-y-2'>
          <p>
            <strong>Name:</strong> My Project
          </p>
          <p>
            <strong>Status:</strong> Active
          </p>
          <p>
            <strong>Created:</strong> 2024-01-01
          </p>
        </div>
      </CardContent>
    </Card>
  ),
};

export const Complex: Story = {
  render: () => (
    <Card className='w-[400px]'>
      <CardHeader>
        <CardTitle>User Profile</CardTitle>
        <CardDescription>
          Manage your account settings and preferences.
        </CardDescription>
        <CardAction>
          <Button variant='ghost' size='sm' onClick={fn()}>
            ⋯
          </Button>
        </CardAction>
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          <div className='flex items-center space-x-4'>
            <div className='h-12 w-12 rounded-full bg-slate-200'></div>
            <div>
              <p className='font-medium'>John Doe</p>
              <p className='text-sm text-gray-500'><EMAIL></p>
            </div>
          </div>
          <div className='space-y-2'>
            <p>
              <strong>Role:</strong> Administrator
            </p>
            <p>
              <strong>Last login:</strong> 2 hours ago
            </p>
            <p>
              <strong>Member since:</strong> January 2024
            </p>
          </div>
        </div>
      </CardContent>
      <CardFooter className='justify-between'>
        <Button variant='outline' onClick={fn()}>
          Cancel
        </Button>
        <Button onClick={fn()}>Save Changes</Button>
      </CardFooter>
    </Card>
  ),
};

export const Minimal: Story = {
  render: () => (
    <Card className='w-[300px]'>
      <CardContent className='pt-6'>
        <p>A simple card with just content.</p>
      </CardContent>
    </Card>
  ),
};

export const HeaderOnly: Story = {
  render: () => (
    <Card className='w-[300px]'>
      <CardHeader>
        <CardTitle>Simple Header</CardTitle>
        <CardDescription>Just a header, no content.</CardDescription>
      </CardHeader>
    </Card>
  ),
};
