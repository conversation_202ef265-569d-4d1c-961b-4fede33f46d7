import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../select';

const meta = {
  title: 'UI/Select',
  component: Select,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  args: {
    onValueChange: fn(),
  },
} satisfies Meta<typeof Select>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <Select>
      <SelectTrigger className='w-[180px]'>
        <SelectValue placeholder='Select a fruit' />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value='apple'>Apple</SelectItem>
        <SelectItem value='banana'>Banana</SelectItem>
        <SelectItem value='blueberry'>Blueberry</SelectItem>
        <SelectItem value='grapes'>Grapes</SelectItem>
        <SelectItem value='pineapple'>Pineapple</SelectItem>
      </SelectContent>
    </Select>
  ),
};

export const WithGroups: Story = {
  render: () => (
    <Select>
      <SelectTrigger className='w-[180px]'>
        <SelectValue placeholder='Select timezone' />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value='est'>Eastern Standard Time (EST)</SelectItem>
        <SelectItem value='cst'>Central Standard Time (CST)</SelectItem>
        <SelectItem value='mst'>Mountain Standard Time (MST)</SelectItem>
        <SelectItem value='pst'>Pacific Standard Time (PST)</SelectItem>
        <SelectItem value='akst'>Alaska Standard Time (AKST)</SelectItem>
        <SelectItem value='hst'>Hawaii Standard Time (HST)</SelectItem>
      </SelectContent>
    </Select>
  ),
};
