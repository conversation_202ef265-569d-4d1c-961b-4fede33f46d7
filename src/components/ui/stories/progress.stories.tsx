import type { Meta, StoryObj } from '@storybook/react-vite';
import { Progress } from '../progress';
import { useState, useEffect } from 'react';

const meta = {
  title: 'UI/Progress',
  component: Progress,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    value: {
      control: { type: 'range', min: 0, max: 100, step: 1 },
      description: 'The progress value (0-100)',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof Progress>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    value: 33,
    className: 'w-64',
  },
};

export const Empty: Story = {
  args: {
    value: 0,
    className: 'w-64',
  },
};

export const Half: Story = {
  args: {
    value: 50,
    className: 'w-64',
  },
};

export const Complete: Story = {
  args: {
    value: 100,
    className: 'w-64',
  },
};

export const Small: Story = {
  args: {
    value: 75,
    className: 'w-32 h-1',
  },
};

export const Large: Story = {
  args: {
    value: 60,
    className: 'w-96 h-4',
  },
};

export const Animated: Story = {
  render: () => {
    const [progress, setProgress] = useState(13);

    useEffect(() => {
      const timer = setTimeout(() => setProgress(66), 500);
      return () => clearTimeout(timer);
    }, []);

    return <Progress value={progress} className='w-64' />;
  },
};

export const WithLabel: Story = {
  render: () => (
    <div className='w-64 space-y-2'>
      <div className='flex justify-between text-sm'>
        <span>Uploading...</span>
        <span>75%</span>
      </div>
      <Progress value={75} />
    </div>
  ),
};

export const FileUpload: Story = {
  render: () => (
    <div className='w-80 space-y-3'>
      <div>
        <div className='mb-1 flex justify-between text-sm'>
          <span className='font-medium'>document.pdf</span>
          <span className='text-muted-foreground'>2.4MB / 3.2MB</span>
        </div>
        <Progress value={75} />
      </div>
      <div>
        <div className='mb-1 flex justify-between text-sm'>
          <span className='font-medium'>image.jpg</span>
          <span className='text-muted-foreground'>Complete</span>
        </div>
        <Progress value={100} />
      </div>
      <div>
        <div className='mb-1 flex justify-between text-sm'>
          <span className='font-medium'>video.mp4</span>
          <span className='text-muted-foreground'>Waiting...</span>
        </div>
        <Progress value={0} />
      </div>
    </div>
  ),
};
