interface LoadingProps {
  count?: number;
  className?: string;
}

export function ClassCardSkeleton({ className }: { className?: string }) {
  return (
    <div className={`h-32 animate-pulse rounded-lg bg-gray-200 ${className || ''}`} />
  );
}

export function Loading({ count = 3, className }: LoadingProps) {
  return (
    <div className={`space-y-4 ${className || ''}`}>
      {Array.from({ length: count }).map((_, index) => (
        <ClassCardSkeleton key={index} />
      ))}
    </div>
  );
}