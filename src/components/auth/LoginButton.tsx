'use client';

/**
 * 카카오 로그인 버튼 컴포넌트들
 * 
 * @description 
 * - LoginButton: 기본 스타일의 카카오 로그인 버튼
 * - KakaoLoginButton: 카카오 디자인 가이드라인을 준수하는 로그인 버튼
 * - KakaoLoginIcon: 카카오 아이콘 컴포넌트
 * 
 * @example
 * // 기본 사용법
 * import { KakaoLoginButton } from '@/components/auth/LoginButton';
 * 
 * <KakaoLoginButton 
 *   redirectTo="/dashboard" 
 *   size="md" 
 * />
 */

interface LoginButtonProps {
  redirectTo?: string;
  className?: string;
  children?: React.ReactNode;
}

export function LoginButton({
  redirectTo = "/dashboard",
  className = "",
  children
}: LoginButtonProps) {
  const handleKakaoLogin = async () => {
    try {
      const { signInWithKakao } = await import('@/lib/supabase/auth');
      await signInWithKakao(redirectTo);
    } catch (error) {
      console.error('카카오 로그인 오류:', error);
      alert('로그인에 실패했습니다. 다시 시도해주세요.');
    }
  };

  return (
    <button
      onClick={handleKakaoLogin}
      type="button"
      className={`
        inline-flex items-center justify-center
        px-6 py-3 border border-transparent
        text-base font-medium rounded-lg
        text-white bg-yellow-400 hover:bg-yellow-500
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-400
        transition-colors duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
        ${className}
      `}
    >
      {children || (
        <>
          <svg
            className="w-5 h-5 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 3C7.58 3 4 6.14 4 10c0 2.38 1.19 4.47 3 5.74V21l3.5-2c.5.06 1 .06 1.5 0L15.5 21v-5.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.58-7-8-7z"
              fill="currentColor"
            />
          </svg>
          카카오로 로그인
        </>
      )}
    </button>
  );
}

export function KakaoLoginIcon({ className = "w-5 h-5" }: { className?: string }) {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 3C7.58 3 4 6.14 4 10c0 2.38 1.19 4.47 3 5.74V21l3.5-2c.5.06 1 .06 1.5 0L15.5 21v-5.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.58-7-8-7z"
        fill="currentColor"
      />
    </svg>
  );
}

// 카카오 디자인 가이드라인 준수 로그인 버튼
export function KakaoLoginButton({
  size = "md",
  variant = "filled",
  redirectTo = "/dashboard",
  disabled = false,
  className = "",
}: {
  size?: "sm" | "md" | "lg";
  variant?: "filled" | "outlined";
  redirectTo?: string;
  disabled?: boolean;
  className?: string;
}) {
  // 카카오 공식 색상 (#FEE500)
  const sizeClasses = {
    sm: "px-4 py-2 text-sm h-10",
    md: "px-6 py-3 text-base h-12", 
    lg: "px-6 py-4 text-lg h-14",
  };

  const handleKakaoLogin = async () => {
    try {
      const { signInWithKakao } = await import('@/lib/supabase/auth');
      await signInWithKakao(redirectTo);
    } catch (error) {
      console.error('카카오 로그인 오류:', error);
      alert('로그인에 실패했습니다. 다시 시도해주세요.');
    }
  };

  return (
    <button
      onClick={handleKakaoLogin}
      type="button"
      disabled={disabled}
      className={`
        inline-flex items-center justify-center
        font-medium rounded-xl
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-400
        transition-all duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
        ${sizeClasses[size]}
        ${className}
      `}
      style={{
        backgroundColor: '#FEE500',
        color: 'rgba(0, 0, 0, 0.85)',
        border: 'none'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#FDD800';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = '#FEE500';
      }}
    >
      {/* 카카오 공식 심볼 */}
      <svg
        className="w-5 h-5 mr-2 flex-shrink-0"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 3C7.58 3 4 6.14 4 10c0 2.38 1.19 4.47 3 5.74V21l3.5-2c.5.06 1 .06 1.5 0L15.5 21v-5.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.58-7-8-7z"
          fill="currentColor"
        />
      </svg>
      <span className="font-medium">
        {size === "sm" ? "로그인" : "카카오 로그인"}
      </span>
    </button>
  );
}
