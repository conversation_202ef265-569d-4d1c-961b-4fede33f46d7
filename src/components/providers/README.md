# AuthProvider 사용법

## 설정

루트 레이아웃에서 AuthProvider로 앱을 감싸세요:

```tsx
// app/layout.tsx
import { AuthProvider } from '@/components/providers/AuthProvider';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ko">
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
```

## 사용법

### 1. 기존 UserContext와 동일한 인터페이스

```tsx
import { useUser } from '@/hooks/useUser';

function MyComponent() {
  const { user, role, loading, isAuthenticated, refreshUser } = useUser();

  if (loading) return <div>로딩 중...</div>;
  
  if (!isAuthenticated) return <div>로그인이 필요합니다.</div>;

  return (
    <div>
      <p>이메일: {user?.email}</p>
      <p>역할: {role}</p>
      <button onClick={refreshUser}>새로고침</button>
    </div>
  );
}
```

### 2. 성능 최적화된 개별 훅 사용

```tsx
import { useCurrentUser, useUserRole, useIsAuthenticated } from '@/hooks/useUser';

function OptimizedComponent() {
  // 필요한 상태만 구독하여 불필요한 리렌더링 방지
  const user = useCurrentUser();
  const role = useUserRole();
  const isAuthenticated = useIsAuthenticated();

  return (
    <div>
      {isAuthenticated && (
        <>
          <p>사용자: {user?.email}</p>
          <p>역할: {role}</p>
        </>
      )}
    </div>
  );
}
```

### 3. 직접 스토어 사용

```tsx
import { useUserStore } from '@/contexts/user.store';

function DirectStoreComponent() {
  const { user, role, setRole, reset } = useUserStore();

  return (
    <div>
      <p>사용자: {user?.email}</p>
      <p>역할: {role}</p>
      <button onClick={() => setRole('ADMIN')}>관리자로 변경</button>
      <button onClick={reset}>초기화</button>
    </div>
  );
}
```

## 마이그레이션

기존 UserContext에서 새로운 시스템으로 마이그레이션:

1. `AuthProvider`를 루트에 추가
2. `useUser()` 훅은 기존과 동일하게 사용 가능
3. 성능이 중요한 컴포넌트는 개별 훅 사용
4. 기존 `UserProvider`는 점진적으로 제거