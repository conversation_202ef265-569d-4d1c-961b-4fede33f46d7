'use client';

import { userStoreActions } from '@/contexts/user.store';
import { onAuthStateChange } from '@/lib/supabase/auth';
import { User } from '@supabase/supabase-js';
import { useEffect } from 'react';

export default function AuthCheck() {
  useEffect(() => {
    const {
      data: { subscription },
    } = onAuthStateChange(async (user: User) => {
      console.log('onAuthStateChange', user);
      try {
        userStoreActions.setUser(user);
        if (user) {
          userStoreActions.setLoading(true);
        } else {
          userStoreActions.setRole(null);
        }
      } catch (error) {
        console.error('AuthCheck::error', error);
      } finally {
        userStoreActions.setLoading(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return null;
}
