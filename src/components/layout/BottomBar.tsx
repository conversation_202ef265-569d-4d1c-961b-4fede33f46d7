'use client';

import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Calendar, CreditCard, MessageCircle } from 'lucide-react';

interface BottomBarAction {
  label: string;
  onClick: () => void;
  variant?: 'default' | 'outline';
  icon?: React.ReactNode;
}

interface BottomBarProps {
  actions: BottomBarAction[];
  className?: string;
}

export function BottomBar({ actions, className }: BottomBarProps) {
  // const pathname = usePathname();

  if (actions.length === 0) return null;

  // const hideGlobalNav = shouldHideBottomNav(pathname);

  return (
    <div
      className={cn(
        'fixed bottom-0 z-[100]',
        'mx-auto w-full max-w-[480px] py-2',
        'border-t border-[#eeeeee] bg-white',
        'px-3',
        className
      )}
    >
      <div className='flex h-full items-center'>
        {actions.length === 1 ? (
          <Button
            onClick={actions[0].onClick}
            className='h-12 w-full text-base font-medium'
            variant={actions[0].variant || 'default'}
          >
            {actions[0].icon}
            {actions[0].label}
          </Button>
        ) : (
          <div className='flex w-full gap-3'>
            {actions.map((action, index) => (
              <Button
                key={index}
                onClick={action.onClick}
                className={cn(
                  'h-12 flex-1 text-base font-medium',
                  'flex items-center justify-center gap-2'
                )}
                variant={
                  action.variant ||
                  (index === actions.length - 1 ? 'default' : 'outline')
                }
              >
                {action.icon}
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Predefined action configs for common use cases
export const bottomBarConfigs = {
  classDetail: (onInquiry: () => void, onViewSchedule: () => void) => [
    {
      label: '문의하기',
      onClick: onInquiry,
      variant: 'outline' as const,
      icon: <MessageCircle className='h-4 w-4' />,
    },
    {
      label: '수업일정 보기',
      onClick: onViewSchedule,
      variant: 'default' as const,
      icon: <Calendar className='h-4 w-4' />,
    },
  ],

  booking: (onBook: () => void) => [
    {
      label: '예약하기',
      onClick: onBook,
      variant: 'default' as const,
      icon: <CreditCard className='h-4 w-4' />,
    },
  ],
};
