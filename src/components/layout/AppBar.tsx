'use client';

import { Button } from '@/components/ui/button';
import { useUserStore } from '@/contexts/user.store';
import { cn } from '@/lib/utils';
import { ArrowLeft, Home, User } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';

interface AppBarProps {
  title?: string;
  className?: string;
}

export function AppBar({ title = 'ShallWe', className }: AppBarProps) {
  const router = useRouter();
  const pathname = usePathname();
  // const { user } = useUser();
  const user = useUserStore(state => state.user);

  const isRoot = pathname === '/';
  const isProfilePage = pathname === '/profile';
  const isAuthenticated = !!user;

  const handleBackClick = () => {
    router.back();
  };

  const handleProfileClick = () => {
    // 로그인하지 않은 사용자는 로그인 페이지로 이동
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    // 로그인한 사용자는 프로필 페이지로 이동
    router.push('/profile');
  };

  const handleTitleClick = () => {
    router.push('/');
  };

  return (
    <header
      className={cn(
        'sticky top-0 z-[49] h-14 w-full border-b border-gray-200 bg-white',
        'flex items-center justify-between px-4',
        className
      )}
    >
      <div className='flex items-center gap-3'>
        {!isRoot && (
          <Button
            variant='ghost'
            size='sm'
            onClick={handleBackClick}
            className='h-8 w-8 p-2'
          >
            <ArrowLeft className='h-4 w-4' />
            <span className='sr-only'>뒤로가기</span>
          </Button>
        )}
      </div>

      <h1
        className='logo absolute left-1/2 -translate-x-1/2 transform cursor-pointer text-lg font-semibold'
        onClick={handleTitleClick}
      >
        {title}
      </h1>

      <div className='flex items-center gap-2'>
        {!isRoot && (
          <Button
            variant='ghost'
            size='sm'
            onClick={handleTitleClick}
            className='h-8 w-8 p-2'
          >
            <Home className='h-4 w-4' />
            <span className='sr-only'>홈</span>
          </Button>
        )}
        {!isProfilePage && (
          <Button
            variant='ghost'
            size='sm'
            onClick={handleProfileClick}
            className='h-8 w-8 p-2'
          >
            <User className='h-4 w-4' />
            <span className='sr-only'>프로필</span>
          </Button>
        )}
      </div>
    </header>
  );
}
