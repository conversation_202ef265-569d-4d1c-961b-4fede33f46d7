import { z } from 'zod';

// 스튜디오 상태 enum
export const StudioStatusEnum = z.enum([
  'active', // 운영중
  'inactive', // 일시중단
  'pending', // 승인대기
  'closed', // 폐업
]);

// 스튜디오 이미지 타입 enum
export const StudioImageTypeEnum = z.enum([
  'featured', // 대표 이미지
  'gallery', // 갤러리 이미지
]);

// 스튜디오 이미지 스키마
export const StudioImageSchema = z.object({
  path: z.string().min(1, '이미지 경로를 입력해주세요'),
  url: z.string().url('올바른 URL 형식을 입력해주세요'),
});

// 스튜디오 이미지 배열 스키마
export const StudioImagesSchema = z
  .array(StudioImageSchema)
  .max(10, '이미지는 최대 10개까지 업로드 가능합니다')
  .refine(
    images => {
      const paths = images.map(img => img.path);
      return new Set(paths).size === paths.length;
    },
    {
      message: '중복된 이미지 경로가 있습니다',
    }
  );

// 스튜디오 링크 스키마 (객체 구조)
export const StudioLinksSchema = z
  .object({
    website: z
      .union([
        z.string().url('올바른 웹사이트 URL을 입력해주세요'),
        z.literal(''),
      ])
      .optional(),
    sns: z
      .union([z.string().url('올바른 SNS URL을 입력해주세요'), z.literal('')])
      .optional(),
  })
  .optional();

// 시설 정보 스키마
export const AmenitiesSchema = z
  .object({
    parking: z
      .array(
        z.object({
          type: z.enum(['free', 'paid']),
          price: z.number().optional(),
          description: z.string().optional(),
        })
      )
      .default([]),

    shower: z
      .array(
        z.object({
          available: z.boolean(),
          description: z.string().optional(),
        })
      )
      .default([]),

    locker: z
      .array(
        z.object({
          type: z.enum(['free', 'paid']),
          price: z.number().optional(),
          description: z.string().optional(),
        })
      )
      .default([]),

    workoutClothes: z
      .array(
        z.object({
          type: z.enum(['free', 'paid']),
          price: z.number().optional(),
          description: z.string().optional(),
        })
      )
      .default([]),

    towel: z
      .array(
        z.object({
          type: z.enum(['personal', 'workout']),
          available: z.boolean(),
          description: z.string().optional(),
        })
      )
      .default([]),

    others: z
      .array(
        z.object({
          name: z.string(),
          description: z.string().optional(),
          price: z.number().optional(),
        })
      )
      .default([]),
  })
  .partial();

// 운영시간 스키마
export const OperatingHoursSchema = z.object({
  monday: z.object({
    open: z
      .string()
      .regex(
        /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
        '올바른 시간 형식을 입력해주세요 (HH:MM)'
      ),
    close: z
      .string()
      .regex(
        /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
        '올바른 시간 형식을 입력해주세요 (HH:MM)'
      ),
    closed: z.boolean().default(false),
  }),
  tuesday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    closed: z.boolean().default(false),
  }),
  wednesday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    closed: z.boolean().default(false),
  }),
  thursday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    closed: z.boolean().default(false),
  }),
  friday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    closed: z.boolean().default(false),
  }),
  saturday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    closed: z.boolean().default(false),
  }),
  sunday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    closed: z.boolean().default(false),
  }),
});

// 위치 정보 스키마
export const LocationSchema = z.object({
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
});

// 기본 스튜디오 정보 스키마
export const StudioBaseSchema = z.object({
  partnerId: z.string().uuid('올바른 파트너 ID를 입력해주세요'),
  name: z
    .string()
    .min(2, '스튜디오 이름은 2자 이상 입력해주세요')
    .max(100, '스튜디오 이름은 100자 이하로 입력해주세요'),
  phone: z.string(),
  description: z.string().optional(),
  address: z
    .string()
    .min(5, '주소를 입력해주세요')
    .max(255, '주소는 255자 이하로 입력해주세요'),
  addressDetail: z
    .string()
    .min(1, '상세 주소를 입력해주세요')
    .max(100, '상세 주소는 100자 이하로 입력해주세요')
    .optional(),
  postalCode: z
    .string()
    .regex(/^[0-9]{5}$/, '올바른 우편번호 형식을 입력해주세요 (5자리 숫자)')
    .optional(),
  nearestStation: z
    .string()
    .max(100, '지하철역 이름은 100자 이하로 입력해주세요')
    .optional(),
  stationDistance: z
    .number()
    .int()
    .min(0, '역까지의 거리는 0 이상이어야 합니다')
    .optional(),
  status: StudioStatusEnum.default('pending'),
  images: StudioImagesSchema.optional(), // JSONB 구조로 변경
});

// 스튜디오 생성 스키마
export const CreateStudioSchema = StudioBaseSchema.merge(LocationSchema).extend(
  {
    amenities: AmenitiesSchema.optional(),
    operatingHours: OperatingHoursSchema.optional(),
    links: StudioLinksSchema,
  }
);

// 스튜디오 수정 스키마 (partnerId 제외)
export const UpdateStudioSchema = CreateStudioSchema.omit({
  partnerId: true,
}).partial();

// 스튜디오 응답 스키마 (partnerId 제외)
export const StudioResponseSchema = CreateStudioSchema.omit({
  partnerId: true,
}).extend({
  id: z.string().uuid(),
  images: StudioImagesSchema.optional(), // JSONB 구조로 변경
  links: StudioLinksSchema,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const StudioListItemSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description: z.string().optional(),
  address: z.string(),
  phone: z.string(),
  status: StudioStatusEnum,
  nearestStation: z.string().optional(),
  createdAt: z.string().datetime(),
});

// 스튜디오 목록 응답 스키마
export const StudioListResponseSchema = z.object({
  studios: z.array(StudioListItemSchema),
  pagination: z.object({
    currentPage: z.number(),
    limit: z.number(),
    totalCount: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }),
  filters: z.object({
    status: z.string().optional(),
    search: z.string().optional(),
    location: z
      .object({
        latitude: z.string(),
        longitude: z.string(),
        radius: z.string(),
      })
      .optional(),
  }),
});

// 타입 추론
export type StudioStatus = z.infer<typeof StudioStatusEnum>;
export type StudioImageType = z.infer<typeof StudioImageTypeEnum>;
export type StudioImage = z.infer<typeof StudioImageSchema>;
export type StudioImages = z.infer<typeof StudioImagesSchema>;
export type StudioLinks = z.infer<typeof StudioLinksSchema>;
export type Amenities = z.infer<typeof AmenitiesSchema>;
export type OperatingHours = z.infer<typeof OperatingHoursSchema>;
export type CreateStudioRequest = z.infer<typeof CreateStudioSchema>;
export type UpdateStudioRequest = z.infer<typeof UpdateStudioSchema>;
export type StudioResponse = z.infer<typeof StudioResponseSchema>;
export type StudioListItem = z.infer<typeof StudioListItemSchema>;
export type StudioListResponse = z.infer<typeof StudioListResponseSchema>;
