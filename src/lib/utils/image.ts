import { StudioImage, StudioImages, StudioImageType } from '@/lib/schemas/studio';

/**
 * 스튜디오 이미지 배열 관리 유틸리티 함수들
 */

/**
 * 새로운 이미지를 배열에 추가
 * @param images 기존 이미지 배열
 * @param newImage 추가할 이미지
 * @returns 업데이트된 이미지 배열
 */
export function addImageToArray(
  images: StudioImages = [],
  newImage: StudioImage
): StudioImages {
  const updatedImages = [...images];
  updatedImages.push(newImage);
  return updatedImages;
}

/**
 * 배열에서 이미지 제거 (인덱스 기반)
 * @param images 기존 이미지 배열
 * @param index 제거할 이미지 인덱스
 * @returns 업데이트된 이미지 배열
 */
export function removeImageFromArray(
  images: StudioImages = [],
  index: number
): StudioImages {
  if (index < 0 || index >= images.length) {
    return images;
  }
  
  const updatedImages = [...images];
  updatedImages.splice(index, 1);
  
  return updatedImages;
}

/**
 * 배열에서 경로로 이미지 제거
 * @param images 기존 이미지 배열
 * @param imagePath 제거할 이미지 경로
 * @returns 업데이트된 이미지 배열
 */
export function removeImageByPath(
  images: StudioImages = [],
  imagePath: string
): StudioImages {
  return images.filter(img => img.path !== imagePath);
}

/**
 * 이미지 순서 변경 (배열 재정렬)
 * @param images 기존 이미지 배열
 * @param fromIndex 이동할 이미지의 현재 인덱스
 * @param toIndex 이동할 목표 인덱스
 * @returns 재정렬된 이미지 배열
 */
export function reorderImage(
  images: StudioImages = [],
  fromIndex: number,
  toIndex: number
): StudioImages {
  if (fromIndex < 0 || fromIndex >= images.length || toIndex < 0 || toIndex >= images.length) {
    return images;
  }
  
  const updatedImages = [...images];
  const [movedImage] = updatedImages.splice(fromIndex, 1);
  updatedImages.splice(toIndex, 0, movedImage);
  
  return updatedImages;
}

/**
 * 대표 이미지 여부 확인 (첫 번째 이미지)
 * @param images 이미지 배열
 * @param index 확인할 이미지 인덱스
 * @returns 대표 이미지 여부
 */
export function isFeaturedImage(images: StudioImages = [], index: number): boolean {
  return index === 0 && images.length > 0;
}

/**
 * 대표 이미지 가져오기
 * @param images 이미지 배열
 * @returns 대표 이미지 또는 null
 */
export function getFeaturedImage(images: StudioImages = []): StudioImage | null {
  return images.length > 0 ? images[0] : null;
}

/**
 * 갤러리 이미지들 가져오기
 * @param images 이미지 배열
 * @returns 갤러리 이미지 배열 (배열 순서 유지)
 */
export function getGalleryImages(images: StudioImages = []): StudioImages {
  return images.slice(1); // 첫 번째 제외한 나머지
}

/**
 * 업로드 API 응답을 Studio 이미지 형식으로 변환
 * @param uploadResults 업로드 API 응답
 * @param imageType 이미지 타입 (첫 번째는 featured, 나머지는 gallery)
 * @returns Studio 이미지 배열
 */
export function convertUploadResultsToImages(
  uploadResults: Array<{ url: string; path: string; success: boolean }>
): StudioImages {
  const images: StudioImages = [];
  
  uploadResults.forEach((result) => {
    if (!result.success) return;
    
    images.push({
      path: result.path,
      url: result.url,
    });
  });
  
  return images;
}

/**
 * 이미지 배열 유효성 검사
 * @param images 검사할 이미지 배열
 * @returns 검사 결과
 */
export function validateImageArray(images: StudioImages): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // 최대 개수 확인
  if (images.length > 10) {
    errors.push('이미지는 최대 10개까지 허용됩니다.');
  }
  
  // 대표 이미지는 첫 번째 이미지로 자동 결정
  
  // 중복 경로 확인
  const paths = images.map(img => img.path);
  const uniquePaths = new Set(paths);
  if (paths.length !== uniquePaths.size) {
    errors.push('중복된 이미지 경로가 있습니다.');
  }
  
  // URL 형식 확인
  images.forEach((img, index) => {
    try {
      new URL(img.url);
    } catch {
      errors.push(`${index + 1}번째 이미지의 URL 형식이 올바르지 않습니다.`);
    }
  });
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * 기존 이미지 배열을 새로운 배열로 업데이트
 * - 기존 이미지는 유지하되, 제거되지 않은 것만
 * - 새로운 이미지는 추가
 * @param existingImages 기존 이미지 배열
 * @param newImages 새로운 이미지 배열
 * @returns 병합된 이미지 배열
 */
export function mergeImageArrays(
  existingImages: StudioImages = [],
  newImages: StudioImages = []
): StudioImages {
  // 새로 제공된 경로들
  const newPaths = new Set(newImages.map(img => img.path));
  
  // 기존 이미지 중 새 배열에 포함된 것들만 유지
  const preservedImages = existingImages.filter(img => newPaths.has(img.path));
  
  // 새로운 이미지들 중 기존에 없던 것들만 추가
  const existingPaths = new Set(preservedImages.map(img => img.path));
  const addedImages = newImages.filter(img => !existingPaths.has(img.path));
  
  // 병합 (배열 순서로 대표/갤러리 이미지 구분)
  return [...preservedImages, ...addedImages];
}