/**
 * 가격 관련 유틸리티 함수들
 */

/**
 * 숫자를 천단위 쉼표가 포함된 문자열로 변환
 * @param price 가격 (숫자)
 * @returns 천단위 쉼표가 포함된 문자열 (예: "120,000")
 */
export function formatPrice(price: number): string {
  return price.toLocaleString('ko-KR');
}

/**
 * 숫자를 한글로 변환
 * @param num 변환할 숫자
 * @returns 한글 표기 (예: "십이만원")
 */
export function numberToKorean(num: number): string {
  if (num === 0) return '영원';
  
  const units = ['', '십', '백', '천'];
  const bigUnits = ['', '만', '억', '조'];
  
  // 원 단위로 변환 (이미 원 단위라고 가정)
  let result = '';
  let unitIndex = 0;
  
  while (num > 0) {
    const chunk = num % 10000;
    if (chunk > 0) {
      let chunkStr = '';
      let tempChunk = chunk;
      let pos = 0;
      
      while (tempChunk > 0) {
        const digit = tempChunk % 10;
        if (digit > 0) {
          let digitStr = '';
          if (digit > 1 || pos === 0) {
            digitStr = ['', '일', '이', '삼', '사', '오', '육', '칠', '팔', '구'][digit];
          }
          if (pos > 0 && digit > 0) {
            digitStr += units[pos];
          }
          chunkStr = digitStr + chunkStr;
        }
        tempChunk = Math.floor(tempChunk / 10);
        pos++;
      }
      
      result = chunkStr + bigUnits[unitIndex] + result;
    }
    num = Math.floor(num / 10000);
    unitIndex++;
  }
  
  return result + '원';
}

/**
 * 가격을 포맷팅하여 "120,000원 (십이만원)" 형태로 반환
 * @param price 가격 (숫자)
 * @returns 포맷팅된 가격 문자열
 */
export function formatPriceWithKorean(price: number): string {
  const formatted = formatPrice(price);
  const korean = numberToKorean(price);
  return `${formatted}원 (${korean})`;
}

/**
 * 회당 가격과 회차를 받아서 총 가격을 계산
 * @param sessionPrice 회당 가격
 * @param sessionCount 총 회차
 * @returns 총 가격
 */
export function calculateTotalPrice(sessionPrice: number, sessionCount: number): number {
  return sessionPrice * sessionCount;
}

/**
 * 회당 가격과 회차 정보를 종합하여 표시용 문자열 생성
 * @param sessionPrice 회당 가격
 * @param sessionCount 총 회차
 * @returns 표시용 가격 정보 문자열
 */
export function formatClassPricing(sessionPrice: number, sessionCount: number): {
  sessionPriceText: string;
  totalPriceText: string;
  sessionPriceKorean: string;
  totalPriceKorean: string;
} {
  const totalPrice = calculateTotalPrice(sessionPrice, sessionCount);
  
  return {
    sessionPriceText: `회당 ${formatPrice(sessionPrice)}원`,
    totalPriceText: `총 ${sessionCount}회 ${formatPrice(totalPrice)}원`,
    sessionPriceKorean: numberToKorean(sessionPrice),
    totalPriceKorean: numberToKorean(totalPrice),
  };
}