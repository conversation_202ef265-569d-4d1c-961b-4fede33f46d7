/**
 * 클래스 라이프사이클 상태 계산 유틸리티
 *
 * @description
 * - 클래스 템플릿과 스케줄 그룹의 상태를 자동으로 계산
 * - 날짜 기반 상태 전환으로 일관성 보장
 * - docs/class-lifecycle/designs/domain-model.md 설계 기준
 */

import { ClassStatusText, OccurrenceStatusText } from '@/lib/db/schema';

// 타입 정의
export type ClassStatusType =
  (typeof ClassStatusText)[keyof typeof ClassStatusText];
export type GroupStatusType = ClassStatusType; // 그룹 상태는 클래스 상태와 동일
export type OccurrenceStatusType =
  (typeof OccurrenceStatusText)[keyof typeof OccurrenceStatusText];

export interface ClassTemplate {
  recruitment_start_date?: Date | string | null;
  recruitment_end_date?: Date | string | null;
  class_start_date?: Date | string | null;
  class_end_date?: Date | string | null;
  status?: string;
}

export interface ScheduleGroup {
  group_start_date?: Date | string | null;
  group_end_date?: Date | string | null;
  is_active?: boolean;
}

export interface ClassOccurrence {
  occurrence_date: Date | string;
  start_time?: string;
  status?: string;
}

/**
 * 날짜 문자열을 Date 객체로 변환
 */
function toDate(dateValue?: Date | string | null): Date | null {
  if (!dateValue) return null;
  if (dateValue instanceof Date) return dateValue;
  return new Date(dateValue);
}

/**
 * 클래스 템플릿 상태 계산
 *
 * @param template - 클래스 템플릿 정보
 * @param now - 현재 시간 (기본값: 현재 시간)
 * @returns ClassStatusType
 */
export function calculateClassStatus(
  template: ClassTemplate,
  now: Date = new Date()
): ClassStatusType {
  // 수동으로 취소된 경우
  if (template.status === ClassStatusText.CANCELLED) {
    return ClassStatusText.CANCELLED;
  }

  const recruitmentStart = toDate(template.recruitment_start_date);
  const recruitmentEnd = toDate(template.recruitment_end_date);
  const classEnd = toDate(template.class_end_date);

  // 필수 날짜가 없으면 upcoming
  if (!recruitmentStart || !recruitmentEnd || !classEnd) {
    return ClassStatusText.UPCOMING;
  }

  // 날짜 기반 상태 계산
  if (now < recruitmentStart) return ClassStatusText.UPCOMING;
  if (now <= recruitmentEnd) return ClassStatusText.RECRUITING;
  if (now <= classEnd) return ClassStatusText.ONGOING;
  return ClassStatusText.COMPLETED;
}

/**
 * 스케줄 그룹 상태 계산
 *
 * @param group - 스케줄 그룹 정보
 * @param template - 클래스 템플릿 정보 (모집 기간 참조)
 * @param now - 현재 시간 (기본값: 현재 시간)
 * @returns GroupStatusType
 */
export function calculateGroupStatus(
  group: ScheduleGroup,
  template: ClassTemplate,
  now: Date = new Date()
): GroupStatusType {
  // 비활성화된 그룹
  if (group.is_active === false) {
    return ClassStatusText.CANCELLED;
  }

  const recruitmentStart = toDate(template.recruitment_start_date);
  const recruitmentEnd = toDate(template.recruitment_end_date);
  const groupEnd = toDate(group.group_end_date);

  // 그룹별 수업 기간이 설정되지 않은 경우
  if (!group.group_start_date || !group.group_end_date) {
    return ClassStatusText.UPCOMING;
  }

  // 템플릿 모집 기간이 설정되지 않은 경우
  if (!recruitmentStart || !recruitmentEnd) {
    return ClassStatusText.UPCOMING;
  }

  // 템플릿의 모집 기간 + 그룹의 수업 기간을 조합하여 상태 계산
  if (now < recruitmentStart) return ClassStatusText.UPCOMING;
  if (now <= recruitmentEnd) return ClassStatusText.RECRUITING;
  if (groupEnd && now <= groupEnd) return ClassStatusText.ONGOING;
  return ClassStatusText.COMPLETED;
}

/**
 * 템플릿 상태를 모든 그룹 상태의 종합으로 계산
 *
 * @param template - 클래스 템플릿 정보
 * @param groups - 스케줄 그룹 배열
 * @param now - 현재 시간 (기본값: 현재 시간)
 * @returns ClassStatusType
 */
export function calculateTemplateStatusFromGroups(
  template: ClassTemplate,
  groups: ScheduleGroup[],
  now: Date = new Date()
): ClassStatusType {
  // 수동으로 취소된 경우
  if (template.status === ClassStatusText.CANCELLED) {
    return ClassStatusText.CANCELLED;
  }

  if (groups.length === 0) return ClassStatusText.UPCOMING;

  const groupStatuses = groups.map(g => calculateGroupStatus(g, template, now));

  // 하나라도 recruiting이면 recruiting
  if (groupStatuses.some(s => s === ClassStatusText.RECRUITING)) {
    return ClassStatusText.RECRUITING;
  }

  // 하나라도 ongoing이면 ongoing
  if (groupStatuses.some(s => s === ClassStatusText.ONGOING)) {
    return ClassStatusText.ONGOING;
  }

  // 모두 completed면 completed
  if (groupStatuses.every(s => s === ClassStatusText.COMPLETED)) {
    return ClassStatusText.COMPLETED;
  }

  // 그 외는 upcoming
  return ClassStatusText.UPCOMING;
}

/**
 * Occurrence 상태 계산
 *
 * @param occurrence - 수업 발생 정보
 * @param now - 현재 시간 (기본값: 현재 시간)
 * @returns OccurrenceStatusType
 */
export function calculateOccurrenceStatus(
  occurrence: ClassOccurrence,
  now: Date = new Date()
): OccurrenceStatusType {
  // 수동으로 취소된 경우
  if (occurrence.status === OccurrenceStatusText.CANCELLED) {
    return OccurrenceStatusText.CANCELLED;
  }

  const occurrenceDate = toDate(occurrence.occurrence_date);
  if (!occurrenceDate) return OccurrenceStatusText.SCHEDULED;

  // 수업 시작 시간이 있으면 더 정확한 계산
  if (occurrence.start_time) {
    const [hours, minutes] = occurrence.start_time.split(':').map(Number);
    const occurrenceDateTime = new Date(occurrenceDate);
    occurrenceDateTime.setHours(hours, minutes, 0, 0);

    // 수업 시작 1시간 후까지는 ongoing으로 간주
    const oneHourAfterStart = new Date(
      occurrenceDateTime.getTime() + 60 * 60 * 1000
    );

    if (now < occurrenceDateTime) return OccurrenceStatusText.SCHEDULED;
    if (now <= oneHourAfterStart) return OccurrenceStatusText.ONGOING;
    return OccurrenceStatusText.COMPLETED;
  }

  // 시작 시간이 없으면 날짜로만 판단
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const occurrenceDateStart = new Date(
    occurrenceDate.getFullYear(),
    occurrenceDate.getMonth(),
    occurrenceDate.getDate()
  );

  if (occurrenceDateStart > todayStart) return OccurrenceStatusText.SCHEDULED;
  if (occurrenceDateStart.getTime() === todayStart.getTime())
    return OccurrenceStatusText.ONGOING;
  return OccurrenceStatusText.COMPLETED;
}

/**
 * 템플릿 수업 기간을 그룹별 기간에서 자동 계산
 *
 * @param groups - 스케줄 그룹 배열
 * @returns 계산된 템플릿 수업 기간
 */
export function calculateTemplateClassDates(groups: ScheduleGroup[]): {
  class_start_date: Date | null;
  class_end_date: Date | null;
} {
  const validGroups = groups.filter(
    g => g.group_start_date && g.group_end_date && g.is_active !== false
  );

  if (validGroups.length === 0) {
    return {
      class_start_date: null,
      class_end_date: null,
    };
  }

  const startDates = validGroups
    .map(g => toDate(g.group_start_date))
    .filter(Boolean) as Date[];
  const endDates = validGroups
    .map(g => toDate(g.group_end_date))
    .filter(Boolean) as Date[];

  return {
    class_start_date:
      startDates.length > 0
        ? new Date(Math.min(...startDates.map(d => d.getTime())))
        : null,
    class_end_date:
      endDates.length > 0
        ? new Date(Math.max(...endDates.map(d => d.getTime())))
        : null,
  };
}

/**
 * 상태 변화 감지
 *
 * @param oldStatus - 이전 상태
 * @param newStatus - 새로운 상태
 * @returns 상태가 변경되었는지 여부
 */
export function hasStatusChanged(
  oldStatus: string,
  newStatus: string
): boolean {
  return oldStatus !== newStatus;
}

/**
 * 상태 변화에 따른 액션 필요 여부 확인
 *
 * @param oldStatus - 이전 상태
 * @param newStatus - 새로운 상태
 * @returns 알림 등의 액션이 필요한지 여부
 */
export function requiresAction(oldStatus: string, newStatus: string): boolean {
  // recruiting -> ongoing 또는 ongoing -> completed 전환 시 알림 필요
  const actionRequiredTransitions = [
    [ClassStatusText.UPCOMING, ClassStatusText.RECRUITING],
    [ClassStatusText.RECRUITING, ClassStatusText.ONGOING],
    [ClassStatusText.ONGOING, ClassStatusText.COMPLETED],
  ];

  return actionRequiredTransitions.some(
    ([from, to]) => oldStatus === from && newStatus === to
  );
}
