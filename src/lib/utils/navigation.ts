// 하단 네비게이션을 숨겨야 할 경로들
export const HIDDEN_NAV_ROUTES = [
  /^\/classes\/[^\/]+$/, // 클래스 상세페이지 (/classes/123)
  /^\/classes\/[^\/]+\/booking/, // 클래스 예약페이지 (/classes/123/booking)
  /^\/onboarding/, // 온보딩 페이지들
  /^\/login/, // 로그인 페이지
  /^\/instructor\/login/, // 강사 로그인
];

/**
 * 기존 회원용 버텀 네비게이션 표시 로직
 */
export function shouldHideBottomNav(pathname: string): boolean {
  const hideOnPaths = [
    '/login',
    '/onboarding',
    '/classes/',
    '/payment',
    '/partner',
  ];

  return hideOnPaths.some(path => pathname.includes(path));
}
