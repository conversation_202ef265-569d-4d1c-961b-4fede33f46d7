import { ApiResponse, SuccessResponseSchema } from '@/types/api';
import { z } from 'zod';
import { BASE_URL } from '../api/api.config';
import { HTTP_STATUS_TO_CODE } from '../constants/statusCode';

export function error({
  code,
  message,
  details,
}: {
  code: string;
  message: string;
  details?: unknown;
}): ApiResponse<never> {
  return {
    success: false,
    error: {
      code,
      message,
      details,
    },
  };
}

function getErrorCodeFromStatus(status: number): string {
  return HTTP_STATUS_TO_CODE[status] ?? 'UNKNOWN_ERROR';
}

export async function apiFetch<T>(
  endpoint: string,
  options: RequestInit = {},
  cache: RequestCache = 'no-store',
  dataSchema: z.ZodType<T>
) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      cache,
    });
    const json = await response.json();
    if (!response.ok) {
      throw new Error(JSON.stringify(json));
    }

    const parsed = SuccessResponseSchema(dataSchema).safeParse(json);

    if (!parsed.success) {
      console.log('parsed.error', parsed.error);
      throw new Error(JSON.stringify(parsed.error));
    }
    return parsed.data;
  } catch (err) {
    console.error('fetch error:', err);
    throw err;
  }
}

export function createApiFetch<T>(endpoint: string, dataSchema: z.ZodType<T>) {
  return async (
    options: RequestInit = {},
    cache: RequestCache = 'no-store'
  ) => {
    return apiFetch(endpoint, options, cache, dataSchema);
  };
}
