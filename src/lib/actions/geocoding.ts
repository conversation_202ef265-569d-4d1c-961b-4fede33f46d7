'use server';

import type { Coordinates } from '@/types/address';
import { configManager } from '../config/config-manager';

/**
 * Kakao 지도 API를 사용하여 주소를 좌표로 변환
 *
 * @param address 변환할 주소
 * @returns 좌표 정보 또는 null
 */
export async function geocodeAddress(
  address: string
): Promise<Coordinates | null> {
  try {
    // Kakao Maps API 키가 설정되어 있는지 확인
    const kakaoApiKey = configManager.getValue('kakao.apiKey');
    if (!kakaoApiKey) {
      console.warn('Kakao Maps API 키가 설정되지 않았습니다.');
      return null;
    }

    const response = await fetch(
      `https://dapi.kakao.com/v2/local/search/address.json?query=${encodeURIComponent(address)}`,
      {
        headers: {
          // Authorization: `KakaoAK ${kakaoApiKey}`,
          Authorization: `KakaoAK ${kakaoApiKey}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`지오코딩 API 호출 실패: ${response.status}`);
    }

    const data = await response.json();

    if (data.documents && data.documents.length > 0) {
      const result = data.documents[0];
      return {
        latitude: parseFloat(result.y),
        longitude: parseFloat(result.x),
      };
    }

    return null;
  } catch (error) {
    console.error('지오코딩 에러:', error);
    return null;
  }
}
