'use server';

import { getAllLevels, getAllNearestStations } from '@/lib/queries/classes';

const GENDER_OPTIONS = [
  { value: 'male', label: '남성' },
  { value: 'female', label: '여성' },
];

export async function getFilterOptions() {
  try {
    const [levels, nearestStations] = await Promise.all([
      getAllLevels(),
      getAllNearestStations(),
    ]);

    return {
      success: true,
      data: {
        levels,
        nearestStations,
        genders: GENDER_OPTIONS,
      },
    };
  } catch (error) {
    console.error('getFilterOptions::error', error);
    return {
      success: false,
      error: 'Failed to load filter options',
    };
  }
}
