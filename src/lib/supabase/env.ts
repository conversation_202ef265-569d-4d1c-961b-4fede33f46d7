function validateEnvironmentVariable(name: string, value: string | undefined, required: boolean = true): string {
  if (!value) {
    if (required) {
      throw new Error(`Environment variable ${name} is required but not set`);
    }
    return '';
  }
  return value;
}

export const env = {
  SUPABASE_URL: validateEnvironmentVariable(
    'NEXT_PUBLIC_SUPABASE_URL',
    process.env.NEXT_PUBLIC_SUPABASE_URL
  ),
  SUPABASE_ANON_KEY: validateEnvironmentVariable(
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  ),
  SUPABASE_SERVICE_ROLE_KEY: validateEnvironmentVariable(
    'SUPABASE_SERVICE_ROLE_KEY',
    process.env.SUPABASE_SERVICE_ROLE_KEY
  ),
  DATABASE_URL: validateEnvironmentVariable(
    'DATABASE_URL',
    process.env.DATABASE_URL
  ),
  PORTONE_USER_CODE: validateEnvironmentVariable(
    'NEXT_PUBLIC_PORTONE_USER_CODE',
    process.env.NEXT_PUBLIC_PORTONE_USER_CODE,
    false
  ),
  PORTONE_API_KEY: validateEnvironmentVariable(
    'PORTONE_API_KEY',
    process.env.PORTONE_API_KEY,
    false
  ),
  PORTONE_API_SECRET: validateEnvironmentVariable(
    'PORTONE_API_SECRET',
    process.env.PORTONE_API_SECRET,
    false
  ),
  BASE_URL: validateEnvironmentVariable(
    'NEXT_PUBLIC_BASE_URL',
    process.env.NEXT_PUBLIC_BASE_URL
  ),
  NODE_ENV: process.env.NODE_ENV || 'development',
} as const;

export type Env = typeof env;

// Runtime environment check
export function checkEnvironment() {
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'DATABASE_URL',
    'NEXT_PUBLIC_BASE_URL',
  ];

  const missingVars = requiredEnvVars.filter(
    (varName) => !process.env[varName]
  );

  if (missingVars.length > 0) {
    console.error('Missing required environment variables:');
    missingVars.forEach((varName) => {
      console.error(`- ${varName}`);
    });

    if (process.env.NODE_ENV === 'production') {
      throw new Error('Missing required environment variables in production');
    } else {
      console.warn('⚠️  Some environment variables are missing. Please check your .env.local file.');
    }
  }
}

// Development helper
export function isDevelopment() {
  return env.NODE_ENV === 'development';
}

export function isProduction() {
  return env.NODE_ENV === 'production';
}
