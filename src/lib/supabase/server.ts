import { createServerClient } from "@supabase/ssr";
import { parse, serialize } from "cookie"; // cookie 라이브러리 사용

export const createClient = (request: Request) => {
  const response = new Response();

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        // [신규] getAll 메서드: 모든 쿠키를 이름과 값의 객체 배열로 반환합니다.
        getAll() {
          const cookies = parse(request.headers.get("Cookie") ?? "");
          return Object.entries(cookies).map(([name, value]) => ({ 
            name, 
            value: value || "" 
          }));
        },
        // [신규] setAll 메서드: 설정할 쿠키 객체들의 배열을 받아 처리합니다.
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            response.headers.append("Set-Cookie", serialize(name, value, options));
          });
        },
      },
    }
  );

  return { supabase, response };
};

// 헬퍼 함수들은 이전과 동일하게 유지됩니다.
export const getSupabaseWithUser = async (request: Request) => {
  const { supabase, response } = createClient(request);

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  return { supabase, response, user, error };
};

export const getSession = async (request: Request) => {
  const { supabase, response } = createClient(request);

  const {
    data: { session },
    error,
  } = await supabase.auth.getSession();

  return { supabase, response, session, error };
};