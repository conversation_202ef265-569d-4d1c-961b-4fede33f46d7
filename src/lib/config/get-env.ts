const loadedEnv = {
  NEXT_PUBLIC_NAVER_MAP_API_KEY: process.env.NEXT_PUBLIC_NAVER_MAP_API_KEY,
  NEXT_PUBLIC_KAKAO_REST_API_KEY: process.env.NEXT_PUBLIC_KAKAO_REST_API_KEY,
  KAKAO_CLIENT_SECRET: process.env.KAKAO_CLIENT_SECRET,
  NEXT_PUBLIC_PORTONE_USER_CODE: process.env.NEXT_PUBLIC_PORTONE_USER_CODE,
  PORTONE_API_KEY: process.env.PORTONE_API_KEY,
  PORTONE_API_SECRET: process.env.PORTONE_API_SECRET,
  NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
  NODE_ENV: process.env.NODE_ENV,
  ADMIN_EMAILS: process.env.ADMIN_EMAILS,
  NEXT_PUBLIC_NAVER_MAP_CLIENT_ID: process.env.NEXT_PUBLIC_NAVER_MAP_CLIENT_ID,
  NEXT_PUBLIC_TOSS_CLIENT_KEY: process.env.NEXT_PUBLIC_TOSS_CLIENT_KEY,
  TOSS_SECRET_KEY: process.env.TOSS_SECRET_KEY,
};

type EnvKey = keyof typeof loadedEnv;

export default function getEnv(key: EnvKey) {
  if (!loadedEnv[key]) {
    console.error(`Environment variable ${key} is not set`);
    return '';
  }

  return loadedEnv[key];
}
