interface RouteTitleConfig {
  title: string;
  exact?: boolean;
}

type RouteConfigEntry = RouteTitleConfig | ((pathname: string) => RouteTitleConfig);

export const ROUTE_TITLES: Record<string, RouteConfigEntry> = {
  '/': { title: 'ShallWe', exact: true },
  '/profile': { title: '마이페이지', exact: true },
  '/classes/': (pathname: string) => {
    if (pathname.includes('/booking')) {
      return { title: '예약하기' };
    }
    return { title: 'ShallWe' };
  },
  '/instructor': { title: '강사 페이지' },
  '/onboarding': { title: '온보딩' },
  '/payment': { title: '결제' },
} as const;

export function getRouteTitle(pathname: string): string {
  // 정확한 매치 우선 확인
  const exactMatch = ROUTE_TITLES[pathname];
  if (exactMatch) {
    if (typeof exactMatch === 'function') {
      return exactMatch(pathname).title;
    }
    return exactMatch.title;
  }

  // 패턴 매치 확인
  for (const [pattern, config] of Object.entries(ROUTE_TITLES)) {
    if (pattern.endsWith('/') && pathname.startsWith(pattern)) {
      if (typeof config === 'function') {
        return config(pathname).title;
      }
      return config.title;
    }
  }

  // 기본값
  return 'ShallWe';
}