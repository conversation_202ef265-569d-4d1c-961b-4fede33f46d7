import { InstructorRepository } from '@/lib/repositories/instructor.repository';
import { type Instructor } from '@/lib/db/schema';
import {
  type CreateInstructorInput,
  type UpdateInstructorInput,
} from '@/lib/schemas/instructor';

export class InstructorService {
  private instructorRepository: InstructorRepository;

  constructor() {
    this.instructorRepository = new InstructorRepository();
  }


  /**
   * 강사 생성
   */
  async createInstructor(
    data: CreateInstructorInput,
    currentPartnerId: string
  ): Promise<Instructor> {
    try {
      // 강사 생성
      const instructor = await this.instructorRepository.create({
        ...data,
        partnerId: currentPartnerId,
      });

      return instructor;
    } catch (error) {
      console.error('Error creating instructor:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('강사 생성에 실패했습니다');
    }
  }

  /**
   * ID로 강사 조회 (권한 검증 포함)
   */
  async findInstructorById(
    instructorId: string,
    currentPartnerId: string
  ): Promise<Instructor | null> {
    try {
      // 강사 조회 및 파트너 소속 확인
      const instructor = await this.instructorRepository.findById(instructorId);
      
      if (!instructor || instructor.partnerId !== currentPartnerId) {
        return null;
      }

      return instructor;
    } catch (error) {
      console.error('Error fetching instructor by id:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('강사 조회에 실패했습니다');
    }
  }

  /**
   * 강사 정보 수정 (권한 검증 포함)
   */
  async updateInstructor(
    instructorId: string,
    currentPartnerId: string,
    data: UpdateInstructorInput
  ): Promise<Instructor | null> {
    try {
      // 강사가 해당 파트너 소속인지 확인
      const instructorExists = await this.instructorRepository.checkExists(
        instructorId,
        currentPartnerId
      );

      if (!instructorExists) {
        return null;
      }

      // 강사 정보 수정
      const updated = await this.instructorRepository.update(instructorId, data);

      return updated;
    } catch (error) {
      console.error('Error updating instructor:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('강사 정보 수정에 실패했습니다');
    }
  }

  /**
   * 파트너별 강사 목록 조회
   */
  async findInstructorsByPartner(
    currentPartnerId: string
  ): Promise<Instructor[]> {
    try {
      // 파트너별 강사 목록 조회
      const partnerInstructors = await this.instructorRepository.findByPartner(currentPartnerId);

      return partnerInstructors;
    } catch (error) {
      console.error('Error fetching instructors by partner:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('파트너 강사 목록 조회에 실패했습니다');
    }
  }

  /**
   * 강사 삭제 (권한 검증 포함, 소프트 삭제)
   */
  async deleteInstructor(
    instructorId: string,
    currentPartnerId: string
  ): Promise<boolean> {
    try {
      // 강사가 해당 파트너 소속인지 확인
      const instructorExists = await this.instructorRepository.checkExists(
        instructorId,
        currentPartnerId
      );

      if (!instructorExists) {
        return false;
      }

      // 강사 소프트 삭제
      const success = await this.instructorRepository.softDelete(instructorId);

      return success;
    } catch (error) {
      console.error('Error deleting instructor:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('강사 삭제에 실패했습니다');
    }
  }
}