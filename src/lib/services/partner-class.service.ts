import { db } from '@/lib/db';
import { studios, instructors } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { 
  partnerClassQueries,
  type InsertClass
} from '@/lib/queries/partner-class.queries';
import { 
  scheduleQueries,
  scheduleGroupQueries,
  type InsertClassSchedule,
  type InsertClassScheduleGroup
} from '@/lib/queries/schedule.queries';
import type { ClassScheduleGroup, NewClassSchedule } from '@/lib/db/schema';
import type { 
  CreateClassInput, 
  UpdateClassInput, 
  ClassListFilter 
} from '@/lib/validations/partner-class.validation';
import type { AuthenticatedPartner } from '@/lib/auth/partner.server';

export interface ClassWithScheduleGroups {
  id: string;
  partnerId: string;
  studioId: string;
  instructorId: string;
  title: string;
  description: string;
  category: string;
  level: string;
  target: string;
  maxParticipants: number;
  pricePerSession: number;
  sessionDurationMinutes: number;
  durationWeeks: number;
  sessionsPerWeek: number;
  images: any;
  status: string;
  visible: boolean | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  scheduleGroups: Array<{
    id: number;
    status: string;
    schedules: Array<{
      id: number;
      dayOfWeek: number;
      startTime: string;
      endTime: string;
    }>;
  }>;
}

export interface ClassDetail extends ClassWithScheduleGroups {
  studio: {
    id: string;
    name: string;
    address: string;
  };
  instructor: {
    id: string;
    name: string;
    profileImages: any;
  };
}

export interface PaginatedClassList {
  data: Array<ClassWithScheduleGroups & {
    studioName: string;
    instructorName: string;
    enrollmentCount: number;
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * 파트너 클래스 관련 비즈니스 로직 처리 (Drizzle Style)
 */
export const partnerClassService = {

  /**
   * 클래스 응답 포맷팅 (스케줄 그룹 구조)
   */
  formatClassResponse(classData: any, scheduleGroups: any[], allSchedules: any[]): ClassWithScheduleGroups {
    // 스케줄을 그룹 ID별로 정리
    const schedulesMap = allSchedules.reduce((acc, schedule) => {
      if (!acc[schedule.schedule_group_id]) {
        acc[schedule.schedule_group_id] = [];
      }
      acc[schedule.schedule_group_id].push(schedule);
      return acc;
    }, {} as Record<number, any[]>);

    return {
      id: classData.id,
      partnerId: classData.partner_id,
      studioId: classData.studio_id,
      instructorId: classData.instructor_id,
      title: classData.title,
      description: classData.description,
      category: classData.category,
      level: classData.level,
      target: classData.target,
      maxParticipants: classData.max_participants,
      pricePerSession: classData.price_per_session,
      sessionDurationMinutes: classData.session_duration_minutes,
      durationWeeks: classData.duration_weeks,
      sessionsPerWeek: classData.sessions_per_week,
      images: classData.images,
      status: classData.status,
      visible: classData.visible,
      createdAt: classData.created_at,
      updatedAt: classData.updated_at,
      scheduleGroups: scheduleGroups.map(group => ({
        id: group.id,
        status: group.status,
        schedules: (schedulesMap[group.id] || []).map(s => ({
          id: s.id,
          dayOfWeek: s.day_of_week,
          startTime: s.start_time,
          endTime: s.end_time,
        }))
      }))
    };
  },

  /**
   * 새로운 클래스 생성
   */
  async createClass(
    partner: AuthenticatedPartner, 
    data: CreateClassInput
  ): Promise<ClassWithScheduleGroups> {
    // 비즈니스 규칙 검증
    await partnerClassService.validateStudioOwnership(partner.id, data.studioId);
    
    // 트랜잭션으로 클래스와 스케줄 그룹 생성 (비즈니스 로직)
    return await db.transaction(async (tx) => {
      // 클래스 생성
      const classData: InsertClass = {
        partner_id: partner.id,
        studio_id: data.studioId,
        instructor_id: data.instructorId,
        title: data.title,
        description: data.description,
        category: data.category,
        level: data.level,
        target: data.target,
        max_participants: data.maxParticipants,
        price_per_session: data.pricePerSession,
        session_duration_minutes: data.sessionDurationMinutes,
        duration_weeks: data.durationWeeks,
        sessions_per_week: data.sessionsPerWeek,
        images: data.images || [],
      };
      
      const [newClass] = await partnerClassQueries.create(tx, classData);
      
      // 스케줄 그룹들 생성 및 각 그룹의 스케줄들 생성
      const allScheduleGroups: ClassScheduleGroup[] = [];
      const allSchedules: NewClassSchedule[] = [];
      
      for (const scheduleGroup of data.scheduleGroups) {
        // 스케줄 그룹 생성
        const groupData: InsertClassScheduleGroup = {
          class_id: newClass.id,
        };
        
        const [newGroup] = await scheduleGroupQueries.create(tx, groupData);
        allScheduleGroups.push(newGroup);
        
        // 해당 그룹의 스케줄들 생성
        const scheduleData: InsertClassSchedule[] = scheduleGroup.schedules.map(schedule => ({
          schedule_group_id: newGroup.id,
          day_of_week: schedule.dayOfWeek, // 이제 영어 약어 문자열
          start_time: schedule.startTime,
          end_time: schedule.endTime,
        }));
        
        const groupSchedules = await scheduleQueries.createMany(tx, scheduleData);
        allSchedules.push(...groupSchedules);
      }
      
      return partnerClassService.formatClassResponse(newClass, allScheduleGroups, allSchedules);
    });
  },

  /**
   * 파트너의 클래스 목록 조회
   */
  async getClassList(
    partner: AuthenticatedPartner, 
    filters: ClassListFilter
  ): Promise<PaginatedClassList> {
    // 스튜디오 필터가 있으면 소유권 확인
    if (filters.studioId) {
      await partnerClassService.validateStudioOwnership(partner.id, filters.studioId);
    }
    
    // 클래스 목록 조회
    const queries = partnerClassQueries.findByPartner(partner.id, filters);
    const [countResult, classes] = await Promise.all([
      queries.count(),
      queries.list()
    ]);
    
    const total = countResult[0].value;
    
    // 각 클래스의 스케줄 그룹 정보 조회 (N+1 방지)
    const classIds = classes.map(c => c.id);
    const allScheduleGroups = await Promise.all(
      classIds.map(classId => scheduleGroupQueries.findByClassId(classId))
    );
    
    // 스케줄 그룹을 classId별로 그룹화
    const scheduleGroupsMap = allScheduleGroups.reduce((acc, groups, index) => {
      const classId = classIds[index];
      acc[classId] = groups;
      return acc;
    }, {} as Record<string, any[]>);
    
    // 모든 스케줄 그룹 ID 수집
    const allGroupIds = allScheduleGroups.flat().map(group => group.id);
    const allSchedules = await scheduleQueries.findByGroupIds(allGroupIds);
    
    // 스케줄을 groupId별로 그룹화
    const schedulesMap = allSchedules.reduce((acc, schedule) => {
      if (!acc[schedule.schedule_group_id]) {
        acc[schedule.schedule_group_id] = [];
      }
      acc[schedule.schedule_group_id].push(schedule);
      return acc;
    }, {} as Record<number, any[]>);
    
    // 응답 구성 (스케줄 그룹 구조 사용)
    const classesWithScheduleGroups = classes.map(cls => ({
      id: cls.id,
      partnerId: cls.partnerId,
      studioId: cls.studioId,
      instructorId: cls.instructorId,
      title: cls.title,
      description: cls.description,
      category: cls.category,
      level: cls.level,
      target: cls.target,
      maxParticipants: cls.maxParticipants,
      pricePerSession: cls.pricePerSession,
      sessionDurationMinutes: cls.sessionDurationMinutes,
      durationWeeks: cls.durationWeeks,
      sessionsPerWeek: cls.sessionsPerWeek,
      images: cls.images,
      status: cls.status,
      visible: cls.visible,
      createdAt: cls.createdAt,
      updatedAt: cls.updatedAt,
      studioName: cls.studioName || '',
      instructorName: cls.instructorName || '',
      scheduleGroups: (scheduleGroupsMap[cls.id] || []).map(group => ({
        id: group.id,
        status: group.status,
        schedules: (schedulesMap[group.id] || []).map(s => ({
          id: s.id,
          dayOfWeek: s.day_of_week,
          startTime: s.start_time,
          endTime: s.end_time,
        }))
      })),
      enrollmentCount: 0 // TODO: 실제 수강신청 카운트 구현
    }));
    
    return {
      data: classesWithScheduleGroups,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit)
      }
    };
  },

  /**
   * 클래스 상세 조회
   */
  async getClassDetail(
    partner: AuthenticatedPartner, 
    classId: string
  ): Promise<ClassDetail | null> {
    // 클래스 조회 및 권한 확인
    const classResult = await partnerClassQueries.findById(classId);
    const classData = classResult[0];
    
    if (!classData || classData.partner_id !== partner.id) {
      return null;
    }
    
    // 관련 정보 병렬 조회
    const [scheduleGroups, studio, instructor] = await Promise.all([
      scheduleGroupQueries.findByClassId(classId),
      partnerClassService.getStudioById(classData.studio_id),
      partnerClassService.getInstructorById(classData.instructor_id)
    ]);
    
    // 각 스케줄 그룹의 스케줄 조회
    const groupIds = scheduleGroups.map(g => g.id);
    const allSchedules = await scheduleQueries.findByGroupIds(groupIds);
    
    // 상세 정보 구성 (formatClassResponse 사용)
    const formattedClass = partnerClassService.formatClassResponse(classData, scheduleGroups, allSchedules);
    
    return {
      ...formattedClass,
      studio: {
        id: studio.id,
        name: studio.name,
        address: studio.address
      },
      instructor: {
        id: instructor.id,
        name: instructor.name,
        profileImages: instructor.profileImages
      }
    };
  },

  /**
   * 클래스 정보 수정
   */
  async updateClass(
    partner: AuthenticatedPartner, 
    classId: string, 
    data: UpdateClassInput
  ): Promise<ClassWithScheduleGroups> {
    // 권한 확인 (비즈니스 규칙)
    const classResult = await partnerClassQueries.findById(classId);
    const existingClass = classResult[0];
    
    if (!existingClass || existingClass.partner_id !== partner.id) {
      throw new Error('Class not found or access denied');
    }
    
    // 트랜잭션으로 클래스와 스케줄 그룹 업데이트 (비즈니스 로직)
    return await db.transaction(async (tx) => {
      // 클래스 정보 업데이트를 위한 데이터 매핑
      const { scheduleGroups: newScheduleGroups, ...classUpdateData } = data;
      const updateData: Partial<InsertClass> = {};
      
      // 필드별 매핑 (null/undefined 처리)
      if (classUpdateData.title) updateData.title = classUpdateData.title;
      if (classUpdateData.description) updateData.description = classUpdateData.description;
      if (classUpdateData.category) updateData.category = classUpdateData.category;
      if (classUpdateData.level) updateData.level = classUpdateData.level;
      if (classUpdateData.target) updateData.target = classUpdateData.target;
      if (classUpdateData.maxParticipants) updateData.max_participants = classUpdateData.maxParticipants;
      if (classUpdateData.pricePerSession) updateData.price_per_session = classUpdateData.pricePerSession;
      if (classUpdateData.sessionDurationMinutes) updateData.session_duration_minutes = classUpdateData.sessionDurationMinutes;
      if (classUpdateData.durationWeeks) updateData.duration_weeks = classUpdateData.durationWeeks;
      if (classUpdateData.sessionsPerWeek) updateData.sessions_per_week = classUpdateData.sessionsPerWeek;
      if (classUpdateData.images !== undefined) updateData.images = classUpdateData.images;
      if (classUpdateData.visible !== undefined) updateData.visible = classUpdateData.visible;
      if (classUpdateData.status) updateData.status = classUpdateData.status;
      
      const [updatedClass] = await partnerClassQueries.update(tx, classId, updateData);
      
      // 스케줄 그룹 업데이트 (있는 경우) - 비즈니스 결정: 전체 교체
      let scheduleGroups = await scheduleGroupQueries.findByClassId(classId, tx);
      let allSchedules: NewClassSchedule[] = [];
      
      if (newScheduleGroups) {
        // 기존 스케줄 그룹 전체 삭제
        await scheduleGroupQueries.deleteByClassId(tx, classId);
        
        // 새 스케줄 그룹들 생성
        scheduleGroups = [] as ClassScheduleGroup[];
        for (const scheduleGroup of newScheduleGroups) {
          // 스케줄 그룹 생성
          const groupData: InsertClassScheduleGroup = {
            class_id: classId,
          };
          
          const [newGroup] = await scheduleGroupQueries.create(tx, groupData);
          scheduleGroups.push(newGroup);
          
          // 해당 그룹의 스케줄들 생성
          const scheduleData: InsertClassSchedule[] = scheduleGroup.schedules.map(schedule => ({
            schedule_group_id: newGroup.id,
            day_of_week: schedule.dayOfWeek, // 이제 영어 약어 문자열
            start_time: schedule.startTime,
            end_time: schedule.endTime,
          }));
          
          const groupSchedules = await scheduleQueries.createMany(tx, scheduleData);
          allSchedules.push(...groupSchedules);
        }
      } else {
        // 기존 스케줄 그룹의 스케줄들 조회
        const groupIds = scheduleGroups.map(g => g.id);
        allSchedules = await scheduleQueries.findByGroupIds(groupIds);
      }
      
      return partnerClassService.formatClassResponse(updatedClass, scheduleGroups, allSchedules);
    });
  },

  /**
   * 클래스 삭제 (soft delete)
   */
  async deleteClass(partner: AuthenticatedPartner, classId: string): Promise<void> {
    // 권한 확인 (비즈니스 규칙)
    const classResult = await partnerClassQueries.findById(classId);
    const existingClass = classResult[0];
    
    if (!existingClass || existingClass.partner_id !== partner.id) {
      throw new Error('Class not found or access denied');
    }
    
    // 비즈니스 규칙 검증: 활성 수강신청 확인
    const hasEnrollments = await partnerClassQueries.hasActiveEnrollments(classId);
    if (hasEnrollments) {
      throw new Error('Cannot delete class with active enrollments');
    }
    
    // 실제 삭제 실행
    await partnerClassQueries.softDelete(classId);
  },

  // === 헬퍼 메서드들 ===

  /**
   * 스튜디오 소유권 검증
   */
  async validateStudioOwnership(partnerId: string, studioId: string): Promise<void> {
    const studioIds = await partnerClassQueries.getPartnerStudioIds(partnerId);
    const ownedStudioIds = studioIds.map(s => s.id);
    console.log('partnerId: ', partnerId);
    console.log('studioIds: ', studioIds);
    console.log('ownedStudioIds: ', ownedStudioIds);
    if (!ownedStudioIds.includes(studioId)) {
      throw new Error('Studio not found or access denied');
    }
  },


  /**
   * 스튜디오 정보 조회
   */
  async getStudioById(studioId: string) {
    const [studio] = await db
      .select()
      .from(studios)
      .where(eq(studios.id, studioId))
      .limit(1);
    
    return studio;
  },

  /**
   * 강사 정보 조회
   */
  async getInstructorById(instructorId: string) {
    const [instructor] = await db
      .select()
      .from(instructors)
      .where(eq(instructors.id, instructorId))
      .limit(1);
    
    return instructor;
  }
};