import type { Coordinates } from '@/types/address';

/**
 * Kakao 지도 API를 사용하여 주소를 좌표로 변환
 *
 * @param address 변환할 주소
 * @returns 좌표 정보 또는 null
 */
export async function geocodeAddress(
  address: string
): Promise<Coordinates | null> {
  try {
    // Kakao Maps API 키가 설정되어 있는지 확인
    const kakaoApiKey = process.env.NEXT_PUBLIC_KAKAO_MAP_API_KEY;
    if (!kakaoApiKey) {
      console.warn('Kakao Maps API 키가 설정되지 않았습니다.');
      return null;
    }

    const response = await fetch(
      `https://dapi.kakao.com/v2/local/search/address.json?query=${encodeURIComponent(address)}`,
      {
        headers: {
          // Authorization: `KakaoAK ${kakaoApiKey}`,
          Authorization: `KakaoAK 968db8b6616e177c9ce0bda0a0b8e022`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`지오코딩 API 호출 실패: ${response.status}`);
    }

    const data = await response.json();

    if (data.documents && data.documents.length > 0) {
      const result = data.documents[0];
      return {
        latitude: parseFloat(result.y),
        longitude: parseFloat(result.x),
      };
    }

    return null;
  } catch (error) {
    console.error('지오코딩 에러:', error);
    return null;
  }
}

/**
 * 클라이언트 사이드에서 Kakao Maps SDK를 사용한 지오코딩
 * (브라우저 환경에서만 동작)
 */
export function geocodeAddressWithSDK(
  address: string
): Promise<Coordinates | null> {
  return new Promise(resolve => {
    // Kakao Maps SDK가 로드되어 있는지 확인
    if (typeof window === 'undefined' || !window.kakao?.maps?.services) {
      console.warn('Kakao Maps SDK가 로드되지 않았습니다.');
      resolve(null);
      return;
    }

    const geocoder = new window.kakao.maps.services.Geocoder();

    geocoder.addressSearch(address, (result: any, status: any) => {
      if (
        status === window.kakao?.maps?.services?.Status.OK &&
        result.length > 0
      ) {
        resolve({
          latitude: parseFloat(result[0].y),
          longitude: parseFloat(result[0].x),
        });
      } else {
        console.warn('주소 검색 결과가 없습니다:', address);
        resolve(null);
      }
    });
  });
}

// Global type declarations for Kakao Maps SDK
declare global {
  interface Window {
    kakao?: {
      maps?: {
        services?: {
          Geocoder: new () => {
            addressSearch: (
              address: string,
              callback: (result: any[], status: any) => void
            ) => void;
          };
          Status: {
            OK: any;
            ZERO_RESULT: any;
            ERROR: any;
          };
        };
      };
    };
  }
}
