import { NextRequest } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';

/**
 * 서버용 파트너 인증 유틸리티
 * API 라우트와 서버 컴포넌트에서 사용
 */

export interface AuthenticatedPartner {
  id: string;
  userId: string;
  email: string;
  contactName: string;
  contactPhone: string;
  status: 'ACTIVE' | 'PENDING' | 'SUSPENDED' | 'REJECTED';
  createdAt: Date;
  updatedAt: Date;
}

export interface PartnerAuthResult {
  success: boolean;
  partner?: AuthenticatedPartner;
  error?: string;
  statusCode?: number;
}

/**
 * NextRequest에서 Supabase 서버 클라이언트 생성
 */
function createSupabaseFromRequest(request: NextRequest) {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // NextRequest는 읽기 전용이므로 설정 작업은 생략
        },
        remove(name: string, options: CookieOptions) {
          // NextRequest는 읽기 전용이므로 제거 작업은 생략
        },
      },
    }
  );
}

/**
 * cookies()를 사용한 Supabase 서버 클라이언트 생성 (서버 컴포넌트용)
 */
function createSupabaseFromCookies() {
  const cookieStore = cookies();
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        async get(name: string) {
          return (await cookieStore).get(name)?.value;
        },
        async set(name: string, value: string, options: CookieOptions) {
          try {
            (await cookieStore).set({ name, value, ...options });
          } catch {
            // 서버 컴포넌트에서는 쿠키 설정이 제한될 수 있음
          }
        },
        async remove(name: string, options: CookieOptions) {
          try {
            (await cookieStore).set({ name, value: '', ...options });
          } catch {
            // 서버 컴포넌트에서는 쿠키 제거가 제한될 수 있음
          }
        },
      },
    }
  );
}

/**
 * NextRequest에서 파트너 정보 추출
 * API 라우트에서 사용
 */
export async function getPartnerFromRequest(request: NextRequest): Promise<PartnerAuthResult> {
  try {
    const supabase = createSupabaseFromRequest(request);

    // 1. 사용자 인증 확인
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: '인증이 필요합니다.',
        statusCode: 401,
      };
    }

    // 2. 파트너 정보 조회
    const { data: partner, error: partnerError } = await supabase
      .from('partners')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (partnerError) {
      console.error('파트너 정보 조회 오류:', partnerError);
      
      if (partnerError.code === 'PGRST116') {
        return {
          success: false,
          error: '파트너 계정을 찾을 수 없습니다.',
          statusCode: 403,
        };
      }

      return {
        success: false,
        error: '파트너 정보 조회 중 오류가 발생했습니다.',
        statusCode: 500,
      };
    }

    if (!partner) {
      return {
        success: false,
        error: '파트너 계정을 찾을 수 없습니다.',
        statusCode: 403,
      };
    }

    // 3. 파트너 상태 확인
    if (partner.status !== 'ACTIVE') {
      return {
        success: false,
        error: `파트너 계정이 ${partner.status} 상태입니다. 관리자에게 문의하세요.`,
        statusCode: 403,
      };
    }

    // 4. 성공 응답
    return {
      success: true,
      partner: {
        id: partner.id,
        userId: partner.user_id,
        email: partner.email,
        contactName: partner.contact_name,
        contactPhone: partner.contact_phone,
        status: partner.status,
        createdAt: partner.created_at,
        updatedAt: partner.updated_at,
      },
    };

  } catch (error) {
    console.error('파트너 인증 오류:', error);
    return {
      success: false,
      error: '서버 오류가 발생했습니다.',
      statusCode: 500,
    };
  }
}

/**
 * 서버 컴포넌트에서 파트너 정보 추출
 */
export async function getPartnerFromCookies(): Promise<PartnerAuthResult> {
  try {
    const supabase = createSupabaseFromCookies();

    // 1. 사용자 인증 확인
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: '인증이 필요합니다.',
        statusCode: 401,
      };
    }

    // 2. 파트너 정보 조회
    const { data: partner, error: partnerError } = await supabase
      .from('partners')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (partnerError) {
      console.error('파트너 정보 조회 오류:', partnerError);
      
      if (partnerError.code === 'PGRST116') {
        return {
          success: false,
          error: '파트너 계정을 찾을 수 없습니다.',
          statusCode: 403,
        };
      }

      return {
        success: false,
        error: '파트너 정보 조회 중 오류가 발생했습니다.',
        statusCode: 500,
      };
    }

    if (!partner) {
      return {
        success: false,
        error: '파트너 계정을 찾을 수 없습니다.',
        statusCode: 403,
      };
    }

    // 3. 파트너 상태 확인
    if (partner.status !== 'ACTIVE') {
      return {
        success: false,
        error: `파트너 계정이 ${partner.status} 상태입니다. 관리자에게 문의하세요.`,
        statusCode: 403,
      };
    }

    // 4. 성공 응답
    return {
      success: true,
      partner: {
        id: partner.id,
        userId: partner.user_id,
        email: partner.email,
        contactName: partner.contact_name,
        contactPhone: partner.contact_phone,
        status: partner.status,
        createdAt: partner.created_at,
        updatedAt: partner.updated_at,
      },
    };

  } catch (error) {
    console.error('파트너 인증 오류:', error);
    return {
      success: false,
      error: '서버 오류가 발생했습니다.',
      statusCode: 500,
    };
  }
}

/**
 * 파트너 인증 필수 체크
 * API 라우트에서 사용하며, 인증 실패 시 NextResponse 반환
 */
export async function requirePartnerAuth(request: NextRequest): Promise<{
  partner?: AuthenticatedPartner;
  errorResponse?: Response;
}> {
  const authResult = await getPartnerFromRequest(request);
  
  if (!authResult.success) {
    const { error, statusCode } = authResult;
    return {
      errorResponse: new Response(
        JSON.stringify({ 
          message: error,
          error: error 
        }),
        {
          status: statusCode || 500,
          headers: { 'Content-Type': 'application/json' },
        }
      ),
    };
  }

  return {
    partner: authResult.partner!,
  };
}

/**
 * 파트너 ID만 빠르게 추출하는 헬퍼 함수
 */
export async function getPartnerIdFromRequest(request: NextRequest): Promise<string | null> {
  const authResult = await getPartnerFromRequest(request);
  return authResult.success ? authResult.partner!.id : null;
}

/**
 * 서버 컴포넌트용 파트너 ID 추출
 */
export async function getPartnerIdFromCookies(): Promise<string | null> {
  const authResult = await getPartnerFromCookies();
  return authResult.success ? authResult.partner!.id : null;
}