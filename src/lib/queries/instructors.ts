import { eq } from 'drizzle-orm';
import { db } from '../db';
import { instructors } from '../db/schema';

/**
 * 강사 전문분야 조회 (JSONB 구조)
 */
export async function findInstructorSpecialties(instructorId: string) {
  const instructor = await db
    .select({
      specialties: instructors.specialties,
    })
    .from(instructors)
    .where(eq(instructors.id, instructorId))
    .limit(1);

  if (!instructor[0]?.specialties) {
    return [];
  }

  // JSONB 배열을 원하는 형태로 변환
  return (instructor[0].specialties as any[]).map((specialty) => ({
    specialty: specialty.type,
    experienceYears: specialty.years,
  }));
}

/**
 * 강사 자격증 조회 (JSONB 구조)
 */
export async function findInstructorCertificates(instructorId: string) {
  const instructor = await db
    .select({
      certificates: instructors.certificates,
    })
    .from(instructors)
    .where(eq(instructors.id, instructorId))
    .limit(1);

  if (!instructor[0]?.certificates) {
    return [];
  }

  // JSONB 배열을 원하는 형태로 변환
  return (instructor[0].certificates as any[]).map((certificate) => ({
    certificateName: certificate.name,
    issuingOrganization: certificate.issuing_organization,
  }));
}
