import { db } from '@/lib/db';
import { class_schedules, class_schedule_groups, ClassScheduleStatusText } from '@/lib/db/schema';
import { eq, inArray, asc } from 'drizzle-orm';

export type InsertClassSchedule = typeof class_schedules.$inferInsert;
export type InsertClassScheduleGroup = typeof class_schedule_groups.$inferInsert;

/**
 * 클래스 스케줄 그룹 관련 순수 Drizzle 쿼리들
 */
export const scheduleGroupQueries = {
  /**
   * 스케줄 그룹 생성
   */
  create: (tx: any, group: InsertClassScheduleGroup) => {
    return tx.insert(class_schedule_groups).values(group).returning();
  },

  /**
   * 여러 스케줄 그룹 동시 생성
   */
  createMany: (tx: any, groups: InsertClassScheduleGroup[]) => {
    if (groups.length === 0) return Promise.resolve([]);
    return tx.insert(class_schedule_groups).values(groups).returning();
  },

  /**
   * 클래스 ID로 스케줄 그룹 목록 조회
   */
  findByClassId: (classId: string, tx?: any) => {
    const connection = tx || db;
    return connection
      .select()
      .from(class_schedule_groups)
      .where(eq(class_schedule_groups.class_id, classId))
      .orderBy(asc(class_schedule_groups.created_at));
  },

  /**
   * 클래스의 모든 스케줄 그룹 삭제
   */
  deleteByClassId: (tx: any, classId: string) =>
    tx.delete(class_schedule_groups)
      .where(eq(class_schedule_groups.class_id, classId)),

  /**
   * 스케줄 그룹 상태 업데이트
   */
  updateStatus: (
    groupId: number,
    status: typeof ClassScheduleStatusText[keyof typeof ClassScheduleStatusText]
  ) =>
    db.update(class_schedule_groups)
      .set({ status, updated_at: new Date() })
      .where(eq(class_schedule_groups.id, groupId)),
};

/**
 * 클래스 스케줄 관련 순수 Drizzle 쿼리들
 */
export const scheduleQueries = {
  /**
   * 여러 스케줄 동시 생성
   */
  createMany: (tx: any, schedules: InsertClassSchedule[]) => {
    if (schedules.length === 0) return Promise.resolve([]);
    return tx.insert(class_schedules).values(schedules).returning();
  },

  /**
   * 스케줄 그룹 ID로 스케줄 목록 조회
   */
  findByGroupId: (groupId: number, tx?: any) => {
    const connection = tx || db;
    return connection
      .select()
      .from(class_schedules)
      .where(eq(class_schedules.schedule_group_id, groupId))
      .orderBy(
        asc(class_schedules.day_of_week),
        asc(class_schedules.start_time)
      );
  },

  /**
   * 여러 스케줄 그룹 ID에 대한 스케줄 조회 (N+1 방지)
   */
  findByGroupIds: (groupIds: number[]) => {
    if (groupIds.length === 0) return Promise.resolve([]);
    
    return db
      .select()
      .from(class_schedules)
      .where(inArray(class_schedules.schedule_group_id, groupIds))
      .orderBy(
        asc(class_schedules.day_of_week),
        asc(class_schedules.start_time)
      );
  },

  /**
   * 스케줄 그룹의 모든 스케줄 삭제
   */
  deleteByGroupId: (tx: any, groupId: number) =>
    tx.delete(class_schedules)
      .where(eq(class_schedules.schedule_group_id, groupId)),

  /**
   * 특정 스케줄 업데이트
   */
  update: (tx: any, scheduleId: number, data: Partial<InsertClassSchedule>) =>
    tx.update(class_schedules)
      .set({ ...data, updated_at: new Date() })
      .where(eq(class_schedules.id, scheduleId))
      .returning(),

  /**
   * 스케줄 그룹의 스케줄 교체 (삭제 후 생성)
   */
  replaceGroupSchedules: async (tx: any, groupId: number, newSchedules: InsertClassSchedule[]) => {
    // 기존 스케줄 삭제
    await scheduleQueries.deleteByGroupId(tx, groupId);
    
    // 새 스케줄 생성
    if (newSchedules.length === 0) return [];
    return await scheduleQueries.createMany(tx, newSchedules);
  }
};