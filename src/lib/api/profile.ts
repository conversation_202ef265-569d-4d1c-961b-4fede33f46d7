import {
  UserProfileResponseSchema,
  BookingHistoryResponseSchema,
  BookingActionResponseSchema,
  type UserStats,
  type BookingHistoryResponse,
} from '@/schemas/profile';

export async function fetchUserProfile(): Promise<UserStats> {
  const response = await fetch('/api/profile');
  
  if (!response.ok) {
    throw new Error('Failed to fetch user profile');
  }
  
  const data = await response.json();
  const validatedResponse = UserProfileResponseSchema.parse(data);
  
  if (!validatedResponse.success) {
    throw new Error('Failed to fetch user profile');
  }
  
  return validatedResponse.data;
}

export async function fetchBookingHistory(status?: string): Promise<BookingHistoryResponse['data']> {
  const url = new URL('/api/profile/bookings', window.location.origin);
  if (status) {
    url.searchParams.set('status', status);
  }
  
  const response = await fetch(url.toString());
  
  if (!response.ok) {
    throw new Error('Failed to fetch booking history');
  }
  
  const data = await response.json();
  const validatedResponse = BookingHistoryResponseSchema.parse(data);
  
  if (!validatedResponse.success) {
    throw new Error('Failed to fetch booking history');
  }
  
  return validatedResponse.data;
}

export async function payDeposit(bookingId: string): Promise<string> {
  const response = await fetch('/api/profile/bookings', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      action: 'pay_deposit',
      bookingId,
    }),
  });
  
  if (!response.ok) {
    throw new Error('Failed to process deposit payment');
  }
  
  const data = await response.json();
  const validatedResponse = BookingActionResponseSchema.parse(data);
  
  if (!validatedResponse.success) {
    throw new Error(validatedResponse.message || 'Failed to process deposit payment');
  }
  
  return validatedResponse.message;
}

export async function cancelBooking(bookingId: string): Promise<string> {
  const response = await fetch('/api/profile/bookings', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      action: 'cancel_booking',
      bookingId,
    }),
  });
  
  if (!response.ok) {
    throw new Error('Failed to cancel booking');
  }
  
  const data = await response.json();
  const validatedResponse = BookingActionResponseSchema.parse(data);
  
  if (!validatedResponse.success) {
    throw new Error(validatedResponse.message || 'Failed to cancel booking');
  }
  
  return validatedResponse.message;
}