import { BASE_URL } from '../api.config';

async function fetchUserProfile() {
  const url = `${BASE_URL}/api/profile`;
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('Failed to fetch user profile');
  }

  const json = await response.json();
  return json.data;
}

async function fetchUserBookingHistory() {
  const url = `${BASE_URL}/api/profile/bookings`;
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('Failed to fetch user booking history');
  }

  const json = await response.json();
  return json.data;
}

export const profileApi = {
  fetchUserProfile,
  fetchUserBookingHistory,
};
