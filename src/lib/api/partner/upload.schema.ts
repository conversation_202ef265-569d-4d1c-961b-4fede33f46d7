import z from 'zod';

export const uploadApiSchema = {
  '/api/partner/upload': {
    // request: z.object({
    //   file: z.instanceof(File),
    // }),
    response: z.object({
      success: z.boolean(),
      url: z.string(),
      path: z.string(),
    }),
  },
};

export type UploadApiSchema = typeof uploadApiSchema;
export type UploadResponse = z.infer<
  UploadApiSchema['/api/partner/upload']['response']
>;
