import { httpClient, HttpClient } from '@/lib/http-client';
import {
  CreateStudioRequest,
  CreateStudioResponse,
  GetStudiosResponse,
  studioApiSchema,
  StudioResponse,
} from './studio.schema';
import { StudioImageType } from '@/lib/schemas/studio';
import { uploadApiSchema } from './upload.schema';
import { instructorApi } from './instructor.api';
import { GetInstructorsResponse } from './instructor.schema';
import { EditStudioFormData } from '@/app/partner/_schemas/form.schema';

export class PartnerStudioApi {
  private httpClient: HttpClient;
  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  getStudiosWithInstructors = async (): Promise<
    {
      studio: StudioResponse;
      instructors: GetInstructorsResponse;
    }[]
  > => {
    try {
      // 1. 스튜디오 목록 가져오기
      const studios = await this.getMyStudios();

      // 2. 각 스튜디오별 강사 가져오기
      const studiosWithInstructors = await Promise.all(
        studios.map(async studio => {
          try {
            const instructors = await instructorApi.getInstructors();
            return {
              studio,
              instructors,
            };
          } catch (error) {
            console.error(`스튜디오 ${studio.id}의 강사 로딩 실패:`, error);
            return {
              studio,
              instructors: [],
            };
          }
        })
      );

      // 3. 강사가 있는 스튜디오만 필터링
      return studiosWithInstructors.filter(item => item.instructors.length > 0);
    } catch (error) {
      console.error('getStudiosWithInstructors error', error);
      throw error;
    }
  };

  getMyStudios = async (): Promise<GetStudiosResponse> => {
    try {
      const json = await this.httpClient.get('/api/partner/studios');

      const result =
        studioApiSchema['GET /api/partner/studios'].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('getStudios error', error);
      throw error;
    }
  };

  uploadStudioImage = async ({
    file,
    studioId,
    prefix,
  }: {
    file: File;
    studioId: string;
    prefix: StudioImageType;
  }) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'studio');
    formData.append('prefix', prefix);
    formData.append('studioId', studioId);

    const json = await this.httpClient.post('/api/partner/upload', formData);
    const result =
      uploadApiSchema['/api/partner/upload'].response.safeParse(json);

    if (!result.success) {
      throw new Error(
        `Invalid response: ${JSON.stringify(result.error.issues)}`
      );
    }

    return result.data;
  };

  createStudio = async (
    req: Omit<CreateStudioRequest, 'partnerId'>
  ): Promise<CreateStudioResponse> => {
    try {
      const json = await this.httpClient.post('/api/partner/studios', req);

      const result =
        studioApiSchema['POST /api/partner/studios'].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      // throw handleError(error);
      console.error('createStudio error', error);
      throw error;
    }
  };

  getStudioById = async (studioId: string): Promise<StudioResponse> => {
    try {
      const json = await this.httpClient.get(
        `/api/partner/studios/${studioId}`
      );

      const result =
        studioApiSchema[
          'GET /api/partner/studios/:studioId'
        ].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('getStudioById error', error);
      throw error;
    }
  };

  updateStudioById = async (
    studioId: string,
    req: EditStudioFormData
  ): Promise<CreateStudioResponse> => {
    try {
      const json = await this.httpClient.put(
        `/api/partner/studios/${studioId}`,
        req
      );

      const result =
        studioApiSchema[
          'PUT /api/partner/studios/:studioId'
        ].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('updateStudioById error', error);
      throw error;
    }
  };
}
export const partnerStudioApi = new PartnerStudioApi(httpClient);
