import { BASE_URL } from '../api.config';

export interface ImageUploadResponse {
  success: boolean;
  url: string;
  path: string;
}

export interface StudioImageData {
  path: string;
  url: string;
}

export interface CreateStudioRequest {
  name: string;
  phone: string;
  address: string;
  latitude: number;
  longitude: number;
  description?: string;
  addressDetail?: string;
  postalCode?: string;
  nearestStation?: string;
  stationDistance?: number;
  amenities?: Record<string, any>;
  operatingHours?: Record<string, any>;
  images?: StudioImageData[];
}

export interface UpdateStudioRequest {
  name?: string;
  phone?: string;
  description?: string;
  addressDetail?: string;
  nearestStation?: string;
  stationDistance?: number;
  amenities?: Record<string, any>;
  operatingHours?: Record<string, any>;
  images?: StudioImageData[];
}

export interface StudioResponse {
  id: string;
  name: string;
  phone: string;
  description?: string;
  address: string;
  addressDetail?: string;
  latitude: number;
  longitude: number;
  nearestStation?: string;
  stationDistance?: number;
  amenities?: Record<string, any>;
  operatingHours?: Record<string, any>;
  images: StudioImageData[];
  status: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 이미지 파일을 업로드합니다.
 */
export async function uploadStudioImage(
  file: File,
  studioId?: string,
  prefix: 'featured' | 'gallery' = 'gallery'
): Promise<ImageUploadResponse> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', 'studio');
  if (studioId) {
    formData.append('studioId', studioId);
  }
  formData.append('prefix', prefix);

  const response = await fetch(`${BASE_URL}/api/partner/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || '이미지 업로드에 실패했습니다.');
  }

  return response.json();
}

/**
 * 새로운 스튜디오를 생성합니다.
 */
export async function createStudio(
  studioData: CreateStudioRequest
): Promise<StudioResponse> {
  const response = await fetch(`${BASE_URL}/api/partner/studios`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(studioData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || '스튜디오 생성에 실패했습니다.');
  }

  return response.json();
}

/**
 * 스튜디오 정보를 수정합니다.
 */
export async function updateStudio(
  studioId: string,
  studioData: UpdateStudioRequest
): Promise<StudioResponse> {
  const response = await fetch(`${BASE_URL}/api/partner/studios/${studioId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(studioData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || '스튜디오 수정에 실패했습니다.');
  }

  return response.json();
}

/**
 * 스튜디오 정보를 조회합니다.
 */
export async function getStudio(studioId: string): Promise<StudioResponse> {
  const response = await fetch(`${BASE_URL}/api/partner/studios/${studioId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || '스튜디오 조회에 실패했습니다.');
  }

  return response.json();
}
