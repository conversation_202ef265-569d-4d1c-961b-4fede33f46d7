import {
  createClassSchema,
  updateClassSchema,
  classListFilterSchema,
} from '@/lib/validations/partner-class.validation';
import z from 'zod';

const ScheduleSchema = z.object({
  // id: z.number(),
  dayOfWeek: z.string(),
  startTime: z.string(),
  endTime: z.string(),
});

// ScheduleGroup 스키마 (공통)
const ScheduleGroupSchema = z.object({
  id: z.number(),
  status: z.string(),
  schedules: z.array(ScheduleSchema),
});

// 기본 클래스 정보 스키마 (ClassWithScheduleGroups 인터페이스 기반)
const BaseClassResponseSchema = z.object({
  id: z.string(),
  partnerId: z.string(),
  studioId: z.string(),
  instructorId: z.string(),
  title: z.string(),
  description: z.string(),
  category: z.string(),
  level: z.string(),
  target: z.string(),
  maxParticipants: z.number(),
  pricePerSession: z.number(),
  sessionDurationMinutes: z.number(),
  durationWeeks: z.number(),
  sessionsPerWeek: z.number(),
  images: z.any(), // 실제 타입이 any이므로 any로 유지
  status: z.string(),
  visible: z.boolean().nullable(),
  createdAt: z.string().nullable(),
  updatedAt: z.string().nullable(),
  scheduleGroups: z.array(ScheduleGroupSchema),
});

// POST, PUT 응답 스키마 (createClass, updateClass 반환 타입)
export const ClassResponseSchema = BaseClassResponseSchema;

// GET /api/partner/classes 응답의 data 항목 스키마
export const ClassListItemResponseSchema = BaseClassResponseSchema.extend({
  studioName: z.string(),
  instructorName: z.string(),
  enrollmentCount: z.number(),
});

export type ClassListItemResponse = z.infer<typeof ClassListItemResponseSchema>;

// 페이지네이션 응답 스키마
export const PaginationResponseSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  totalPages: z.number(),
});

// GET /api/partner/classes 전체 응답 스키마
export const ClassListResponseSchema = z.object({
  data: z.array(ClassListItemResponseSchema),
  pagination: PaginationResponseSchema,
});

// GET /api/partner/classes/{id} 응답 스키마 (getClassDetail 반환 타입)
export const ClassDetailResponseSchema = BaseClassResponseSchema.extend({
  studio: z.object({
    id: z.string(),
    name: z.string(),
    address: z.string(),
  }),
  instructor: z.object({
    id: z.string(),
    name: z.string(),
    profileImages: z.any(), // 실제 타입이 any이므로 any로 유지
  }),
});

// DELETE /api/partner/classes/{id} 응답 스키마
export const DeleteClassResponseSchema = z.object({
  message: z.string(),
});

export const createClassRequestSchema = createClassSchema.omit({
  images: true,
});

export const classApiSchema = {
  'GET /api/partner/classes': {
    request: classListFilterSchema,
    response: ClassListResponseSchema,
  },
  'POST /api/partner/classes': {
    request: createClassRequestSchema,
    response: ClassResponseSchema,
  },
  'GET /api/partner/classes/{id}': {
    request: z.object({ id: z.string() }),
    response: ClassDetailResponseSchema,
  },
  'PUT /api/partner/classes/{id}': {
    request: updateClassSchema,
    response: ClassResponseSchema,
  },
  'DELETE /api/partner/classes/{id}': {
    request: z.object({ id: z.string() }),
    response: DeleteClassResponseSchema,
  },
};

export type ClassApiSchema = typeof classApiSchema;
export type GetClassesRequest = z.infer<typeof classListFilterSchema>;
export type GetClassesResponse = z.infer<typeof ClassListResponseSchema>;

export type CreateClassRequest = z.infer<typeof createClassRequestSchema>;
export type ScheduleGroup = CreateClassRequest['scheduleGroups'][number];
export type Schedule = ScheduleGroup['schedules'][number];

export type CreateClassResponse = z.infer<typeof ClassResponseSchema>;

export type GetClassResponse = z.infer<typeof ClassDetailResponseSchema>;

export type UpdateClassRequest = z.infer<typeof updateClassSchema>;
export type UpdateClassResponse = z.infer<typeof ClassResponseSchema>;

export type DeleteClassResponse = z.infer<typeof DeleteClassResponseSchema>;
