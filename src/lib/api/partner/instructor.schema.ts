import {
  CreateInstructorSchema,
  UpdateInstructorSchema,
} from '@/lib/schemas/instructor';
import z from 'zod';

// API 엔드포인트 키 타입 정의
export const INSTRUCTOR_API_ENDPOINTS = {
  CREATE_INSTRUCTOR: 'POST /api/partner/instructors',
  GET_INSTRUCTORS: 'GET /api/partner/instructors',
  GET_INSTRUCTOR_BY_ID: 'GET /api/partner/instructors/{instructorId}',
  UPDATE_INSTRUCTOR: 'PUT /api/partner/instructors/{instructorId}',
  DELETE_INSTRUCTOR: 'DELETE /api/partner/instructors/{instructorId}',
} as const;

// 엔드포인트 키 타입
export type InstructorApiEndpoint =
  (typeof INSTRUCTOR_API_ENDPOINTS)[keyof typeof INSTRUCTOR_API_ENDPOINTS];

export const InstructorResponseSchema = CreateInstructorSchema.extend({
  id: z.string(),
  partnerId: z.string(),
  status: z.enum(['active', 'inactive', 'deleted']).default('active'),
  createdAt: z.string(),
  updatedAt: z.string(),
  profileImages: z
    .array(
      z.object({
        url: z.string(),
        path: z.string(),
      })
    )
    .nullable(),
});

export const instructorApiSchema = {
  [INSTRUCTOR_API_ENDPOINTS.CREATE_INSTRUCTOR]: {
    request: CreateInstructorSchema,
    response: InstructorResponseSchema,
  },
  [INSTRUCTOR_API_ENDPOINTS.GET_INSTRUCTORS]: {
    request: z.object({}),
    response: z.array(InstructorResponseSchema),
  },
  [INSTRUCTOR_API_ENDPOINTS.GET_INSTRUCTOR_BY_ID]: {
    request: z.object({}),
    response: InstructorResponseSchema,
  },
  [INSTRUCTOR_API_ENDPOINTS.UPDATE_INSTRUCTOR]: {
    request: UpdateInstructorSchema,
    response: InstructorResponseSchema,
  },
  [INSTRUCTOR_API_ENDPOINTS.DELETE_INSTRUCTOR]: {
    request: z.object({}),
    response: z.object({
      message: z.string(),
      instructorId: z.string(),
    }),
  },
} as const;

export type InstructorApiSchema = typeof instructorApiSchema;

// Request/Response 타입 정의 (타입 안전한 키 사용)
export type CreateInstructorRequest = z.infer<
  InstructorApiSchema[typeof INSTRUCTOR_API_ENDPOINTS.CREATE_INSTRUCTOR]['request']
>;
export type CreateInstructorResponse = z.infer<
  InstructorApiSchema[typeof INSTRUCTOR_API_ENDPOINTS.CREATE_INSTRUCTOR]['response']
>;

export type GetInstructorsResponse = z.infer<
  InstructorApiSchema[typeof INSTRUCTOR_API_ENDPOINTS.GET_INSTRUCTORS]['response']
>;

export type GetInstructorResponse = z.infer<
  InstructorApiSchema[typeof INSTRUCTOR_API_ENDPOINTS.GET_INSTRUCTOR_BY_ID]['response']
>;

export type UpdateInstructorRequest = z.infer<
  InstructorApiSchema[typeof INSTRUCTOR_API_ENDPOINTS.UPDATE_INSTRUCTOR]['request']
>;
export type UpdateInstructorResponse = z.infer<
  InstructorApiSchema[typeof INSTRUCTOR_API_ENDPOINTS.UPDATE_INSTRUCTOR]['response']
>;

export type DeleteInstructorResponse = z.infer<
  InstructorApiSchema[typeof INSTRUCTOR_API_ENDPOINTS.DELETE_INSTRUCTOR]['response']
>;

export type InstructorResponse = z.infer<typeof InstructorResponseSchema>;
