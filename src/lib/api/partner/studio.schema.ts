import {
  CreateStudioSchema,
  StudioResponseSchema,
  UpdateStudioSchema,
} from '@/lib/schemas/studio';

import z from 'zod';

export const studioApiSchema = {
  'GET /api/partner/studios': {
    response: z.array(StudioResponseSchema),
  },
  'POST /api/partner/studios': {
    request: CreateStudioSchema,
    response: StudioResponseSchema,
  },
  'GET /api/partner/studios/:studioId': {
    response: StudioResponseSchema,
  },
  'PUT /api/partner/studios/:studioId': {
    request: UpdateStudioSchema,
    response: StudioResponseSchema,
  },
};

export type StudioApiSchema = typeof studioApiSchema;
export type GetStudiosResponse = z.infer<
  StudioApiSchema['GET /api/partner/studios']['response']
>;
export type StudioResponse = GetStudiosResponse[number];
export type CreateStudioRequest = z.infer<
  StudioApiSchema['POST /api/partner/studios']['request']
>;
export type CreateStudioResponse = z.infer<
  StudioApiSchema['POST /api/partner/studios']['response']
>;

export type GetStudioResponse = z.infer<
  StudioApiSchema['GET /api/partner/studios/:studioId']['response']
>;

export type UpdateStudioRequest = z.infer<
  StudioApiSchema['PUT /api/partner/studios/:studioId']['request']
>;

export type UpdateStudioResponse = z.infer<
  StudioApiSchema['PUT /api/partner/studios/:studioId']['response']
>;
