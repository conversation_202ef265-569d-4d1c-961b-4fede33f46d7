import {
  type PaymentSuccessRequest,
  type PaymentHistoryRequest,
  type PaymentHistoryResponse,
  type PaymentSuccessDetails,
  type NextStep,
} from '@/schemas/payment';

export async function getPaymentSuccessData(
  params: PaymentSuccessRequest
): Promise<PaymentSuccessDetails> {
  try {
    const mockNextSteps: NextStep[] = [
      {
        title: '센터에서 남은 수업료 결제',
        description: '센터에서 남은 수업료를 결제해주세요.',
        completed: false,
      },
      {
        title: '예약 확정 안내 발송',
        description: '예약 확정 안내가 곧 발송됩니다.',
        completed: false,
      },
      {
        title: '수업 일정 및 위치 확인',
        description: '수업 일정 및 위치를 확인해주세요.',
        completed: false,
      },
    ];

    const paymentData: PaymentSuccessDetails = {
      id: params.enrollmentId,
      memberName: '김회원',
      className: '기초 체력 수업',
      instructorName: '박트레이너',
      studioName: '강남 피트니스 센터',
      scheduleGroupName: '저녁반',
      paymentMethod: 'credit_card',
      paymentAmount: 30000,
      paymentType: 'deposit',
      paymentDate: new Date().toISOString().split('T')[0],
      paymentStatus: 'completed',
      transactionId: params.transactionId || `txn_${Date.now()}`,
      enrollmentId: params.enrollmentId,
      classTemplateId: 'class_template_001',
      scheduleGroupId: 'schedule_group_001',
      nextSteps: mockNextSteps,
      classStartDate: '2025-01-20',
      classEndDate: '2025-03-20',
      remainingAmount: 150000,
      totalAmount: 180000,
    };

    return paymentData;
  } catch (error) {
    console.error('Error fetching payment success data:', error);
    throw new Error('Failed to fetch payment success data');
  }
}

export async function getPaymentHistoryData(
  params: PaymentHistoryRequest
): Promise<PaymentHistoryResponse['data']> {
  try {
    const mockPayments = [
      {
        id: 'payment_001',
        memberName: '김회원',
        className: '기초 체력 수업',
        instructorName: '박트레이너',
        studioName: '강남 피트니스 센터',
        scheduleGroupName: '저녁반',
        paymentMethod: 'credit_card' as const,
        paymentAmount: 30000,
        paymentType: 'deposit' as const,
        paymentDate: '2025-01-15',
        paymentStatus: 'completed' as const,
        transactionId: 'txn_20250115_001',
        enrollmentId: 'enrollment_001',
        classTemplateId: 'class_template_001',
        scheduleGroupId: 'schedule_group_001',
      },
      {
        id: 'payment_002',
        memberName: '김회원',
        className: '요가 입문 클래스',
        instructorName: '이요가',
        studioName: '서울 요가 스튜디오',
        scheduleGroupName: '오전반',
        paymentMethod: 'kakaopay' as const,
        paymentAmount: 25000,
        paymentType: 'deposit' as const,
        paymentDate: '2025-01-10',
        paymentStatus: 'completed' as const,
        transactionId: 'txn_20250110_002',
        enrollmentId: 'enrollment_002',
        classTemplateId: 'class_template_002',
        scheduleGroupId: 'schedule_group_002',
      },
      {
        id: 'payment_003',
        memberName: '김회원',
        className: '필라테스 중급',
        instructorName: '최필라',
        studioName: '명동 필라테스 스튜디오',
        scheduleGroupName: '점심반',
        paymentMethod: 'naverpay' as const,
        paymentAmount: 120000,
        paymentType: 'full_payment' as const,
        paymentDate: '2025-01-05',
        paymentStatus: 'completed' as const,
        transactionId: 'txn_20250105_003',
        enrollmentId: 'enrollment_003',
        classTemplateId: 'class_template_003',
        scheduleGroupId: 'schedule_group_003',
      },
    ];

    const { limit = 20, offset = 0 } = params;

    const totalPayments = mockPayments.length;
    const paginatedPayments = mockPayments.slice(offset, offset + limit);
    const hasMore = offset + limit < totalPayments;

    return {
      payments: paginatedPayments,
      total: totalPayments,
      hasMore,
    };
  } catch (error) {
    console.error('Error fetching payment history:', error);
    throw new Error('Failed to fetch payment history');
  }
}

export const paymentServerApi = {
  getPaymentSuccessData,
  getPaymentHistoryData,
};
