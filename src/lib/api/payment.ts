import {
  type PaymentSuccessRequest,
  type PaymentSuccessFullResponse,
  type PaymentSuccessErrorResponse,
  type PaymentHistoryRequest,
  type PaymentHistoryResponse,
} from '@/schemas/payment';

const API_BASE_URL = '/api/payment';

export class PaymentApiError extends Error {
  constructor(
    message: string,
    public code?: string,
    public status?: number
  ) {
    super(message);
    this.name = 'PaymentApiError';
  }
}

export async function getPaymentSuccess(
  params: PaymentSuccessRequest
): Promise<PaymentSuccessFullResponse> {
  try {
    const searchParams = new URLSearchParams();
    searchParams.append('enrollmentId', params.enrollmentId);
    if (params.transactionId) {
      searchParams.append('transactionId', params.transactionId);
    }

    const response = await fetch(`${API_BASE_URL}/success?${searchParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data: PaymentSuccessFullResponse | PaymentSuccessErrorResponse =
      await response.json();

    if (!response.ok || !data.success) {
      const errorData = data as PaymentSuccessErrorResponse;
      throw new PaymentApiError(
        errorData.error,
        errorData.code,
        response.status
      );
    }

    return data as PaymentSuccessFullResponse;
  } catch (error) {
    if (error instanceof PaymentApiError) {
      throw error;
    }
    throw new PaymentApiError('Failed to fetch payment success data');
  }
}

export async function confirmPaymentSuccess(
  params: PaymentSuccessRequest
): Promise<PaymentSuccessFullResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/success`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    const data: PaymentSuccessFullResponse | PaymentSuccessErrorResponse =
      await response.json();

    if (!response.ok || !data.success) {
      const errorData = data as PaymentSuccessErrorResponse;
      throw new PaymentApiError(
        errorData.error,
        errorData.code,
        response.status
      );
    }

    return data as PaymentSuccessFullResponse;
  } catch (error) {
    if (error instanceof PaymentApiError) {
      throw error;
    }
    throw new PaymentApiError('Failed to confirm payment success');
  }
}

export async function getPaymentHistory(
  params: PaymentHistoryRequest
): Promise<PaymentHistoryResponse> {
  try {
    const searchParams = new URLSearchParams();

    if (params.status) {
      searchParams.append('status', params.status);
    }
    if (params.paymentType) {
      searchParams.append('paymentType', params.paymentType);
    }
    if (params.startDate) {
      searchParams.append('startDate', params.startDate);
    }
    if (params.endDate) {
      searchParams.append('endDate', params.endDate);
    }
    if (params.limit) {
      searchParams.append('limit', params.limit.toString());
    }
    if (params.offset) {
      searchParams.append('offset', params.offset.toString());
    }

    const response = await fetch(`${API_BASE_URL}/history?${searchParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data: PaymentHistoryResponse | PaymentSuccessErrorResponse =
      await response.json();

    if (!response.ok || !data.success) {
      const errorData = data as PaymentSuccessErrorResponse;
      throw new PaymentApiError(
        errorData.error,
        errorData.code,
        response.status
      );
    }

    return data as PaymentHistoryResponse;
  } catch (error) {
    if (error instanceof PaymentApiError) {
      throw error;
    }
    throw new PaymentApiError('Failed to fetch payment history');
  }
}

export const paymentApi = {
  getPaymentSuccess,
  confirmPaymentSuccess,
  getPaymentHistory,
};
