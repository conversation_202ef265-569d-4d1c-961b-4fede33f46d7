import {
  classDetailSchema,
  classListItemSchema,
} from '@/app/api/classes/schema';
import { createApiFetch } from '@/lib/utils/api-utils';
import { z } from 'zod';

// 스키마 정의
const FilterOptionsSchema = z.object({
  levels: z.array(z.object({ value: z.string(), label: z.string() })),
  nearestStations: z.array(z.object({ value: z.string(), label: z.string() })),
  genders: z.array(z.object({ value: z.string(), label: z.string() })),
});

// API 함수들 생성
const fetchFilterOptions = createApiFetch(
  '/api/filter-options',
  FilterOptionsSchema
);

export async function getActiveClasses(options?: {
  page?: number;
  limit?: number;
  filters?: {
    level?: string;
    nearestStation?: string;
    gender?: string;
  };
}) {
  const searchParams = new URLSearchParams();

  if (options?.page) {
    searchParams.set('page', options.page.toString());
  }

  if (options?.limit) {
    searchParams.set('limit', options.limit.toString());
  }

  // 필터 파라미터 추가
  if (options?.filters?.level) {
    searchParams.set('level', options.filters.level);
  }

  if (options?.filters?.nearestStation) {
    searchParams.set('nearestStation', options.filters.nearestStation);
  }

  if (options?.filters?.gender) {
    searchParams.set('gender', options.filters.gender);
  }

  const queryString = searchParams.toString();
  const url = queryString ? `/api/classes?${queryString}` : '/api/classes';
  const fetcher = createApiFetch(url, z.array(classListItemSchema));
  const json = await fetcher();
  return json.data;
}

export async function getClassDetailById(id: string) {
  const fetcher = createApiFetch(`/api/classes/${id}`, classDetailSchema);
  const json = await fetcher();
  return json.data;
}

async function getFilterOptions() {
  return fetchFilterOptions();
}

export const ClassesApi = {
  getActiveClasses,
  getClassDetailById,
  getFilterOptions,
};
