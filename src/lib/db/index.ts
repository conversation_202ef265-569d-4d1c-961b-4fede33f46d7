// import { drizzle } from 'drizzle-orm/postgres-js';
// import postgres from 'postgres';
// import * as schema from './schema';
// import { env, isDevelopment } from '../supabase/env';

// // Environment validation - graceful fallback for development
// let connectionString: string | null = null;
// let isDbAvailable = false;

// try {
//   connectionString = env.DATABASE_URL;
//   isDbAvailable = true;
// } catch (error) {
//   if (isDevelopment()) {
//     console.warn('⚠️  DATABASE_URL not set. Using mock database client.');
//     console.warn(
//       '   Set DATABASE_URL in .env.local to enable real database access.'
//     );
//   } else {
//     throw error;
//   }
// }

// // Mock database client for development without DATABASE_URL
// const createMockDb = () => ({
//   select: () => ({
//     from: () => ({
//       where: () => ({
//         limit: () => Promise.resolve([]),
//         offset: () => Promise.resolve([]),
//       }),
//       limit: () => Promise.resolve([]),
//       offset: () => Promise.resolve([]),
//     }),
//   }),
//   insert: () => ({
//     values: () => ({
//       returning: () => Promise.resolve([]),
//     }),
//   }),
//   update: () => ({
//     set: () => ({
//       where: () => ({
//         returning: () => Promise.resolve([]),
//       }),
//     }),
//   }),
//   delete: () => ({
//     where: () => ({
//       returning: () => Promise.resolve([]),
//     }),
//   }),
//   execute: () => Promise.resolve(null),
//   transaction: (fn: any) => fn(createMockDb()),
// });

// // Create database client or mock
// let db: any;

// if (!isDbAvailable) {
//   db = createMockDb();
// } else {
//   // PostgreSQL client configuration
//   const client = postgres(connectionString!, {
//     max: isDevelopment() ? 5 : 20, // Connection pool size
//     idle_timeout: 20, // Close idle connections after 20 seconds
//     connect_timeout: 10, // Connection timeout
//     debug: isDevelopment(), // Enable debug in development
//   });

//   db = drizzle(client, {
//     schema,
//     logger: isDevelopment(), // Enable query logging in development
//   });
// }

// export { db };

// export type DB = typeof db;
// export * from './schema';

// // Health check function
// export async function checkDatabaseConnection() {
//   if (!isDbAvailable) {
//     return {
//       success: false,
//       message:
//         'Database not configured. Set DATABASE_URL environment variable.',
//     };
//   }

//   try {
//     await db.execute('SELECT 1');
//     return { success: true, message: 'Database connection successful' };
//   } catch (error) {
//     return {
//       success: false,
//       message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
//     };
//   }
// }

// // Helper to check if database is available
// export function isDatabaseAvailable(): boolean {
//   return isDbAvailable;
// }

import { drizzle } from 'drizzle-orm/postgres-js';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';
import { env, isDevelopment } from '../supabase/env';

const connectionString = env.DATABASE_URL;

if (!connectionString) {
  throw new Error(
    'DATABASE_URL is required. Set it in your environment variables.'
  );
}

const client = postgres(connectionString, {
  max: isDevelopment() ? 5 : 20,
  debug: isDevelopment(),
});

export const db: PostgresJsDatabase<typeof schema> = drizzle(client, {
  schema,
  logger: isDevelopment(),
});

// Health check function (옵션: 필요시 사용)
export async function checkDatabaseConnection() {
  try {
    await db.execute('SELECT 1');
    return { success: true, message: 'Database connection successful' };
  } catch (error) {
    return {
      success: false,
      message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

export type * as Schema from './schema';
