/**
 * 서울시 지하철역 데이터
 * 
 * @description
 * - 서울시 모든 지하철역 목록
 * - 호선별 분류
 * - 검색 및 자동완성 기능을 위한 데이터
 */

export interface SubwayStation {
  id: string;
  name: string;
  line: string;
  lineNumber: string;
  englishName: string;
  lines?: string[]; // 환승역을 위한 호선 배열
}

export const SUBWAY_STATIONS: SubwayStation[] = [
  // 1호선
  { id: '101', name: '종각', line: '1호선', lineNumber: '1', englishName: 'Jonggak' },
  { id: '102', name: '종로3가', line: '1호선', lineNumber: '1', englishName: 'Jongno 3-ga' },
  { id: '103', name: '종로5가', line: '1호선', lineNumber: '1', englishName: 'Jongno 5-ga' },
  { id: '104', name: '동대문', line: '1호선', lineNumber: '1', englishName: 'Dongdaemun' },
  { id: '105', name: '신설동', line: '1호선', lineNumber: '1', englishName: 'Sinseol-dong' },
  { id: '106', name: '제기동', line: '1호선', lineNumber: '1', englishName: 'Jegi-dong' },
  { id: '107', name: '청량리', line: '1호선', lineNumber: '1', englishName: 'Cheongnyangni' },

  // 2호선 - 주요역들
  { id: '201', name: '시청', line: '2호선', lineNumber: '2', englishName: 'City Hall' },
  { id: '202', name: '을지로입구', line: '2호선', lineNumber: '2', englishName: 'Euljiro 1-ga' },
  { id: '203', name: '을지로3가', line: '2호선', lineNumber: '2', englishName: 'Euljiro 3-ga' },
  { id: '204', name: '을지로4가', line: '2호선', lineNumber: '2', englishName: 'Euljiro 4-ga' },
  { id: '205', name: '동대문역사문화공원', line: '2호선', lineNumber: '2', englishName: 'Dongdaemun History & Culture Park' },
  { id: '206', name: '신당', line: '2호선', lineNumber: '2', englishName: 'Sindang' },
  { id: '207', name: '상왕십리', line: '2호선', lineNumber: '2', englishName: 'Sangwangsimni' },
  { id: '208', name: '왕십리', line: '2호선', lineNumber: '2', englishName: 'Wangsimni' },
  { id: '209', name: '한양대', line: '2호선', lineNumber: '2', englishName: 'Hanyang Univ.' },
  { id: '210', name: '뚝섬', line: '2호선', lineNumber: '2', englishName: 'Ttukseom' },
  { id: '211', name: '성수', line: '2호선', lineNumber: '2', englishName: 'Seongsu' },
  { id: '212', name: '건대입구', line: '2호선', lineNumber: '2', englishName: 'Konkuk Univ.' },
  { id: '213', name: '구의', line: '2호선', lineNumber: '2', englishName: 'Guui' },
  { id: '214', name: '강변', line: '2호선', lineNumber: '2', englishName: 'Gangbyeon' },
  { id: '215', name: '잠실나루', line: '2호선', lineNumber: '2', englishName: 'Jamsil Naru' },
  { id: '216', name: '잠실', line: '2호선', lineNumber: '2', englishName: 'Jamsil' },
  { id: '217', name: '잠실새내', line: '2호선', lineNumber: '2', englishName: 'Jamsil Saenae' },
  { id: '218', name: '종합운동장', line: '2호선', lineNumber: '2', englishName: 'Sports Complex' },
  { id: '219', name: '삼성', line: '2호선', lineNumber: '2', englishName: 'Samsung' },
  { id: '220', name: '선릉', line: '2호선', lineNumber: '2', englishName: 'Seolleung' },
  { id: '221', name: '역삼', line: '2호선', lineNumber: '2', englishName: 'Yeoksam' },
  { id: '222', name: '강남', line: '2호선', lineNumber: '2', englishName: 'Gangnam' },
  { id: '223', name: '교대', line: '2호선', lineNumber: '2', englishName: 'Gyodae' },
  { id: '224', name: '서초', line: '2호선', lineNumber: '2', englishName: 'Seocho' },
  { id: '225', name: '방배', line: '2호선', lineNumber: '2', englishName: 'Bangbae' },
  { id: '226', name: '사당', line: '2호선', lineNumber: '2', englishName: 'Sadang' },
  { id: '227', name: '낙성대', line: '2호선', lineNumber: '2', englishName: 'Nakseongdae' },
  { id: '228', name: '서울대입구', line: '2호선', lineNumber: '2', englishName: 'Seoul Nat\'l Univ.' },
  { id: '229', name: '봉천', line: '2호선', lineNumber: '2', englishName: 'Bongcheon' },
  { id: '230', name: '신림', line: '2호선', lineNumber: '2', englishName: 'Sillim' },
  { id: '231', name: '신대방', line: '2호선', lineNumber: '2', englishName: 'Sindaebang' },
  { id: '232', name: '구로디지털단지', line: '2호선', lineNumber: '2', englishName: 'Guro Digital Complex' },
  { id: '233', name: '대림', line: '2호선', lineNumber: '2', englishName: 'Daerim' },
  { id: '234', name: '신도림', line: '2호선', lineNumber: '2', englishName: 'Sindorim' },
  { id: '235', name: '문래', line: '2호선', lineNumber: '2', englishName: 'Mullae' },
  { id: '236', name: '영등포구청', line: '2호선', lineNumber: '2', englishName: 'Yeongdeungpo-gu Office' },
  { id: '237', name: '당산', line: '2호선', lineNumber: '2', englishName: 'Dangsan' },
  { id: '238', name: '합정', line: '2호선', lineNumber: '2', englishName: 'Hapjeong' },
  { id: '239', name: '홍대입구', line: '2호선', lineNumber: '2', englishName: 'Hongik Univ.' },
  { id: '240', name: '신촌', line: '2호선', lineNumber: '2', englishName: 'Sinchon' },
  { id: '241', name: '이대', line: '2호선', lineNumber: '2', englishName: 'Ewha Womans Univ.' },
  { id: '242', name: '아현', line: '2호선', lineNumber: '2', englishName: 'Ahyeon' },
  { id: '243', name: '충정로', line: '2호선', lineNumber: '2', englishName: 'Chungjeongno' },

  // 3호선 - 주요역들
  { id: '301', name: '지축', line: '3호선', lineNumber: '3', englishName: 'Jichuk' },
  { id: '302', name: '구파발', line: '3호선', lineNumber: '3', englishName: 'Gupabal' },
  { id: '303', name: '연신내', line: '3호선', lineNumber: '3', englishName: 'Yeonsinnae' },
  { id: '304', name: '불광', line: '3호선', lineNumber: '3', englishName: 'Bulgwang' },
  { id: '305', name: '녹번', line: '3호선', lineNumber: '3', englishName: 'Nokbeon' },
  { id: '306', name: '홍제', line: '3호선', lineNumber: '3', englishName: 'Hongje' },
  { id: '307', name: '무악재', line: '3호선', lineNumber: '3', englishName: 'Muakjae' },
  { id: '308', name: '독립문', line: '3호선', lineNumber: '3', englishName: 'Dongnimmun' },
  { id: '309', name: '경복궁', line: '3호선', lineNumber: '3', englishName: 'Gyeongbokgung' },
  { id: '310', name: '안국', line: '3호선', lineNumber: '3', englishName: 'Anguk' },
  { id: '311', name: '종로3가', line: '3호선', lineNumber: '3', englishName: 'Jongno 3-ga' },
  { id: '312', name: '을지로3가', line: '3호선', lineNumber: '3', englishName: 'Euljiro 3-ga' },
  { id: '313', name: '충무로', line: '3호선', lineNumber: '3', englishName: 'Chungmuro' },
  { id: '314', name: '동대입구', line: '3호선', lineNumber: '3', englishName: 'Dongguk Univ.' },
  { id: '315', name: '약수', line: '3호선', lineNumber: '3', englishName: 'Yaksu' },
  { id: '316', name: '금고', line: '3호선', lineNumber: '3', englishName: 'Geumgo' },
  { id: '317', name: '옥수', line: '3호선', lineNumber: '3', englishName: 'Oksu' },
  { id: '318', name: '압구정', line: '3호선', lineNumber: '3', englishName: 'Apgujeong' },
  { id: '319', name: '신사', line: '3호선', lineNumber: '3', englishName: 'Sinsa' },
  { id: '320', name: '잠원', line: '3호선', lineNumber: '3', englishName: 'Jamwon' },
  { id: '321', name: '고속터미널', line: '3호선', lineNumber: '3', englishName: 'Express Bus Terminal' },
  { id: '322', name: '교대', line: '3호선', lineNumber: '3', englishName: 'Gyodae' },
  { id: '323', name: '남부터미널', line: '3호선', lineNumber: '3', englishName: 'Nambu Bus Terminal' },
  { id: '324', name: '양재', line: '3호선', lineNumber: '3', englishName: 'Yangjae' },
  { id: '325', name: '매봉', line: '3호선', lineNumber: '3', englishName: 'Maebong' },
  { id: '326', name: '도곡', line: '3호선', lineNumber: '3', englishName: 'Dogok' },
  { id: '327', name: '대치', line: '3호선', lineNumber: '3', englishName: 'Daechi' },
  { id: '328', name: '학여울', line: '3호선', lineNumber: '3', englishName: 'Hagyeoul' },
  { id: '329', name: '대청', line: '3호선', lineNumber: '3', englishName: 'Daecheong' },
  { id: '330', name: '일원', line: '3호선', lineNumber: '3', englishName: 'Irwon' },
  { id: '331', name: '수서', line: '3호선', lineNumber: '3', englishName: 'Suseo' },

  // 4호선 - 주요역들
  { id: '401', name: '당고개', line: '4호선', lineNumber: '4', englishName: 'Danggogae' },
  { id: '402', name: '상계', line: '4호선', lineNumber: '4', englishName: 'Sanggye' },
  { id: '403', name: '노원', line: '4호선', lineNumber: '4', englishName: 'Nowon' },
  { id: '404', name: '창동', line: '4호선', lineNumber: '4', englishName: 'Changdong' },
  { id: '405', name: '쌍문', line: '4호선', lineNumber: '4', englishName: 'Ssangmun' },
  { id: '406', name: '수유', line: '4호선', lineNumber: '4', englishName: 'Suyu' },
  { id: '407', name: '미아', line: '4호선', lineNumber: '4', englishName: 'Mia' },
  { id: '408', name: '미아사거리', line: '4호선', lineNumber: '4', englishName: 'Mia Samgeori' },
  { id: '409', name: '길음', line: '4호선', lineNumber: '4', englishName: 'Gireum' },
  { id: '410', name: '성신여대입구', line: '4호선', lineNumber: '4', englishName: 'Sungshin Women\'s Univ.' },
  { id: '411', name: '한성대입구', line: '4호선', lineNumber: '4', englishName: 'Hansung Univ.' },
  { id: '412', name: '혜화', line: '4호선', lineNumber: '4', englishName: 'Hyehwa' },
  { id: '413', name: '동대문', line: '4호선', lineNumber: '4', englishName: 'Dongdaemun' },
  { id: '414', name: '동대문역사문화공원', line: '4호선', lineNumber: '4', englishName: 'Dongdaemun History & Culture Park' },
  { id: '415', name: '충무로', line: '4호선', lineNumber: '4', englishName: 'Chungmuro' },
  { id: '416', name: '명동', line: '4호선', lineNumber: '4', englishName: 'Myeong-dong' },
  { id: '417', name: '회현', line: '4호선', lineNumber: '4', englishName: 'Hoehyeon' },
  { id: '418', name: '서울역', line: '4호선', lineNumber: '4', englishName: 'Seoul Station' },
  { id: '419', name: '숙대입구', line: '4호선', lineNumber: '4', englishName: 'Sookmyung Women\'s Univ.' },
  { id: '420', name: '삼각지', line: '4호선', lineNumber: '4', englishName: 'Samgakji' },
  { id: '421', name: '신용산', line: '4호선', lineNumber: '4', englishName: 'Sinyongsan' },
  { id: '422', name: '이촌', line: '4호선', lineNumber: '4', englishName: 'Ichon' },
  { id: '423', name: '동작', line: '4호선', lineNumber: '4', englishName: 'Dongjak' },
  { id: '424', name: '총신대입구', line: '4호선', lineNumber: '4', englishName: 'Chongshin Univ.' },
  { id: '425', name: '사당', line: '4호선', lineNumber: '4', englishName: 'Sadang' },
  { id: '426', name: '남태령', line: '4호선', lineNumber: '4', englishName: 'Namtaeryeong' },

  // 5호선 - 주요역들
  { id: '501', name: '방화', line: '5호선', lineNumber: '5', englishName: 'Banghwa' },
  { id: '502', name: '개화산', line: '5호선', lineNumber: '5', englishName: 'Gaehwasan' },
  { id: '503', name: '김포공항', line: '5호선', lineNumber: '5', englishName: 'Gimpo Airport' },
  { id: '504', name: '송정', line: '5호선', lineNumber: '5', englishName: 'Songjeong' },
  { id: '505', name: '마곡', line: '5호선', lineNumber: '5', englishName: 'Magok' },
  { id: '506', name: '발산', line: '5호선', lineNumber: '5', englishName: 'Balsan' },
  { id: '507', name: '우장산', line: '5호선', lineNumber: '5', englishName: 'Ujangsan' },
  { id: '508', name: '화곡', line: '5호선', lineNumber: '5', englishName: 'Hwagok' },
  { id: '509', name: '까치산', line: '5호선', lineNumber: '5', englishName: 'Kkachisan' },
  { id: '510', name: '신정', line: '5호선', lineNumber: '5', englishName: 'Sinjeong' },
  { id: '511', name: '목동', line: '5호선', lineNumber: '5', englishName: 'Mok-dong' },
  { id: '512', name: '오목교', line: '5호선', lineNumber: '5', englishName: 'Omokgyo' },
  { id: '513', name: '양평', line: '5호선', lineNumber: '5', englishName: 'Yangpyeong' },
  { id: '514', name: '영등포구청', line: '5호선', lineNumber: '5', englishName: 'Yeongdeungpo-gu Office' },
  { id: '515', name: '영등포시장', line: '5호선', lineNumber: '5', englishName: 'Yeongdeungpo Market' },
  { id: '516', name: '신길', line: '5호선', lineNumber: '5', englishName: 'Singil' },
  { id: '517', name: '여의도', line: '5호선', lineNumber: '5', englishName: 'Yeouido' },
  { id: '518', name: '여의나루', line: '5호선', lineNumber: '5', englishName: 'Yeouinaru' },
  { id: '519', name: '마포', line: '5호선', lineNumber: '5', englishName: 'Mapo' },
  { id: '520', name: '공덕', line: '5호선', lineNumber: '5', englishName: 'Gongdeok' },
  { id: '521', name: '애오개', line: '5호선', lineNumber: '5', englishName: 'Aeogae' },
  { id: '522', name: '충정로', line: '5호선', lineNumber: '5', englishName: 'Chungjeongno' },
  { id: '523', name: '서대문', line: '5호선', lineNumber: '5', englishName: 'Seodaemun' },
  { id: '524', name: '광화문', line: '5호선', lineNumber: '5', englishName: 'Gwanghwamun' },
  { id: '525', name: '종로3가', line: '5호선', lineNumber: '5', englishName: 'Jongno 3-ga' },
  { id: '526', name: '을지로4가', line: '5호선', lineNumber: '5', englishName: 'Euljiro 4-ga' },
  { id: '527', name: '동대문역사문화공원', line: '5호선', lineNumber: '5', englishName: 'Dongdaemun History & Culture Park' },
  { id: '528', name: '청구', line: '5호선', lineNumber: '5', englishName: 'Cheonggu' },
  { id: '529', name: '신금고', line: '5호선', lineNumber: '5', englishName: 'Singeumgo' },
  { id: '530', name: '행당', line: '5호선', lineNumber: '5', englishName: 'Haengdang' },
  { id: '531', name: '왕십리', line: '5호선', lineNumber: '5', englishName: 'Wangsimni' },
  { id: '532', name: '마장', line: '5호선', lineNumber: '5', englishName: 'Majang' },
  { id: '533', name: '답십리', line: '5호선', lineNumber: '5', englishName: 'Dapsimni' },
  { id: '534', name: '장한평', line: '5호선', lineNumber: '5', englishName: 'Janghanpyeong' },
  { id: '535', name: '군자', line: '5호선', lineNumber: '5', englishName: 'Gunja' },
  { id: '536', name: '아차산', line: '5호선', lineNumber: '5', englishName: 'Achasan' },
  { id: '537', name: '광나루', line: '5호선', lineNumber: '5', englishName: 'Gwangnaru' },
  { id: '538', name: '천호', line: '5호선', lineNumber: '5', englishName: 'Cheonho' },
  { id: '539', name: '강동', line: '5호선', lineNumber: '5', englishName: 'Gangdong' },
  { id: '540', name: '길동', line: '5호선', lineNumber: '5', englishName: 'Gildong' },
  { id: '541', name: '굽은다리', line: '5호선', lineNumber: '5', englishName: 'Gubeundari' },
  { id: '542', name: '명일', line: '5호선', lineNumber: '5', englishName: 'Myeongil' },
  { id: '543', name: '고덕', line: '5호선', lineNumber: '5', englishName: 'Godeok' },
  { id: '544', name: '상일동', line: '5호선', lineNumber: '5', englishName: 'Sangil-dong' },

  // 6호선 - 주요역들
  { id: '601', name: '응암', line: '6호선', lineNumber: '6', englishName: 'Eungam' },
  { id: '602', name: '역촌', line: '6호선', lineNumber: '6', englishName: 'Yeokchon' },
  { id: '603', name: '불광', line: '6호선', lineNumber: '6', englishName: 'Bulgwang' },
  { id: '604', name: '독바위', line: '6호선', lineNumber: '6', englishName: 'Dokbawi' },
  { id: '605', name: '연신내', line: '6호선', lineNumber: '6', englishName: 'Yeonsinnae' },
  { id: '606', name: '구산', line: '6호선', lineNumber: '6', englishName: 'Gusan' },
  { id: '607', name: '새절', line: '6호선', lineNumber: '6', englishName: 'Saejeol' },
  { id: '608', name: '증산', line: '6호선', lineNumber: '6', englishName: 'Jeungsan' },
  { id: '609', name: '디지털미디어시티', line: '6호선', lineNumber: '6', englishName: 'Digital Media City' },
  { id: '610', name: '월드컵경기장', line: '6호선', lineNumber: '6', englishName: 'World Cup Stadium' },
  { id: '611', name: '마포구청', line: '6호선', lineNumber: '6', englishName: 'Mapo-gu Office' },
  { id: '612', name: '망원', line: '6호선', lineNumber: '6', englishName: 'Mangwon' },
  { id: '613', name: '합정', line: '6호선', lineNumber: '6', englishName: 'Hapjeong' },
  { id: '614', name: '상수', line: '6호선', lineNumber: '6', englishName: 'Sangsu' },
  { id: '615', name: '광흥창', line: '6호선', lineNumber: '6', englishName: 'Gwangheungchang' },
  { id: '616', name: '대흥', line: '6호선', lineNumber: '6', englishName: 'Daeheung' },
  { id: '617', name: '공덕', line: '6호선', lineNumber: '6', englishName: 'Gongdeok' },
  { id: '618', name: '효창공원앞', line: '6호선', lineNumber: '6', englishName: 'Hyochang Park' },
  { id: '619', name: '삼각지', line: '6호선', lineNumber: '6', englishName: 'Samgakji' },
  { id: '620', name: '녹사평', line: '6호선', lineNumber: '6', englishName: 'Noksapyeong' },
  { id: '621', name: '이태원', line: '6호선', lineNumber: '6', englishName: 'Itaewon' },
  { id: '622', name: '한강진', line: '6호선', lineNumber: '6', englishName: 'Hangangjin' },
  { id: '623', name: '버티고개', line: '6호선', lineNumber: '6', englishName: 'Beottigogae' },
  { id: '624', name: '약수', line: '6호선', lineNumber: '6', englishName: 'Yaksu' },
  { id: '625', name: '청구', line: '6호선', lineNumber: '6', englishName: 'Cheonggu' },
  { id: '626', name: '신당', line: '6호선', lineNumber: '6', englishName: 'Sindang' },
  { id: '627', name: '동묘앞', line: '6호선', lineNumber: '6', englishName: 'Dongmyo' },
  { id: '628', name: '창신', line: '6호선', lineNumber: '6', englishName: 'Changsin' },
  { id: '629', name: '보문', line: '6호선', lineNumber: '6', englishName: 'Bomun' },
  { id: '630', name: '안암', line: '6호선', lineNumber: '6', englishName: 'Anam' },
  { id: '631', name: '고려대', line: '6호선', lineNumber: '6', englishName: 'Korea Univ.' },
  { id: '632', name: '월곡', line: '6호선', lineNumber: '6', englishName: 'Wolgok' },
  { id: '633', name: '상월곡', line: '6호선', lineNumber: '6', englishName: 'Sangwolgok' },
  { id: '634', name: '돌곶이', line: '6호선', lineNumber: '6', englishName: 'Dolgoji' },
  { id: '635', name: '석계', line: '6호선', lineNumber: '6', englishName: 'Seokgye' },
  { id: '636', name: '태릉입구', line: '6호선', lineNumber: '6', englishName: 'Taereung' },
  { id: '637', name: '화랑대', line: '6호선', lineNumber: '6', englishName: 'Hwarangdae' },
  { id: '638', name: '봉화산', line: '6호선', lineNumber: '6', englishName: 'Bonghwasan' },

  // 7호선 - 주요역들
  { id: '701', name: '장암', line: '7호선', lineNumber: '7', englishName: 'Jangam' },
  { id: '702', name: '도봉산', line: '7호선', lineNumber: '7', englishName: 'Dobongsan' },
  { id: '703', name: '수락산', line: '7호선', lineNumber: '7', englishName: 'Suraksan' },
  { id: '704', name: '마들', line: '7호선', lineNumber: '7', englishName: 'Madeul' },
  { id: '705', name: '노원', line: '7호선', lineNumber: '7', englishName: 'Nowon' },
  { id: '706', name: '중계', line: '7호선', lineNumber: '7', englishName: 'Junggye' },
  { id: '707', name: '하계', line: '7호선', lineNumber: '7', englishName: 'Hagye' },
  { id: '708', name: '공릉', line: '7호선', lineNumber: '7', englishName: 'Gongneung' },
  { id: '709', name: '태릉입구', line: '7호선', lineNumber: '7', englishName: 'Taereung' },
  { id: '710', name: '먹골', line: '7호선', lineNumber: '7', englishName: 'Meokgol' },
  { id: '711', name: '중화', line: '7호선', lineNumber: '7', englishName: 'Junghwa' },
  { id: '712', name: '상봉', line: '7호선', lineNumber: '7', englishName: 'Sangbong' },
  { id: '713', name: '면목', line: '7호선', lineNumber: '7', englishName: 'Myeonmok' },
  { id: '714', name: '사가정', line: '7호선', lineNumber: '7', englishName: 'Sagajeong' },
  { id: '715', name: '용마산', line: '7호선', lineNumber: '7', englishName: 'Yongmasan' },
  { id: '716', name: '중곡', line: '7호선', lineNumber: '7', englishName: 'Junggok' },
  { id: '717', name: '군자', line: '7호선', lineNumber: '7', englishName: 'Gunja' },
  { id: '718', name: '어린이대공원', line: '7호선', lineNumber: '7', englishName: 'Children\'s Grand Park' },
  { id: '719', name: '건대입구', line: '7호선', lineNumber: '7', englishName: 'Konkuk Univ.' },
  { id: '720', name: '뚝섬유원지', line: '7호선', lineNumber: '7', englishName: 'Ttukseom Resort' },
  { id: '721', name: '청담', line: '7호선', lineNumber: '7', englishName: 'Cheongdam' },
  { id: '722', name: '강남구청', line: '7호선', lineNumber: '7', englishName: 'Gangnam-gu Office' },
  { id: '723', name: '학동', line: '7호선', lineNumber: '7', englishName: 'Hakdong' },
  { id: '724', name: '논현', line: '7호선', lineNumber: '7', englishName: 'Nonhyeon' },
  { id: '725', name: '반포', line: '7호선', lineNumber: '7', englishName: 'Banpo' },
  { id: '726', name: '고속터미널', line: '7호선', lineNumber: '7', englishName: 'Express Bus Terminal' },
  { id: '727', name: '내방', line: '7호선', lineNumber: '7', englishName: 'Naebang' },
  { id: '728', name: '이수', line: '7호선', lineNumber: '7', englishName: 'Isu' },
  { id: '729', name: '남성', line: '7호선', lineNumber: '7', englishName: 'Namseong' },
  { id: '730', name: '숭실대입구', line: '7호선', lineNumber: '7', englishName: 'Soongsil Univ.' },
  { id: '731', name: '상도', line: '7호선', lineNumber: '7', englishName: 'Sangdo' },
  { id: '732', name: '장승배기', line: '7호선', lineNumber: '7', englishName: 'Jangseungbaegi' },
  { id: '733', name: '신대방삼거리', line: '7호선', lineNumber: '7', englishName: 'Sindaebang Samgeori' },
  { id: '734', name: '보라매', line: '7호선', lineNumber: '7', englishName: 'Boramae' },
  { id: '735', name: '신풍', line: '7호선', lineNumber: '7', englishName: 'Sinpung' },
  { id: '736', name: '대림', line: '7호선', lineNumber: '7', englishName: 'Daerim' },
  { id: '737', name: '남구로', line: '7호선', lineNumber: '7', englishName: 'Namguro' },
  { id: '738', name: '가산디지털단지', line: '7호선', lineNumber: '7', englishName: 'Gasan Digital Complex' },

  // 8호선 - 주요역들
  { id: '801', name: '암사', line: '8호선', lineNumber: '8', englishName: 'Amsa' },
  { id: '802', name: '천호', line: '8호선', lineNumber: '8', englishName: 'Cheonho' },
  { id: '803', name: '강동구청', line: '8호선', lineNumber: '8', englishName: 'Gangdong-gu Office' },
  { id: '804', name: '몽촌토성', line: '8호선', lineNumber: '8', englishName: 'Mongchontoseong' },
  { id: '805', name: '잠실', line: '8호선', lineNumber: '8', englishName: 'Jamsil' },
  { id: '806', name: '석촌', line: '8호선', lineNumber: '8', englishName: 'Seokchon' },
  { id: '807', name: '송파', line: '8호선', lineNumber: '8', englishName: 'Songpa' },
  { id: '808', name: '가락시장', line: '8호선', lineNumber: '8', englishName: 'Garak Market' },
  { id: '809', name: '문정', line: '8호선', lineNumber: '8', englishName: 'Munjeong' },
  { id: '810', name: '장지', line: '8호선', lineNumber: '8', englishName: 'Jangji' },
  { id: '811', name: '복정', line: '8호선', lineNumber: '8', englishName: 'Bokjeong' },
  { id: '812', name: '산성', line: '8호선', lineNumber: '8', englishName: 'Sanseong' },
  { id: '813', name: '남한산성입구', line: '8호선', lineNumber: '8', englishName: 'Namhansanseong' },
  { id: '814', name: '단대오거리', line: '8호선', lineNumber: '8', englishName: 'Dandae Ogeori' },
  { id: '815', name: '신흥', line: '8호선', lineNumber: '8', englishName: 'Sinheung' },
  { id: '816', name: '수진', line: '8호선', lineNumber: '8', englishName: 'Sujin' },
  { id: '817', name: '모란', line: '8호선', lineNumber: '8', englishName: 'Moran' },

  // 9호선 - 주요역들
  { id: '901', name: '개화', line: '9호선', lineNumber: '9', englishName: 'Gaehwa' },
  { id: '902', name: '김포공항', line: '9호선', lineNumber: '9', englishName: 'Gimpo Airport' },
  { id: '903', name: '공항시장', line: '9호선', lineNumber: '9', englishName: 'Airport Market' },
  { id: '904', name: '신방화', line: '9호선', lineNumber: '9', englishName: 'Sinbanghwa' },
  { id: '905', name: '마곡나루', line: '9호선', lineNumber: '9', englishName: 'Magoknaru' },
  { id: '906', name: '양천향교', line: '9호선', lineNumber: '9', englishName: 'Yangcheon Hyanggyo' },
  { id: '907', name: '가양', line: '9호선', lineNumber: '9', englishName: 'Gayang' },
  { id: '908', name: '증미', line: '9호선', lineNumber: '9', englishName: 'Jeungmi' },
  { id: '909', name: '등촌', line: '9호선', lineNumber: '9', englishName: 'Deungchon' },
  { id: '910', name: '염창', line: '9호선', lineNumber: '9', englishName: 'Yeomchang' },
  { id: '911', name: '신목동', line: '9호선', lineNumber: '9', englishName: 'Sinmok-dong' },
  { id: '912', name: '선유도', line: '9호선', lineNumber: '9', englishName: 'Seonyudo' },
  { id: '913', name: '당산', line: '9호선', lineNumber: '9', englishName: 'Dangsan' },
  { id: '914', name: '국회의사당', line: '9호선', lineNumber: '9', englishName: 'National Assembly' },
  { id: '915', name: '여의도', line: '9호선', lineNumber: '9', englishName: 'Yeouido' },
  { id: '916', name: '샛강', line: '9호선', lineNumber: '9', englishName: 'Saetgang' },
  { id: '917', name: '노량진', line: '9호선', lineNumber: '9', englishName: 'Noryangjin' },
  { id: '918', name: '노들', line: '9호선', lineNumber: '9', englishName: 'Nodeul' },
  { id: '919', name: '흑석', line: '9호선', lineNumber: '9', englishName: 'Heukseok' },
  { id: '920', name: '동작', line: '9호선', lineNumber: '9', englishName: 'Dongjak' },
  { id: '921', name: '구반포', line: '9호선', lineNumber: '9', englishName: 'Gubanpo' },
  { id: '922', name: '신반포', line: '9호선', lineNumber: '9', englishName: 'Sinbanpo' },
  { id: '923', name: '고속터미널', line: '9호선', lineNumber: '9', englishName: 'Express Bus Terminal' },
  { id: '924', name: '사평', line: '9호선', lineNumber: '9', englishName: 'Sapyeong' },
  { id: '925', name: '신논현', line: '9호선', lineNumber: '9', englishName: 'Sinnonhyeon' },
  { id: '926', name: '언주', line: '9호선', lineNumber: '9', englishName: 'Eonju' },
  { id: '927', name: '선정릉', line: '9호선', lineNumber: '9', englishName: 'Seonjeongneung' },
  { id: '928', name: '삼성중앙', line: '9호선', lineNumber: '9', englishName: 'Samsung Jungang' },
  { id: '929', name: '봉은사', line: '9호선', lineNumber: '9', englishName: 'Bongeunsa' },
  { id: '930', name: '종합운동장', line: '9호선', lineNumber: '9', englishName: 'Sports Complex' },

  // 수인·분당선 (수인분당선) - 주요역들
  { id: '1001', name: '수원', line: '수인분당선', lineNumber: 'SB', englishName: 'Suwon' },
  { id: '1002', name: '매탄권선', line: '수인분당선', lineNumber: 'SB', englishName: 'Maetan Gwonseon' },
  { id: '1003', name: '수원시청', line: '수인분당선', lineNumber: 'SB', englishName: 'Suwon City Hall' },
  { id: '1004', name: '매교', line: '수인분당선', lineNumber: 'SB', englishName: 'Maegyo' },
  { id: '1005', name: '수원역', line: '수인분당선', lineNumber: 'SB', englishName: 'Suwon Station' },
  { id: '1006', name: '기흥', line: '수인분당선', lineNumber: 'SB', englishName: 'Giheung' },
  { id: '1007', name: '영통', line: '수인분당선', lineNumber: 'SB', englishName: 'Yeongtong' },
  { id: '1008', name: '청명', line: '수인분당선', lineNumber: 'SB', englishName: 'Cheongmyeong' },
  { id: '1009', name: '용인시청', line: '수인분당선', lineNumber: 'SB', englishName: 'Yongin City Hall' },
  { id: '1010', name: '구성', line: '수인분당선', lineNumber: 'SB', englishName: 'Guseong' },
  { id: '1011', name: '신갈', line: '수인분당선', lineNumber: 'SB', englishName: 'Singal' },
  { id: '1012', name: '죽전', line: '수인분당선', lineNumber: 'SB', englishName: 'Jukjeon' },
  { id: '1013', name: '보정', line: '수인분당선', lineNumber: 'SB', englishName: 'Bojeong' },
  { id: '1014', name: '구미', line: '수인분당선', lineNumber: 'SB', englishName: 'Gumi' },
  { id: '1015', name: '동천', line: '수인분당선', lineNumber: 'SB', englishName: 'Dongcheon' },
  { id: '1016', name: '수지구청', line: '수인분당선', lineNumber: 'SB', englishName: 'Suji-gu Office' },
  { id: '1017', name: '성복', line: '수인분당선', lineNumber: 'SB', englishName: 'Seongbok' },
  { id: '1018', name: '상현', line: '수인분당선', lineNumber: 'SB', englishName: 'Sanghyeon' },
  { id: '1019', name: '광교중앙', line: '수인분당선', lineNumber: 'SB', englishName: 'Gwanggyo Jungang' },
  { id: '1020', name: '광교', line: '수인분당선', lineNumber: 'SB', englishName: 'Gwanggyo' },
  { id: '1021', name: '야탑', line: '수인분당선', lineNumber: 'SB', englishName: 'Yatap' },
  { id: '1022', name: '이매', line: '수인분당선', lineNumber: 'SB', englishName: 'Imae' },
  { id: '1023', name: '정자', line: '수인분당선', lineNumber: 'SB', englishName: 'Jeongja' },
  { id: '1024', name: '미금', line: '수인분당선', lineNumber: 'SB', englishName: 'Migeum' },
  { id: '1025', name: '오리', line: '수인분당선', lineNumber: 'SB', englishName: 'Ori' },
  { id: '1026', name: '수내', line: '수인분당선', lineNumber: 'SB', englishName: 'Sunae' },
  { id: '1027', name: '서현', line: '수인분당선', lineNumber: 'SB', englishName: 'Seohyeon' },
  { id: '1028', name: '분당', line: '수인분당선', lineNumber: 'SB', englishName: 'Bundang' },
  { id: '1029', name: '가천대', line: '수인분당선', lineNumber: 'SB', englishName: 'Gachon Univ.' },
  { id: '1030', name: '태평', line: '수인분당선', lineNumber: 'SB', englishName: 'Taepyeong' },
  { id: '1031', name: '모란', line: '수인분당선', lineNumber: 'SB', englishName: 'Moran' },
  { id: '1032', name: '야탑', line: '수인분당선', lineNumber: 'SB', englishName: 'Yatap' },
  { id: '1033', name: '복정', line: '수인분당선', lineNumber: 'SB', englishName: 'Bokjeong' },
  { id: '1034', name: '수서', line: '수인분당선', lineNumber: 'SB', englishName: 'Suseo' },
  { id: '1035', name: '대모산입구', line: '수인분당선', lineNumber: 'SB', englishName: 'Daemosan' },
  { id: '1036', name: '개포동', line: '수인분당선', lineNumber: 'SB', englishName: 'Gaepo-dong' },
  { id: '1037', name: '구룡', line: '수인분당선', lineNumber: 'SB', englishName: 'Guryong' },
  { id: '1038', name: '도곡', line: '수인분당선', lineNumber: 'SB', englishName: 'Dogok' },
  { id: '1039', name: '한티', line: '수인분당선', lineNumber: 'SB', englishName: 'Hanti' },
  { id: '1040', name: '선릉', line: '수인분당선', lineNumber: 'SB', englishName: 'Seolleung' },
  { id: '1041', name: '선정릉', line: '수인분당선', lineNumber: 'SB', englishName: 'Seonjeongneung' },
  { id: '1042', name: '강남구청', line: '수인분당선', lineNumber: 'SB', englishName: 'Gangnam-gu Office' },
  { id: '1043', name: '압구정로데오', line: '수인분당선', lineNumber: 'SB', englishName: 'Apgujeong Rodeo' },
  { id: '1044', name: '강남', line: '수인분당선', lineNumber: 'SB', englishName: 'Gangnam' },
  { id: '1045', name: '왕십리', line: '수인분당선', lineNumber: 'SB', englishName: 'Wangsimni' },
];

/**
 * 지하철역 검색 함수
 * 
 * @param query 검색어
 * @param limit 결과 개수 제한 (기본 10개)
 * @returns 검색 결과 배열
 */
export function searchSubwayStations(query: string, limit: number = 10): SubwayStation[] {
  if (!query.trim()) {
    return [];
  }

  const searchTerm = query.toLowerCase().trim();
  
  return SUBWAY_STATIONS
    .filter(station => 
      station.name.toLowerCase().includes(searchTerm) ||
      station.englishName.toLowerCase().includes(searchTerm) ||
      station.line.includes(searchTerm)
    )
    .slice(0, limit);
}

/**
 * 호선별 지하철역 그룹핑
 */
export function getStationsByLine(): Record<string, SubwayStation[]> {
  return SUBWAY_STATIONS.reduce((acc, station) => {
    if (!acc[station.line]) {
      acc[station.line] = [];
    }
    acc[station.line].push(station);
    return acc;
  }, {} as Record<string, SubwayStation[]>);
}

/**
 * 모든 호선 목록
 */
export const SUBWAY_LINES = [
  '1호선', '2호선', '3호선', '4호선', '5호선', 
  '6호선', '7호선', '8호선', '9호선', '수인분당선'
];

/**
 * 인기 지하철역 목록 (사용 빈도 높은 주요역)
 */
export const POPULAR_STATIONS = [
  '강남', '홍대입구', '신촌', '잠실', '공덕', '사당', '여의도', '선릉'
];

/**
 * 환승역 정보를 통합한 SubwayStation 배열 반환
 * 같은 역명의 여러 호선을 하나로 합침
 */
export function getUnifiedStations(): SubwayStation[] {
  const stationMap = new Map<string, SubwayStation>();

  SUBWAY_STATIONS.forEach(station => {
    const existing = stationMap.get(station.name);
    
    if (existing) {
      // 기존 역이 있으면 호선 정보를 통합
      const existingLines = existing.lines || [existing.line];
      if (!existingLines.includes(station.line)) {
        existing.lines = [...existingLines, station.line];
        existing.line = existing.lines.join(', '); // 표시용
      }
    } else {
      // 새로운 역 추가
      stationMap.set(station.name, {
        ...station,
        lines: [station.line]
      });
    }
  });

  return Array.from(stationMap.values());
}

/**
 * 인기역 목록을 통합된 형태로 반환
 */
export function getPopularStations(): SubwayStation[] {
  const unifiedStations = getUnifiedStations();
  return POPULAR_STATIONS
    .map(stationName => unifiedStations.find(station => station.name === stationName))
    .filter((station): station is SubwayStation => station !== undefined);
}

/**
 * 환승역을 고려한 지하철역 검색 함수
 */
export function searchUnifiedSubwayStations(query: string, limit: number = 10): SubwayStation[] {
  if (!query.trim()) {
    return [];
  }

  const searchTerm = query.toLowerCase().trim();
  const unifiedStations = getUnifiedStations();
  
  return unifiedStations
    .filter(station => 
      station.name.toLowerCase().includes(searchTerm) ||
      station.englishName.toLowerCase().includes(searchTerm) ||
      station.line.toLowerCase().includes(searchTerm)
    )
    .slice(0, limit);
}