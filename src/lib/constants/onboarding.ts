/**
 * 온보딩 관련 상수 및 타입 정의
 * 
 * @description
 * - 온보딩 과정에서 사용되는 모든 상수값들
 * - TypeScript 타입 정의
 * - Zod 스키마 (유효성 검사용)
 */

import { z } from 'zod';
import { type SubwayStation } from '@/lib/data/subway-stations';

// ===== 운동 목표 관련 =====

export const FITNESS_GOALS = {
  BASIC_FITNESS: '기초체력',
  STRENGTH_BUILDING: '근력강화', 
  POSTURE_CORRECTION: '체형교정',
  WEIGHT_LOSS: '체중감량',
  REHABILITATION: '재활',
  OTHER: '기타',
} as const;

export const FITNESS_GOALS_OPTIONS = [
  { value: 'BASIC_FITNESS', label: FITNESS_GOALS.BASIC_FITNESS, icon: '💪', description: '기초적인 체력과 지구력 향상' },
  { value: 'STRENGTH_BUILDING', label: FITNESS_GOALS.STRENGTH_BUILDING, icon: '🏋️', description: '근력과 근지구력 강화' },
  { value: 'POSTURE_CORRECTION', label: FITNESS_GOALS.POSTURE_CORRECTION, icon: '🧘', description: '바른 자세와 체형 교정' },
  { value: 'WEIGHT_LOSS', label: FITNESS_GOALS.WEIGHT_LOSS, icon: '⚖️', description: '건강한 체중 감량' },
  { value: 'REHABILITATION', label: FITNESS_GOALS.REHABILITATION, icon: '🏥', description: '부상 회복 및 재활' },
  { value: 'OTHER', label: FITNESS_GOALS.OTHER, icon: '🎯', description: '기타 개인적인 목표' },
] as const;

// ===== 운동 종목 관련 =====

export const SPECIALTY_OPTIONS = [
  { value: 'YOGA', label: '요가', icon: '🧘‍♀️', description: '몸과 마음의 균형' },
  { value: 'PILATES', label: '필라테스', icon: '🤸‍♀️', description: '코어 강화와 유연성' },
  { value: 'FITNESS', label: '헬스/웨이트', icon: '💪', description: '근력 운동과 체력 증진' },
  { value: 'CROSSFIT', label: '크로스핏', icon: '🏋️‍♀️', description: '고강도 기능성 운동' },
  { value: 'SWIMMING', label: '수영', icon: '🏊‍♀️', description: '전신 유산소 운동' },
  { value: 'BOXING', label: '복싱', icon: '🥊', description: '격투기와 체력 단련' },
  { value: 'DANCE', label: '댄스', icon: '💃', description: '리듬감과 표현력' },
  { value: 'RUNNING', label: '러닝', icon: '🏃‍♀️', description: '지구력과 심폐 기능' },
  { value: 'CLIMBING', label: '클라이밍', icon: '🧗‍♀️', description: '전신 근력과 문제 해결' },
  { value: 'MARTIAL_ARTS', label: '무술/격투기', icon: '🥋', description: '호신술과 정신 수양' },
] as const;

// ===== 요일 관련 =====

export const DAYS_OF_WEEK = {
  MONDAY: 'MONDAY',
  TUESDAY: 'TUESDAY', 
  WEDNESDAY: 'WEDNESDAY',
  THURSDAY: 'THURSDAY',
  FRIDAY: 'FRIDAY',
  SATURDAY: 'SATURDAY',
  SUNDAY: 'SUNDAY',
} as const;

export const DAYS_OPTIONS = [
  { value: 'MONDAY', label: '월요일', shortLabel: '월' },
  { value: 'TUESDAY', label: '화요일', shortLabel: '화' },
  { value: 'WEDNESDAY', label: '수요일', shortLabel: '수' },
  { value: 'THURSDAY', label: '목요일', shortLabel: '목' },
  { value: 'FRIDAY', label: '금요일', shortLabel: '금' },
  { value: 'SATURDAY', label: '토요일', shortLabel: '토' },
  { value: 'SUNDAY', label: '일요일', shortLabel: '일' },
] as const;

// ===== 시간대 관련 =====

export const TIME_SLOTS = {
  DAWN: '새벽',      // 05:00-09:00
  MORNING: '오전',   // 09:00-12:00 
  AFTERNOON: '오후', // 12:00-18:00
  EVENING: '저녁',   // 18:00-22:00
  LATE_NIGHT: '심야', // 22:00-05:00
} as const;

export const TIME_SLOTS_OPTIONS = [
  { value: 'DAWN', label: TIME_SLOTS.DAWN, time: '05:00-09:00', icon: '🌅' },
  { value: 'MORNING', label: TIME_SLOTS.MORNING, time: '09:00-12:00', icon: '🌞' },
  { value: 'AFTERNOON', label: TIME_SLOTS.AFTERNOON, time: '12:00-18:00', icon: '☀️' },
  { value: 'EVENING', label: TIME_SLOTS.EVENING, time: '18:00-22:00', icon: '🌆' },
  { value: 'LATE_NIGHT', label: TIME_SLOTS.LATE_NIGHT, time: '22:00-05:00', icon: '🌙' },
] as const;

// ===== 운동 수준 관련 =====

export const FITNESS_LEVELS = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced', 
  ALL_LEVELS: 'all_levels',
} as const;

export const FITNESS_LEVEL_OPTIONS = [
  { value: 'beginner', label: '초급자', description: '운동을 처음 시작하거나 경험이 적은 분', icon: '🌱' },
  { value: 'intermediate', label: '중급자', description: '어느 정도 운동 경험이 있는 분', icon: '🌿' },
  { value: 'advanced', label: '고급자', description: '충분한 운동 경험과 실력을 갖춘 분', icon: '🌳' },
  { value: 'all_levels', label: '모든 레벨', description: '수준에 관계없이 누구나 참여 가능', icon: '🌈' },
] as const;

// ===== 성별 관련 =====

export const GENDER_OPTIONS = [
  { value: 'MALE', label: '남성', icon: '👨' },
  { value: 'FEMALE', label: '여성', icon: '👩' },
  { value: 'OTHER', label: '기타', icon: '👤' },
] as const;

// ===== 온보딩 단계 관련 =====

export const ONBOARDING_STEPS = {
  GOALS: 1,      // 운동 목표
  LOCATION: 2,   // 희망 지역
  GENDER: 3,     // 성별
  SPECIALTY: 4,  // 운동 종목
  SCHEDULE: 5,   // 요일/시간대
  LEVEL: 6,      // 수준
} as const;

export const ONBOARDING_STEP_INFO = {
  [ONBOARDING_STEPS.GOALS]: {
    title: '운동 목표',
    description: '어떤 목표로 운동을 시작하시나요?',
    subtitle: '선택사항 • 여러 개 선택 가능해요',
  },
  [ONBOARDING_STEPS.LOCATION]: {
    title: '희망 지역',
    description: '어느 지역에서 운동하고 싶으신가요?',
    subtitle: '선택사항 • 지하철역 기준으로 선택해주세요',
  },
  [ONBOARDING_STEPS.GENDER]: {
    title: '성별',
    description: '성별을 선택해주세요',
    subtitle: '선택사항 • 강사 매칭 시 참고됩니다',
  },
  [ONBOARDING_STEPS.SPECIALTY]: {
    title: '운동 종목',
    description: '어떤 운동에 관심이 있으신가요?',
    subtitle: '선택사항 • 여러 개 선택 가능해요',
  },
  [ONBOARDING_STEPS.SCHEDULE]: {
    title: '선호 일정',
    description: '언제 운동하시는 게 좋을까요?',
    subtitle: '선택사항 • 요일과 시간대를 선택해주세요',
  },
  [ONBOARDING_STEPS.LEVEL]: {
    title: '운동 수준',
    description: '현재 운동 수준은 어느 정도인가요?',
    subtitle: '선택사항 • 레벨에 맞는 클래스를 추천해드려요',
  },
} as const;

// ===== TypeScript 타입 정의 =====

export type FitnessGoal = keyof typeof FITNESS_GOALS;
export type DayOfWeek = keyof typeof DAYS_OF_WEEK;
export type TimeSlot = keyof typeof TIME_SLOTS;
export type FitnessLevel = keyof typeof FITNESS_LEVELS;
export type OnboardingStep = typeof ONBOARDING_STEPS[keyof typeof ONBOARDING_STEPS];

// ===== 온보딩 데이터 인터페이스 =====

export interface OnboardingData {
  // Step 1: 운동 목표 (선택사항)
  fitnessGoals?: string[];
  
  // Step 2: 희망 지역 (선택사항)
  preferredStations?: SubwayStation[];
  
  // Step 3: 성별 (선택사항)
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  
  // Step 4: 운동 종목 (선택사항)
  preferredSpecialties?: string[];
  
  // Step 5: 요일/시간대 (선택사항)
  preferredDays?: string[];
  preferredTimeSlots?: string[];
  
  // Step 6: 운동 수준 (선택사항)
  fitnessLevel?: 'beginner' | 'intermediate' | 'advanced' | 'all_levels';
}

export interface OnboardingStatus {
  isCompleted: boolean;
  completedAt?: string;
  currentStep?: number;
}

// ===== Zod 스키마 정의 (유효성 검사) =====

export const OnboardingDataSchema = z.object({
  fitnessGoals: z.array(z.string()).optional(),
  
  preferredStations: z.array(z.object({
    id: z.string(),
    name: z.string(), 
    line: z.string(),
    lineNumber: z.string(),
    englishName: z.string(),
  })).optional(),
  
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']).optional(),
  
  preferredSpecialties: z.array(z.string()).optional(),
  
  preferredDays: z.array(z.string()).optional(),
  
  preferredTimeSlots: z.array(z.string()).optional(),
  
  fitnessLevel: z.enum(['beginner', 'intermediate', 'advanced', 'all_levels']).optional(),
});

// ===== 단계별 유효성 검사 스키마 (모든 단계 선택사항) =====

export const StepSchemas = {
  [ONBOARDING_STEPS.GOALS]: z.object({
    fitnessGoals: OnboardingDataSchema.shape.fitnessGoals,
  }),
  
  [ONBOARDING_STEPS.LOCATION]: z.object({
    preferredStations: OnboardingDataSchema.shape.preferredStations,
  }),
  
  [ONBOARDING_STEPS.GENDER]: z.object({
    gender: OnboardingDataSchema.shape.gender,
  }),
  
  [ONBOARDING_STEPS.SPECIALTY]: z.object({
    preferredSpecialties: OnboardingDataSchema.shape.preferredSpecialties,
  }),
  
  [ONBOARDING_STEPS.SCHEDULE]: z.object({
    preferredDays: OnboardingDataSchema.shape.preferredDays,
    preferredTimeSlots: OnboardingDataSchema.shape.preferredTimeSlots,
  }),
  
  [ONBOARDING_STEPS.LEVEL]: z.object({
    fitnessLevel: OnboardingDataSchema.shape.fitnessLevel,
  }),
} as const;

// ===== 로컬 스토리지 키 =====

export const STORAGE_KEYS = {
  ONBOARDING_DATA: 'member_onboarding_data',
  CURRENT_STEP: 'member_onboarding_step',
  ONBOARDING_COMPLETED: 'shallwe_onboarding_completed',
} as const;