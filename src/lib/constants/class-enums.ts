// 클래스 관련 enum 매핑 상수들

export const TARGET_AUDIENCE_MAP: Record<string, string> = {
  BEGINNER: '운동을 처음 시작하는 분',
  WANT_SAFE_EXERCISE: '안전한 운동을 배우고 싶은 분',
  BASIC_SKILLS: '기본 호흡법 및 동작부터 배우고 싶은 분',
  FLEXIBILITY: '유연성 향상을 원하는 분',
  STRENGTH: '근력 강화를 원하는 분',
  STRESS_RELIEF: '스트레스 해소를 원하는 분',
  POSTURE_CORRECTION: '자세 교정을 원하는 분',
  WEIGHT_LOSS: '체중 감량을 원하는 분',
  INJURY_RECOVERY: '부상 회복을 위한 분',
  ADVANCED_SKILL: '고급 기술을 배우고 싶은 분',
};

export const SPECIALTY_MAP: Record<string, string> = {
  YOGA: '요가',
  PILATES: '필라테스',
  FITNESS: '피트니스',
  CROSSFIT: '크로스핏',
  SWIMMING: '수영',
  BOXING: '복싱',
  DANCE: '댄스',
  RUNNING: '러닝',
  CLIMBING: '클라이밍',
  MARTIAL_ARTS: '무술',
};

export const DIFFICULTY_LEVEL_MAP: Record<string, string> = {
  BEGINNER: '초급',
  INTERMEDIATE: '중급',
  ADVANCED: '고급',
  ALL_LEVELS: '전체',
};

export const GENDER_PREFERENCE_MAP: Record<string, string> = {
  MIXED: '혼성',
  MALE: '남성만',
  FEMALE: '여성만',
};

export const DAY_OF_WEEK_MAP: Record<string, string> = {
  MONDAY: '월요일',
  TUESDAY: '화요일',
  WEDNESDAY: '수요일',
  THURSDAY: '목요일',
  FRIDAY: '금요일',
  SATURDAY: '토요일',
  SUNDAY: '일요일',
};

export const CLASS_STATUS_MAP: Record<string, string> = {
  upcoming: '시작예정',
  recruiting: '모집중',
  ongoing: '진행중',
  completed: '완료',
  cancelled: '취소',
};

export const ENROLLMENT_STATUS_MAP: Record<string, string> = {
  pending: '결제대기',
  confirmed: '확정',
  cancelled: '취소',
  completed: '완료',
  waitlisted: '대기중',
};

export const MATERIAL_MAP: Record<string, string> = {
  YOGA_MAT: '요가 매트',
  TOWEL: '수건',
  WATER_BOTTLE: '물병',
  COMFORTABLE_CLOTHES: '편안한 운동복',
  SPORTS_SHOES: '운동화',
  YOGA_BLOCK: '요가 블록',
  RESISTANCE_BAND: '저항 밴드',
  DUMBBELLS: '덤벨',
  YOGA_STRAP: '요가 스트랩',
  MEDITATION_CUSHION: '명상 쿠션',
};

// 옵션 배열 생성을 위한 헬퍼 함수들
export const TARGET_AUDIENCE_OPTIONS = Object.entries(TARGET_AUDIENCE_MAP).map(
  ([value, label]) => ({
    value,
    label,
  })
);

export const SPECIALTY_OPTIONS = Object.entries(SPECIALTY_MAP).map(
  ([value, label]) => ({
    value,
    label,
  })
);

export const DIFFICULTY_LEVEL_OPTIONS = Object.entries(
  DIFFICULTY_LEVEL_MAP
).map(([value, label]) => ({
  value,
  label,
}));

export const GENDER_PREFERENCE_OPTIONS = Object.entries(
  GENDER_PREFERENCE_MAP
).map(([value, label]) => ({
  value,
  label,
}));

export const DAY_OF_WEEK_OPTIONS = Object.entries(DAY_OF_WEEK_MAP).map(
  ([value, label]) => ({
    value,
    label,
  })
);

export const MATERIAL_OPTIONS = Object.entries(MATERIAL_MAP).map(
  ([value, label]) => ({
    value,
    label,
  })
);
