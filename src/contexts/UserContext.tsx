// 'use client';

// import React, { createContext, useContext, useEffect, useState } from 'react';
// import { User } from '@supabase/supabase-js';
// import {
//   getCurrentUser,
//   getUserRole,
//   onAuthStateChange,
// } from '@/lib/supabase/auth';

// /**
//  * UserContext는 전역적으로 사용자 정보와 역할을 관리하는 React Context입니다.
//  *
//  * 주요 목적:
//  * 1. 중복 API 호출 방지: 여러 컴포넌트에서 각각 사용자 정보를 가져오는 대신 한 곳에서 관리
//  * 2. 성능 최적화: 사용자 정보를 한 번만 가져와서 모든 컴포넌트가 공유
//  * 3. 일관성 보장: 모든 컴포넌트가 동일한 사용자 정보를 참조
//  *
//  * 제공하는 정보:
//  * - user: 현재 로그인한 사용자 정보 (Supabase User 객체)
//  * - role: 사용자 역할 ('STUDENT', 'INSTRUCTOR', 'ADMIN' 등)
//  * - loading: 사용자 정보를 가져오는 중인지 여부
//  * - refreshUser: 사용자 정보를 수동으로 새로고침하는 함수
//  */
// interface UserContextType {
//   user: User | null;
//   role: string | null;
//   loading: boolean;
//   refreshUser: () => Promise<void>;
// }
// const UserContext = createContext<UserContextType | null>(null);

// export function UserProvider({ children }: { children: React.ReactNode }) {
//   const [user, setUser] = useState<User | null>(null);
//   const [role, setRole] = useState<string | null>(null);
//   const [loading, setLoading] = useState(true);

//   const loadUserData = async () => {
//     try {
//       const currentUser = await getCurrentUser();
//       setUser(currentUser);

//       if (currentUser) {
//         const userRole = await getUserRole();
//         setRole(userRole);
//       } else {
//         setRole(null);
//       }
//     } catch (error) {
//       console.error('사용자 데이터 로드 오류:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   useEffect(() => {
//     // 초기 로드
//     loadUserData();

//     // Auth 상태 변경 감지
//     const {
//       data: { subscription },
//     } = onAuthStateChange(async newUser => {
//       setUser(newUser);
//       if (newUser) {
//         const userRole = await getUserRole();
//         setRole(userRole);
//       } else {
//         setRole(null);
//       }
//     });

//     return () => {
//       subscription.unsubscribe();
//     };
//   }, []);

//   const refreshUser = async () => {
//     await loadUserData();
//   };

//   return (
//     <UserContext.Provider value={{ user, role, loading, refreshUser }}>
//       {children}
//     </UserContext.Provider>
//   );
// }

// export function useUser() {
//   const context = useContext(UserContext);
//   if (!context) {
//     throw new Error('useUser must be used within a UserProvider');
//   }
//   return context;
// }
