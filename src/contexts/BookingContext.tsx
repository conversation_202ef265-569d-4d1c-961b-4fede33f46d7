'use client';

import { createContext, useContext, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { payDeposit, cancelBooking } from '@/lib/api/profile';

import type { Booking } from '@/schemas/profile';

type BookingData = Booking;

interface BookingContextType {
  selectedBooking: BookingData | null;
  isPaymentModalOpen: boolean;
  error: string | null;
  openPaymentModal: (booking: BookingData) => void;
  closePaymentModal: () => void;
  handlePayDeposit: (bookingId: string) => Promise<void>;
  handleCancelBooking: (bookingId: string) => Promise<void>;
  handleViewDetails: (bookingId: string) => void;
  setError: (error: string | null) => void;
}

const BookingContext = createContext<BookingContextType | undefined>(undefined);

export function BookingProvider({ children }: { children: ReactNode }) {
  const [selectedBooking, setSelectedBooking] = useState<BookingData | null>(null);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const openPaymentModal = (booking: BookingData) => {
    // payment 페이지로 이동 (쿼리 파라미터로 booking 정보 전달)
    router.push(`/payment?bookingId=${booking.id}&classTitle=${encodeURIComponent(booking.classTitle)}&price=${booking.price}`);
  };

  const closePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setSelectedBooking(null);
  };

  const handlePayDeposit = async (bookingId: string) => {
    try {
      const message = await payDeposit(bookingId);
      console.log('Pay deposit success:', message);
      closePaymentModal();
      window.location.reload();
    } catch (err) {
      console.error('Pay deposit error:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to process payment'
      );
    }
  };

  const handleCancelBooking = async (bookingId: string) => {
    try {
      const message = await cancelBooking(bookingId);
      console.log('Cancel booking success:', message);
    } catch (err) {
      console.error('Cancel booking error:', err);
      setError(err instanceof Error ? err.message : 'Failed to cancel booking');
    }
  };

  const handleViewDetails = (bookingId: string) => {
    console.log('View booking details:', bookingId);
  };

  return (
    <BookingContext.Provider
      value={{
        selectedBooking,
        isPaymentModalOpen,
        error,
        openPaymentModal,
        closePaymentModal,
        handlePayDeposit,
        handleCancelBooking,
        handleViewDetails,
        setError,
      }}
    >
      {children}
    </BookingContext.Provider>
  );
}

export function useBooking() {
  const context = useContext(BookingContext);
  if (context === undefined) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
}