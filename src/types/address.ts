export interface DaumAddressData {
  /** 우편번호 */
  zonecode: string;
  /** 주소 (도로명 주소) */
  address: string;
  /** 지번 주소 */
  jibunAddress: string;
  /** 도로명 주소 */
  roadAddress: string;
  /** 영문 주소 */
  englishAddress: string;
  /** 건물명 */
  buildingName: string;
  /** 아파트 여부 */
  apartment: 'Y' | 'N';
  /** 사용자 선택 여부 */
  userSelectedType: 'R' | 'J'; // R: 도로명, J: 지번
  /** 도/시 */
  sido: string;
  /** 시/군/구 */
  sigungu: string;
  /** 법정동/법정리 */
  bname: string;
  /** 법정동/법정리 */
  bname1: string;
  /** 법정리의 읍/면 이름 */
  bname2: string;
  /** 행정동 */
  hname: string;
  /** 도로명 */
  roadname: string;
  /** 건물번호 */
  buildingCode: string;
}

export interface Coordinates {
  /** 위도 */
  latitude: number;
  /** 경도 */
  longitude: number;
}

export interface AddressWithCoordinates extends DaumAddressData {
  /** 좌표 정보 */
  coordinates?: Coordinates;
}

export interface AddressSearchProps {
  /** 주소 선택 완료 콜백 */
  onComplete: (data: AddressWithCoordinates) => void;
  /** 검색창 플레이스홀더 */
  placeholder?: string;
  /** 버튼 텍스트 */
  buttonText?: string;
  /** 에러 메시지 */
  error?: string;
  /** 선택된 주소 값 */
  value?: string;
  /** 비활성화 여부 */
  disabled?: boolean;
  /** 크기 */
  size?: 'sm' | 'md' | 'lg';
  /** 좌표 정보도 함께 가져올지 여부 */
  includeCoordinates?: boolean;
  /** 팝업 테마 */
  theme?: {
    bgColor?: string;
    searchBgColor?: string;
    contentBgColor?: string;
    pageBgColor?: string;
    textColor?: string;
    queryTextColor?: string;
    postcodeTextColor?: string;
    emphTextColor?: string;
    outlineColor?: string;
  };
}

declare global {
  interface Window {
    daum: {
      Postcode: new (options: {
        oncomplete: (data: DaumAddressData) => void;
        onresize?: (size: { width: number; height: number }) => void;
        onclose?: (state: 'FORCE_CLOSE' | 'COMPLETE_CLOSE') => void;
        onsearch?: (data: { query: string; count: number }) => void;
        width?: number;
        height?: number;
        animation?: boolean;
        focusInput?: boolean;
        focusContent?: boolean;
        pleaseReadGuide?: number;
        pleaseReadGuideTimer?: number;
        maxSuggestItems?: number;
        showMoreHName?: boolean;
        hideMapBtn?: boolean;
        hideEngBtn?: boolean;
        alwaysShowEngAddr?: boolean;
        submitMode?: boolean;
        autoMapping?: boolean;
        autoMappingNumpad?: boolean;
        shorthand?: boolean;
        useBannerLink?: boolean;
        useSuggest?: boolean;
        theme?: AddressSearchProps['theme'];
      }) => {
        open: (options?: {
          q?: string;
          left?: number;
          top?: number;
          popupTitle?: string;
          popupKey?: string;
          autoClose?: boolean;
        }) => void;
        embed: (
          element: HTMLElement,
          options?: {
            q?: string;
            autoClose?: boolean;
          }
        ) => void;
      };
    };
  }
}
