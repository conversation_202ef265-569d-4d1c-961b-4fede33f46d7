/**
 * 파트너 대시보드 관련 TypeScript 타입 정의
 * 
 * @description
 * - 파트너 대시보드 데이터 구조
 * - 클래스 및 수업 관련 타입
 * - API 요청/응답 타입
 * - 컴포넌트 Props 타입
 */

// ===== 기본 데이터 타입 =====

/**
 * 클래스 상태 타입
 */
export type ClassStatus = 'recruiting' | 'ongoing' | 'completed' | 'upcoming' | 'cancelled';

/**
 * 수업(occurrence) 상태 타입  
 */
export type OccurrenceStatus = 'pending' | 'confirmed' | 'ongoing' | 'completed' | 'cancelled';

/**
 * 클래스 상태별 통계
 */
export interface ClassStatusStats {
  recruiting: number;
  ongoing: number;
  completed: number;
  upcoming: number;
}

/**
 * 월간 통계 지표
 */
export interface MonthlyStats {
  totalClasses: number;      // 이번 달 총 수업 수
  totalAttendance: number;   // 총 출석 수
  completionRate: number;    // 완료율 (%)
}

/**
 * 예약 및 수업료 통계
 */
export interface BookingStats {
  totalBookings: number;     // 총 예약 건수
  expectedTuition: number;   // 예상 수업료 (현장 결제 예정)
  completedTuition: number;  // 완료 수업료 (실제 완료)
}

/**
 * 스튜디오 현황 통계
 */
export interface StudioStats {
  total: number;       // 총 스튜디오 수
  active: number;      // 활성 스튜디오 수  
  classes: number;     // 활성 클래스 수
  instructors: number; // 강사 수
}

/**
 * 수업 발생(occurrence) 정보
 */
export interface ClassOccurrence {
  id: string;
  classTemplateId: string;
  title: string;
  date: string;
  startTime: string;
  endTime: string;
  instructor: string;
  students: number;
  maxStudents: number;
  status: OccurrenceStatus;
  studioName: string;
  groupName?: string;
  notes?: string;
}

/**
 * 알림 정보
 */
export interface NotificationItem {
  id: string;
  type: 'booking' | 'review' | 'payment' | 'cancellation';
  message: string;
  createdAt: string;
  read: boolean;
}

// ===== 파트너 대시보드 메인 데이터 =====

/**
 * 파트너 대시보드 전체 데이터
 */
export interface PartnerDashboardData {
  partner: {
    id: string;
    name: string;
    totalClasses: number;    // 총 클래스 수
    monthlyClasses: number;  // 이번 달 수업 수  
  };
  classStats: ClassStatusStats;
  monthlyStats: MonthlyStats;
  bookingStats: BookingStats;
  studioStats: StudioStats;
  todayClasses: ClassOccurrence[];
  thisWeekClasses: ClassOccurrence[];
  nextWeekClasses: ClassOccurrence[];
  notifications: NotificationItem[];
}

// ===== API 요청/응답 타입 =====

/**
 * 대시보드 데이터 조회 쿼리 파라미터
 */
export interface PartnerDashboardQuery {
  timezone?: string; // 시간대 (예: 'Asia/Seoul', 기본값)
}

/**
 * 대시보드 데이터 조회 성공 응답
 */
export interface PartnerDashboardSuccessResponse {
  success: true;
  data: PartnerDashboardData;
}

/**
 * 대시보드 데이터 조회 실패 응답  
 */
export interface PartnerDashboardErrorResponse {
  success: false;
  message: string;
  error?: string;
}

/**
 * 대시보드 데이터 조회 응답 (유니온 타입)
 */
export type PartnerDashboardResponse = 
  | PartnerDashboardSuccessResponse 
  | PartnerDashboardErrorResponse;

// ===== 컴포넌트 Props 타입 =====

/**
 * ProfileCard 컴포넌트 Props
 */
export interface ProfileCardProps {
  partnerName: string;
  totalClasses: number;
  monthlyClasses: number;
  classStats: ClassStatusStats;
  monthlyStats: MonthlyStats;
  onStatusClick: (status: ClassStatus) => void;
  isLoading?: boolean;
}

/**
 * 빠른 액션 버튼 데이터 (간소화됨)
 */
export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
}

/**
 * QuickActionGrid 컴포넌트 Props (props 제거됨)
 */
export interface QuickActionGridProps {
  isLoading?: boolean;
}

/**
 * TodayClasses 컴포넌트 Props
 */
export interface TodayClassesProps {
  classes: ClassOccurrence[];
  onClassClick: (classId: string) => void;
  isLoading?: boolean;
}

/**
 * WeeklySchedule 컴포넌트 Props
 */
export interface WeeklyScheduleProps {
  title: string;
  classes: ClassOccurrence[];
  onClassClick: (classId: string) => void;
  onViewAll: () => void;
  showViewAll?: boolean;
  isLoading?: boolean;
}

/**
 * EmptyState 컴포넌트 Props
 */
export interface EmptyStateProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  actionButton?: {
    text: string;
    href: string;
  };
}

// ===== 상태 관리 타입 =====

/**
 * 대시보드 페이지 상태
 */
export interface DashboardPageState {
  data: PartnerDashboardData | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

/**
 * 대시보드 액션 타입
 */
export type DashboardAction = 
  | { type: 'FETCH_START' }
  | { type: 'FETCH_SUCCESS'; payload: PartnerDashboardData }
  | { type: 'FETCH_ERROR'; payload: string }
  | { type: 'REFRESH_START' }
  | { type: 'CLEAR_ERROR' };

// ===== 유틸리티 타입 =====

/**
 * 클래스 상태별 표시 텍스트
 */
export const ClassStatusDisplayText: Record<ClassStatus, string> = {
  recruiting: '모집중',
  ongoing: '진행중', 
  completed: '완료',
  upcoming: '시작예정',
  cancelled: '취소',
} as const;

/**
 * 클래스 상태별 색상 클래스 (Tailwind CSS)
 */
export const ClassStatusColorClass: Record<ClassStatus, string> = {
  recruiting: 'bg-blue-100 text-blue-800',
  ongoing: 'bg-green-100 text-green-800',
  completed: 'bg-gray-100 text-gray-800', 
  upcoming: 'bg-yellow-100 text-yellow-800',
  cancelled: 'bg-red-100 text-red-800',
} as const;

/**
 * 수업 상태별 표시 텍스트
 */
export const OccurrenceStatusDisplayText: Record<OccurrenceStatus, string> = {
  pending: '대기중',
  confirmed: '확정',
  ongoing: '진행중',
  completed: '완료', 
  cancelled: '취소',
} as const;

/**
 * 수업 상태별 색상 클래스 (Tailwind CSS)
 */
export const OccurrenceStatusColorClass: Record<OccurrenceStatus, string> = {
  pending: 'bg-yellow-100 text-yellow-800',
  confirmed: 'bg-blue-100 text-blue-800',
  ongoing: 'bg-green-100 text-green-800',
  completed: 'bg-gray-100 text-gray-800',
  cancelled: 'bg-red-100 text-red-800', 
} as const;

// ===== 타입 가드 함수 =====

/**
 * 대시보드 응답 성공 여부 확인
 */
export function isDashboardSuccess(
  response: PartnerDashboardResponse
): response is PartnerDashboardSuccessResponse {
  return response.success === true;
}

/**
 * 유효한 클래스 상태인지 확인
 */
export function isValidClassStatus(status: string): status is ClassStatus {
  return ['recruiting', 'ongoing', 'completed', 'upcoming', 'cancelled'].includes(status);
}

/**
 * 유효한 수업 상태인지 확인
 */
export function isValidOccurrenceStatus(status: string): status is OccurrenceStatus {
  return ['pending', 'confirmed', 'ongoing', 'completed', 'cancelled'].includes(status);
}

/**
 * 오늘 날짜인지 확인
 */
export function isToday(dateString: string): boolean {
  const today = new Date().toISOString().split('T')[0];
  return dateString === today;
}

/**
 * 빈 대시보드 데이터인지 확인
 */
export function isEmptyDashboard(data: PartnerDashboardData): boolean {
  return (
    data.todayClasses.length === 0 &&
    data.thisWeekClasses.length === 0 &&
    Object.values(data.classStats).every(count => count === 0)
  );
}

// ===== 기본값 상수 =====

/**
 * 빈 대시보드 데이터 기본값
 */
export const EMPTY_DASHBOARD_DATA: PartnerDashboardData = {
  partner: {
    id: '',
    name: '',
    totalClasses: 0,
    monthlyClasses: 0,
  },
  classStats: {
    recruiting: 0,
    ongoing: 0,
    completed: 0,
    upcoming: 0,
  },
  monthlyStats: {
    totalClasses: 0,
    totalAttendance: 0,
    completionRate: 0,
  },
  bookingStats: {
    totalBookings: 0,
    expectedTuition: 0,
    completedTuition: 0,
  },
  studioStats: {
    total: 0,
    active: 0,
    classes: 0,
    instructors: 0,
  },
  todayClasses: [],
  thisWeekClasses: [],
  nextWeekClasses: [],
  notifications: [],
};

/**
 * 대시보드 새로고침 간격 (밀리초)
 */
export const DASHBOARD_REFRESH_INTERVAL = 5 * 60 * 1000; // 5분

/**
 * 대시보드 데이터 캐시 TTL (밀리초)  
 */
export const DASHBOARD_CACHE_TTL = 60 * 1000; // 1분