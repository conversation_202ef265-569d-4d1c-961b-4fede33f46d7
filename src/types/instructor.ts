// 강사 기본 정보 타입
export interface Instructor {
  id: string;
  memberId: string;
  name: string;
  email: string;
  phone: string;
  profileImageUrl?: string;
  shortBio?: string;
  detailedBio?: string;
  experienceYears: number;
  isVerified: boolean;
  isActive: boolean;
  rating?: number;
  totalReviews: number;
  specialties: InstructorSpecialty[];
  certificates: InstructorCertificate[];
  createdAt: string;
  updatedAt: string;
}

// 강사 전문분야 타입
export interface InstructorSpecialty {
  id: string;
  instructorId: string;
  specialty: SpecialtyType;
  experienceYears: number;
  createdAt: string;
}

// 강사 자격증 타입
export interface InstructorCertificate {
  id: string;
  instructorId: string;
  certificateName: string;
  issuingOrganization: string;
  issueDate: string;
  expiryDate?: string;
  certificateNumber?: string;
  imageUrl?: string;
  isVerified: boolean;
  createdAt: string;
}

// 전문분야 타입
export type SpecialtyType =
  | 'YOGA'
  | 'PILATES'
  | 'FITNESS'
  | 'CROSSFIT'
  | 'SWIMMING'
  | 'BOXING'
  | 'DANCE'
  | 'RUNNING'
  | 'CLIMBING'
  | 'MARTIAL_ARTS'
  | 'MEDITATION'
  | 'STRETCHING'
  | 'BARRE'
  | 'SPINNING'
  | 'ZUMBA'
  | 'KICKBOXING'
  | 'THERAPEUTIC';

// 전문분야 한글 라벨 매핑
export const SPECIALTY_LABELS: Record<SpecialtyType, string> = {
  YOGA: '요가',
  PILATES: '필라테스',
  FITNESS: '피트니스',
  CROSSFIT: '크로스핏',
  SWIMMING: '수영',
  BOXING: '복싱',
  DANCE: '댄스',
  RUNNING: '러닝',
  CLIMBING: '클라이밍',
  MARTIAL_ARTS: '무술',
  MEDITATION: '명상',
  STRETCHING: '스트레칭',
  BARRE: '바레',
  SPINNING: '스피닝',
  ZUMBA: '줌바',
  KICKBOXING: '킥복싱',
  THERAPEUTIC: '치료',
};
