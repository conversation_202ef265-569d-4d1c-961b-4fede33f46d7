import { z } from 'zod';
import { PaymentStatusSchema } from './payment';

export const UserStatsSchema = z.object({
  nickname: z.string(),
  avatar: z.string().optional(),
  totalClasses: z.number(),
  attendedClasses: z.number(),
  points: z.number(),
  nextClass: z
    .object({
      title: z.string(),
      date: z.string(),
      time: z.string(),
    })
    .optional(),
});

export const BookingStatusSchema = z.enum([
  'reserved',
  'payment_pending',
  'in_progress',
  'completed',
  'cancelled',
  'recruiting',
]);

export const BookingSchema = z.object({
  id: z.string(),
  classTitle: z.string(),
  coachName: z.string(),
  date: z.string(),
  time: z.string(),
  status: BookingStatusSchema,
  price: z.number(),
  location: z.string(),
  level: z.string().optional(),
  groupSize: z.number().optional(),
  schedule: z.string().optional(),
  paymentDueDate: z.string().optional(),
  payment: z.object({
    status: PaymentStatusSchema,
    method: z.string().optional(),
    transactionId: z.string().optional(),
  }).optional(),
});

export const UserProfileResponseSchema = z.object({
  success: z.boolean(),
  data: UserStatsSchema,
});

export const UserProfileErrorResponseSchema = z.object({
  success: z.literal(false),
  data: z.null(),
});

export const BookingHistoryRequestSchema = z.object({
  status: BookingStatusSchema.optional(),
});

export const BookingHistoryResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    reserved: z.array(BookingSchema),
    inProgress: z.array(BookingSchema),
    completed: z.array(BookingSchema),
  }),
});

export const BookingActionRequestSchema = z.object({
  action: z.enum(['pay_deposit', 'cancel_booking']),
  bookingId: z.string(),
});

export const BookingActionResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
});

export type UserStats = z.infer<typeof UserStatsSchema>;
export type BookingStatus = z.infer<typeof BookingStatusSchema>;
export type Booking = z.infer<typeof BookingSchema>;
export type UserProfileResponse = z.infer<typeof UserProfileResponseSchema>;
export type UserProfileErrorResponse = z.infer<typeof UserProfileErrorResponseSchema>;
export type BookingHistoryRequest = z.infer<typeof BookingHistoryRequestSchema>;
export type BookingHistoryResponse = z.infer<typeof BookingHistoryResponseSchema>;
export type BookingActionRequest = z.infer<typeof BookingActionRequestSchema>;
export type BookingActionResponse = z.infer<typeof BookingActionResponseSchema>;