// import { z } from 'zod';

// // Enrollment status 스키마
// export const EnrollmentStatusSchema = z.enum([
//   'RECRUITMENT_ENDED',
//   'NOT_STARTED',
//   'ONGOING',
//   'COMPLETED',
//   'CANCELLED',
//   'AVAILABLE',
// ]);

// // 강사 전문분야 스키마
// export const InstructorSpecialtySchema = z.object({
//   specialty: z.string(),
//   experienceYears: z.number(),
// });

// // 강사 자격증 스키마
// export const InstructorCertificateSchema = z.object({
//   name: z.string(),
//   issuedBy: z.string(),
//   issuedAt: z.string(),
//   expiresAt: z.string().optional(),
// });

// // 클래스 템플릿 스키마
// export const ClassTemplateSchema = z.object({
//   id: z.string(),
//   title: z.string(),
//   description: z.string(),
//   specialty: z.string(),
//   level: z.string(),
//   durationMinutes: z.number(),
//   pricePerSession: z.number(),
//   curriculum: z.string().optional(),
//   recruitmentEndDate: z.string(),
//   classStartDate: z.string(),
//   classEndDate: z.string(),
//   status: z.string(),
//   maxCapacity: z.number(),
// });

// // 강사 정보 스키마
// export const InstructorSchema = z.object({
//   id: z.string(),
//   name: z.string(),
//   shortBio: z.string().optional(),
//   specialties: z.array(InstructorSpecialtySchema),
//   certificates: z.array(InstructorCertificateSchema),
// });

// // 스튜디오 정보 스키마
// export const StudioSchema = z.object({
//   id: z.string(),
//   name: z.string(),
//   address: z.string(),
//   nearestStation: z.string().optional(),
//   amenities: z.array(z.string()),
//   latitude: z.number(),
//   longitude: z.number(),
//   description: z.string(),
// });

// // 스케줄 스키마
// export const ScheduleSchema = z.object({
//   scheduleGroupId: z.string(),
//   dayOfWeek: z.number(),
//   startTime: z.string(),
//   endTime: z.string(),
// });

// // 스케줄 그룹 스키마
// export const ScheduleGroupSchema = z.object({
//   id: z.string(),
//   groupName: z.string(),
//   maxParticipants: z.number(),
//   pricePerSession: z.number().optional(),
//   schedules: z.array(ScheduleSchema),
//   currentEnrollments: z.number(),
//   availableSpots: z.number(),
// });

// // 신청 정보 스키마
// export const EnrollmentInfoSchema = z.object({
//   isAvailable: z.boolean(),
//   reasonCode: EnrollmentStatusSchema,
//   closeAt: z.string().optional(),
// });

// // 클래스 상세 정보 스키마 (findActiveClassDetailById 반환값)
// export const ClassDetailSchema = z.object({
//   classTemplate: ClassTemplateSchema,
//   instructor: InstructorSchema,
//   studio: StudioSchema,
//   scheduleGroups: z.array(ScheduleGroupSchema),
//   enrollmentInfo: EnrollmentInfoSchema,
// });

// // 클래스 목록 아이템 스키마 (간소화된 버전)
// export const ClassListItemSchema = z.object({
//   classTemplate: z.object({
//     id: z.string(),
//     title: z.string(),
//     category: z.string(),
//     specialty: z.string(),
//     level: z.string(),
//     durationInMinutes: z.number(),
//     pricePerSession: z.number(),
//     maxCapacity: z.number(),
//   }),
//   studio: z.object({
//     id: z.string(),
//     name: z.string(),
//     nearestStation: z.string().nullish(),
//   }),
//   instructor: z.object({
//     id: z.string(),
//     name: z.string(),
//     shortBio: z.string().optional(),
//     specialties: z.array(InstructorSpecialtySchema),
//   }),
//   status: z.string(),
// });

// // 페이지네이션 스키마
// export const PaginationSchema = z.object({
//   page: z.number(),
//   limit: z.number(),
//   total: z.number(),
//   totalPages: z.number(),
// });

// // 클래스 목록 스키마 (findAllActiveClasses 반환값)
// export const ClassListSchema = z.object({
//   data: z.array(ClassListItemSchema),
//   pagination: PaginationSchema,
// });

// // 레벨 옵션 스키마
// export const LevelOptionSchema = z.object({
//   value: z.string(),
//   label: z.string(),
// });

// // 역 옵션 스키마
// export const StationOptionSchema = z.object({
//   value: z.string(),
//   label: z.string(),
// });

// // API 응답 스키마들
// export const ClassDetailResponseSchema = ClassDetailSchema.nullable();
// export const ClassListResponseSchema = ClassListSchema;
// export const LevelsResponseSchema = z.array(LevelOptionSchema);
// export const StationsResponseSchema = z.array(StationOptionSchema);

// // 타입 정의 (스키마에서 추론)
// export type EnrollmentStatus = z.infer<typeof EnrollmentStatusSchema>;
// export type InstructorSpecialty = z.infer<typeof InstructorSpecialtySchema>;
// export type InstructorCertificate = z.infer<typeof InstructorCertificateSchema>;
// export type ClassTemplate = z.infer<typeof ClassTemplateSchema>;
// export type Instructor = z.infer<typeof InstructorSchema>;
// export type Studio = z.infer<typeof StudioSchema>;
// export type Schedule = z.infer<typeof ScheduleSchema>;
// export type ScheduleGroup = z.infer<typeof ScheduleGroupSchema>;
// export type EnrollmentInfo = z.infer<typeof EnrollmentInfoSchema>;
// export type ClassDetail = z.infer<typeof ClassDetailSchema>;
// export type ClassListItemType = z.infer<typeof ClassListItemSchema>;
// export type Pagination = z.infer<typeof PaginationSchema>;
// export type ClassList = z.infer<typeof ClassListSchema>;
// export type LevelOption = z.infer<typeof LevelOptionSchema>;
// export type StationOption = z.infer<typeof StationOptionSchema>;

// // API 응답 타입들
// export type ClassDetailResponse = z.infer<typeof ClassDetailResponseSchema>;
// export type ClassListResponse = z.infer<typeof ClassListResponseSchema>;
// export type LevelsResponse = z.infer<typeof LevelsResponseSchema>;
// export type StationsResponse = z.infer<typeof StationsResponseSchema>;
