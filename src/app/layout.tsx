import { MobileContainer } from '@/components/layout/MobileContainer';
import QueryProvider from '@/components/providers/QueryProvider';
import getEnv from '@/lib/config/get-env';
import type { Metadata, Viewport } from 'next';
import Script from 'next/script';
import './globals.css';
import AuthCheck from '@/components/providers/AuthCheck';
import { Toaster } from 'sonner';

export const metadata: Metadata = {
  title: 'ShallWe - 운동 클래스 예약 앱',
  description: '쉽고 빠른 운동 클래스 예약 서비스',
  keywords: ['운동', '피트니스', '클래스', '예약', '건강'],
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#6114cc',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='ko'>
      {/* TODO: Load Naver Map SDK only when needed */}
      <Script
        src={`https://oapi.map.naver.com/openapi/v3/maps.js?ncpKeyId=${getEnv('NEXT_PUBLIC_NAVER_MAP_CLIENT_ID')}`}
      />
      <body className='antialiased'>
        <AuthCheck />
        <QueryProvider>
          <MobileContainer>{children}</MobileContainer>
          <Toaster position='top-right' />
        </QueryProvider>
      </body>
    </html>
  );
}
