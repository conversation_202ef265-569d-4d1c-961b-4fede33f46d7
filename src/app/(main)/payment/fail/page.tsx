'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { XCircle, ArrowLeft } from 'lucide-react';

export default function PaymentFailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [failureInfo, setFailureInfo] = useState({
    code: '',
    message: '',
    orderId: '',
  });

  useEffect(() => {
    const code = searchParams.get('code') || '';
    const message = searchParams.get('message') || '결제에 실패했습니다.';
    const orderId = searchParams.get('orderId') || '';

    setFailureInfo({ code, message, orderId });
  }, [searchParams]);

  const handleRetry = () => {
    router.back();
  };

  const handleGoHome = () => {
    router.push('/');
  };

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50 p-4'>
      <div className='w-full max-w-md'>
        <Card>
          <CardContent className='p-8'>
            <div className='space-y-6 text-center'>
              {/* Error Icon */}
              <div className='flex justify-center'>
                <XCircle className='h-16 w-16 text-red-500' />
              </div>

              {/* Title */}
              <div>
                <h1 className='mb-2 text-2xl font-bold text-gray-900'>
                  결제 실패
                </h1>
                <p className='text-gray-600'>
                  결제 처리 중 문제가 발생했습니다.
                </p>
              </div>

              {/* Error Details */}
              {failureInfo.message && (
                <div className='rounded-lg border border-red-200 bg-red-50 p-4'>
                  <div className='text-left'>
                    <p className='mb-1 text-sm font-medium text-red-800'>
                      오류 메시지
                    </p>
                    <p className='text-sm text-red-600'>
                      {failureInfo.message}
                    </p>
                    {failureInfo.code && (
                      <>
                        <p className='mt-3 mb-1 text-sm font-medium text-red-800'>
                          오류 코드
                        </p>
                        <p className='font-mono text-sm text-red-600'>
                          {failureInfo.code}
                        </p>
                      </>
                    )}
                    {failureInfo.orderId && (
                      <>
                        <p className='mt-3 mb-1 text-sm font-medium text-red-800'>
                          주문번호
                        </p>
                        <p className='font-mono text-sm text-red-600'>
                          {failureInfo.orderId}
                        </p>
                      </>
                    )}
                  </div>
                </div>
              )}

              {/* Common Error Solutions */}
              <div className='rounded-lg bg-gray-50 p-4 text-left'>
                <h3 className='mb-2 font-medium text-gray-900'>해결 방법</h3>
                <ul className='space-y-1 text-sm text-gray-600'>
                  <li>• 카드 정보를 다시 확인해주세요</li>
                  <li>• 결제 한도를 확인해주세요</li>
                  <li>• 다른 결제수단을 시도해주세요</li>
                  <li>• 잠시 후 다시 시도해주세요</li>
                </ul>
              </div>

              {/* Action Buttons */}
              <div className='space-y-3'>
                <Button
                  onClick={handleRetry}
                  className='bg-primary hover:bg-primary/90 w-full'
                >
                  <ArrowLeft className='mr-2 h-4 w-4' />
                  다시 시도하기
                </Button>
                <Button
                  variant='outline'
                  onClick={handleGoHome}
                  className='w-full'
                >
                  홈으로 돌아가기
                </Button>
              </div>

              {/* Support Info */}
              <div className='text-center'>
                <p className='text-xs text-gray-500'>
                  문제가 지속되면 고객센터로 문의해주세요.
                </p>
                <p className='mt-1 text-xs text-gray-500'>
                  고객센터: 1588-0000 (평일 09:00-18:00)
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
