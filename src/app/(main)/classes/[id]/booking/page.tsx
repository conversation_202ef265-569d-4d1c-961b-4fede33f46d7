'use client';

import { useState } from 'react';
import { BookingCard } from '@/components/shared/booking/BookingCard';
import { BookingPaymentModal } from '@/components/shared/booking/BookingPaymentModal';
import { BookingConfirmationModal } from '@/components/shared/booking/BookingConfirmationModal';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { BottomBar, bottomBarConfigs } from '@/components/layout/BottomBar';
import { MessageCircle, Calendar } from 'lucide-react';
import { useRouter } from 'next/navigation';

// Mock data for available schedules
const mockSchedules = [
  {
    id: 'schedule-1',
    title: 'A. 월/목 수업 (주 2회)',
    frequency: '주 2회',
    times: [
      { day: '월요일', time: '10:00 ~ 11:00' },
      { day: '목요일', time: '10:00 ~ 11:00' },
    ],
    spotsLeft: 2,
    studentDemographics: {
      ageRange: '45-54세',
      gender: '여성',
      count: 2,
    },
  },
  {
    id: 'schedule-2',
    title: 'B. 화/금 수업 (주 2회)',
    frequency: '주 2회',
    times: [
      { day: '화요일', time: '14:00 ~ 15:00' },
      { day: '금요일', time: '14:00 ~ 15:00' },
    ],
    spotsLeft: 3,
    studentDemographics: {
      ageRange: '30-39세',
      gender: '여성',
      count: 1,
    },
  },
  {
    id: 'schedule-3',
    title: 'C. 주말 수업 (주 1회)',
    frequency: '주 1회',
    times: [{ day: '토요일', time: '09:00 ~ 10:00' }],
    spotsLeft: 1,
    studentDemographics: {
      ageRange: '35-49세',
      gender: '혼성',
      count: 3,
    },
  },
];

export default function BookingPage() {
  const router = useRouter();
  const [selectedSchedule, setSelectedSchedule] = useState<string | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);

  const handleScheduleSelect = (scheduleId: string) => {
    setSelectedSchedule(scheduleId === selectedSchedule ? null : scheduleId);
  };

  const handleBooking = () => {
    if (selectedSchedule) {
      setShowPaymentModal(true);
    }
  };

  const handlePaymentConfirm = () => {
    setShowPaymentModal(false);
    setShowConfirmationModal(true);
  };

  const handleConfirmationClose = () => {
    setShowConfirmationModal(false);
    router.push('/profile');
  };

  const handleRequestCustomTime = () => {
    console.log('Request custom time clicked');
  };

  const bottomBarActions = selectedSchedule
    ? bottomBarConfigs.booking(handleBooking)
    : [];

  return (
    <div className='pb-20'>
      <div className='space-y-6 p-4'>
        <div>
          <h1 className='mb-2 text-xl font-bold text-gray-900'>
            예약 가능한 수업 일정
          </h1>
          <p className='text-gray-600'>원하시는 수업 시간을 선택해주세요</p>
        </div>

        <div className='space-y-4'>
          {mockSchedules.map(schedule => (
            <BookingCard
              key={schedule.id}
              schedule={{
                ...schedule,
                isSelected: selectedSchedule === schedule.id,
              }}
              onSelect={handleScheduleSelect}
            />
          ))}
        </div>

        <Card className='border-2 border-dashed border-gray-300'>
          <CardContent className='p-6 text-center'>
            <MessageCircle className='mx-auto mb-3 h-8 w-8 text-gray-400' />
            <h3 className='mb-2 font-medium text-gray-900'>
              희망하는 수업 시간이 없으신가요?
            </h3>
            <p className='mb-4 text-sm text-gray-600'>
              원하시는 시간대를 요청해주시면 코치님께 전달드려요
            </p>
            <Button
              variant='outline'
              onClick={handleRequestCustomTime}
              className='w-full'
            >
              희망 수업 시간 요청하기
            </Button>
          </CardContent>
        </Card>

        {selectedSchedule && (
          <Card className='border-primary/20 bg-primary/5'>
            <CardContent className='p-4'>
              <div className='text-primary flex items-center gap-2'>
                <Calendar className='h-5 w-5' />
                <span className='font-medium'>
                  선택된 수업:{' '}
                  {mockSchedules.find(s => s.id === selectedSchedule)?.title}
                </span>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {selectedSchedule && <BottomBar actions={bottomBarActions} />}

      <BookingPaymentModal />

      <BookingConfirmationModal
        isOpen={showConfirmationModal}
        onClose={handleConfirmationClose}
        bookingData={{
          title: '주 2회 화/목 그룹PT 확정 완료',
          schedule: '월/목 10:00-11:00',
          successMessage: '수업 예약이 완료되었습니다!',
        }}
      />
    </div>
  );
}
