import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, TrendingUp, Calendar, AlertCircle } from 'lucide-react';
import { ClassStats, Enrollment } from '../types';

interface EnrollmentInfoProps {
  stats: ClassStats;
  enrollments: Enrollment[];
}

export function EnrollmentInfo({ stats, enrollments }: EnrollmentInfoProps) {
  const enrollmentRate = stats.total_enrollments > 0 
    ? Math.round((stats.confirmed_enrollments / stats.total_enrollments) * 100)
    : 0;

  return (
    <div className='space-y-4'>
      <div className='flex items-center gap-2 mb-4'>
        <Users className='h-5 w-5' />
        <h3 className='text-lg font-semibold'>수강 현황</h3>
      </div>

      {/* 통계 카드 */}
      <div className='grid grid-cols-2 gap-4'>
        <Card>
          <CardContent className='p-4 text-center'>
            <div className='text-2xl font-bold text-primary mb-1'>
              {stats.total_enrollments}
            </div>
            <div className='text-sm text-gray-600'>총 신청자</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className='p-4 text-center'>
            <div className='text-2xl font-bold text-green-600 mb-1'>
              {stats.confirmed_enrollments}
            </div>
            <div className='text-sm text-gray-600'>확정 수강생</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className='p-4 text-center'>
            <div className='text-2xl font-bold text-orange-600 mb-1'>
              {stats.pending_enrollments}
            </div>
            <div className='text-sm text-gray-600'>대기 중</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className='p-4 text-center'>
            <div className='text-2xl font-bold text-red-600 mb-1'>
              {stats.cancelled_enrollments}
            </div>
            <div className='text-sm text-gray-600'>취소</div>
          </CardContent>
        </Card>
      </div>

      {/* 수강률 */}
      <Card>
        <CardHeader>
          <CardTitle className='text-base flex items-center gap-2'>
            <TrendingUp className='h-4 w-4' />
            수강률
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex items-center justify-between'>
            <div className='text-lg font-semibold'>
              {enrollmentRate}%
            </div>
            <Badge variant={enrollmentRate >= 80 ? 'default' : 'secondary'}>
              {enrollmentRate >= 80 ? '높음' : enrollmentRate >= 50 ? '보통' : '낮음'}
            </Badge>
          </div>
          <div className='w-full bg-gray-200 rounded-full h-2 mt-2'>
            <div 
              className='bg-primary h-2 rounded-full transition-all duration-300'
              style={{ width: `${enrollmentRate}%` }}
            ></div>
          </div>
        </CardContent>
      </Card>

      {/* 수익 정보 */}
      <Card>
        <CardHeader>
          <CardTitle className='text-base flex items-center gap-2'>
            <Calendar className='h-4 w-4' />
            수익 현황
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold text-primary mb-2'>
            {stats.total_revenue.toLocaleString()}원
          </div>
          <div className='text-sm text-gray-600'>
            총 예상 수익
          </div>
        </CardContent>
      </Card>

      {/* 수강생 목록 (현재는 빈 배열) */}
      {enrollments.length === 0 ? (
        <Card>
          <CardContent className='p-4 text-center text-gray-500'>
            <AlertCircle className='h-8 w-8 mx-auto mb-2' />
            <p>아직 수강 신청자가 없습니다.</p>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className='text-base'>수강생 목록</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              {enrollments.map((enrollment) => (
                <div key={enrollment.id} className='p-3 border rounded-lg'>
                  {/* 수강생 정보가 API에 추가되면 여기에 표시 */}
                  <p className='text-sm text-gray-600'>수강생 정보</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}