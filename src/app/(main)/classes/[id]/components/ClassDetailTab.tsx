'use client';

import { ClassDetailType } from '@/app/api/classes/schema';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';
import { ClassInfo } from './ClassInfo';
import { InstructorInfo } from './InstructorInfo';
import StudioInfo from './StudioInfo';

interface ClassDetailTabProps {
  classDetail: ClassDetailType;
}

export default function ClassDetailTab({ classDetail }: ClassDetailTabProps) {
  const { classTemplate, instructor, studio } = classDetail;
  const [activeTab, setActiveTab] = useState('class');

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };
  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className='px-4'>
      <TabsList className='w-full'>
        <TabsTrigger value='class'>수업소개</TabsTrigger>
        <TabsTrigger value='coach'>코치소개</TabsTrigger>
        <TabsTrigger value='center'>센터소개</TabsTrigger>
        <TabsTrigger value='faq'>FAQ</TabsTrigger>
      </TabsList>

      <TabsContent value='class' className='mt-6'>
        <ClassInfo classTemplate={classTemplate} />
      </TabsContent>

      <TabsContent value='coach' className='mt-6'>
        <InstructorInfo instructor={instructor} />
      </TabsContent>

      <TabsContent value='center' className='mt-6'>
        <StudioInfo studio={studio} />
      </TabsContent>

      <TabsContent value='faq' className='mt-6'>
        {/* <FAQ faq={classDetail.faq || []} /> */}
      </TabsContent>
    </Tabs>
  );
}
