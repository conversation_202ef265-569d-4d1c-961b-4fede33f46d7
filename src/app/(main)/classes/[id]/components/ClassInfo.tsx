import type { ClassDetailTemplateType } from '@/app/api/classes/schema';

interface ClassInfoProps {
  classTemplate: ClassDetailTemplateType;
}

export function ClassInfo({ classTemplate }: ClassInfoProps) {
  return (
    <div className='space-y-6'>
      {/* 기본 수업 소개 */}
      <div>
        <h3 className='mb-4 text-lg font-semibold'>수업 소개</h3>
        <p className='mb-4 leading-relaxed whitespace-pre-line text-gray-700'>
          {classTemplate.description}
        </p>
        <div className='grid grid-cols-2 gap-4 text-sm'>
          <div>
            <p className='font-medium text-gray-900'>수업 시간</p>
            <p className='text-gray-600'>{classTemplate.durationMinutes}분</p>
          </div>
          <div>
            <p className='font-medium text-gray-900'>최대 인원</p>
            <p className='text-gray-600'>{classTemplate.maxCapacity}명</p>
          </div>
          <div>
            <p className='font-medium text-gray-900'>난이도</p>
            <p className='text-gray-600'>{classTemplate.level}</p>
          </div>
          <div>
            <p className='font-medium text-gray-900'>운동 종목</p>
            <p className='text-gray-600'>{classTemplate.specialty}</p>
          </div>
        </div>
      </div>

      {/* 수업 추천 대상 */}
      <div>
        <h3 className='mb-4 text-lg font-semibold'>수업 추천 대상</h3>
        <ul className='marker:text-primary list-inside list-disc space-y-2 text-black'>
          <li>운동을 처음 시작하는 분</li>
          <li>운동을 안한지 오래되어 다시 배우고 싶은 분</li>
          <li>기본 호흡 및 동작부터 배우고 싶으신 분</li>
        </ul>
      </div>

      {/* 커리큘럼 */}
      <div>
        <h3 className='mb-4 text-lg font-semibold'>커리큘럼</h3>
      </div>

      {/* 준비물 */}
      <div>
        <h3 className='mb-4 text-lg font-semibold'>준비물</h3>
        <ul className='marker:text-primary list-inside list-disc space-y-2 text-black'>
          <li>운동복, 수건 준비</li>
          <li>수업 10분 전 도착</li>
          <li>실내 운동화 준비</li>
        </ul>
      </div>
    </div>
  );
}
