import type { ClassDetailStudioType } from '@/app/api/classes/schema';
import NaverMap from '@/components/shared/NaverMap';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';

interface StudioInfoProps {
  studio: ClassDetailStudioType;
}

export default function StudioInfo({ studio }: StudioInfoProps) {
  const mockImages = ['/mock.jpg', '/mock.jpg', '/mock.jpg'];
  const mockNaverMapUrl = `https://map.naver.com/v5/search/${encodeURIComponent(studio.name)}`;

  return (
    <div className='space-y-6'>
      {/* Studio Header */}
      <div>
        <h2 className='mb-2 text-xl font-bold'>{studio.name}</h2>
        <div className='flex gap-2'>
          <Badge variant='secondary' className='text-xs'>
            네이버 링크
          </Badge>
          <Badge variant='secondary' className='text-xs'>
            SNS 링크
          </Badge>
        </div>
      </div>

      {/* Studio Images */}
      <div className='grid grid-cols-3 gap-2'>
        {mockImages.map((image, index) => (
          <div
            key={index}
            className='aspect-square overflow-hidden rounded-lg bg-gray-200'
          >
            <img
              src={image}
              alt={`${studio.name} 사진 ${index + 1}`}
              className='h-full w-full object-cover'
            />
          </div>
        ))}
      </div>

      {/* Studio Description */}
      <div className='text-sm leading-relaxed text-gray-700'>
        몸과 함께 삶을 바꾸는 곳 Turn your Life PT전문 스튜디오 {studio.name}
        입니다. 머신만 돌리는 수업이 아닌 내 몸을 잘 이해하고 잘 쓸 수 있는
        수업을 지향합니다.
      </div>

      {/* Facility Information */}
      <div>
        <h3 className='mb-3 text-lg font-semibold'>시설 정보</h3>
        <div className='grid grid-cols-2 gap-y-3 text-sm'>
          <div className='flex items-center gap-2'>
            <div className='bg-primary h-1 w-1 rounded-full'></div>
            <span>샤워실</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='bg-primary h-1 w-1 rounded-full'></div>
            <span>락카룸</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='bg-primary h-1 w-1 rounded-full'></div>
            <span>주차장</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='bg-primary h-1 w-1 rounded-full'></div>
            <span>에어컨</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='bg-primary h-1 w-1 rounded-full'></div>
            <span>밴드, 케틀벨 등</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='bg-primary h-1 w-1 rounded-full'></div>
            <span>인바디</span>
          </div>
        </div>
      </div>

      {/* Location */}
      <div>
        <div className='mb-4 flex items-center justify-between'>
          <h3 className='text-lg font-semibold'>주소</h3>
          <Button
            variant='outline'
            size='sm'
            onClick={() => window.open(mockNaverMapUrl, '_blank')}
            className='text-xs'
          >
            네이버 지도 보러가기
            <ExternalLink className='ml-1 h-3 w-3' />
          </Button>
        </div>

        {/* Map Container */}
        {/* <div className='mb-3 flex h-48 items-center justify-center rounded-lg bg-gray-100'>
          <div className='text-center text-gray-500'>
            <MapPin className='mx-auto mb-2 h-8 w-8' />
            <p className='text-sm'>네이버 지도 맵</p>
          </div>
        </div> */}
        <NaverMap lat={37.5172962} lng={127.1008311} />

        {/* <div className='space-y-2 text-sm text-gray-700'>
          <div className='flex items-start gap-2'>
            <MapPin className='mt-0.5 h-4 w-4 flex-shrink-0' />
            <span>{studio.address}</span>
          </div>
          {studio.phone && (
            <div className='flex items-center gap-2'>
              <Phone className='h-4 w-4' />
              <span>{studio.phone}</span>
            </div>
          )}
          {studio.hours && (
            <div className='flex items-center gap-2'>
              <Clock className='h-4 w-4' />
              <span>{studio.hours}</span>
            </div>
          )}
        </div> */}
      </div>
    </div>
  );
}
