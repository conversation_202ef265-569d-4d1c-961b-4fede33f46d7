import { ClassDetailInstructorType } from '@/app/api/classes/schema';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface CoachInfoProps {
  instructor: ClassDetailInstructorType;
}

const CertIcon = () => (
  <svg
    width='18'
    height='29'
    viewBox='0 0 18 29'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <rect width='17.1764' height='17.1764' rx='8.58818' fill='#6114CC' />
    <rect
      width='4.32444'
      height='21.191'
      transform='matrix(0.855428 0.517922 -0.230298 0.97312 9.45996 5.72559)'
      fill='#6114CC'
    />
    <rect
      width='4.67082'
      height='20.9303'
      transform='matrix(0.604201 -0.796832 0.429027 0.903292 5.37354 9.37598)'
      fill='#6114CC'
    />
    <rect
      x='2.28955'
      y='2.28906'
      width='12.596'
      height='12.596'
      rx='6.298'
      fill='white'
    />
  </svg>
);

export function InstructorInfo({ instructor }: CoachInfoProps) {
  return (
    <div className='space-y-6'>
      {/* 코치 소개 섹션 */}
      <div>
        <h2 className='mb-4 text-lg font-bold'>코치 소개</h2>
        <div className='mb-4 flex items-center gap-4'>
          <Avatar className='h-20 w-20'>
            <AvatarImage src={'/mock.jpg'} />
            <AvatarFallback className='text-lg'>
              {instructor.name.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div className='flex-1'>
            <div className='mb-1 flex items-center gap-2'>
              <h3 className='text-xl font-bold'>{instructor.name}</h3>
              <span className='text-gray-500'>
                / {instructor.specialties[0].experienceYears}년차
              </span>
            </div>
            <p className='text-primary mb-2 font-medium'>
              {instructor.shortBio}
            </p>

            {/* Tags */}
            <div className='flex gap-4 text-sm'>
              {instructor.specialties.map((specialty, index) => (
                <Badge
                  key={index}
                  variant='secondary'
                  className='text-primary bg-purple-50 hover:bg-purple-100'
                >
                  #{specialty.specialty}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* TODO: desc */}
        {/* <div className='rounded-lg bg-gray-50 p-4'>
          <p className='leading-relaxed text-gray-700'>
            안녕하세요! 5년간 재활 치료 중심 기능성 운동 코칭을 해온 최하얀
            코치입니다. 무리한 고강도 단기 운동보다는 몸에 무담가지 앉고 지속
            가능한 운동을 알려드립니다.
          </p>
        </div> */}
      </div>

      {/* 커리어 / 보유 자격증 섹션 */}
      <div>
        <h2 className='mb-4 text-lg font-bold'>커리어 / 보유 자격증</h2>
        <div className='space-y-3'>
          {instructor.certificates && instructor.certificates.length > 0 && (
            <>
              {instructor.certificates.map((certificate, index) => (
                <div key={index} className='flex items-center gap-3'>
                  <CertIcon />
                  <span className='font-medium text-gray-800'>
                    {certificate.certificateName}
                  </span>
                </div>
              ))}
            </>
          )}

          {/* 기본 자격증들 (데이터가 없는 경우 표시) */}
          {/* {(!data['instructor.certificates'] ||
            data['instructor.certificates'].length === 0) && (
            <>
              <div className='flex items-center gap-3'>
                <CertIcon />
                <span className='font-medium text-gray-800'>
                  스포츠생활지도사 1급
                </span>
              </div>
              <div className='flex items-center gap-3'>
                <CertIcon />
                <span className='font-medium text-gray-800'>
                  노인스포츠지도사
                </span>
              </div>
              <div className='flex items-center gap-3'>
                <CertIcon />
                <span className='font-medium text-gray-800'>
                  건강운동관리사
                </span>
              </div>
            </>
          )} */}
        </div>
      </div>

      <div>
        <h2 className='mb-4 text-lg font-bold'>트레이너 후기</h2>
      </div>
    </div>
  );
}
