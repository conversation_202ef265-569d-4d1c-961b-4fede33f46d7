'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { BottomBar, bottomBarConfigs } from '@/components/layout/BottomBar';
import { Calendar } from 'lucide-react';

interface ClientActionsProps {
  classId: string;
}

export function ClientActions({ classId }: ClientActionsProps) {
  const router = useRouter();

  const handleInquiry = () => {
    console.log('문의하기 clicked');
  };

  const handleViewSchedule = () => {
    router.push(`/classes/${classId}/booking`);
  };

  return (
    <Button className='px-6 font-bold' onClick={handleViewSchedule}>
      <Calendar className='h-4 w-4' />
      수업 일정 보기
    </Button>
  );
}

export function ClientBottomBar({ classId }: ClientActionsProps) {
  const router = useRouter();

  const handleInquiry = () => {
    console.log('문의하기 clicked');
  };

  const handleViewSchedule = () => {
    router.push(`/classes/${classId}/booking`);
  };

  const bottomBarActions = bottomBarConfigs.classDetail(
    handleInquiry,
    handleViewSchedule
  );

  return <BottomBar actions={bottomBarActions} />;
}