import { Card, CardContent } from '@/components/ui/card';

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQProps {
  faq: FAQItem[];
}

export function FAQ({ faq }: FAQProps) {
  return (
    <div className='space-y-4'>
      {faq.map((item, index) => (
        <Card key={index}>
          <CardContent className='p-4'>
            <h4 className='text-primary mb-2 font-medium'>Q. {item.question}</h4>
            <p className='text-gray-700'>A. {item.answer}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}