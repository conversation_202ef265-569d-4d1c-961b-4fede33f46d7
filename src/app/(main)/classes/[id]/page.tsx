import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { getClassDetailById } from '@/lib/api/classes/api';
import { Clock, MapPin, Star, Users } from 'lucide-react';
import ClassDetailTab from './components/ClassDetailTab';
import { ClientBottomBar } from './components/ClientActions';

interface ClassDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function ClassDetailPage({
  params,
}: ClassDetailPageProps) {
  const { id } = await params;

  const data = await getClassDetailById(id);
  const { classTemplate, studio, instructor } = data;

  return (
    <div className='pb-20'>
      <div className='relative h-64 w-full'>
        <img
          src={'/mock.jpg'}
          alt={classTemplate.title}
          className='object-cover'
        />
        <div className='absolute top-4 left-4'>
          <Badge className='bg-primary text-primary-foreground border-none'>
            초급자 추천
          </Badge>
        </div>
      </div>

      <div className='space-y-4 p-4'>
        <h1 className='text-xl font-bold text-gray-900'>
          {classTemplate.title}
        </h1>

        <div className='flex items-center gap-3'>
          <Avatar className='h-12 w-12'>
            <AvatarImage src={'/mock.jpg'} />
            <AvatarFallback>{instructor.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div className='flex-1'>
            <p className='font-medium text-gray-900'>{instructor.name}</p>
            <p className='text-sm text-gray-600'>{instructor.shortBio}</p>
          </div>
        </div>

        <div className='grid grid-cols-2 gap-4 text-sm text-gray-600'>
          <div className='flex items-center gap-2'>
            <MapPin className='h-4 w-4' />
            <span>{studio.nearestStation}</span>
          </div>
          <div className='flex items-center gap-2'>
            <Clock className='h-4 w-4' />
            <span>{classTemplate.durationMinutes}분 수업</span>
          </div>
          <div className='flex items-center gap-2'>
            <Users className='h-4 w-4' />
            <span>{classTemplate.maxCapacity}인 그룹 수업</span>
          </div>
          <div className='flex items-center gap-2'>
            <Star className='text-primary h-4 w-4' />
            <span className='text-primary'>초급 클래스</span>
          </div>
        </div>

        <div className='mb-1 flex items-center justify-end border-t pt-4'>
          <div className='text-right'>
            <div className='flex items-center gap-1'>
              <span className='text-primary'>회당</span>
              <span className='text-primary text-xl font-bold'>
                {classTemplate.pricePerSession.toLocaleString()}원
              </span>
            </div>
          </div>
        </div>
      </div>

      <ClassDetailTab classDetail={data} />
      <ClientBottomBar classId={id} />
    </div>
  );
}
