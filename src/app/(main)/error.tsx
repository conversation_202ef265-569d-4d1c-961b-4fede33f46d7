'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className='space-y-6'>
      <section className='pt-4'>
        <h2 className='mb-4 px-4 text-xl font-bold text-gray-900'>
          추천 클래스
        </h2>
        <div className='px-4 text-center'>
          <div className='mb-4 text-gray-500'>
            클래스를 불러오는 중 오류가 발생했습니다.
          </div>
          <Button onClick={reset} variant="outline">
            다시 시도
          </Button>
        </div>
      </section>
    </div>
  );
}