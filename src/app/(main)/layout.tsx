'use client';

import { AppBar } from '@/components/layout/AppBar';
import { GlobalBottomNav } from '@/components/layout/GlobalBottomNav';
import OnboardingGuard from '@/components/OnboardingGuard';
import { getRouteTitle } from '@/lib/config/route-titles';
import { shouldHideBottomNav } from '@/lib/utils/navigation';
import { usePathname } from 'next/navigation';
import { Suspense } from 'react';

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const title = getRouteTitle(pathname);

  const shouldHideNav = shouldHideBottomNav(pathname);

  return (
    <>
      <Suspense fallback={
        <div className='flex min-h-screen items-center justify-center bg-gray-50'>
          <div className='text-center'>
            <div className='mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent'></div>
            <p className='text-sm text-gray-600'>로딩 중...</p>
          </div>
        </div>
      }>
        <OnboardingGuard>
          <div className='min-h-screen border-x border-gray-100 bg-white'>
            <AppBar title={title} />
            <main className={shouldHideNav ? '' : 'pb-20'}>{children}</main>
            <GlobalBottomNav />
          </div>
        </OnboardingGuard>
      </Suspense>
    </>
  );
}
