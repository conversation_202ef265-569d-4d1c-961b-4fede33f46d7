import { BookingTabs } from '@/components/shared/profile/BookingTabs';
import { UserStats } from '@/components/shared/profile/UserStats';
import { profileApi } from '@/lib/api/profile/api';

export default async function ProfilePage() {
  try {
    const [user, bookings] = await Promise.all([
      profileApi.fetchUserProfile(),
      profileApi.fetchUserBookingHistory(),
    ]);

    return (
      <div className='space-y-6 p-4'>
        <UserStats user={user} />
        <BookingTabs bookings={bookings} />
      </div>
    );
  } catch (error) {
    console.error('Error loading profile data:', error);
    return (
      <div className='flex min-h-screen items-center justify-center'>
        <div className='text-center text-red-500'>
          <p>Error loading profile data</p>
          <p className='mt-2 text-sm'>Please try refreshing the page</p>
        </div>
      </div>
    );
  }
}
