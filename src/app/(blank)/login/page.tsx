'use client';

import { KakaoButton } from '@/components/auth/KakaoButton';

export default function LoginPage() {
  return (
    <div className='w-full max-w-sm space-y-8 px-4 pt-4'>
      {/* 로고 영역 */}
      <div className='text-left'>
        <h1 className='logo mb-8 text-4xl font-bold'>SHALLWE</h1>
        <div className='space-y-1'>
          <p className='text-2xl font-bold text-gray-900'>우리동네</p>
          <p className='text-2xl font-bold text-gray-900'>프리미엄 그룹 수업</p>
          <p className='text-2xl font-bold text-gray-900'>쉘위 입니다.</p>
        </div>
      </div>

      {/* 로그인 버튼들 */}
      <div className='space-y-3'>
        {/* 카카오 로그인 버튼 */}
        <KakaoButton redirectTo='/' />
      </div>
    </div>
  );
}
