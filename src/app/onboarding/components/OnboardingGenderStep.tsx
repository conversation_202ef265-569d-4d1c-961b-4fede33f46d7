import { GENDER_OPTIONS } from '@/lib/constants/onboarding';

interface OnboardingGenderStepProps {
  selectedGender: string;
  onGenderSelect: (gender: string) => void;
}

export default function OnboardingGenderStep({
  selectedGender,
  onGenderSelect,
}: OnboardingGenderStepProps) {
  return (
    <div className='space-y-6'>
      {/* 헤더 */}
      <div className='text-center'>
        <h2 className='mb-2 text-xl font-semibold text-gray-900'>
          성별을 선택해주세요
        </h2>
        <p className='text-sm text-gray-600'>강사 매칭 시 참고됩니다</p>
      </div>

      {/* 성별 선택 */}
      <div className='grid grid-cols-3 gap-4'>
        {GENDER_OPTIONS.map(option => {
          const isSelected = selectedGender === option.value;

          return (
            <button
              key={option.value}
              type='button'
              onClick={() => onGenderSelect(option.value)}
              className={`rounded-xl border p-6 text-center transition-all duration-200 ${
                isSelected
                  ? 'border-primary bg-primary/10 shadow-sm'
                  : 'border-gray-200 bg-white hover:bg-gray-50'
              } `}
            >
              <div className='flex flex-col items-center space-y-3'>
                {/* 아이콘 */}
                <div className='text-3xl'>{option.icon}</div>

                {/* 제목 */}
                <p
                  className={`text-sm font-medium ${
                    isSelected ? 'text-primary' : 'text-gray-900'
                  }`}
                >
                  {option.label}
                </p>

                {/* 선택 인디케이터 */}
                {/* {isSelected && (
                  <div className='flex h-5 w-5 items-center justify-center rounded-full bg-indigo-600'>
                    <svg
                      className='h-3 w-3 text-white'
                      fill='currentColor'
                      viewBox='0 0 20 20'
                    >
                      <path
                        fillRule='evenodd'
                        d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                        clipRule='evenodd'
                      />
                    </svg>
                  </div>
                )} */}
              </div>
            </button>
          );
        })}
      </div>

      {/* 개인정보 안내 */}
      <div className='rounded-xl bg-gray-50 p-4'>
        <div className='flex items-start space-x-3'>
          <div className='mt-0.5 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-gray-400'>
            <svg
              className='h-3 w-3 text-white'
              fill='currentColor'
              viewBox='0 0 20 20'
            >
              <path
                fillRule='evenodd'
                d='M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z'
                clipRule='evenodd'
              />
            </svg>
          </div>
          <div>
            <p className='mb-1 text-sm font-medium text-gray-800'>
              🔒 개인정보 보호
            </p>
            <ul className='space-y-1 text-xs text-gray-600'>
              <li>• 성별 정보는 적절한 강사 매칭을 위해서만 사용됩니다</li>
              <li>• 언제든지 설정에서 변경할 수 있습니다</li>
              <li>• 개인정보는 안전하게 보호됩니다</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
