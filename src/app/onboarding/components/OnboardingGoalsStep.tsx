import { FITNESS_GOALS_OPTIONS } from '@/lib/constants/onboarding';

interface OnboardingGoalsStepProps {
  selectedGoals: string[];
  onGoalToggle: (goal: string) => void;
}

export default function OnboardingGoalsStep({
  selectedGoals,
  onGoalToggle,
}: OnboardingGoalsStepProps) {
  return (
    <div className='space-y-6'>
      {/* 헤더 */}
      <div className='text-center'>
        <h2 className='mb-2 text-xl font-semibold text-gray-900'>
          어떤 목표로 운동을 시작하시나요?
        </h2>
        <p className='text-sm text-gray-600'>여러 개 선택 가능해요</p>
      </div>

      {/* 목표 선택 그리드 */}
      <div className='grid grid-cols-2 gap-4'>
        {FITNESS_GOALS_OPTIONS.map(option => {
          const isSelected = selectedGoals.includes(option.value);

          return (
            <button
              key={option.value}
              type='button'
              onClick={() => onGoalToggle(option.value)}
              className={`rounded-xl border p-4 text-left transition-all duration-200 ${
                isSelected
                  ? 'border-primary bg-primary/10 shadow-sm'
                  : 'border-gray-200 bg-white hover:bg-gray-50'
              } `}
            >
              <div className='flex flex-col items-center space-y-2 text-center'>
                {/* 아이콘 */}
                <div className='mb-1 text-2xl'>{option.icon}</div>

                {/* 제목 */}
                <div>
                  <p
                    className={`text-sm font-medium ${
                      isSelected ? 'text-primary' : 'text-gray-900'
                    }`}
                  >
                    {option.label}
                  </p>

                  {/* 설명 */}
                  <p
                    className={`mt-1 text-xs ${
                      isSelected ? 'text-primary/80' : 'text-gray-500'
                    }`}
                  >
                    {option.description}
                  </p>
                </div>

                {/* 선택 인디케이터 */}
                {/* {isSelected && (
                  <div className='flex h-5 w-5 items-center justify-center rounded-full bg-indigo-600'>
                    <svg
                      className='h-3 w-3 text-white'
                      fill='currentColor'
                      viewBox='0 0 20 20'
                    >
                      <path
                        fillRule='evenodd'
                        d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                        clipRule='evenodd'
                      />
                    </svg>
                  </div>
                )} */}
              </div>
            </button>
          );
        })}
      </div>

      {/* 선택 안내 */}
      {selectedGoals.length === 0 && (
        <div className='py-4 text-center'>
          <p className='text-sm text-gray-500'>
            원하는 운동 목표를 선택해주세요
          </p>
        </div>
      )}

      {selectedGoals.length > 0 && (
        <div className='bg-primary/10 rounded-xl p-4'>
          <p className='text-primary text-center text-sm'>
            <span className='font-medium'>{selectedGoals.length}개 목표</span>를
            선택하셨어요
          </p>
        </div>
      )}
    </div>
  );
}
