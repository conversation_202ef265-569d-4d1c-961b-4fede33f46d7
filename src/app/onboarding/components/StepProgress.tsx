'use client';

import { ONBOARDING_STEPS } from '@/lib/constants/onboarding';

interface StepProgressProps {
  currentStep: number;
  totalSteps?: number;
}

export default function StepProgress({
  currentStep,
  totalSteps = Object.keys(ONBOARDING_STEPS).length,
}: StepProgressProps) {
  return (
    <div className='mb-6'>
      {/* 진행률 도트 */}
      <div className='mb-4 flex items-center justify-center'>
        <div className='flex space-x-2'>
          {Array.from({ length: totalSteps }, (_, index) => {
            const stepNumber = index + 1;
            const isCompleted = stepNumber < currentStep;
            const isCurrent = stepNumber === currentStep;

            return (
              <div
                key={stepNumber}
                className={`h-2 w-2 rounded-full transition-colors duration-200 ${
                  isCompleted || isCurrent ? 'bg-primary' : 'bg-gray-300'
                } `}
              />
            );
          })}
        </div>
      </div>

      {/* 진행률 텍스트 */}
      <div className='text-center'>
        <p className='text-xs text-gray-500'>
          {currentStep} / {totalSteps} 단계
        </p>
      </div>
    </div>
  );
}
