import { useState, useEffect, useRef } from 'react';
import { type SubwayStation } from '@/lib/data/subway-stations';

interface SubwayStationSearchProps {
  selectedStations: SubwayStation[];
  onStationSelect: (station: SubwayStation) => void;
  onStationRemove: (stationId: string) => void;
  placeholder?: string;
  maxSelections?: number;
}

export default function SubwayStationSearch({
  selectedStations,
  onStationSelect,
  onStationRemove,
  placeholder = '지하철역을 검색해주세요',
  maxSelections = 5,
}: SubwayStationSearchProps) {
  const [query, setQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SubwayStation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [popularStations, setPopularStations] = useState<SubwayStation[]>([]);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 디바운싱을 적용한 검색 함수
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (query.trim().length < 1) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    setIsLoading(true);
    searchTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await fetch(
          `/api/subway-stations/search?q=${encodeURIComponent(query)}&limit=10`
        );

        if (response.ok) {
          const data = await response.json();
          // 이미 선택된 역은 검색 결과에서 제외
          const filteredResults = data.stations.filter(
            (station: SubwayStation) =>
              !selectedStations.some(selected => selected.id === station.id)
          );
          setSearchResults(filteredResults);
          setShowResults(true);
        }
      } catch (error) {
        console.error('지하철역 검색 오류:', error);
        setSearchResults([]);
      } finally {
        setIsLoading(false);
      }
    }, 300);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query, selectedStations]);

  // 컴포넌트 마운트 시 인기 역 로드
  useEffect(() => {
    const loadPopularStations = async () => {
      try {
        const response = await fetch('/api/subway-stations/popular');
        if (response.ok) {
          const data = await response.json();
          setPopularStations(data.stations);
        }
      } catch (error) {
        console.error('인기 역 로드 오류:', error);
      }
    };

    loadPopularStations();
  }, []);

  // 역 선택 처리
  const handleStationSelect = (station: SubwayStation) => {
    if (selectedStations.length >= maxSelections) {
      alert(`최대 ${maxSelections}개까지만 선택할 수 있습니다.`);
      return;
    }

    onStationSelect(station);
    setQuery('');
    setShowResults(false);
    inputRef.current?.focus();
  };

  // 입력창 포커스 해제 시 결과 숨기기 (약간의 지연)
  const handleBlur = () => {
    setTimeout(() => setShowResults(false), 200);
  };

  // 입력창 포커스 시 결과 표시
  const handleFocus = () => {
    if (query.length > 0) {
      setShowResults(true);
    }
  };

  return (
    <div className='space-y-4'>
      {/* 인기 지역 버튼들 */}
      {popularStations.length > 0 && (
        <div>
          <p className='mb-3 text-sm font-medium text-gray-700'>인기 지역</p>
          <div className='flex flex-wrap gap-2'>
            {popularStations
              .filter(
                station =>
                  !selectedStations.some(selected => selected.id === station.id)
              )
              .map(station => (
                <button
                  key={station.id}
                  type='button'
                  onClick={() => handleStationSelect(station)}
                  className='rounded-lg bg-gray-100 px-3 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200'
                >
                  {station.name}
                  {station.lines && station.lines.length > 1
                    ? ` (${station.lines.join(',')})`
                    : ` (${station.line})`}
                </button>
              ))}
          </div>
        </div>
      )}

      {/* 검색 입력창 */}
      <div className='relative'>
        <input
          ref={inputRef}
          type='text'
          value={query}
          onChange={e => setQuery(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className='w-full rounded-xl border border-gray-200 px-4 py-3 transition-colors focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500'
          placeholder={placeholder}
        />

        {/* 로딩 인디케이터 */}
        {isLoading && (
          <div className='absolute top-1/2 right-3 -translate-y-1/2 transform'>
            <div className='h-4 w-4 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent'></div>
          </div>
        )}

        {/* 검색 결과 드롭다운 */}
        {showResults && searchResults.length > 0 && (
          <div className='absolute top-full right-0 left-0 z-10 mt-1 max-h-60 overflow-y-auto rounded-xl border border-gray-200 bg-white shadow-lg'>
            {searchResults.map(station => (
              <button
                key={station.id}
                type='button'
                onClick={() => handleStationSelect(station)}
                className='w-full px-4 py-3 text-left transition-colors first:rounded-t-xl last:rounded-b-xl hover:bg-gray-50'
              >
                <div className='flex items-center justify-between'>
                  <div>
                    <span className='font-medium text-gray-900'>
                      {station.name}
                    </span>
                    <span className='ml-2 text-sm text-gray-500'>
                      {station.lines && station.lines.length > 1
                        ? `(${station.lines.join(',')})`
                        : station.line}
                    </span>
                  </div>
                  <div className='flex space-x-1'>
                    {station.lines && station.lines.length > 1 ? (
                      station.lines.map((line, index) => (
                        <div
                          key={index}
                          className={`h-3 w-3 rounded-full ${line.includes('1') && 'bg-blue-600'} ${line.includes('2') && 'bg-green-600'} ${line.includes('3') && 'bg-orange-600'} ${line.includes('4') && 'bg-cyan-600'} ${line.includes('5') && 'bg-purple-600'} ${line.includes('6') && 'bg-amber-600'} ${line.includes('7') && 'bg-emerald-600'} ${line.includes('8') && 'bg-pink-600'} ${line.includes('9') && 'bg-yellow-600'} ${line.includes('SB') && 'bg-indigo-600'} `}
                        ></div>
                      ))
                    ) : (
                      <div
                        className={`h-4 w-4 rounded-full ${station.lineNumber === '1' && 'bg-blue-600'} ${station.lineNumber === '2' && 'bg-green-600'} ${station.lineNumber === '3' && 'bg-orange-600'} ${station.lineNumber === '4' && 'bg-cyan-600'} ${station.lineNumber === '5' && 'bg-purple-600'} ${station.lineNumber === '6' && 'bg-amber-600'} ${station.lineNumber === '7' && 'bg-emerald-600'} ${station.lineNumber === '8' && 'bg-pink-600'} ${station.lineNumber === '9' && 'bg-yellow-600'} ${station.lineNumber === 'SB' && 'bg-indigo-600'} `}
                      ></div>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}

        {/* 검색 결과 없음 */}
        {showResults &&
          query.length > 0 &&
          searchResults.length === 0 &&
          !isLoading && (
            <div className='absolute top-full right-0 left-0 z-10 mt-1 rounded-xl border border-gray-200 bg-white p-4 text-center text-sm text-gray-500 shadow-lg'>
              검색 결과가 없습니다.
            </div>
          )}
      </div>

      {/* 선택된 역 목록 */}
      {selectedStations.length > 0 && (
        <div>
          <p className='mb-3 text-sm font-medium text-gray-700'>
            선택된 지역 ({selectedStations.length}/{maxSelections})
          </p>
          <div className='flex flex-wrap gap-2'>
            {selectedStations.map(station => (
              <div
                key={station.id}
                className='flex items-center space-x-2 rounded-lg bg-indigo-50 px-3 py-2 text-sm text-indigo-800'
              >
                <span>{station.name}</span>
                <span className='text-indigo-600'>
                  (
                  {station.lines && station.lines.length > 1
                    ? station.lines.join(',')
                    : station.line}
                  )
                </span>
                <button
                  type='button'
                  onClick={() => onStationRemove(station.id)}
                  className='text-indigo-600 transition-colors hover:text-indigo-800'
                >
                  <svg
                    className='h-4 w-4'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M6 18L18 6M6 6l12 12'
                    />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 선택 안내 */}
      {selectedStations.length === 0 && (
        <div className='py-4 text-center'>
          <p className='text-sm text-gray-500'>
            운동하고 싶은 지역의 지하철역을 검색해보세요
          </p>
        </div>
      )}
    </div>
  );
}
