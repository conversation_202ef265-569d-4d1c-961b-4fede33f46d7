import type { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';

export const metadata: Metadata = {
  title: '계정 정지 | Shallwe 파트너',
  description: '파트너 계정이 일시 정지되었습니다.',
};

export default function PartnerSuspendedPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        {/* 아이콘 */}
        <div className="flex justify-center">
          <div className="bg-red-100 rounded-full p-4">
            <svg
              className="h-12 w-12 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
        </div>

        {/* 콘텐츠 */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            계정이 일시 정지되었습니다
          </h1>
          <p className="text-gray-600 mb-8">
            파트너 계정이 일시적으로 정지된 상태입니다.
            <br />
            자세한 사항은 고객센터로 문의해 주세요.
          </p>

          {/* 정지 사유 예시 */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8 text-left">
            <h3 className="text-sm font-semibold text-red-800 mb-2">
              계정 정지 가능 사유
            </h3>
            <ul className="text-sm text-red-700 space-y-1">
              <li>• 이용약관 위반</li>
              <li>• 허위 정보 등록</li>
              <li>• 고객 민원 누적</li>
              <li>• 기타 운영 정책 위반</li>
            </ul>
          </div>

          {/* 액션 버튼 */}
          <div className="space-y-3">
            <a 
              href="mailto:<EMAIL>?subject=파트너 계정 정지 문의"
              className="block"
            >
              <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                고객센터 문의하기
              </Button>
            </a>
            
            <Link href="/" className="block">
              <Button variant="outline" className="w-full">
                메인 페이지로 이동
              </Button>
            </Link>
          </div>

          {/* 문의 안내 */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              <p className="font-medium mb-2">고객센터 연락처</p>
              <p>
                이메일:{' '}
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-purple-600 hover:text-purple-500"
                >
                  <EMAIL>
                </a>
              </p>
              <p>
                전화: 02-1234-5678
              </p>
              <p className="mt-2 text-xs">
                운영시간: 평일 09:00 - 18:00
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}