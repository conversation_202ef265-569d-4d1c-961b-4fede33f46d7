'use client';
import { create } from 'zustand';
import { combine, devtools } from 'zustand/middleware';

interface PartnerState {
  studioId: string | null | undefined;
}

const initialState: PartnerState = {
  studioId: undefined,
};

export const usePartnerStore = create(
  devtools(
    combine(initialState, set => {
      return {
        setStudioId: (studioId: string | null) =>
          set({ studioId }, false, 'setStudioId'),
      };
    }),
    {
      name: 'partner-store', // DevTools에서 표시될 이름
      enabled: process.env.NODE_ENV === 'development', // 개발 환경에서만 활성화
    }
  )
);

export const partnerStore = usePartnerStore.getState();
