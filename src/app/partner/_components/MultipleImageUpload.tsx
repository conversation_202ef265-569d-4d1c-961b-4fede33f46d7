import { useState } from 'react';
import { Camera, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ImagePreviewData {
  file: File;
  preview: string;
}

interface MultipleImageUploadProps {
  images: File[];
  onChange: (files: File[]) => void;
  type: 'studio' | 'instructor' | 'class';
  maxImages?: number;
}

export default function MultipleImageUpload({
  images,
  onChange,
  type,
  maxImages = 10,
}: MultipleImageUploadProps) {
  const [imagePreviewData, setImagePreviewData] = useState<ImagePreviewData[]>(
    []
  );

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    const newFiles: File[] = [];
    const newPreviews: ImagePreviewData[] = [];

    Array.from(files).forEach(file => {
      if (images.length + newFiles.length < maxImages) {
        const preview = URL.createObjectURL(file);
        newFiles.push(file);
        newPreviews.push({ file, preview });
      }
    });

    onChange([...images, ...newFiles]);
    setImagePreviewData(prev => [...prev, ...newPreviews]);
  };

  const removeImage = (index: number) => {
    const newFiles = images.filter((_, i) => i !== index);
    onChange(newFiles);

    setImagePreviewData(prev => {
      URL.revokeObjectURL(prev[index].preview);
      return prev.filter((_, i) => i !== index);
    });
  };

  const getTitle = () => {
    switch (type) {
      case 'studio':
        return '센터 대표 사진';
      case 'instructor':
        return '강사 대표 사진';
      case 'class':
        return '운동 대표 사진';
      default:
        return '대표 사진';
    }
  };

  const getDescription = () => {
    switch (type) {
      case 'studio':
        return `센터 내부 공간을 보여주는 다양한 사진을 업로드하세요. (최대 ${maxImages}장)`;
      case 'instructor':
        return `강사님의 프로필 사진을 업로드해주세요. (최대 ${maxImages}장)`;
      case 'class':
        return '수업의 분위기나 운동 모습을 보여주는 사진을 업로드하세요.';
      default:
        return `사진을 업로드해주세요. (최대 ${maxImages}장)`;
    }
  };

  return (
    <div>
      <label className='text-sm font-medium'>
        {getTitle()}
        <span className='text-primary font-bold'>*</span>
      </label>
      <p className='text-muted-foreground mb-2 text-xs'>{getDescription()}</p>
      <div className='grid grid-cols-3 gap-3'>
        {imagePreviewData.map((imageData, index) => (
          <div key={index} className='relative aspect-square'>
            <img
              src={imageData.preview}
              alt={`Upload ${index + 1}`}
              className='h-full w-full rounded-lg object-cover'
            />
            <button
              type='button'
              onClick={() => removeImage(index)}
              className='absolute -top-2 -right-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-xs text-white'
            >
              <X size={12} />
            </button>
          </div>
        ))}
        {images.length < maxImages && (
          <label
            className={cn(
              // Base styles
              'group flex aspect-square cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed transition-all duration-200 ease-in-out',
              // Default state
              'border-gray-300 bg-gray-50/50',
              // Hover state
              'hover:border-primary hover:bg-primary/5 hover:scale-[1.02] hover:shadow-md',
              // Focus state
              'focus-within:border-primary focus-within:bg-primary/5 focus-within:scale-[1.02] focus-within:shadow-md'
            )}
          >
            <Camera
              size={24}
              className='group-hover:text-primary group-focus-within:text-primary mb-1 text-gray-400 transition-colors duration-200'
            />
            <span className='group-hover:text-primary group-focus-within:text-primary text-xs text-gray-500 transition-colors duration-200'>
              사진 추가
            </span>
            <input
              type='file'
              accept='image/*'
              multiple
              onChange={handleImageUpload}
              className='hidden'
              aria-label='사진 파일을 선택하여 업로드하세요'
            />
          </label>
        )}
      </div>
    </div>
  );
}
