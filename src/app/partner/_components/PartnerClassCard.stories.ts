import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';
import PartnerClassCard from './PartnerClassCard';

const meta = {
  title: 'Partner/Classes/PartnerClassCard',
  component: PartnerClassCard,
  argTypes: {
    difficultyLevel: {
      control: { type: 'select' },
      options: ['beginner', 'intermediate', 'advanced'],
      description: '클래스 난이도 레벨',
    },
    title: {
      control: 'text',
      description: '클래스 제목',
    },
    category: {
      control: 'text',
      description: '운동 분야/카테고리',
    },
    maxCapacity: {
      control: { type: 'number', min: 1, max: 50 },
      description: '수업 정원',
    },
    registeredSessionCount: {
      control: { type: 'number', min: 0 },
      description: '등록된 수업 시간 수',
    },
    ongoingSessionCount: {
      control: { type: 'number', min: 0 },
      description: '진행 중 수업 시간 수',
    },
    pricePerSession: {
      control: { type: 'number', min: 0 },
      description: '1인당 회당 수강료',
    },
    studioName: {
      control: 'text',
      description: '센터명',
    },
    registrationDate: {
      control: 'text',
      description: '등록일',
    },
    initialActive: {
      control: 'boolean',
      description: '클래스 활성화 상태 초기값',
    },
  },
  args: {
    onViewDetails: fn(),
    onEdit: fn(),
    onActiveChange: fn(),
  },
} satisfies Meta<typeof ClassDetailCard>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * 기본 클래스 상세 카드
 */
export const Default: Story = {
  args: {
    title: '운동 제목',
    difficultyLevel: 'beginner',
    instructor: {
      name: '담당 강사',
    },
    category: '운동 분야',
    maxCapacity: 12,
    registeredSessionCount: 12,
    ongoingSessionCount: 3,
    pricePerSession: 20000,
    studioName: '센터명',
    registrationDate: '2025/07/24',
    initialActive: true,
  },
};

/**
 * 요가 클래스 예시
 */
export const YogaClass: Story = {
  args: {
    title: '하타 요가',
    difficultyLevel: 'beginner',
    instructor: {
      name: '김민지',
    },
    category: '요가',
    maxCapacity: 15,
    registeredSessionCount: 24,
    ongoingSessionCount: 8,
    pricePerSession: 25000,
    studioName: '평화 요가 센터',
    registrationDate: '2024/01/15',
    initialActive: true,
  },
};

/**
 * 피트니스 클래스 예시
 */
export const FitnessClass: Story = {
  args: {
    title: '크로스핏 초급',
    difficultyLevel: 'intermediate',
    instructor: {
      name: '박철수',
    },
    category: '피트니스',
    maxCapacity: 10,
    registeredSessionCount: 16,
    ongoingSessionCount: 5,
    pricePerSession: 35000,
    studioName: '스트롱 피트니스',
    registrationDate: '2024/02/01',
    initialActive: false,
  },
};

/**
 * 필라테스 클래스 예시
 */
export const PilatesClass: Story = {
  args: {
    title: '매트 필라테스',
    difficultyLevel: 'advanced',
    instructor: {
      name: '최은영',
    },
    category: '필라테스',
    maxCapacity: 8,
    registeredSessionCount: 20,
    ongoingSessionCount: 6,
    pricePerSession: 40000,
    studioName: '바디라인 스튜디오',
    registrationDate: '2024/03/10',
    initialActive: true,
  },
};

/**
 * 단일 강사 클래스
 */
export const SingleInstructor: Story = {
  args: {
    title: '아쉬탕가 요가',
    difficultyLevel: 'advanced',
    instructor: {
      name: '김요가',
    },
    category: '요가',
    maxCapacity: 6,
    registeredSessionCount: 18,
    ongoingSessionCount: 4,
    pricePerSession: 45000,
    studioName: '요가마스터',
    registrationDate: '2024/04/20',
    initialActive: true,
  },
};

/**
 * 높은 수강료 클래스
 */
export const PremiumClass: Story = {
  args: {
    title: '개인 맞춤 PT',
    difficultyLevel: 'intermediate',
    instructor: {
      name: '전문트레이너',
    },
    category: '퍼스널 트레이닝',
    maxCapacity: 1,
    registeredSessionCount: 30,
    ongoingSessionCount: 12,
    pricePerSession: 80000,
    studioName: '프리미엄 헬스',
    registrationDate: '2024/05/01',
    initialActive: false,
  },
};

/**
 * 신규 등록 클래스 (수업 진행 없음)
 */
export const NewClass: Story = {
  args: {
    title: '수영 기초반',
    difficultyLevel: 'beginner',
    instructor: {
      name: '수영코치',
    },
    category: '수영',
    maxCapacity: 12,
    registeredSessionCount: 0,
    ongoingSessionCount: 0,
    pricePerSession: 30000,
    studioName: '아쿠아 센터',
    registrationDate: '2024/12/01',
    initialActive: true,
  },
};
