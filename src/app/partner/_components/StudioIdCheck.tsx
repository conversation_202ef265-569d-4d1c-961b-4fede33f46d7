'use client';

import { partnerStudio<PERSON>pi } from '@/lib/api/partner/studio.api';
import { useQuery } from '@tanstack/react-query';
import { partnerStore } from '../_store/partner.store';

export default function StudioIdCheck({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: studios } = useQuery({
    queryKey: ['partner', 'studios'],
    queryFn: async () => {
      const data = await partnerStudioApi.getMyStudios();

      if (data.length === 0) {
        partnerStore.setStudio(null, null);
      } else {
        const firstStudio = data[0];
        partnerStore.setStudio(firstStudio.id, firstStudio.name);
      }

      return data;
    },
    // TODO: check query key
    staleTime: 5 * 60 * 1000, // 5분간 캐시
  });

  if (!studios) {
    return null;
  }

  return <>{children}</>;
}
