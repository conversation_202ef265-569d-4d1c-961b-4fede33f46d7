'use client';

import { cn } from '@/lib/utils';
import { Home, Users, BookOpen, Building2 } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

/**
 * 파트너 페이지에서 버텀 네비게이션을 숨겨야 하는 경로들
 */
const PARTNER_BOTTOM_NAV_HIDDEN_PATHS = [
  '/partner/login',
  '/partner/register',
  '/partner/pending',
  '/partner/suspended',
];

/**
 * 주어진 경로에서 파트너 버텀 네비게이션을 표시해야 하는지 결정
 * @param pathname - 현재 경로
 * @returns 버텀 네비게이션 표시 여부
 */
export function shouldShowPartnerBottomNav(pathname: string): boolean {
  // 파트너 경로가 아니면 숨김
  if (!pathname.startsWith('/partner')) {
    return false;
  }

  // 숨겨야 할 경로인지 확인
  return !PARTNER_BOTTOM_NAV_HIDDEN_PATHS.some(hiddenPath =>
    pathname.startsWith(hiddenPath)
  );
}

const navItems = [
  {
    href: '/partner/dashboard',
    label: '홈',
    icon: Home,
    activePattern: /^\/partner\/dashboard$/,
  },
  {
    href: '/partner/classes',
    label: '클래스',
    icon: BookOpen,
    activePattern: /^\/partner\/classes/,
  },
  {
    href: '/partner/studios',
    label: '센터관리',
    icon: Building2,
    activePattern: /^\/partner\/studios/,
  },
  {
    href: '/partner/instructor',
    label: '강사관리',
    icon: Users,
    activePattern: /^\/partner\/(instructor|profile|onboarding)/,
  },
];

export function PartnerBottomNav() {
  const pathname = usePathname();

  return (
    <nav
      className={cn(
        'fixed bottom-0 z-[49]',
        'mx-auto w-full max-w-[480px]',
        'border-t border-[#eeeeee] bg-white',
        'px-4 py-2'
      )}
    >
      <div className='flex justify-around'>
        {navItems.map(item => {
          const IconComponent = item.icon;
          const isActive = item.activePattern.test(pathname);

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex flex-col items-center px-3 py-2 transition-colors',
                isActive
                  ? 'text-indigo-600'
                  : 'text-gray-500 hover:text-gray-700'
              )}
            >
              <IconComponent
                className={cn(
                  'mb-1 h-6 w-6',
                  isActive ? 'text-indigo-600' : 'text-gray-500'
                )}
              />
              <span
                className={cn(
                  'text-xs font-medium',
                  isActive ? 'text-indigo-600' : 'text-gray-500'
                )}
              >
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
