import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { useState } from 'react';
import { ClassListItem } from '../_types/client.type';

export interface PartnerClassCardProps {
  item: ClassListItem;
  /** 상세보기 클릭 핸들러 */
  onViewDetails: () => void;
  /** 수정 클릭 핸들러 */
  onEdit: () => void;
  /** 클래스 활성화 상태 변경 핸들러 */
  onActiveChange?: (active: boolean) => void;
}

/**
 * 난이도 레벨을 한국어로 변환하는 함수
 */
const getDifficultyLabel = (level: ClassListItem['level']): string => {
  const labels = {
    beginner: '초급 대상',
    intermediate: '중급 대상',
    advanced: '고급 대상',
  };
  return labels[level];
};

/**
 * 가격을 한국어 형식으로 포매팅하는 함수
 */
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('ko-KR').format(price);
};

export function PartnerClassCard({
  item,
  onViewDetails,
  onEdit,
  onActiveChange,
}: PartnerClassCardProps) {
  const [isActive, setIsActive] = useState(item.initialActive);

  const handleActiveChange = (checked: boolean) => {
    setIsActive(checked);
    onActiveChange?.(checked);
  };
  return (
    <div className='max-w-[600px] rounded-md p-3 shadow-sm'>
      <header className='p-0'>
        <div className='flex items-start justify-between'>
          <div>
            <h3 className='text-foreground text-lg font-semibold'>
              {item.title}
            </h3>
          </div>
          <div className='flex items-center gap-2'>
            <span className='text-muted-foreground text-sm'>
              {isActive ? '노출중' : '미노출'}
            </span>
            <div className='relative'>
              <Switch
                checked={isActive}
                onCheckedChange={handleActiveChange}
                aria-label='클래스 활성화 토글'
              />
            </div>
          </div>
        </div>
      </header>
      <div className='space-y-6'>
        {/* 클래스 정보 */}
        <div className='grid grid-cols-2 gap-4'>
          <div>
            <p className='text-muted-foreground mb-1 text-sm'>담당 강사</p>
            <p className='text-foreground font-semibold'>
              {item.instructorName}
            </p>
          </div>
          <div>
            <p className='text-muted-foreground mb-1 text-sm'>운동 분야</p>
            <p className='text-foreground font-semibold'>{item.category}</p>
          </div>
          <div>
            <p className='text-muted-foreground mb-1 text-sm'>운동 수준</p>
            <p className='text-foreground font-semibold'>
              {getDifficultyLabel(item.level)}
            </p>
          </div>
          <div>
            <p className='text-muted-foreground mb-1 text-sm'>수업 정원</p>
            <p className='text-foreground font-semibold'>
              {item.maxParticipants}명
            </p>
          </div>
          <div>
            <p className='text-muted-foreground mb-1 text-sm'>
              등록된 수업 시간
            </p>
            <p className='text-foreground font-semibold'>
              {item.enrollmentCount}개
            </p>
          </div>
          <div>
            <p className='text-muted-foreground mb-1 text-sm'>
              진행 중 수업 시간
            </p>
            {/* TODO: 진행 중 수업 시간 추가 */}
            <p className='text-foreground font-semibold'>
              {item.enrollmentCount}개
            </p>
          </div>
        </div>
        <div className='flex items-center justify-between border-t pt-4'>
          <div>
            <div className='flex flex-col items-baseline gap-[0.5px]'>
              <span className='text-primary text-sm font-medium'>1인 기준</span>
              <span className='text-primary text-2xl font-bold'>
                회당 {formatPrice(item.pricePerSession)}원
              </span>
              <span className='text-muted-foreground text-xs'>
                {item.studioName} · {item.registrationDate} 등록
              </span>
            </div>
          </div>

          {/* 액션 버튼 */}
          <div className='flex max-w-[40%] flex-1 gap-3'>
            <Button
              className='flex-1 bg-gray-100 text-black hover:bg-gray-200'
              onClick={onViewDetails}
            >
              상세보기
            </Button>
            <Button className='flex-1' onClick={onEdit}>
              수정
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PartnerClassCard;
