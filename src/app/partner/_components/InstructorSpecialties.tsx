import { Button } from '@/components/ui/button';
import { SPECIALTY_OPTIONS } from '@/lib/schemas/instructor';
import { Minus, Plus } from 'lucide-react';

interface InstructorSpecialtiesProps {
  // Form 연동을 위한 props
  selectedSpecialties: string[];
  onSpecialtyToggle: (specialty: string) => void;
  onExperienceAdjust: (specialty: string, adjustment: number) => void;

  // 현재 전문분야 데이터 (form.watch('specialties')에서 가져온 값)
  specialtiesData: Array<{ type: string; years: number }>;

  // 에러 상태
  error?: string;

  // UI 설정 (선택사항)
  className?: string;
  disabled?: boolean;
}

export default function InstructorSpecialties({
  selectedSpecialties,
  onSpecialtyToggle,
  onExperienceAdjust,
  specialtiesData,
  error,
  className = '',
  disabled = false,
}: InstructorSpecialtiesProps) {
  return (
    <div className={className}>
      <div>
        <label className='text-sm font-medium'>
          전문 분야 선택 <span className='text-primary font-bold'>*</span>
        </label>
        <p className='text-muted-foreground mb-3 text-xs'>
          강사님의 전문 분야를 모두 선택해주세요.
        </p>
        <div className='grid grid-cols-2 gap-2'>
          {SPECIALTY_OPTIONS.map(option => (
            <Button
              key={option}
              type='button'
              variant={
                selectedSpecialties.includes(option) ? 'default' : 'outline'
              }
              onClick={() => !disabled && onSpecialtyToggle(option)}
              disabled={disabled}
              className='text-xs'
            >
              {option}
            </Button>
          ))}
        </div>
        {error && <p className='text-destructive mt-1 text-sm'>{error}</p>}
      </div>

      {selectedSpecialties.length > 0 && (
        <div className='mt-4'>
          <label className='text-sm font-medium'>분야별 경력 선택</label>
          <div className='mt-3 flex flex-col gap-3'>
            {selectedSpecialties.map(specialty => {
              const specialtyData = specialtiesData.find(
                spec => spec.type === specialty
              );
              const experience = specialtyData?.years || 1;
              const specialtyLabel = SPECIALTY_OPTIONS.find(
                opt => opt === specialty
              );

              return (
                <div
                  key={specialty}
                  className='flex items-center justify-between rounded-md border bg-zinc-50 p-2 px-3'
                >
                  <span className='text-sm font-medium'>{specialtyLabel}</span>
                  <div className='flex items-center gap-3'>
                    <Button
                      type='button'
                      variant='outline'
                      size='sm'
                      onClick={() => onExperienceAdjust(specialty, -1)}
                      disabled={disabled || experience <= 1}
                      className='h-8 w-8 p-0'
                    >
                      <Minus size={14} />
                    </Button>
                    <span className='min-w-[3rem] text-center text-sm font-semibold'>
                      {experience}년
                    </span>
                    <Button
                      type='button'
                      variant='outline'
                      size='sm'
                      onClick={() => onExperienceAdjust(specialty, 1)}
                      disabled={disabled || experience >= 20}
                      className='h-8 w-8 p-0'
                    >
                      <Plus size={14} />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
