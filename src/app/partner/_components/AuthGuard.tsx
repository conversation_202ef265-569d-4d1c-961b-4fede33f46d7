'use client';

import { ReactNode, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/contexts/user.store';

export default function AuthGuard({ children }: { children: ReactNode }) {
  const router = useRouter();
  const { user, role, loading } = useUserStore();

  useEffect(() => {
    if (!loading) {
      if (!user || role !== 'INSTRUCTOR') {
        router.push('/partner/login');
      }
    }
  }, [user, role, loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!user || role !== 'INSTRUCTOR') {
    return null;
  }

  return <>{children}</>;
}
