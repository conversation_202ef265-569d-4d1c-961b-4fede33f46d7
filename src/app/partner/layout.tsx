'use client';

import {
  PartnerBottomNav,
  shouldShowPartnerBottomNav,
} from '@/app/partner/_components/PartnerBottomNav';
import { cn } from '@/lib/utils';
import { usePathname } from 'next/navigation';

interface PartnerLayoutProps {
  children: React.ReactNode;
}

export default function PartnerLayout({ children }: PartnerLayoutProps) {
  const pathname = usePathname();
  const showBottomNav = shouldShowPartnerBottomNav(pathname);

  return (
    <>
      <>
        {/* TODO: check appbar logic in partner page */}
        {/* <AppBar /> */}
        <main className={cn('bg-white', showBottomNav ? 'pb-20' : undefined)}>
          {children}
        </main>
        {showBottomNav && <PartnerBottomNav />}
      </>
    </>
  );
}
