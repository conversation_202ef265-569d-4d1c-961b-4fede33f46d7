'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useSuspenseQuery } from '@tanstack/react-query';
import { classApi } from '@/lib/api/partner/class.api';
import { ClassListItemResponse } from '@/lib/api/partner/class.schema';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import PartnerClassCard from '../../_components/PartnerClassCard';
import { ClassListItem } from '../../_types/client.type';

// 로딩 상태를 위한 스켈레톤 컴포넌트
function PartnerClassCardSkeleton() {
  return (
    <div className='max-w-[600px] rounded-md border border-gray-200 p-3 shadow-sm'>
      <header className='mb-4 p-0'>
        <div className='flex items-start justify-between'>
          <Skeleton className='h-6 w-48' />
          <div className='flex items-center gap-2'>
            <Skeleton className='h-5 w-12' />
            <Skeleton className='h-6 w-12' />
          </div>
        </div>
      </header>
      <div className='space-y-6'>
        <div className='grid grid-cols-2 gap-4'>
          {[...Array(6)].map((_, i) => (
            <div key={i}>
              <Skeleton className='mb-1 h-4 w-20' />
              <Skeleton className='h-5 w-24' />
            </div>
          ))}
        </div>
        <div className='flex items-center justify-between border-t pt-4'>
          <div>
            <Skeleton className='mb-1 h-4 w-16' />
            <Skeleton className='mb-1 h-8 w-32' />
            <Skeleton className='h-3 w-40' />
          </div>
          <div className='flex max-w-[40%] flex-1 gap-3'>
            <Skeleton className='h-10 w-full' />
            <Skeleton className='h-10 w-full' />
          </div>
        </div>
      </div>
    </div>
  );
}

// 스켈레톤 목록 컴포넌트
function ClassListSkeleton() {
  return (
    <div className='space-y-4'>
      <PartnerClassCardSkeleton />
      <PartnerClassCardSkeleton />
      <PartnerClassCardSkeleton />
    </div>
  );
}

// 데이터가 없을 때 표시할 컴포넌트
function EmptyClassList() {
  return (
    <div className='flex flex-col items-center justify-center rounded-lg border-2 border-dashed px-4 py-12 text-center'>
      <h2 className='text-xl font-semibold text-gray-800'>
        아직 등록된 클래스가 없어요
      </h2>
      <p className='mt-2 mb-6 text-gray-500'>
        새로운 클래스를 개설하고 수강생을 모집해보세요!
      </p>
      <Button asChild>
        <a href='/partner/classes/new'>클래스 개설하기</a>
      </Button>
    </div>
  );
}

// API 응답을 클라이언트 타입으로 변환하는 매퍼 함수
const toClassListItem = (item: ClassListItemResponse): ClassListItem => {
  return {
    id: item.id,
    title: item.title,
    level: item.level,
    instructorName: item.instructorName,
    category: item.category,
    maxParticipants: item.maxParticipants,
    enrollmentCount: item.scheduleGroups.reduce(
      (sum, group) => sum + group.schedules.length,
      0
    ),
    initialActive: item.visible ?? false,
    pricePerSession: item.pricePerSession,
    studioName: item.studioName,
    registrationDate: item.createdAt
      ? new Date(item.createdAt).toLocaleDateString('ko-KR')
      : 'N/A',
  };
};

function ClassList() {
  const { data: response } = useSuspenseQuery({
    queryKey: ['partner', 'classes'],
    queryFn: () => classApi.getAllClasses({ page: 1, limit: 10 }),
  });

  const classItems = response.data.map(toClassListItem);

  if (classItems.length === 0) {
    return <EmptyClassList />;
  }

  const handleViewDetails = (classId: string) => {
    console.log('상세보기 클릭:', classId);
    // TODO: 상세보기 페이지로 이동
  };

  const handleEdit = (classId: string) => {
    console.log('수정 클릭:', classId);
    // TODO: 수정 페이지로 이동
  };

  const handleActiveChange = (classId: string, active: boolean) => {
    console.log('활성화 상태 변경:', classId, active);
    // TODO: API 호출로 활성화 상태 업데이트
  };

  return (
    <div className='space-y-4'>
      {classItems.map(item => (
        <PartnerClassCard
          key={item.id}
          item={item}
          onViewDetails={() => handleViewDetails(item.id)}
          onEdit={() => handleEdit(item.id)}
          onActiveChange={active => handleActiveChange(item.id, active)}
        />
      ))}
    </div>
  );
}

export default function PartnerClassesPage() {
  return (
    <div className='p-4'>
      {/* Header */}
      <div className='mb-6 flex justify-between'>
        <h1 className='text-foreground text-xl font-bold'>클래스 관리</h1>
        <Button size='sm' asChild>
          <a href='/partner/classes/new'>클래스 개설</a>
        </Button>
      </div>

      <Suspense fallback={<ClassListSkeleton />}>
        <ClassList />
      </Suspense>
    </div>
  );
}
