'use client';
import MultipleImageUpload from '@/app/partner/_components/MultipleImageUpload';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { classApi } from '@/lib/api/partner/class.api';
import {
  CreateClassRequest,
  createClassRequestSchema,
} from '@/lib/api/partner/class.schema';
import { GetInstructorsResponse } from '@/lib/api/partner/instructor.schema';
import { instructorApi } from '@/lib/api/partner/instructor.api';
import { usePartnerStore } from '@/app/partner/_store/partner.store';
import { CreateClassInput } from '@/lib/validations/partner-class.validation';
import { DevTool } from '@hookform/devtools';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ClassScheduleSelector } from './_components/ClassScheduleSelector';
import { SpecialtyLevelSelector } from './_components/SpecialtyLevelSelector';
import { StudioInstructorSelector } from './_components/StudioInstructorSelector';
import { TargetAudienceSelector } from './_components/TargetAudienceSelector';
import { SessionDurationMinutesSelector } from './_components/SessionDurationMinutesSelector';

import { ScheduleGroup } from './_components/types';

const capacityOptions = [
  { value: '5', label: '5명' },
  { value: '8', label: '8명' },
  { value: '10', label: '10명' },
  { value: '12', label: '12명' },
  { value: '15', label: '15명' },
  { value: '20', label: '20명' },
];

const weeklyFrequencyOptions = [
  { value: '1', label: '주 1회' },
  { value: '2', label: '주 2회' },
  { value: '3', label: '주 3회' },
];

export default function NewClassPage() {
  const router = useRouter();
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const { studioId, studioName } = usePartnerStore();

  const [instructors, setInstructors] = useState<GetInstructorsResponse>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<CreateClassRequest>({
    resolver: zodResolver(createClassRequestSchema),
    defaultValues: {
      studioId: studioId || '',
      instructorId: '',
      title: '',
      description: '',
      category: 'yoga',
      level: 'beginner',
      target: 'mixed',
      maxParticipants: 10,
      sessionsPerWeek: 2,
      pricePerSession: 20000,
      sessionDurationMinutes: 60,
      durationWeeks: 4,
      scheduleGroups: [],
    },
  });
  const weeklyFrequency = form.watch('sessionsPerWeek');
  const duration = form.watch('sessionDurationMinutes');
  const scheduleGroups = form.watch('scheduleGroups');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const data = await instructorApi.getInstructors();
        setInstructors(data);
      } catch (err) {
        setError('강사 데이터를 불러오는 데 실패했습니다.');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleScheduleGroupsChange = (newScheduleGroups: ScheduleGroup[]) => {
    console.log('newScheduleGroups', newScheduleGroups);
    form.setValue('scheduleGroups', newScheduleGroups);
  };

  const onSubmit = async (data: CreateClassRequest) => {
    console.log('data', data);
    try {
      // 이미지 업로드 검증
      // if (uploadedImages.length === 0) {
      //   alert('최소 1개의 운동 대표 사진을 업로드해주세요.');
      //   return;
      // }

      // // 스케줄 그룹 검증
      // if (scheduleGroups.length === 0) {
      //   alert('최소 1개의 스케줄 그룹을 등록해주세요.');
      //   return;
      // }

      // 전체 스케줄 개수 계산
      const totalScheduleCount = scheduleGroups.reduce(
        (acc, group) => acc + group.schedules.length,
        0
      );

      // 주차 횟수와 스케줄 개수 일치 검증 (최소 2회)
      const weeklyFrequency = data.sessionsPerWeek;
      if (totalScheduleCount < 2) {
        alert('최소 주 2회 수업 시간을 등록해주세요.');
        return;
      }
      if (totalScheduleCount !== weeklyFrequency) {
        alert(
          `주 ${weeklyFrequency}회 설정에 맞춰 ${weeklyFrequency}개의 수업 시간을 등록해주세요. (현재 ${totalScheduleCount}개)`
        );
        return;
      }

      // const imageUploadPromises = uploadedImages.map(file =>
      //   classApi.uploadClassImage({ file })
      // );
      // const uploadedImageResults = await Promise.all(imageUploadPromises);
      // const images = uploadedImageResults.map(result => ({
      //   url: result.url,
      //   path: result.path,
      // }));

      // TODO: check 이미지 업로드
      const payload: CreateClassInput = {
        ...data,
        images: [
          {
            url: 'https://example.com/image1.jpg',
            path: '/uploads/classes/image1.jpg',
          },
        ],
        scheduleGroups: scheduleGroups,
        pricePerSession: Number(data.pricePerSession),
        maxParticipants: Number(data.maxParticipants),
        sessionDurationMinutes: Number(data.sessionDurationMinutes),
        durationWeeks: Number(data.durationWeeks),
        sessionsPerWeek: Number(data.sessionsPerWeek),
      };

      await classApi.createClass(payload);
      alert('클래스가 성공적으로 등록되었습니다!');
      router.push('/partner/classes');
    } catch (error) {
      console.error('클래스 등록 실패:', error);
      alert('클래스 등록에 실패했습니다.');
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  console.log('scheduleGroups', scheduleGroups);
  return (
    <div className='p-3'>
      <div className='mb-4'>
        <h1 className='text-foreground text-lg font-semibold'>클래스 등록</h1>
      </div>

      <div className=''>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit, error => {
              console.error('handleSubmit error', error);
            })}
            className='flex flex-col gap-6'
          >
            {/* 센터/강사 선택 */}
            <StudioInstructorSelector
              control={form.control}
              studioName={studioName}
              instructors={instructors}
            />

            {/* 기본 정보 */}
            <div className=''>
              <h2 className='mb-4 text-base font-semibold'>기본 정보</h2>
              <div className='flex flex-col gap-4'>
                {/* 제목 */}
                <FormField
                  control={form.control}
                  name='title'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        그룹 운동 제목{' '}
                        <span className='text-primary font-bold'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='예: 초보자를 위한 하타 요가'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 상세 소개 */}
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        그룹 운동 상세 소개{' '}
                        <span className='text-primary font-bold'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='수업의 특징, 커리큘럼, 수강생에게 도움이 되는 점 등을 설명해주세요.'
                          className='min-h-[100px]'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 운동 대표 사진 업로드 */}
                <MultipleImageUpload
                  images={uploadedImages}
                  onChange={setUploadedImages}
                  type='class'
                  maxImages={5}
                />

                <SpecialtyLevelSelector control={form.control} />

                <div className='flex flex-col gap-4'>
                  <TargetAudienceSelector control={form.control} />

                  <FormField
                    control={form.control}
                    name='maxParticipants'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          수업 정원{' '}
                          <span className='text-primary font-bold'>*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={String(field.value)}
                        >
                          <FormControl>
                            <SelectTrigger className='w-full'>
                              <SelectValue placeholder='수업 정원' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {capacityOptions.map(option => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <label className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'>
                    수업 기간 <span className='text-primary font-bold'>*</span>
                  </label>
                  <Select disabled={true} defaultValue='4'>
                    <SelectTrigger className='w-full'>
                      <SelectValue placeholder='수업 기간' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='4'>1개월</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className='flex flex-col gap-4'>
                  <FormField
                    control={form.control}
                    name='sessionsPerWeek'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          주 운동 횟수{' '}
                          <span className='text-primary font-bold'>*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={String(field.value)}
                        >
                          <FormControl>
                            <SelectTrigger className='w-full'>
                              <SelectValue placeholder='주 운동 횟수' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {weeklyFrequencyOptions.map(option => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <SessionDurationMinutesSelector control={form.control} />
                </div>

                <FormField
                  control={form.control}
                  name='pricePerSession'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        회당 수강료{' '}
                        <span className='text-primary font-bold'>*</span>
                      </FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <Input
                            type='number'
                            placeholder='20,000'
                            {...field}
                          />
                          <span className='text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 text-sm'>
                            원
                          </span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* 수업 시간 개설 */}
            <ClassScheduleSelector
              setValue={form.setValue}
              weeklyFrequency={weeklyFrequency || 0}
              duration={duration || 0}
              onScheduleGroupsChange={handleScheduleGroupsChange}
            />

            {/* 수업 개설 버튼 */}
            <div className='pt-6'>
              <Button
                type='submit'
                className='h-12 w-full text-base font-medium'
                size='lg'
              >
                수업 개설 등록
              </Button>
            </div>
          </form>
        </Form>
      </div>
      <DevTool control={form.control} placement='top-right' />
    </div>
  );
}
