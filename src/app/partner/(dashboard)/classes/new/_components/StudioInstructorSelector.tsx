'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useWatch, type Control } from 'react-hook-form';
import { GetInstructorsResponse } from '@/lib/api/partner/instructor.schema';
import { CreateClassRequest } from '@/lib/api/partner/class.schema';
import { StudioResponse } from '@/lib/api/partner/studio.schema';

interface StudioInstructorSelectorProps {
  control: Control<CreateClassRequest>;
  studios: StudioResponse[];
  instructors: GetInstructorsResponse;
}

export function StudioInstructorSelector({
  control,
  studios,
  instructors,
}: StudioInstructorSelectorProps) {
  const selectedStudioId = useWatch({
    control,
    name: 'studioId',
  });

  // instructors는 이미 선택된 스튜디오의 강사만 포함
  const filteredInstructors = instructors;

  return (
    <div className=''>
      <h2 className='mb-4 text-base font-semibold'>센터/강사</h2>
      <div className='flex flex-col gap-4'>
        <FormField
          control={control}
          name='studioId'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                센터 <span className='text-primary font-bold'>*</span>
              </FormLabel>
              <Select
                defaultValue={field.value}
                onValueChange={value => {
                  field.onChange(value);
                }}
              >
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='센터 선택' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {studios.map(center => (
                    <SelectItem key={center.id} value={center.id}>
                      {center.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name='instructorId'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                강사 <span className='text-primary font-bold'>*</span>
              </FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!selectedStudioId}
              >
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue
                      placeholder={
                        !selectedStudioId
                          ? '먼저 센터를 선택하세요'
                          : filteredInstructors.length === 0
                            ? '강사가 없습니다'
                            : '강사 선택'
                      }
                    />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {filteredInstructors.map(instructor => (
                    <SelectItem key={instructor.id} value={instructor.id}>
                      {instructor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
