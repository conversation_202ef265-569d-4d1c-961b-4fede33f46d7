'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { type Control } from 'react-hook-form';
import { GetInstructorsResponse } from '@/lib/api/partner/instructor.schema';
import { CreateClassRequest } from '@/lib/api/partner/class.schema';

interface StudioInstructorSelectorProps {
  control: Control<CreateClassRequest>;
  studioName: string | null | undefined;
  instructors: GetInstructorsResponse;
}

export function StudioInstructorSelector({
  control,
  studioName,
  instructors,
}: StudioInstructorSelectorProps) {
  return (
    <div className=''>
      <h2 className='mb-4 text-base font-semibold'>센터/강사</h2>
      <div className='flex flex-col gap-4'>
        <div>
          <label className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'>
            센터 <span className='text-primary font-bold'>*</span>
          </label>
          <div className='bg-muted text-muted-foreground mt-2 rounded-md border px-3 py-2'>
            {studioName || '센터 정보를 불러오는 중...'}
          </div>
        </div>

        <FormField
          control={control}
          name='instructorId'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                강사 <span className='text-primary font-bold'>*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue
                      placeholder={
                        instructors.length === 0
                          ? '강사가 없습니다'
                          : '강사 선택'
                      }
                    />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {instructors.map(instructor => (
                    <SelectItem key={instructor.id} value={instructor.id}>
                      {instructor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
