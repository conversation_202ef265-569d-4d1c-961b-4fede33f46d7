'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CreateClassRequest } from '@/lib/api/partner/class.schema';
import { Control } from 'react-hook-form';

interface SessionDurationMinutesSelectorProps {
  control: Control<CreateClassRequest>;
}

const classDurationOptions = [
  { value: '50', label: '50분' },
  { value: '60', label: '60분' },
  { value: '90', label: '90분' },
];

export function SessionDurationMinutesSelector({
  control,
}: SessionDurationMinutesSelectorProps) {
  return (
    <FormField
      control={control}
      name='sessionDurationMinutes'
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            수업 시간 <span className='text-primary font-bold'>*</span>
          </FormLabel>
          <Select
            onValueChange={field.onChange}
            defaultValue={String(field.value)}
          >
            <FormControl>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='수업 시간' />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {classDurationOptions.map(option => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
