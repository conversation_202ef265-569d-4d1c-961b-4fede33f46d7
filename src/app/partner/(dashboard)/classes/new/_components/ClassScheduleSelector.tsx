import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DAY_OF_WEEK_OPTIONS } from '@/lib/constants/class-enums';
import {
  DayOfWeekCode,
  type DayOfWeekCode as DayOfWeekCodeType,
} from '@/lib/db/schema';
import { CreateClassRequest } from '@/lib/api/partner/class.schema';
import { Clock, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { UseFormSetValue } from 'react-hook-form';
import { ScheduleGroup, ScheduleGroupWithId, ScheduleWithId } from './types';

interface ClassScheduleSelectorProps {
  setValue: UseFormSetValue<CreateClassRequest>;
  weeklyFrequency: number;
  duration: number;
  onScheduleGroupsChange: (scheduleGroups: ScheduleGroup[]) => void;
}

const timeOptions = Array.from({ length: 48 }, (_, i) => {
  const hour = Math.floor(i / 2);
  const minute = (i % 2) * 30;
  const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  return { value: timeString, label: timeString };
});

const createEmptySchedules = (count: number): ScheduleWithId[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: `temp-${Date.now()}-${index}`,
    dayOfWeek: '' as DayOfWeekCodeType,
    startTime: '',
    endTime: '',
  }));
};
const dayOfWeekTextMap = {
  [DayOfWeekCode.MON]: '월요일',
  [DayOfWeekCode.TUE]: '화요일',
  [DayOfWeekCode.WED]: '수요일',
  [DayOfWeekCode.THU]: '목요일',
  [DayOfWeekCode.FRI]: '금요일',
  [DayOfWeekCode.SAT]: '토요일',
  [DayOfWeekCode.SUN]: '일요일',
};

export function ClassScheduleSelector({
  weeklyFrequency,
  duration,
  onScheduleGroupsChange,
}: ClassScheduleSelectorProps) {
  const [scheduleGroups, setScheduleGroups] = useState<ScheduleGroupWithId[]>(
    []
  );
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [newSchedules, setNewSchedules] = useState<ScheduleWithId[]>([]);

  const calculateEndTime = (
    startTime: string,
    durationMinutes: number
  ): string => {
    const [hours, minutes] = startTime.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes + durationMinutes;
    const endHours = Math.floor(totalMinutes / 60);
    const endMinutes = totalMinutes % 60;
    return `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;
  };

  const validateScheduleGroup = (
    schedules: ScheduleWithId[]
  ): string | null => {
    // 모든 필수 정보 입력 확인
    for (let i = 0; i < schedules.length; i++) {
      const schedule = schedules[i];
      if (!schedule.dayOfWeek || !schedule.startTime || !schedule.endTime) {
        return `${i + 1}번째 수업 시간의 모든 정보를 입력해주세요.`;
      }
    }

    // 시작 시간이 종료 시간보다 빠른지 체크
    for (let i = 0; i < schedules.length; i++) {
      const schedule = schedules[i];
      if (schedule.startTime >= schedule.endTime) {
        return `${i + 1}번째 수업의 시작 시간은 종료 시간보다 빨라야 합니다.`;
      }
    }

    // 그룹 내 중복 요일 체크
    const days = schedules.map(s => s.dayOfWeek);
    if (new Set(days).size !== days.length) {
      return '같은 그룹 내에서 동일한 요일에 여러 스케줄을 설정할 수 없습니다.';
    }

    // 같은 그룹 내 모든 스케줄은 동일한 시간대여야 함
    if (schedules.length > 1) {
      const firstSchedule = schedules[0];
      const hasSameTime = schedules.every(
        s =>
          s.startTime === firstSchedule.startTime &&
          s.endTime === firstSchedule.endTime
      );
      if (!hasSameTime) {
        return '같은 그룹 내 모든 스케줄은 동일한 시간대여야 합니다.';
      }
    }

    // 그룹당 최대 7개 제한
    if (schedules.length > 7) {
      return '각 그룹에는 최대 7개까지 스케줄 설정 가능합니다.';
    }

    return null;
  };

  const validateAllGroups = (newGroup: ScheduleWithId[]): string | null => {
    // 전체 그룹에서 중복된 요일 체크
    const allDays = scheduleGroups
      .flatMap(group => group.schedules.map(s => s.dayOfWeek))
      .concat(newGroup.map(s => s.dayOfWeek));

    if (new Set(allDays).size !== allDays.length) {
      return '서로 다른 그룹에서 동일한 요일을 설정할 수 없습니다.';
    }

    // 최대 5개 그룹 제한
    if (scheduleGroups.length >= 5) {
      return '최대 5개까지 스케줄 그룹 설정 가능합니다.';
    }

    return null;
  };

  const addScheduleGroup = () => {
    // 개별 그룹 검증
    const groupError = validateScheduleGroup(newSchedules);
    if (groupError) {
      alert(groupError);
      return;
    }

    // 전체 그룹 검증
    const allGroupsError = validateAllGroups(newSchedules);
    if (allGroupsError) {
      alert(allGroupsError);
      return;
    }

    // 주차 횟수 제한 체크
    const totalScheduleCount =
      scheduleGroups.reduce((acc, group) => acc + group.schedules.length, 0) +
      newSchedules.length;

    if (weeklyFrequency > 0 && totalScheduleCount > weeklyFrequency) {
      alert(
        `주 ${weeklyFrequency}회로 설정되어 더 이상 시간을 추가할 수 없습니다.`
      );
      return;
    }

    // 스케줄 그룹 추가
    const newGroup: ScheduleGroupWithId = {
      id: `group-${Date.now()}`,
      schedules: newSchedules,
    };

    const updatedGroups = [...scheduleGroups, newGroup];
    setScheduleGroups(updatedGroups);
    // setValue('scheduleGroups', updatedGroups);

    // 메인 페이지로 데이터 전달 (id 제거)
    const cleanGroups: ScheduleGroup[] = updatedGroups.map(group => ({
      schedules: group.schedules.map(({ id, ...schedule }) => schedule),
    }));
    console.log('cleanGroups', cleanGroups);
    onScheduleGroupsChange(cleanGroups);
    setNewSchedules(createEmptySchedules(weeklyFrequency));
    setShowScheduleForm(false);
  };

  const removeScheduleGroup = (groupId: string) => {
    const updatedGroups = scheduleGroups.filter(group => group.id !== groupId);
    setScheduleGroups(updatedGroups);

    // 메인 페이지로 데이터 전달
    const cleanGroups: ScheduleGroup[] = updatedGroups.map(group => ({
      schedules: group.schedules.map(({ id, ...schedule }) => schedule),
    }));
    onScheduleGroupsChange(cleanGroups);
  };

  const updateNewSchedule = (
    index: number,
    field: keyof ScheduleWithId,
    value: string
  ) => {
    setNewSchedules(prev =>
      prev.map((item, i) => {
        if (i === index) {
          const updatedItem = { ...item, [field]: value };

          // 시작 시간 변경 시 종료 시간 자동 계산
          if (field === 'startTime' && duration && value) {
            updatedItem.endTime = calculateEndTime(value, duration);
          }

          return updatedItem;
        }
        return item;
      })
    );
  };

  useEffect(() => {
    if (weeklyFrequency) {
      setNewSchedules(createEmptySchedules(weeklyFrequency));
    }
  }, [weeklyFrequency]);

  useEffect(() => {
    if (duration) {
      // 기존 newSchedules의 종료 시간 재계산
      setNewSchedules(prev =>
        prev.map(schedule => {
          if (schedule.startTime) {
            return {
              ...schedule,
              endTime: calculateEndTime(schedule.startTime, duration),
            };
          }
          return schedule;
        })
      );
    }
  }, [duration]);

  return (
    <div className=''>
      <h2 className='mb-1 text-base font-semibold'>수업 시간 개설</h2>
      <div className='flex flex-col gap-1'>
        <p className='text-primary text-xs'>
          1. 가능한 수업 시간을 모두 열어주세요.
        </p>
        <p className='text-primary text-xs'>
          2. 수업 정원 모집이 완료되면 수업을 &apos;확정&apos;하고, 운동
          시작일을 안내해주세요.
        </p>
        <p className='text-primary text-xs'>
          3. 같은 그룹 내 모든 스케줄은 동일한 시간대여야 합니다.
        </p>
      </div>
      <div className='my-2'>
        <Button
          type='button'
          variant='secondary'
          onClick={() => {
            if (!weeklyFrequency) {
              alert('먼저 주 운동 횟수를 선택해주세요.');
              return;
            }

            if (!duration) {
              alert('먼저 수업 시간을 선택해주세요.');
              return;
            }
            setShowScheduleForm(true);
          }}
          className='w-full cursor-pointer'
        >
          수업 시간 그룹 추가
        </Button>
      </div>
      <div className='flex flex-col gap-4'>
        {/* Schedule Groups */}
        {scheduleGroups.length > 0 && (
          <div className='flex flex-col gap-3'>
            {scheduleGroups.map((group, groupIndex) => (
              <div
                key={group.id}
                className='relative rounded-sm border border-gray-200 bg-gray-50 p-3'
              >
                <div className='absolute top-2 right-2'>
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={() => removeScheduleGroup(group.id)}
                    className='h-6 w-6 p-0 text-red-500 hover:text-red-700'
                  >
                    <X size={14} />
                  </Button>
                </div>
                <div className='flex flex-col gap-1'>
                  <div className='mb-1 text-xs font-medium text-gray-500'>
                    그룹 {groupIndex + 1} ({group.schedules[0]?.startTime} -{' '}
                    {group.schedules[0]?.endTime})
                  </div>
                  {group.schedules.map(schedule => (
                    <div
                      key={schedule.id}
                      className='flex items-center space-x-2'
                    >
                      <Clock size={14} className='text-muted-foreground' />
                      <span className='text-sm text-gray-600'>
                        {
                          DAY_OF_WEEK_OPTIONS.find(
                            d => d.value === schedule.dayOfWeek
                          )?.label
                        }{' '}
                        {schedule.startTime} - {schedule.endTime}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 수업 시간 그룹 추가 폼 */}
        {showScheduleForm && (
          <section className='border-primary-2 bg-gray-50 p-2'>
            <div className='flex flex-col gap-2'>
              <div className='mb-2 text-sm font-medium text-gray-700'>
                새 스케줄 그룹 (같은 시간대의 다른 요일들)
              </div>
              {newSchedules.map((schedule, index) => (
                <div key={schedule.id} className='flex flex-col gap-2'>
                  <div>
                    <Select
                      value={schedule.dayOfWeek}
                      onValueChange={value =>
                        updateNewSchedule(index, 'dayOfWeek', value)
                      }
                    >
                      <SelectTrigger className='w-full bg-white'>
                        <SelectValue placeholder='요일 선택' />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(dayOfWeekTextMap).map(
                          ([key, value]) => (
                            <SelectItem key={key} value={key}>
                              {value}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className='flex items-center gap-2'>
                    <div className='flex-1'>
                      <Select
                        value={schedule.startTime}
                        onValueChange={value =>
                          updateNewSchedule(index, 'startTime', value)
                        }
                      >
                        <SelectTrigger className='w-full bg-white'>
                          <SelectValue placeholder='시작 시간' />
                        </SelectTrigger>
                        <SelectContent>
                          {timeOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className='flex-1'>
                      <Select
                        value={schedule.endTime}
                        onValueChange={value =>
                          updateNewSchedule(index, 'endTime', value)
                        }
                        disabled={!!duration && !!schedule.startTime}
                      >
                        <SelectTrigger className='w-full bg-white'>
                          <SelectValue
                            placeholder={
                              duration && schedule.startTime
                                ? '자동 계산됨'
                                : '종료 시간'
                            }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {timeOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              ))}
              <div className='flex space-x-2'>
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  onClick={() => {
                    setShowScheduleForm(false);
                    setNewSchedules(createEmptySchedules(weeklyFrequency));
                  }}
                  className='flex-1'
                >
                  취소
                </Button>
                <Button
                  type='button'
                  size='sm'
                  onClick={addScheduleGroup}
                  className='flex-1'
                >
                  그룹 등록
                </Button>
              </div>
            </div>
          </section>
        )}

        {scheduleGroups.length === 0 && (
          <div className='text-muted-foreground py-8 text-center'>
            <Clock size={48} className='mx-auto mb-2 opacity-50' />
            <p className='text-sm'>아직 등록된 수업 시간이 없습니다.</p>
            <p className='text-xs'>
              수업 시간 그룹 추가 버튼을 눌러 시간을 등록해주세요.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
