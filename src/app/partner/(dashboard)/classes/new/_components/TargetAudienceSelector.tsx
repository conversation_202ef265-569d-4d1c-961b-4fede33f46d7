'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { type Control } from 'react-hook-form';
import { ClassTargetText } from '@/lib/db/schema';
import { CreateClassRequest } from '@/lib/api/partner/class.schema';

interface TargetAudienceSelectorProps {
  control: Control<CreateClassRequest>;
}

const targetLabel = {
  [ClassTargetText.WOMEN_ONLY]: '여성',
  [ClassTargetText.MEN_ONLY]: '남성',
  [ClassTargetText.MIXED]: '모두',
};

const targetOptions = Object.values(ClassTargetText).map(value => ({
  value,
  label: targetLabel[value as keyof typeof targetLabel],
}));

export function TargetAudienceSelector({
  control,
}: TargetAudienceSelectorProps) {
  return (
    <FormField
      control={control}
      name='target'
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            수강 대상 <span className='text-primary font-bold'>*</span>
          </FormLabel>
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='수강 대상' />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {targetOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
