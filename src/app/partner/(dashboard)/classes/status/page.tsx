'use client';

import { Button } from '@/components/ui/button';
import ClassStatusCard from './components/ClassStatusCard';

// Mock 데이터 - 다양한 클래스 상태를 표현
const mockClassStatusData = [
  {
    id: 1,
    classInfo: {
      title: '하타 요가 기초',
      maxCapacity: 12,
    },
    instructor: {
      name: '김민지',
    },
    schedule: [
      {
        weekDay: '월',
        startTime: '10:00',
        endTime: '11:00',
      },
      {
        weekDay: '수',
        startTime: '10:00',
        endTime: '11:00',
      },
      {
        weekDay: '금',
        startTime: '10:00',
        endTime: '11:00',
      },
    ],
    enrollments: [
      { name: '박수진' },
      { name: '이영희' },
      { name: '최민수' },
      { name: '강지은' },
      { name: '윤서연' },
    ],
  },
  {
    id: 2,
    classInfo: {
      title: '크로스핏 초급',
      maxCapacity: 8,
    },
    instructor: {
      name: '박철수',
    },
    schedule: [
      {
        weekDay: '화',
        startTime: '19:00',
        endTime: '20:00',
      },
      {
        weekDay: '목',
        startTime: '19:00',
        endTime: '20:00',
      },
    ],
    enrollments: [
      { name: '김태현' },
      { name: '이소영' },
      { name: '박민호' },
      { name: '정수빈' },
      { name: '한지우' },
      { name: '송민경' },
      { name: '오준석' },
      { name: '임나영' },
    ],
  },
  {
    id: 3,
    classInfo: {
      title: '매트 필라테스',
      maxCapacity: 10,
    },
    instructor: {
      name: '최은영',
    },
    schedule: [
      {
        weekDay: '월',
        startTime: '14:00',
        endTime: '15:00',
      },
      {
        weekDay: '금',
        startTime: '14:00',
        endTime: '15:00',
      },
    ],
    enrollments: [],
  },
  {
    id: 4,
    classInfo: {
      title: '개인 맞춤 PT',
      maxCapacity: 1,
    },
    instructor: {
      name: '전문트레이너',
    },
    schedule: [
      {
        weekDay: '화',
        startTime: '16:00',
        endTime: '17:00',
      },
    ],
    enrollments: [{ name: '김현우' }],
  },
  {
    id: 5,
    classInfo: {
      title: '저녁 복싱 클래스',
      maxCapacity: 12,
    },
    instructor: {
      name: '최복싱',
    },
    schedule: [
      {
        weekDay: '월',
        startTime: '20:00',
        endTime: '21:30',
      },
      {
        weekDay: '수',
        startTime: '20:00',
        endTime: '21:30',
      },
    ],
    enrollments: [
      { name: '직장인A' },
      { name: '직장인B' },
      { name: '직장인C' },
      { name: '직장인D' },
      { name: '직장인E' },
      { name: '직장인F' },
      { name: '직장인G' },
    ],
  },
  {
    id: 6,
    classInfo: {
      title: '수영 중급반',
      maxCapacity: 6,
    },
    instructor: {
      name: '물고기코치',
    },
    schedule: [
      {
        weekDay: '화',
        startTime: '18:00',
        endTime: '19:00',
      },
      {
        weekDay: '목',
        startTime: '18:00',
        endTime: '19:00',
      },
      {
        weekDay: '토',
        startTime: '10:00',
        endTime: '11:00',
      },
    ],
    enrollments: [
      { name: '수영선수' },
      { name: '물개' },
      { name: '돌고래' },
      { name: '바다거북' },
      { name: '상어' },
    ],
  },
];

export default function PartnerClassesStatusPage() {
  const handleRefresh = () => {
    // 새로고침 로직
    console.log('데이터 새로고침');
  };

  const handleViewAll = () => {
    // 전체 클래스 보기 로직
    console.log('전체 클래스 보기');
  };

  // 통계 데이터 계산
  const totalClasses = mockClassStatusData.length;
  const totalEnrollments = mockClassStatusData.reduce(
    (sum, classData) => sum + classData.enrollments.length,
    0
  );
  const fullClasses = mockClassStatusData.filter(
    classData => classData.enrollments.length >= classData.classInfo.maxCapacity
  ).length;

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* 헤더 */}
      <div className='border-b border-gray-200 bg-white'>
        <div className='px-4 py-6'>
          <div className='mb-4 flex items-center justify-between'>
            <h1 className='text-xl font-semibold text-gray-900'>클래스 현황</h1>
            <Button variant='outline' size='sm' onClick={handleRefresh}>
              새로고침
            </Button>
          </div>

          {/* 통계 요약 */}
          <div className='grid grid-cols-3 gap-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-600'>
                {totalClasses}
              </div>
              <div className='text-sm text-gray-500'>전체 클래스</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-green-600'>
                {totalEnrollments}
              </div>
              <div className='text-sm text-gray-500'>총 신청자</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-orange-600'>
                {fullClasses}
              </div>
              <div className='text-sm text-gray-500'>정원 마감</div>
            </div>
          </div>
        </div>
      </div>

      {/* 클래스 리스트 */}
      <div className='p-4'>
        {mockClassStatusData.length > 0 ? (
          <div className='space-y-4'>
            {mockClassStatusData.map(classData => (
              <ClassStatusCard
                key={classData.id}
                classInfo={classData.classInfo}
                instructor={classData.instructor}
                schedule={classData.schedule}
                enrollments={classData.enrollments}
              />
            ))}
          </div>
        ) : (
          /* 빈 상태 */
          <div className='py-12 text-center'>
            <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-200'>
              <svg
                className='h-8 w-8 text-gray-400'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253'
                />
              </svg>
            </div>
            <h3 className='mb-2 text-lg font-medium text-gray-900'>
              아직 등록된 클래스가 없습니다
            </h3>
            <p className='mb-6 text-gray-500'>
              새로운 클래스를 등록하여 수강생을 모집해보세요
            </p>
            <Button onClick={handleViewAll}>클래스 등록하기</Button>
          </div>
        )}
      </div>
    </div>
  );
}
