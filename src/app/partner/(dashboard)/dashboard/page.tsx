'use client';

import { ClassStatus, PartnerDashboardData } from '@/types/partner-dashboard';
import { useRouter } from 'next/navigation';
import { useUserStore } from '@/contexts/user.store';
import { usePartnerStore } from '@/app/partner/_store/partner.store';
import ProfileCard from './_components/ProfileCard';
import QuickActionGrid from './_components/QuickActionGrid';
import TodayClasses from './_components/TodayClasses';
import WeeklySchedule from './_components/WeeklySchedule';

// Mock 데이터
const MOCK_DASHBOARD_DATA: PartnerDashboardData = {
  partner: {
    id: 'partner-123',
    name: '김파트너',
    totalClasses: 15,
    monthlyClasses: 8,
  },
  classStats: {
    recruiting: 3,
    ongoing: 5,
    completed: 12,
    upcoming: 2,
  },
  monthlyStats: {
    totalClasses: 8,
    totalAttendance: 42,
    completionRate: 85,
  },
  bookingStats: {
    totalBookings: 45,
    expectedTuition: 1200000,
    completedTuition: 850000,
  },
  studioStats: {
    total: 2,
    active: 2,
    classes: 15,
    instructors: 4,
  },
  todayClasses: [
    {
      id: 'class-today-1',
      classTemplateId: 'template-1',
      title: '요가 기초반',
      date: new Date().toISOString().split('T')[0],
      startTime: '10:00',
      endTime: '11:00',
      instructor: '이요가',
      students: 8,
      maxStudents: 12,
      status: 'confirmed',
      studioName: '강남 스튜디오',
      groupName: '기초반',
    },
    {
      id: 'class-today-2',
      classTemplateId: 'template-2',
      title: '필라테스 중급',
      date: new Date().toISOString().split('T')[0],
      startTime: '14:00',
      endTime: '15:30',
      instructor: '박필라',
      students: 6,
      maxStudents: 10,
      status: 'confirmed',
      studioName: '서초 스튜디오',
      groupName: '중급반',
    },
  ],
  thisWeekClasses: [
    {
      id: 'class-week-1',
      classTemplateId: 'template-3',
      title: '요가 중급반',
      date: '2024-07-29',
      startTime: '09:00',
      endTime: '10:30',
      instructor: '최요가',
      students: 10,
      maxStudents: 15,
      status: 'confirmed',
      studioName: '강남 스튜디오',
      groupName: '중급반',
    },
    {
      id: 'class-week-2',
      classTemplateId: 'template-4',
      title: '필라테스 고급',
      date: '2024-07-30',
      startTime: '19:00',
      endTime: '20:30',
      instructor: '김필라',
      students: 5,
      maxStudents: 8,
      status: 'confirmed',
      studioName: '서초 스튜디오',
      groupName: '고급반',
    },
  ],
  nextWeekClasses: [
    {
      id: 'class-next-1',
      classTemplateId: 'template-5',
      title: '요가 심화반',
      date: '2024-08-05',
      startTime: '10:00',
      endTime: '11:30',
      instructor: '정요가',
      students: 12,
      maxStudents: 15,
      status: 'confirmed',
      studioName: '강남 스튜디오',
      groupName: '심화반',
    },
  ],
  notifications: [
    {
      id: 'notif-1',
      type: 'booking',
      message: '새로운 예약이 접수되었습니다.',
      createdAt: new Date().toISOString(),
      read: false,
    },
    {
      id: 'notif-2',
      type: 'review',
      message: '수업 후기가 등록되었습니다.',
      createdAt: new Date(Date.now() - 3600000).toISOString(),
      read: true,
    },
  ],
};

export default function PartnerDashboardPage() {
  const router = useRouter();
  const contextUser = useUserStore(state => state.user);
  const userLoading = useUserStore(store => store.loading);
  const { studioId } = usePartnerStore();

  // 이벤트 핸들러
  const handleStatusClick = (status: ClassStatus) => {
    router.push(`/partner/classes?status=${status}`);
  };

  const handleClassClick = (classId: string) => {
    router.push(`/partner/classes/${classId}`);
  };

  const handleViewAllClasses = () => {
    router.push('/partner/classes');
  };

  const handleLogout = async () => {
    try {
      const { signOut } = await import('@/lib/supabase/auth');
      await signOut();
      router.push('/partner/login');
    } catch (error) {
      console.error('로그아웃 오류:', error);
    }
  };

  // 로딩 상태
  if (userLoading || studioId === undefined) {
    return <DashboardLoadingSkeleton />;
  }

  // 사용자 정보 없음
  if (!contextUser) {
    return (
      <div className=''>
        <div className='text-center'>
          <h1 className='mb-2 text-xl font-semibold text-gray-900'>
            로그인이 필요합니다
          </h1>
          <p className='mb-4 text-gray-600'>
            파트너 대시보드에 접근하려면 로그인해주세요.
          </p>
          <button
            onClick={() => router.push('/partner/login')}
            className='inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700'
          >
            로그인 페이지로 이동
          </button>
        </div>
      </div>
    );
  }

  // 스튜디오 정보 없음
  // if (true) {
  //   return (
  //     <div className='flex min-h-[calc(100vh-200px)] items-center justify-center'>
  //       <div className='w-full max-w-md p-8 text-center'>
  //         <h2 className='text-2xl font-bold text-gray-900'>
  //           센터를 등록하고 파트너 활동을 시작하세요
  //         </h2>
  //         <p className='mt-3 text-gray-600'>
  //           아직 등록된 센터가 없습니다. <br /> 센터를 등록하여 클래스를 만들고
  //           회원을 맞이할 준비를 하세요.
  //         </p>
  //         <Button
  //           className='mt-8 w-full'
  //           onClick={() => router.push('/partner/studios/new')}
  //         >
  //           센터 등록하기
  //         </Button>
  //       </div>
  //     </div>
  //   );
  // }

  // Mock 데이터 사용
  const data = MOCK_DASHBOARD_DATA;
  const displayName =
    contextUser.user_metadata?.name ||
    contextUser.email?.split('@')[0] ||
    '파트너님';

  return (
    <div className=''>
      {/* 프로필 카드 */}
      <ProfileCard
        partnerName={displayName}
        totalClasses={data.partner.totalClasses}
        monthlyClasses={data.partner.monthlyClasses}
        classStats={data.classStats}
        monthlyStats={data.monthlyStats}
        onStatusClick={handleStatusClick}
      />

      <QuickActionGrid />

      {/* 오늘의 수업 */}
      <TodayClasses
        classes={data.todayClasses}
        onClassClick={handleClassClick}
      />

      {/* 이번 주 수업 일정 */}
      <WeeklySchedule
        title='이번 주 수업 일정'
        classes={data.thisWeekClasses}
        onClassClick={handleClassClick}
        onViewAll={handleViewAllClasses}
        showViewAll={true}
      />

      {/* 다음 주 수업 미리보기 */}
      <WeeklySchedule
        title='다음 주 수업'
        classes={data.nextWeekClasses}
        onClassClick={handleClassClick}
        onViewAll={handleViewAllClasses}
        showViewAll={true}
      />

      {/* 로그아웃 버튼 */}
      <div className='mb-6 px-4'>
        <button
          onClick={handleLogout}
          className='w-full rounded-md border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50'
        >
          로그아웃
        </button>
      </div>
    </div>
  );
}

/**
 * 대시보드 전체 로딩 스켈레톤
 */
function DashboardLoadingSkeleton() {
  return (
    <div className='min-h-screen bg-gray-50'>
      {/* 프로필 카드 스켈레톤 */}
      <div className='px-4 py-6'>
        <div className='animate-pulse rounded-2xl bg-white p-6 shadow-sm'>
          <div className='mb-6 flex items-center'>
            <div className='mr-4 h-16 w-16 rounded-full bg-gray-200'></div>
            <div className='flex-1'>
              <div className='mb-2 h-6 w-32 rounded bg-gray-200'></div>
              <div className='h-4 w-48 rounded bg-gray-200'></div>
            </div>
          </div>
          <div className='mb-4 grid grid-cols-2 gap-3'>
            {[...Array(4)].map((_, i) => (
              <div key={i} className='rounded-lg bg-gray-50 p-3'>
                <div className='mx-auto mb-2 h-6 w-8 rounded bg-gray-200'></div>
                <div className='mx-auto h-3 w-12 rounded bg-gray-200'></div>
              </div>
            ))}
          </div>
          <div className='grid grid-cols-3 gap-4 border-t border-gray-200 pt-4'>
            {[...Array(3)].map((_, i) => (
              <div key={i} className='text-center'>
                <div className='mx-auto mb-2 h-5 w-5 rounded bg-gray-200'></div>
                <div className='mx-auto mb-2 h-8 w-12 rounded bg-gray-200'></div>
                <div className='mx-auto h-4 w-16 rounded bg-gray-200'></div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 빠른 액션 스켈레톤 */}
      <div className='mb-6 px-4'>
        <div className='grid grid-cols-2 gap-4'>
          {[...Array(4)].map((_, i) => (
            <div
              key={i}
              className='animate-pulse rounded-2xl bg-white p-4 shadow-sm'
            >
              <div className='mb-2 flex items-center'>
                <div className='mr-3 h-8 w-8 rounded-lg bg-gray-200'></div>
                <div className='h-4 w-20 rounded bg-gray-200'></div>
              </div>
              <div className='h-3 w-24 rounded bg-gray-200'></div>
            </div>
          ))}
        </div>
      </div>

      {/* 오늘의 수업 스켈레톤 */}
      <div className='mb-6 px-4'>
        <div className='animate-pulse rounded-2xl bg-gradient-to-r from-indigo-50 to-purple-50 p-5'>
          <div className='mb-3 h-6 w-24 rounded bg-indigo-200'></div>
          <div className='space-y-3'>
            {[...Array(2)].map((_, i) => (
              <div key={i} className='rounded-lg bg-white p-4 shadow-sm'>
                <div className='mb-2 flex items-center justify-between'>
                  <div className='h-5 w-32 rounded bg-gray-200'></div>
                  <div className='h-5 w-12 rounded-full bg-gray-200'></div>
                </div>
                <div className='flex items-center justify-between'>
                  <div className='h-4 w-24 rounded bg-gray-200'></div>
                  <div className='h-4 w-16 rounded bg-gray-200'></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
