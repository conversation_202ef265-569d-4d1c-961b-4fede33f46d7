'use client';

import React from 'react';
import Link from 'next/link';
import { EmptyStateProps } from '@/types/partner-dashboard';

/**
 * 빈 상태 컴포넌트
 * 데이터가 없을 때 표시되는 UI
 */
export default function EmptyState({
  title,
  description,
  icon,
  actionButton
}: EmptyStateProps) {
  const defaultIcon = (
    <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
    </svg>
  );

  return (
    <div className="text-center py-12 px-4">
      <div className="flex justify-center mb-4">
        {icon || defaultIcon}
      </div>
      
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {title}
      </h3>
      
      <p className="text-gray-500 mb-6 max-w-md mx-auto">
        {description}
      </p>
      
      {actionButton && (
        <Link
          href={actionButton.href}
          className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors"
        >
          {actionButton.text}
        </Link>
      )}
    </div>
  );
}

/**
 * 미리 정의된 빈 상태 컴포넌트들
 */
export const EmptyStates = {
  /**
   * 클래스가 없을 때
   */
  NoClasses: () => (
    <EmptyState
      title="등록된 클래스가 없습니다"
      description="첫 번째 클래스를 등록하여 파트너 대시보드를 시작해보세요."
      icon={
        <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      }
      actionButton={{
        text: '클래스 등록하기',
        href: '/partner/classes/new'
      }}
    />
  ),

  /**
   * 강사가 없을 때
   */
  NoInstructors: () => (
    <EmptyState
      title="등록된 강사가 없습니다"
      description="강사를 초대하여 클래스를 운영할 수 있습니다."
      icon={
        <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      }
      actionButton={{
        text: '강사 초대하기',
        href: '/partner/instructors'
      }}
    />
  ),

  /**
   * 스튜디오가 없을 때
   */
  NoStudios: () => (
    <EmptyState
      title="등록된 스튜디오가 없습니다"
      description="스튜디오를 등록하여 클래스를 개설할 수 있습니다."
      icon={
        <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      }
      actionButton={{
        text: '스튜디오 등록하기',
        href: '/partner/studios'
      }}
    />
  ),

  /**
   * 오늘 수업이 없을 때
   */
  NoTodayClasses: () => (
    <EmptyState
      title="오늘은 수업이 없습니다"
      description="푹 쉬시거나 내일 수업을 준비해보세요."
      icon={
        <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
        </svg>
      }
    />
  ),

  /**
   * 이번 주 수업이 없을 때
   */
  NoWeeklyClasses: () => (
    <EmptyState
      title="이번 주에는 수업이 없습니다"
      description="다음 주 일정을 확인하거나 새로운 클래스를 등록해보세요."
      icon={
        <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
        </svg>
      }
      actionButton={{
        text: '클래스 일정 보기',
        href: '/partner/classes'
      }}
    />
  ),

  /**
   * 데이터 로딩 실패
   */
  LoadingError: ({ onRetry }: { onRetry: () => void }) => (
    <div className="text-center py-12 px-4">
      <div className="flex justify-center mb-4">
        <svg className="w-16 h-16 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        데이터를 불러올 수 없습니다
      </h3>
      
      <p className="text-gray-500 mb-6 max-w-md mx-auto">
        네트워크 연결을 확인한 후 다시 시도해주세요.
      </p>
      
      <button
        onClick={onRetry}
        className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors"
      >
        다시 시도
      </button>
    </div>
  )
};