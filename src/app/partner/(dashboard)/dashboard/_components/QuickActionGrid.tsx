'use client';

import React from 'react';
import Link from 'next/link';

// 간소화된 액션 데이터 타입
interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
}

export default function QuickActionGrid() {
  const actions = createDefaultQuickActions();

  return (
    <div className='mb-6 px-3'>
      <div className='grid grid-cols-2 gap-3'>
        {actions.map((action, index) => {
          const colorClasses = getActionColorClasses(index);

          return (
            <Link
              key={action.id}
              href={action.href}
              className='hover:border-primary rounded-md border bg-white p-3 text-left transition-colors'
            >
              <div className='mb-2 flex items-center'>
                <div
                  className={`h-8 w-8 ${colorClasses.bg} mr-3 flex items-center justify-center rounded-lg`}
                >
                  <div className={`h-5 w-5 ${colorClasses.icon}`}>
                    {action.icon}
                  </div>
                </div>
                <span className='font-medium text-black'>{action.title}</span>
              </div>
              <p className='text-sm text-gray-500'>{action.description}</p>
            </Link>
          );
        })}
      </div>
    </div>
  );
}

function getActionColorClasses(index: number) {
  const colorSets = [
    { bg: 'bg-indigo-100', icon: 'text-indigo-600' }, // 클래스 현황
    { bg: 'bg-green-100', icon: 'text-green-600' }, // 클래스 등록
    { bg: 'bg-blue-100', icon: 'text-blue-600' }, // 센터 관리
    { bg: 'bg-purple-100', icon: 'text-purple-600' }, // 강사 관리
  ];

  return colorSets[index % colorSets.length];
}
export function createDefaultQuickActions(): QuickAction[] {
  return [
    {
      id: 'classes',
      title: '클래스 현황',
      description: '클래스 현황 조회',
      icon: (
        <svg fill='none' stroke='currentColor' viewBox='0 0 24 24'>
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4'
          />
        </svg>
      ),
      href: '/partner/classes/status',
    },
    {
      id: 'new-class',
      title: '클래스 등록',
      description: '새 클래스 만들기',
      icon: (
        <svg fill='none' stroke='currentColor' viewBox='0 0 24 24'>
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M12 6v6m0 0v6m0-6h6m-6 0H6'
          />
        </svg>
      ),
      href: '/partner/classes/new',
    },
    {
      id: 'studios',
      title: '센터 관리',
      description: '스튜디오 조회 및 클래스 등록',
      icon: (
        <svg fill='none' stroke='currentColor' viewBox='0 0 24 24'>
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4'
          />
        </svg>
      ),
      href: '/partner/studios',
    },
    {
      id: 'instructors',
      title: '강사 관리',
      description: '강사 관리',
      icon: (
        <svg fill='none' stroke='currentColor' viewBox='0 0 24 24'>
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
          />
        </svg>
      ),
      href: '/partner/instructor',
    },
  ];
}
