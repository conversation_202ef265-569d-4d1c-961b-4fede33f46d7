import React from 'react';
import {
  OccurrenceStatusDisplayText,
  OccurrenceStatusColorClass,
  isToday,
  ClassOccurrence,
} from '@/types/partner-dashboard';

interface WeeklyScheduleProps {
  title: string;
  classes: ClassOccurrence[];
  onClassClick: (classId: string) => void;
  onViewAll: () => void;
  showViewAll?: boolean;
}
const formatTime = (timeString: string) => {
  return timeString.slice(0, 5); // HH:MM 형식으로 변환
};
const formatDateWithDay = (dateString: string) => {
  const date = new Date(dateString);
  const days = ['일', '월', '화', '수', '목', '금', '토'];
  return `${date.getMonth() + 1}/${date.getDate()}(${days[date.getDay()]})`;
};
export default function WeeklySchedule({
  title,
  classes,
  onClassClick,
  onViewAll,
  showViewAll = true,
}: WeeklyScheduleProps) {
  return (
    <section className='mb-6 px-4'>
      <header className='mb-4 flex items-center justify-between'>
        <h3 className='text-lg font-semibold text-gray-900'>{title}</h3>
        {showViewAll && (
          <button
            onClick={onViewAll}
            className='text-sm text-indigo-600 hover:text-indigo-700'
          >
            전체보기
          </button>
        )}
      </header>

      {classes.length > 0 ? (
        <div className='space-y-3'>
          {classes.map(classItem => (
            <div
              key={classItem.id}
              className='hover:border-primary cursor-pointer rounded-md border px-3 py-2 transition-all'
              onClick={() => onClassClick(classItem.classTemplateId)}
            >
              <div className='mb-2 flex items-center justify-between'>
                <div className='flex items-center gap-2'>
                  <div
                    className={`h-2 w-2 rounded-full ${isToday(classItem.date) ? 'bg-red-500' : 'bg-blue-500'}`}
                  ></div>
                  <div>
                    <h4 className='text-sm font-medium text-gray-900'>
                      {classItem.title}
                    </h4>
                    {classItem.groupName && (
                      <p className='text-xs text-gray-600'>
                        {classItem.groupName}
                      </p>
                    )}
                  </div>
                </div>
                <span
                  className={`rounded-md px-2 py-1 text-xs ${OccurrenceStatusColorClass[classItem.status]}`}
                >
                  {OccurrenceStatusDisplayText[classItem.status]}
                </span>
              </div>

              <div className='flex items-center justify-between text-xs text-gray-500'>
                <p>
                  {formatDateWithDay(classItem.date)} •{' '}
                  {formatTime(classItem.startTime)}-
                  {formatTime(classItem.endTime)}
                </p>
                <p>
                  수강생 {classItem.students}/{classItem.maxStudents}명
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className='py-8 text-center'>
          <svg
            className='mx-auto mb-4 h-12 w-12 text-gray-300'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
            />
          </svg>
          <p className='mb-2 text-sm text-gray-500'>
            {title.includes('이번') ? '이번 주에는' : '다음 주에는'} 수업이
            없습니다
          </p>
          {showViewAll && (
            <button
              onClick={onViewAll}
              className='text-sm text-indigo-600 hover:text-indigo-700'
            >
              수업 일정 보기
            </button>
          )}
        </div>
      )}
    </section>
  );
}
