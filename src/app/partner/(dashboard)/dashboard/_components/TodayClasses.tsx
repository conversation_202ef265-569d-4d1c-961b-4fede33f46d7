import React from 'react';
import {
  OccurrenceStatusDisplayText,
  OccurrenceStatusColorClass,
  ClassOccurrence,
} from '@/types/partner-dashboard';

interface TodayClassesProps {
  classes: ClassOccurrence[];
  onClassClick: (classId: string) => void;
}

export default function TodayClasses({
  classes,
  onClassClick,
}: TodayClassesProps) {
  const formatTime = (timeString: string) => {
    return timeString.slice(0, 5); // HH:MM 형식으로 변환
  };

  return (
    <div className='mb-6 px-4'>
      <div className='rounded-2xl bg-gradient-to-r from-indigo-50 to-purple-50 p-5'>
        <h3 className='mb-3 text-lg font-semibold text-indigo-900'>
          오늘의 수업
        </h3>

        {classes.length > 0 ? (
          <div className='space-y-3'>
            {classes.map(classItem => (
              <div
                key={classItem.id}
                className='hover:border-primary cursor-pointer rounded-md border bg-white p-4 transition-colors'
                onClick={() => onClassClick(classItem.classTemplateId)}
              >
                <div className='mb-2 flex items-center justify-between'>
                  <h4 className='text-base font-medium text-gray-900'>
                    {classItem.title}
                  </h4>
                  <span
                    className={`rounded-md px-2 py-1 text-xs ${OccurrenceStatusColorClass[classItem.status]}`}
                  >
                    {OccurrenceStatusDisplayText[classItem.status]}
                  </span>
                </div>

                <div className='mb-2 flex items-center justify-between text-sm text-gray-600'>
                  <p>
                    {formatTime(classItem.startTime)} -{' '}
                    {formatTime(classItem.endTime)}
                  </p>
                  {classItem.groupName && <p>{classItem.groupName}</p>}
                </div>

                <div className='flex items-center justify-between text-xs text-gray-500'>
                  <p>{classItem.studioName}</p>
                  <p>
                    수강생 {classItem.students}/{classItem.maxStudents}명
                  </p>
                </div>

                {classItem.notes && (
                  <div className='mt-2 rounded bg-gray-50 p-2 text-xs text-gray-600'>
                    📝 {classItem.notes}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className='py-6 text-center'>
            <div className='mx-auto mb-3 flex h-16 w-16 items-center justify-center rounded-md bg-indigo-100'>
              <svg
                className='h-8 w-8 text-indigo-600'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z'
                />
              </svg>
            </div>
            <h4 className='mb-2 text-base font-medium text-indigo-800'>
              오늘은 수업이 없습니다
            </h4>
            <p className='text-sm text-indigo-600'>푹 쉬세요! 🌟</p>
          </div>
        )}
      </div>
    </div>
  );
}
