'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { instructor<PERSON><PERSON> } from '@/lib/api/partner/instructor.api';
import { useRouter } from 'next/navigation';
import { InstructorFormData } from '@/app/partner/_schemas/form.schema';
import { mapInstructorFormDataToRequestData } from '@/app/partner/_utils/transform';
import InstructorForm from '@/components/partner/instructor/InstructorForm';

export default function NewInstructorPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (data: InstructorFormData) => {
    try {
      setIsLoading(true);

      const requestData = await mapInstructorFormDataToRequestData(data);
      await instructorApi.createInstructor(requestData);

      toast.success('강사가 성공적으로 등록되었습니다.');
      router.push('/partner/instructor');
    } catch (error) {
      console.error('강사 등록 실패:', error);
      toast.error('강사 등록에 실패했습니다.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <InstructorForm
      mode='create'
      onSubmit={handleSubmit}
      isLoading={isLoading}
    />
  );
}
