'use client';
import { usePartnerStore } from '../../_store/partner.store';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function PartnerStudiosPage() {
  const studioId = usePartnerStore(state => state.studioId);
  const router = useRouter();

  useEffect(() => {
    // studioId가 있으면 edit 페이지로, 없으면 new 페이지로 리다이렉트
    if (studioId) {
      router.replace('/partner/studios/edit');
    } else {
      router.replace('/partner/studios/new');
    }
  }, [studioId, router]);

  // 리다이렉트 중 로딩 상태 표시
  // TODO: Proper loading state
  return (
    <div className='p-3'>
      <div className='flex h-64 items-center justify-center'>
        <div className='text-center'>
          <div className='border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2'></div>
          <p className='text-muted-foreground text-sm'>
            페이지를 준비중입니다...
          </p>
        </div>
      </div>
    </div>
  );
}
