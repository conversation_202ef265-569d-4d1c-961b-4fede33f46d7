'use client';
import StudioForm from '@/app/partner/_components/StudioForm';
import { useMyStudio } from '@/app/partner/_hooks/useMyStudio';
import { EditStudioFormData } from '@/app/partner/_schemas/form.schema';
import { partnerStudioApi } from '@/lib/api/partner/studio.api';

export default function EditStudioPage() {
  const { data: studioData, isLoading, isError, error } = useMyStudio();

  const handleSubmit = async (data: EditStudioFormData) => {
    if (!studioData?.id) {
      throw new Error('스튜디오 ID가 없습니다.');
    }
    await partnerStudioApi.updateStudioById(studioData.id, data);
  };

  if (isLoading) {
    return (
      <div className='p-3'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <div className='border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2'></div>
            <p className='text-muted-foreground text-sm'>
              스튜디오 정보를 불러오는 중...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (isError || !studioData) {
    return (
      <div className='p-3'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <p className='text-destructive'>
              스튜디오 데이터를 불러올 수 없습니다.
            </p>
            {error && (
              <p className='text-muted-foreground text-sm'>{error.message}</p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <StudioForm
      mode='edit'
      defaultValues={studioData as EditStudioFormData}
      onSubmit={handleSubmit}
      submitButtonText='센터수정'
    />
  );
}
