'use client';
import { partnerStudio<PERSON>pi } from '@/lib/api/partner/studio.api';
import { StudioFormData } from '@/app/partner/_schemas/form.schema';
import StudioForm from '@/app/partner/_components/StudioForm';
import { mapStudioFormDataToRequestData } from '@/app/partner/_utils/transform';

export default function NewStudioPage() {
  const handleSubmit = async (data: StudioFormData) => {
    const requestData = await mapStudioFormDataToRequestData(data);
    await partnerStudioApi.createStudio(requestData);
  };

  return (
    <StudioForm
      mode='create'
      onSubmit={handleSubmit}
      submitButtonText='센터등록'
    />
  );
}
