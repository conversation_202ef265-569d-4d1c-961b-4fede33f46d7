import { partnerStudioApi } from '@/lib/api/partner/studio.api';
import { useQuery, useSuspenseQuery } from '@tanstack/react-query';

export const useGetStudioById = (studioId: string) => {
  return useQuery({
    queryKey: ['studios', studioId],
    queryFn: () => partnerStudioApi.getStudioById(studioId),
    enabled: !!studioId, // studioId가 있을 때만 쿼리 실행
  });
};

export const useSuspenseGetStudioById = (studioId: string) => {
  return useSuspenseQuery({
    queryKey: ['studios', studioId],
    queryFn: () => partnerStudioApi.getStudioById(studioId),
  });
};
