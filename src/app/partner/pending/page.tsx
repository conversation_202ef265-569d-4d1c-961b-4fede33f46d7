import type { Metadata } from 'next';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export const metadata: Metadata = {
  title: '승인 대기 중 | Shallwe 파트너',
  description: '파트너 승인이 진행중입니다.',
};

export default function PartnerPendingPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        {/* 아이콘 */}
        <div className="flex justify-center">
          <div className="bg-yellow-100 rounded-full p-4">
            <svg
              className="h-12 w-12 text-yellow-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </div>

        {/* 콘텐츠 */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            파트너 승인 대기 중
          </h1>
          <p className="text-gray-600 mb-8">
            파트너 가입 신청이 접수되었습니다.
            <br />
            승인은 영업일 기준 1-2일 이내에 완료되며,
            <br />
            승인 완료 시 이메일로 안내드립니다.
          </p>

          {/* 추가 정보 */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
            <p className="text-sm text-yellow-800">
              <strong>신청 정보 확인하기</strong>
              <br />
              가입 신청 시 입력하신 정보가 정확한지 확인해 주세요.
              <br />
              잘못된 정보로 인해 승인이 지연될 수 있습니다.
            </p>
          </div>

          {/* 액션 버튼 */}
          <div className="space-y-3">
            <Link href="/partner/login" className="block">
              <Button variant="outline" className="w-full">
                로그인 페이지로 돌아가기
              </Button>
            </Link>
            
            <Link href="/" className="block">
              <Button variant="ghost" className="w-full">
                메인 페이지로 이동
              </Button>
            </Link>
          </div>

          {/* 문의 안내 */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              승인 관련 문의사항이 있으신가요?
              <br />
              <a 
                href="mailto:<EMAIL>" 
                className="text-purple-600 hover:text-purple-500 font-medium"
              >
                <EMAIL>
              </a>
              으로 연락 주세요.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}