import type { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '파트너 회원가입 | ShallWe',
  description: 'ShallWe 플랫폼에 파트너로 가입하여 스튜디오를 운영하세요.',
};

export default function PartnerRegisterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <main className="flex-1">
        {children}
      </main>
      
      <footer className="bg-white border-t mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-gray-500">
            <p>&copy; 2024 ShallWe. All rights reserved.</p>
            <div className="mt-2 space-x-4">
              <Link href="/terms" className="hover:text-gray-700">이용약관</Link>
              <Link href="/privacy" className="hover:text-gray-700">개인정보처리방침</Link>
              <Link href="/contact" className="hover:text-gray-700">문의하기</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}