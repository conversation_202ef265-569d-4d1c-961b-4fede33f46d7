import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { confirmTossPayment, TossPaymentError } from '@/lib/api/toss-payments';

const TossPaymentConfirmSchema = z.object({
  paymentKey: z.string().min(1, 'Payment key is required'),
  orderId: z.string().min(1, 'Order ID is required'),
  amount: z.number().min(1, 'Amount must be greater than 0'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const validationResult = TossPaymentConfirmSchema.safeParse(body);

    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request body',
          code: 'VALIDATION_ERROR',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const { paymentKey, orderId, amount } = validationResult.data;

    console.log('Processing Toss payment confirmation:', {
      paymentKey,
      orderId,
      amount,
    });

    // 토스페이먼츠 결제 승인 요청
    const tossPaymentResult = await confirmTossPayment({
      paymentKey,
      orderId,
      amount,
    });

    console.log('Toss payment confirmed successfully:', {
      paymentKey: tossPaymentResult.paymentKey,
      orderId: tossPaymentResult.orderId,
      status: tossPaymentResult.status,
      totalAmount: tossPaymentResult.totalAmount,
    });

    // TODO: 여기서 데이터베이스에 결제 정보를 저장해야 합니다
    // 예: 결제 내역 저장, 수업 등록 확정, 이메일/SMS 발송 등

    const response = {
      success: true,
      data: {
        paymentKey: tossPaymentResult.paymentKey,
        orderId: tossPaymentResult.orderId,
        status: tossPaymentResult.status,
        method: tossPaymentResult.method,
        totalAmount: tossPaymentResult.totalAmount,
        approvedAt: tossPaymentResult.approvedAt,
        receipt: tossPaymentResult.receipt,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error confirming Toss payment:', error);

    if (error instanceof TossPaymentError) {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
          code: error.code || 'TOSS_PAYMENT_ERROR',
        },
        { status: error.status || 500 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }
}
