import { NextRequest, NextResponse } from 'next/server';
import {
  PaymentHistoryRequestSchema,
  type PaymentHistoryResponse,
  type PaymentSuccessErrorResponse,
  type PaymentDetails,
} from '@/schemas/payment';

export async function GET(
  request: NextRequest
): Promise<NextResponse<PaymentHistoryResponse | PaymentSuccessErrorResponse>> {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const paymentType = searchParams.get('paymentType');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');

    const validationResult = PaymentHistoryRequestSchema.safeParse({
      status,
      paymentType,
      startDate,
      endDate,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
    });

    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request parameters',
          code: 'VALIDATION_ERROR',
        },
        { status: 400 }
      );
    }

    const { limit: validLimit, offset: validOffset } = validationResult.data;

    const mockPayments: PaymentDetails[] = [
      {
        id: 'payment_001',
        memberName: '김회원',
        className: '기초 체력 수업',
        instructorName: '박트레이너',
        studioName: '강남 피트니스 센터',
        scheduleGroupName: '저녁반',
        paymentMethod: 'credit_card',
        paymentAmount: 30000,
        paymentType: 'deposit',
        paymentDate: '2025-01-15',
        paymentStatus: 'completed',
        transactionId: 'txn_20250115_001',
        enrollmentId: 'enrollment_001',
        classTemplateId: 'class_template_001',
        scheduleGroupId: 'schedule_group_001',
      },
      {
        id: 'payment_002',
        memberName: '김회원',
        className: '요가 입문 클래스',
        instructorName: '이요가',
        studioName: '서울 요가 스튜디오',
        scheduleGroupName: '오전반',
        paymentMethod: 'kakaopay',
        paymentAmount: 25000,
        paymentType: 'deposit',
        paymentDate: '2025-01-10',
        paymentStatus: 'completed',
        transactionId: 'txn_20250110_002',
        enrollmentId: 'enrollment_002',
        classTemplateId: 'class_template_002',
        scheduleGroupId: 'schedule_group_002',
      },
      {
        id: 'payment_003',
        memberName: '김회원',
        className: '필라테스 중급',
        instructorName: '최필라',
        studioName: '명동 필라테스 스튜디오',
        scheduleGroupName: '점심반',
        paymentMethod: 'naverpay',
        paymentAmount: 120000,
        paymentType: 'full_payment',
        paymentDate: '2025-01-05',
        paymentStatus: 'completed',
        transactionId: 'txn_20250105_003',
        enrollmentId: 'enrollment_003',
        classTemplateId: 'class_template_003',
        scheduleGroupId: 'schedule_group_003',
      },
    ];

    const totalPayments = mockPayments.length;
    const paginatedPayments = mockPayments.slice(validOffset, validOffset + validLimit);
    const hasMore = validOffset + validLimit < totalPayments;

    const response: PaymentHistoryResponse = {
      success: true,
      data: {
        payments: paginatedPayments,
        total: totalPayments,
        hasMore,
      },
    };

    console.log('Payment history retrieved:', {
      total: totalPayments,
      returned: paginatedPayments.length,
      limit: validLimit,
      offset: validOffset,
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error retrieving payment history:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }
}