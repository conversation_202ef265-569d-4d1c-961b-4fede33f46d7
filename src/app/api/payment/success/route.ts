import { NextRequest, NextResponse } from 'next/server';
import {
  PaymentSuccessRequestSchema,
  type PaymentSuccessFullResponse,
  type PaymentSuccessErrorResponse,
  type PaymentSuccessDetails,
  type NextStep,
} from '@/schemas/payment';

export async function GET(
  request: NextRequest
): Promise<NextResponse<PaymentSuccessFullResponse | PaymentSuccessErrorResponse>> {
  try {
    const { searchParams } = new URL(request.url);
    const enrollmentId = searchParams.get('enrollmentId');
    const transactionId = searchParams.get('transactionId');

    const validationResult = PaymentSuccessRequestSchema.safeParse({
      enrollmentId,
      transactionId,
    });

    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request parameters',
          code: 'VALIDATION_ERROR',
        },
        { status: 400 }
      );
    }

    const { enrollmentId: validEnrollmentId } = validationResult.data;

    const mockNextSteps: NextStep[] = [
      {
        title: '센터에서 남은 수업료 결제',
        description: '센터에서 남은 수업료를 결제해주세요.',
        completed: false,
      },
      {
        title: '예약 확정 안내 발송',
        description: '예약 확정 안내가 곧 발송됩니다.',
        completed: false,
      },
      {
        title: '수업 일정 및 위치 확인',
        description: '수업 일정 및 위치를 확인해주세요.',
        completed: false,
      },
    ];

    const mockPaymentData: PaymentSuccessDetails = {
      id: validEnrollmentId,
      memberName: '김회원',
      className: '기초 체력 수업',
      instructorName: '박트레이너',
      studioName: '강남 피트니스 센터',
      scheduleGroupName: '저녁반',
      paymentMethod: 'credit_card',
      paymentAmount: 30000,
      paymentType: 'deposit',
      paymentDate: new Date().toISOString().split('T')[0],
      paymentStatus: 'completed',
      transactionId: transactionId || `txn_${Date.now()}`,
      enrollmentId: validEnrollmentId,
      classTemplateId: 'class_template_001',
      scheduleGroupId: 'schedule_group_001',
      nextSteps: mockNextSteps,
      classStartDate: '2025-01-20',
      classEndDate: '2025-03-20',
      remainingAmount: 150000,
      totalAmount: 180000,
    };

    const response: PaymentSuccessFullResponse = {
      success: true,
      data: mockPaymentData,
    };

    console.log('Payment success data retrieved:', {
      enrollmentId: validEnrollmentId,
      transactionId,
      amount: mockPaymentData.paymentAmount,
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error retrieving payment success data:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest
): Promise<NextResponse<PaymentSuccessFullResponse | PaymentSuccessErrorResponse>> {
  try {
    const body = await request.json();
    
    const validationResult = PaymentSuccessRequestSchema.safeParse(body);

    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request body',
          code: 'VALIDATION_ERROR',
        },
        { status: 400 }
      );
    }

    const { enrollmentId, transactionId } = validationResult.data;

    const mockNextSteps: NextStep[] = [
      {
        title: '센터에서 남은 수업료 결제',
        description: '센터에서 남은 수업료를 결제해주세요.',
        completed: false,
      },
      {
        title: '예약 확정 안내 발송',
        description: '예약 확정 안내가 곧 발송됩니다.',
        completed: false,
      },
      {
        title: '수업 일정 및 위치 확인',
        description: '수업 일정 및 위치를 확인해주세요.',
        completed: false,
      },
    ];

    const mockPaymentData: PaymentSuccessDetails = {
      id: enrollmentId,
      memberName: '김회원',
      className: '기초 체력 수업',
      instructorName: '박트레이너',
      studioName: '강남 피트니스 센터',
      scheduleGroupName: '저녁반',
      paymentMethod: 'credit_card',
      paymentAmount: 30000,
      paymentType: 'deposit',
      paymentDate: new Date().toISOString().split('T')[0],
      paymentStatus: 'completed',
      transactionId: transactionId || `txn_${Date.now()}`,
      enrollmentId,
      classTemplateId: 'class_template_001',
      scheduleGroupId: 'schedule_group_001',
      nextSteps: mockNextSteps,
      classStartDate: '2025-01-20',
      classEndDate: '2025-03-20',
      remainingAmount: 150000,
      totalAmount: 180000,
    };

    const response: PaymentSuccessFullResponse = {
      success: true,
      data: mockPaymentData,
    };

    console.log('Payment success confirmation processed:', {
      enrollmentId,
      transactionId,
      amount: mockPaymentData.paymentAmount,
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error processing payment success confirmation:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }
}