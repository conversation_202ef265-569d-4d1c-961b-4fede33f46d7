# Payment API Documentation

This documentation covers the payment-related API endpoints for handling payment success confirmations and payment history.

## Endpoints

### GET /api/payment/success
Retrieves payment success information for a specific enrollment.

#### Query Parameters
- `enrollmentId` (required): UUID of the enrollment
- `transactionId` (optional): Transaction ID from payment gateway

#### Response
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "memberName": "김회원",
    "className": "기초 체력 수업",
    "instructorName": "박트레이너",
    "studioName": "강남 피트니스 센터",
    "scheduleGroupName": "저녁반",
    "paymentMethod": "credit_card",
    "paymentAmount": 30000,
    "paymentType": "deposit",
    "paymentDate": "2025-01-18",
    "paymentStatus": "completed",
    "transactionId": "txn_1234567890",
    "enrollmentId": "uuid",
    "classTemplateId": "uuid",
    "scheduleGroupId": "uuid",
    "nextSteps": [
      {
        "title": "센터에서 남은 수업료 결제",
        "description": "센터에서 남은 수업료를 결제해주세요.",
        "completed": false
      }
    ],
    "classStartDate": "2025-01-20",
    "classEndDate": "2025-03-20",
    "remainingAmount": 150000,
    "totalAmount": 180000
  }
}
```

### POST /api/payment/success
Confirms payment success and processes enrollment.

#### Request Body
```json
{
  "enrollmentId": "uuid",
  "transactionId": "txn_1234567890"
}
```

#### Response
Same as GET endpoint response.

### GET /api/payment/history
Retrieves payment history for the current user.

#### Query Parameters
- `status` (optional): Filter by payment status
- `paymentType` (optional): Filter by payment type
- `startDate` (optional): Start date filter (YYYY-MM-DD)
- `endDate` (optional): End date filter (YYYY-MM-DD)
- `limit` (optional): Number of results (default: 20, max: 100)
- `offset` (optional): Offset for pagination (default: 0)

#### Response
```json
{
  "success": true,
  "data": {
    "payments": [
      {
        "id": "payment_001",
        "memberName": "김회원",
        "className": "기초 체력 수업",
        "instructorName": "박트레이너",
        "studioName": "강남 피트니스 센터",
        "scheduleGroupName": "저녁반",
        "paymentMethod": "credit_card",
        "paymentAmount": 30000,
        "paymentType": "deposit",
        "paymentDate": "2025-01-15",
        "paymentStatus": "completed",
        "transactionId": "txn_20250115_001",
        "enrollmentId": "enrollment_001",
        "classTemplateId": "class_template_001",
        "scheduleGroupId": "schedule_group_001"
      }
    ],
    "total": 3,
    "hasMore": false
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

### Error Codes
- `VALIDATION_ERROR`: Invalid request parameters
- `PAYMENT_NOT_FOUND`: Payment record not found
- `ENROLLMENT_NOT_FOUND`: Enrollment record not found
- `INTERNAL_ERROR`: Server error

## Payment Methods
- `credit_card`: 신용·체크카드
- `debit_card`: 체크카드
- `bank_transfer`: 계좌이체
- `mobile_payment`: 휴대폰결제
- `kakaopay`: 카카오페이
- `naverpay`: 네이버페이
- `payco`: 페이코

## Payment Status
- `pending`: 결제 대기
- `processing`: 결제 진행중
- `completed`: 결제 완료
- `failed`: 결제 실패
- `cancelled`: 결제 취소
- `refunded`: 환불 완료
- `partial_refund`: 부분 환불

## Payment Types
- `deposit`: 예약금
- `full_payment`: 전액 결제
- `remaining_payment`: 잔금 결제
- `refund`: 환불

## Usage Examples

### Client-side Usage
```typescript
import { paymentApi } from '@/lib/api/payment';

// Get payment success data
const successData = await paymentApi.getPaymentSuccess({
  enrollmentId: 'uuid',
  transactionId: 'txn_123'
});

// Get payment history
const history = await paymentApi.getPaymentHistory({
  status: 'completed',
  limit: 10
});
```

### Server-side Usage
```typescript
import { paymentServerApi } from '@/lib/api/payment-server';

// Get payment success data in Server Component
const successData = await paymentServerApi.getPaymentSuccessData({
  enrollmentId: 'uuid',
  transactionId: 'txn_123'
});
```

## Integration Notes

### Server Components vs Client Components

**Use Server Components for:**
- Payment success page (initial load with URL parameters)
- Payment history page (SEO-friendly, better performance)
- Admin dashboards with payment data

**Use Client Components for:**
- Real-time payment status updates
- Interactive payment history with filtering
- Payment action buttons (retry, cancel)
- Optimistic UI updates

### Authentication
All endpoints require user authentication. Ensure proper session handling and user context.

### Security Considerations
- Validate all payment data server-side
- Log all payment operations for audit trails
- Implement rate limiting for payment endpoints
- Sanitize user inputs to prevent injection attacks