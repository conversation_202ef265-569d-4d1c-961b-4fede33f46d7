import { NextRequest, NextResponse } from 'next/server';
import { searchUnifiedSubwayStations, getPopularStations } from '@/lib/data/subway-stations';

/**
 * 지하철역 검색 API
 * GET /api/subway-stations/search
 * 
 * @description
 * - 검색어 기반으로 지하철역 목록 반환
 * - 성능 최적화를 위한 쿼리 파라미터 지원
 * - 자동완성 기능을 위한 빠른 응답
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const limitParam = searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam, 10) : 10;

    // 검색어 길이 검증
    if (query.length < 1) {
      return NextResponse.json({
        stations: [],
        total: 0,
        query: '',
      });
    }

    // 검색어가 너무 길면 제한
    if (query.length > 20) {
      return NextResponse.json(
        { error: '검색어는 20자 이내로 입력해주세요.' },
        { status: 400 }
      );
    }

    // limit 범위 검증 (1-50)
    const validLimit = Math.min(Math.max(limit, 1), 50);

    // 지하철역 검색 실행
    const stations = searchUnifiedSubwayStations(query, validLimit);

    // 응답 데이터 구성
    return NextResponse.json({
      stations,
      total: stations.length,
      query,
      limit: validLimit,
    });

  } catch (error) {
    console.error('지하철역 검색 API 오류:', error);
    
    return NextResponse.json(
      { 
        error: '검색 중 오류가 발생했습니다.',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}