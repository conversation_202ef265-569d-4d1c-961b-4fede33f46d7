import { NextResponse } from 'next/server';
import { getPopularStations } from '@/lib/data/subway-stations';

/**
 * 인기 지하철역 목록 API
 * GET /api/subway-stations/popular
 */

export async function GET() {
  try {
    const popularStations = getPopularStations();

    return NextResponse.json({
      stations: popularStations,
      total: popularStations.length,
    });

  } catch (error) {
    console.error('인기 지하철역 조회 API 오류:', error);
    
    return NextResponse.json(
      { 
        error: '인기 지하철역 조회 중 오류가 발생했습니다.',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}