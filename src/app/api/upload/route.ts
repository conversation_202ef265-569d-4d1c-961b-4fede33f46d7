import { NextRequest, NextResponse } from 'next/server';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { UploadService } from '@/lib/services/upload.service';
import { createServerClient } from '@supabase/ssr';

/**
 * 이미지 업로드 API
 * 
 * @description 파트너가 이미지를 업로드합니다.
 * 현재는 스튜디오 이미지만 지원하며, 추후 강사/클래스 이미지도 지원 예정입니다.
 * 
 * @param {NextRequest} request - multipart/form-data 형식의 요청
 * 
 * @returns {200} 업로드 성공
 * @returns {400} 잘못된 요청 (파일 없음, 형식 오류 등)
 * @returns {401} 인증되지 않은 요청
 * @returns {403} 권한 없음 (파트너가 아님)
 * @returns {413} 파일 크기 초과 (5MB)
 * @returns {415} 지원하지 않는 파일 형식
 * @returns {500} 서버 내부 오류
 * 
 * @example
 * POST /api/upload
 * Content-Type: multipart/form-data
 * 
 * FormData:
 * - files: File[] (최대 10개)
 * - type: 'studio' | 'instructor' | 'class'
 * - studioId: string (type이 'studio'인 경우 필수)
 * 
 * Response:
 * {
 *   "success": true,
 *   "uploaded": [
 *     {
 *       "url": "https://[project].supabase.co/storage/v1/object/public/images/studios/[path]/featured-123.jpg",
 *       "path": "studios/[partnerId]/[studioId]/featured-123.jpg",
 *       "index": 0
 *     }
 *   ],
 *   "failed": []
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    // FormData 파싱
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const type = formData.get('type') as string;
    const studioId = formData.get('studioId') as string;

    // 파일 존재 확인
    if (!files || files.length === 0) {
      return NextResponse.json(
        { message: '업로드할 파일이 없습니다.' },
        { status: 400 }
      );
    }

    // 타입 검증
    if (!type || !['studio', 'instructor', 'class'].includes(type)) {
      return NextResponse.json(
        { message: '올바른 업로드 타입을 지정해주세요.' },
        { status: 400 }
      );
    }

    // 스튜디오 이미지인 경우 studioId 필수
    if (type === 'studio' && !studioId) {
      return NextResponse.json(
        { message: '스튜디오 ID가 필요합니다.' },
        { status: 400 }
      );
    }

    // Supabase 클라이언트 생성 (서버사이드)
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value;
          },
          set() {},
          remove() {},
        },
      }
    );

    // 업로드 서비스 인스턴스 생성
    const uploadService = new UploadService(supabase);

    // 이미지 업로드
    const result = await uploadService.uploadMultipleImages(
      files,
      type as 'studio' | 'instructor' | 'class',
      {
        partnerId: partner!.id,
        studioId: studioId || undefined,
      }
    );

    if (!result.success && result.failed.length > 0) {
      // 일부 또는 전체 실패
      return NextResponse.json(
        {
          message: '일부 파일 업로드에 실패했습니다.',
          ...result,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Upload API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { message: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: '이미지 업로드 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

/**
 * 이미지 삭제 API
 * 
 * @description 파트너가 업로드한 이미지를 삭제합니다.
 * 
 * @param {NextRequest} request - JSON 요청
 * 
 * @returns {200} 삭제 성공
 * @returns {400} 잘못된 요청
 * @returns {401} 인증되지 않은 요청
 * @returns {403} 권한 없음
 * @returns {500} 서버 내부 오류
 * 
 * @example
 * DELETE /api/upload
 * Content-Type: application/json
 * 
 * {
 *   "paths": ["studios/[partnerId]/[studioId]/image-123.jpg"]
 * }
 */
export async function DELETE(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const body = await request.json();
    const { paths } = body;

    if (!paths || !Array.isArray(paths) || paths.length === 0) {
      return NextResponse.json(
        { message: '삭제할 파일 경로를 지정해주세요.' },
        { status: 400 }
      );
    }

    // 파트너 소유 이미지인지 확인 (경로에 partnerId 포함 여부)
    const isOwner = paths.every(path => 
      path.includes(`studios/${partner!.id}/`)
    );

    if (!isOwner) {
      return NextResponse.json(
        { message: '삭제 권한이 없습니다.' },
        { status: 403 }
      );
    }

    // Supabase 클라이언트 생성
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value;
          },
          set() {},
          remove() {},
        },
      }
    );

    // 업로드 서비스 인스턴스 생성
    const uploadService = new UploadService(supabase);

    // 이미지 삭제
    const result = await uploadService.deleteMultipleImages(paths);

    if (!result.success) {
      return NextResponse.json(
        { message: result.error || '이미지 삭제에 실패했습니다.' },
        { status: 400 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Delete API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { message: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: '이미지 삭제 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}