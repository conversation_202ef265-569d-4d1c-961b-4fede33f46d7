import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // 임시로 하드코딩된 지하철역 목록 반환
    // 실제 프로덕션에서는 별도 stations 테이블에서 조회
    const mockStations = [
      { id: '1', name: '강남역', line: '2호선', latitude: 37.4979, longitude: 127.0276 },
      { id: '2', name: '선릉역', line: '2호선', latitude: 37.5044, longitude: 127.0489 },
      { id: '3', name: '논현역', line: '7호선', latitude: 37.5103, longitude: 127.0284 },
      { id: '4', name: '역삼역', line: '2호선', latitude: 37.5000, longitude: 127.0364 },
      { id: '5', name: '삼성역', line: '2호선', latitude: 37.5081, longitude: 127.0633 },
    ];

    return NextResponse.json(mockStations);

  } catch (error) {
    console.error('지하철역 목록 조회 오류:', error);
    return NextResponse.json({ message: '지하철역 목록 조회 중 오류가 발생했습니다.' }, { status: 500 });
  }
}