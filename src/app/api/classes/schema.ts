import { z } from 'zod';

const InstructorSpecialtySchema = z.object({
  specialty: z.string(),
  experienceYears: z.number(),
});

const InstructorCertificateSchema = z.object({
  certificateName: z.string(),
  issuingOrganization: z.string(),
});

const ScheduleSchema = z.object({
  scheduleGroupId: z.string(),
  dayOfWeek: z.string(),
  startTime: z.string(),
  endTime: z.string(),
});

const ScheduleGroupSchema = z.object({
  id: z.string(),
  groupName: z.string(),
  maxParticipants: z.number(),
  pricePerSession: z.number().optional(),
  schedules: z.array(ScheduleSchema),
  currentEnrollments: z.number(),
  availableSpots: z.number(),
});

const EnrollmentInfoSchema = z.object({
  isAvailable: z.boolean(),
  reasonCode: z.enum([
    'RECRUITMENT_ENDED',
    'NOT_STARTED',
    'ONGOING',
    'COMPLETED',
    'CANCELLED',
    'AVAILABLE',
  ]),
  closeAt: z.string().optional(),
});

// Class List Item
export const classListItemSchema = z.object({
  classTemplate: z.object({
    id: z.string(),
    title: z.string(),
    category: z.string(),
    specialty: z.string(),
    level: z.string(),
    durationInMinutes: z.number(),
    pricePerSession: z.number(),
    maxCapacity: z.number(),
    status: z.string(),
  }),
  studio: z.object({
    id: z.string(),
    name: z.string(),
    nearestStation: z.string().nullish(),
  }),
  instructor: z.object({
    id: z.string(),
    name: z.string(),
    shortBio: z.string().optional(),
    specialties: z.array(InstructorSpecialtySchema),
  }),
});

export type ClassListItemType = z.infer<typeof classListItemSchema>;
export type ClassTemplateType = ClassListItemType['classTemplate'];
export type StudioType = ClassListItemType['studio'];
export type InstructorType = ClassListItemType['instructor'];

// Class Detail
export const classDetailSchema = z.object({
  classTemplate: z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    specialty: z.string(),
    level: z.string(),
    durationMinutes: z.number(),
    pricePerSession: z.number(),
    curriculum: z.record(z.string(), z.any()).optional(),
    recruitmentEndDate: z.string(),
    classStartDate: z.string(),
    classEndDate: z.string(),
    status: z.string(),
    maxCapacity: z.number(),
  }),
  instructor: z.object({
    id: z.string(),
    name: z.string(),
    shortBio: z.string().optional(),
    specialties: z.array(InstructorSpecialtySchema),
    certificates: z.array(InstructorCertificateSchema),
  }),
  studio: z.object({
    id: z.string(),
    name: z.string(),
    address: z.string(),
    nearestStation: z.string().optional(),
    amenities: z.record(z.string(), z.any()),
    latitude: z.string(),
    longitude: z.string(),
    description: z.string(),
  }),
  scheduleGroups: z.array(ScheduleGroupSchema),
  enrollmentInfo: EnrollmentInfoSchema,
});

export type ClassDetailType = z.infer<typeof classDetailSchema>;
export type ClassDetailTemplateType = ClassDetailType['classTemplate'];
export type ClassDetailInstructorType = ClassDetailType['instructor'];
export type ClassDetailStudioType = ClassDetailType['studio'];
export type ScheduleGroupType = ClassDetailType['scheduleGroups'][0];
export type EnrollmentInfoType = ClassDetailType['enrollmentInfo'];
