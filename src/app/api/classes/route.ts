import { NextRequest, NextResponse } from 'next/server';

/**
 * 클래스 목록 조회 API (임시 구현)
 * GET /api/classes
 * 
 * @description
 * - 빌드 에러 해결을 위한 임시 API 껍데기
 * - 실제 구현은 추후 진행 예정
 */
export async function GET(request: NextRequest) {
  try {
    // 임시 빈 배열 반환
    return NextResponse.json([]);
  } catch (error) {
    console.error('Classes list fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}