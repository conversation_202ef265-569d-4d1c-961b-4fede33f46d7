import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { db } from '@/lib/db';
import { members } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

/**
 * 현재 로그인한 사용자의 역할을 반환하는 API
 */
export async function GET(request: Request) {
  try {
    const { supabase } = createClient(request);
    
    // 사용자 인증 확인
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: '인증이 필요합니다.' }, { status: 401 });
    }

    // 데이터베이스에서 사용자 역할 조회
    const memberData = await db
      .select({ role: members.role })
      .from(members)
      .where(eq(members.id, user.id))
      .limit(1);

    if (!memberData.length) {
      return NextResponse.json({ error: '사용자 정보를 찾을 수 없습니다.' }, { status: 404 });
    }

    return NextResponse.json({ 
      role: memberData[0].role,
      userId: user.id 
    });

  } catch (error) {
    console.error('사용자 역할 조회 오류:', error);
    return NextResponse.json({ error: '서버 오류가 발생했습니다.' }, { status: 500 });
  }
}