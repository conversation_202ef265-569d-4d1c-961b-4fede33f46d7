import {
  BookingActionRequestSchema,
  type BookingActionResponse,
} from '@/schemas/profile';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

export async function GET() {
  try {
    // TODO: Add filter params
    const result = {
      reserved: [
        {
          id: '1',
          classTitle: '운동 경험이 없는 초보분들을 위한 기초 체력 수업',
          coachName: '최하얀 코치님',
          date: '2025-01-15',
          time: '10:00 ~ 11:00',
          status: 'reserved' as const,
          price: 20000,
          location: '공덕역 5분',
          payment: {
            status: 'completed' as const,
            method: 'credit_card',
            transactionId: 'txn_123456789',
          },
        },
        {
          id: '2',
          classTitle: '직장인을 위한 저녁 요가 클래스',
          coachName: '김요가 코치님',
          date: '2025-01-17',
          time: '19:00 ~ 20:00',
          status: 'payment_pending' as const,
          price: 25000,
          location: '강남역 3분',
          payment: {
            status: 'pending' as const,
          },
        },
      ],
      inProgress: [
        {
          id: '3',
          classTitle: '홈트레이닝 기초반',
          coachName: '박홈트 코치님',
          date: '2025-01-10',
          time: '14:00 ~ 14:45',
          status: 'in_progress' as const,
          price: 15000,
          location: '온라인',
          payment: {
            status: 'completed' as const,
            method: 'kakaopay',
            transactionId: 'txn_987654321',
          },
        },
      ],
      completed: [
        {
          id: '4',
          classTitle: '스트레칭 클래스',
          coachName: '이스트레칭 코치님',
          date: '2025-01-05',
          time: '09:00 ~ 10:00',
          status: 'completed' as const,
          price: 18000,
          location: '홍대입구역 2분',
          payment: {
            status: 'completed' as const,
            method: 'naverpay',
            transactionId: 'txn_555666777',
          },
        },
        {
          id: '5',
          classTitle: '기초 필라테스',
          coachName: '최하얀 코치님',
          date: '2025-01-03',
          time: '10:00 ~ 11:00',
          status: 'completed' as const,
          price: 20000,
          location: '공덕역 5분',
          payment: {
            status: 'completed' as const,
            method: 'credit_card',
            transactionId: 'txn_111222333',
          },
        },
      ],
    };

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error fetching booking history:', error);

    return NextResponse.json({
      success: false,
      data: null,
      error: {
        message: 'Error fetching booking history',
      },
    });
  }
}

export async function POST(
  request: NextRequest
): Promise<NextResponse<BookingActionResponse>> {
  try {
    const body = await request.json();
    const validatedRequest = BookingActionRequestSchema.parse(body);

    const { action, bookingId } = validatedRequest;

    switch (action) {
      case 'pay_deposit':
        console.log(`Processing deposit payment for booking: ${bookingId}`);
        return NextResponse.json({
          success: true,
          message: '예약금 결제가 완료되었습니다.',
        });

      case 'cancel_booking':
        console.log(`Cancelling booking: ${bookingId}`);
        return NextResponse.json({
          success: true,
          message: '예약이 취소되었습니다.',
        });

      default:
        return NextResponse.json(
          {
            success: false,
            message: '지원하지 않는 작업입니다.',
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error processing booking action:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '잘못된 요청 형식입니다.',
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: '서버 오류가 발생했습니다.',
      },
      { status: 500 }
    );
  }
}
