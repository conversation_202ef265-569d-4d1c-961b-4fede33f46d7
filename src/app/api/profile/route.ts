import {
  type UserProfileErrorResponse,
  type UserProfileResponse,
} from '@/schemas/profile';
import { NextResponse } from 'next/server';

export async function GET(): Promise<
  NextResponse<UserProfileResponse | UserProfileErrorResponse>
> {
  try {
    const result = {
      nickname: '김운동',
      avatar: '/mock.jpg',
      totalClasses: 24,
      attendedClasses: 22,
      points: 12500, // TOOD: Add points
      nextClasses: [
        {
          title: '기초 체력 수업',
          date: '2025-01-15',
          time: '10:00 ~ 11:00',
        },
      ],
    };

    const response: UserProfileResponse = {
      success: true,
      data: result,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching user profile:', error);

    return NextResponse.json(
      {
        success: false,
        data: null,
        error: {
          message: 'Error fetching user profile',
        },
      },
      { status: 500 }
    );
  }
}
