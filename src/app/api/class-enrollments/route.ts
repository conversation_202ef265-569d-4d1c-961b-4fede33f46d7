// TODO: 새로운 classes, new_class_schedules 스키마 기반으로 수강신청 API 재구현 필요
// 기존 class_templates, class_enrollments 구조는 더 이상 사용하지 않음
// 새로운 구조: classes (partner_id, studio_id, instructor_id) + new_class_schedules

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  return NextResponse.json(
    { 
      message: '새로운 클래스 시스템으로 마이그레이션 중입니다.',
      todo: '새로운 classes 스키마 기반으로 수강신청 API 구현 필요'
    },
    { status: 501 }
  );
}

export async function GET(request: NextRequest) {
  return NextResponse.json(
    { 
      message: '새로운 클래스 시스템으로 마이그레이션 중입니다.',
      todo: '새로운 classes 스키마 기반으로 수강신청 목록 API 구현 필요'
    },
    { status: 501 }
  );
}
