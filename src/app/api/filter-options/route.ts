import { COMMON_ERRORS } from '@/lib/constants/errors';
import { getAllLevels, getAllNearestStations } from '@/lib/queries/classes';
import { error } from '@/lib/utils/api-utils';
import { NextResponse } from 'next/server';

const GENDER_OPTIONS = [
  { value: 'male', label: '남성' },
  { value: 'female', label: '여성' },
  { value: 'mixed', label: '혼성' },
];

export async function GET() {
  try {
    const [levels, nearestStations] = await Promise.all([
      getAllLevels(),
      getAllNearestStations(),
    ]);

    const json = {
      levels,
      nearestStations,
      genders: GENDER_OPTIONS,
    };

    return NextResponse.json(json);
  } catch (e) {
    console.error('/api/filter-options::error', e);
    return NextResponse.json(
      error({
        ...COMMON_ERRORS.SERVER_ERROR.json,
        details: e,
      }),
      { status: COMMON_ERRORS.SERVER_ERROR.status }
    );
  }
}