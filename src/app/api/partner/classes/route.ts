import { NextRequest, NextResponse } from 'next/server';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { partnerClassService } from '@/lib/services/partner-class.service';
import { 
  createClassSchema, 
  classListFilterSchema
} from '@/lib/validations/partner-class.validation';

/**
 * 파트너 클래스 목록 조회
 * 
 * @description 파트너가 소유한 클래스 목록을 조회합니다.
 * 스튜디오별 필터링, 상태별 필터링, 검색 등을 지원합니다.
 * 
 * @param {NextRequest} request - Next.js 요청 객체 (인증 정보 포함)
 * 
 * @returns {200} 클래스 목록 조회 성공
 * @returns {401} 인증되지 않은 요청 (로그인 필요)
 * @returns {403} 권한 없음 (파트너 계정이 아니거나 비활성 상태)
 * @returns {400} 잘못된 쿼리 파라미터
 * @returns {500} 서버 내부 오류
 * 
 * @example
 * ```
 * GET /api/partner/classes
 * GET /api/partner/classes?studioId=uuid&status=active&page=1&limit=10
 * GET /api/partner/classes?search=요가&category=yoga&level=beginner
 * 
 * Response:
 * {
 *   "data": [
 *     {
 *       "id": "class-uuid",
 *       "title": "초급 요가 클래스",
 *       "description": "요가 기초를 배우는 클래스입니다",
 *       "category": "yoga",
 *       "level": "beginner",
 *       "target": "all",
 *       "maxParticipants": 8,
 *       "pricePerSession": 15000,
 *       "studioName": "강남 요가 스튜디오",
 *       "instructorName": "김강사",
 *       "schedules": [
 *         {
 *           "dayOfWeek": 1,
 *           "startTime": "10:00",
 *           "endTime": "11:00"
 *         }
 *       ],
 *       "enrollmentCount": 5
 *     }
 *   ],
 *   "pagination": {
 *     "page": 1,
 *     "limit": 10,
 *     "total": 25,
 *     "totalPages": 3
 *   }
 * }
 * ```
 */
export async function GET(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    // 쿼리 파라미터 파싱 및 검증
    const { searchParams } = new URL(request.url);
    const filterParams = {
      studioId: searchParams.get('studioId') || undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
    };

    const validatedFilters = classListFilterSchema.parse(filterParams);

    // 서비스 호출
    const result = await partnerClassService.getClassList(partner!, validatedFilters);

    return NextResponse.json(result);
  } catch (error) {
    console.error('클래스 목록 조회 오류:', error);

    // Zod 검증 오류
    if (error && typeof error === 'object' && 'issues' in error) {
      return NextResponse.json(
        { 
          message: '잘못된 요청 파라미터입니다.',
          errors: error
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      // 비즈니스 로직 오류
      if (error.message.includes('access denied') || 
          error.message.includes('not found')) {
        return NextResponse.json(
          { message: error.message },
          { status: 403 }
        );
      }
    }

    return NextResponse.json(
      { message: '클래스 목록 조회 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

/**
 * 파트너 클래스 생성
 * 
 * @description 파트너가 새로운 클래스를 생성합니다.
 * 클래스 정보와 스케줄 정보를 함께 받아서 트랜잭션으로 처리합니다.
 * 
 * @param {NextRequest} request - Next.js 요청 객체 (인증 정보 포함)
 * 
 * @returns {201} 클래스 생성 성공
 * @returns {400} 잘못된 요청 데이터
 * @returns {401} 인증되지 않은 요청 (로그인 필요) 
 * @returns {403} 권한 없음 (파트너 계정이 아니거나 비활성 상태)
 * @returns {422} 잘못된 데이터 형식
 * @returns {500} 서버 내부 오류
 * 
 * @example
 * ```
 * POST /api/partner/classes
 * Content-Type: application/json
 * 
 * {
 *   "studioId": "studio-uuid",
 *   "instructorId": "instructor-uuid", 
 *   "title": "초급 요가 클래스",
 *   "description": "요가 기초를 배우는 클래스입니다",
 *   "category": "yoga",
 *   "level": "beginner",
 *   "target": "all",
 *   "maxParticipants": 8,
 *   "pricePerSession": 15000,
 *   "sessionDurationMinutes": 60,
 *   "durationWeeks": 4,
 *   "sessionsPerWeek": 4,
 *   "images": [
 *     {
 *       "url": "https://example.com/image1.jpg",
 *       "path": "classes/studio-uuid/image1.jpg"
 *     }
 *   ],
 *   "scheduleGroups": [
 *     {
 *       "schedules": [
 *         {
 *           "dayOfWeek": 1,
 *           "startTime": "10:00",
 *           "endTime": "11:00"
 *         },
 *         {
 *           "dayOfWeek": 5,
 *           "startTime": "10:00",
 *           "endTime": "11:00"
 *         }
 *       ]
 *     },
 *     {
 *       "schedules": [
 *         {
 *           "dayOfWeek": 2,
 *           "startTime": "19:00",
 *           "endTime": "20:00"
 *         },
 *         {
 *           "dayOfWeek": 4,
 *           "startTime": "19:00",
 *           "endTime": "20:00"
 *         }
 *       ]
 *     }
 *   ]
 * }
 * 
 * Response:
 * {
 *   "id": "class-uuid",
 *   "partnerId": "partner-uuid",
 *   "studioId": "studio-uuid",
 *   "instructorId": "instructor-uuid",
 *   "title": "초급 요가 클래스",
 *   "description": "요가 기초를 배우는 클래스입니다",
 *   "category": "yoga",
 *   "level": "beginner",
 *   "target": "all",
 *   "maxParticipants": 8,
 *   "pricePerSession": 15000,
 *   "sessionDurationMinutes": 60,
 *   "durationWeeks": 4,
 *   "sessionsPerWeek": 4,
 *   "status": "active",
 *   "visible": true,
 *   "scheduleGroups": [
 *     {
 *       "id": 1,
 *       "status": "pending",
 *       "schedules": [
 *         {
 *           "id": 1,
 *           "dayOfWeek": 1,
 *           "startTime": "10:00",
 *           "endTime": "11:00"
 *         },
 *         {
 *           "id": 2,
 *           "dayOfWeek": 5,
 *           "startTime": "10:00",
 *           "endTime": "11:00"
 *         }
 *       ]
 *     },
 *     {
 *       "id": 2,
 *       "status": "pending",
 *       "schedules": [
 *         {
 *           "id": 3,
 *           "dayOfWeek": 2,
 *           "startTime": "19:00",
 *           "endTime": "20:00"
 *         },
 *         {
 *           "id": 4,
 *           "dayOfWeek": 4,
 *           "startTime": "19:00",
 *           "endTime": "20:00"
 *         }
 *       ]
 *     }
 *   ],
 *   "createdAt": "2024-01-01T00:00:00.000Z",
 *   "updatedAt": "2024-01-01T00:00:00.000Z"
 * }
 * ```
 */
export async function POST(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const body = await request.json();

    // 요청 데이터 검증
    const validatedData = createClassSchema.parse(body);

    // 서비스 호출
    const newClass = await partnerClassService.createClass(partner!, validatedData);

    return NextResponse.json(newClass, { status: 201 });
  } catch (error) {
    console.error('클래스 생성 오류:', error);

    // Zod 검증 오류
    if (error && typeof error === 'object' && 'issues' in error) {
      return NextResponse.json(
        { 
          message: '요청 데이터가 올바르지 않습니다.',
          errors: error
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      // 비즈니스 로직 오류
      if (error.message.includes('access denied') || 
          error.message.includes('not found') ||
          error.message.includes('does not belong')) {
        return NextResponse.json(
          { message: error.message },
          { status: 422 }
        );
      }
    }

    return NextResponse.json(
      { message: '클래스 생성 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}