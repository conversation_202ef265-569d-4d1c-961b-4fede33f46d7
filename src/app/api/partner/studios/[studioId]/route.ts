import { NextRequest, NextResponse } from 'next/server';
import { StudioService } from '@/lib/services/studio.service';
import { UpdateStudioSchema } from '@/lib/schemas/studio';
import { requirePartnerAuth } from '@/lib/auth/partner.server';

/**
 * 파트너 스튜디오 단건 조회
 * 
 * @description 파트너가 등록한 특정 스튜디오의 상세 정보를 조회합니다.
 * 파트너는 자신이 등록한 스튜디오만 조회할 수 있습니다.
 * 
 * @param {NextRequest} request - Next.js 요청 객체 (인증 정보 포함)
 * @param {Object} params - 라우트 파라미터
 * @param {string} params.studioId - 스튜디오 ID (UUID 형식)
 * 
 * @returns {200} 스튜디오 정보 조회 성공
 * @returns {401} 인증되지 않은 요청 (로그인 필요)
 * @returns {403} 권한 없음 (파트너 계정이 아니거나 비활성 상태)
 * @returns {404} 스튜디오를 찾을 수 없음
 * @returns {422} 잘못된 파라미터 형식
 * @returns {500} 서버 내부 오류
 * 
 * @example
 * ```
 * GET /api/partner/studios/550e8400-e29b-41d4-a716-************
 * 
 * Response:
 * {
 *   "id": "550e8400-e29b-41d4-a716-************",
 *   "name": "헬퍼 피트니스 센터 강남점",
 *   "phone": "02-1234-5678",
 *   "description": "최고의 운동 시설을 제공합니다",
 *   "address": "서울시 강남구 테헤란로 123",
 *   "addressDetail": "1층",
 *   "postalCode": "06234",
 *   "latitude": 37.5665,
 *   "longitude": 126.9780,
 *   "nearestStation": "강남역",
 *   "stationDistance": 200,
 *   "amenities": {
 *     "parking": [{"type": "free", "description": "무료 주차장 이용 가능"}],
 *     "shower": [{"available": true, "description": "샤워실 완비"}],
 *     "locker": [{"type": "paid", "price": 1000, "description": "유료 라커 이용"}]
 *   },
 *   "operatingHours": {
 *     "monday": { "open": "06:00", "close": "23:00" },
 *     "tuesday": { "open": "06:00", "close": "23:00" }
 *   },
 *   "status": "active",
 *   "links": {
 *     "website": "https://studio.com",
 *     "sns": "https://instagram.com/studio"
 *   },
 *   "createdAt": "2024-01-01T00:00:00.000Z",
 *   "updatedAt": "2024-01-01T00:00:00.000Z"
 * }
 * ```
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ studioId: string }> }
) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const studioId = (await params).studioId;
    
    const studioService = new StudioService();
    const studio = await studioService.getStudioByIdAndPartnerId(studioId, partner!.id);
    
    if (!studio) {
      return NextResponse.json(
        { message: '스튜디오를 찾을 수 없습니다.' },
        { status: 404 }
      );
    }

    return NextResponse.json(studio);
  } catch (error) {
    console.error('스튜디오 조회 오류:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('올바르지 않은')) {
        return NextResponse.json(
          { message: error.message },
          { status: 422 }
        );
      }
    }
    
    return NextResponse.json(
      { message: '스튜디오 조회 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

/**
 * 파트너 스튜디오 정보 수정
 * 
 * @description 파트너가 등록한 특정 스튜디오의 정보를 수정합니다.
 * 파트너는 자신이 등록한 스튜디오만 수정할 수 있습니다.
 * 
 * @param {NextRequest} request - Next.js 요청 객체 (인증 정보 포함)
 * @param {Object} params - 라우트 파라미터
 * @param {string} params.studioId - 스튜디오 ID (UUID 형식)
 * 
 * @returns {200} 스튜디오 수정 성공
 * @returns {400} 잘못된 요청 데이터
 * @returns {401} 인증되지 않은 요청 (로그인 필요)
 * @returns {403} 권한 없음 (파트너 계정이 아니거나 비활성 상태)
 * @returns {404} 스튜디오를 찾을 수 없음
 * @returns {422} 잘못된 파라미터 형식
 * @returns {500} 서버 내부 오류
 * 
 * @example
 * ```
 * PUT /api/partner/studios/550e8400-e29b-41d4-a716-************
 * Content-Type: application/json
 * 
 * // 부분 업데이트 예시 - 모든 필드는 선택사항(Optional)
 * {
 *   "name": "새로운 스튜디오 이름",    // Optional - 업데이트할 경우만 포함
 *   "phone": "02-9876-5432",        // Optional - 업데이트할 경우만 포함
 *   "description": "업데이트된 설명",  // Optional - 업데이트할 경우만 포함
 *   "images": [                        // Optional - 최대 10장, JSONB 구조
 *     {
 *       "path": "studios/partner-id/studio-id/featured-12345.jpg",
 *       "url": "https://example.com/featured.jpg"
 *     },
 *     {
 *       "path": "studios/partner-id/studio-id/gallery-12346.jpg",
 *       "url": "https://example.com/gallery1.jpg"
 *     }
 *   ],
 *   "links": {                         // Optional - JSON 구조
 *     "website": "https://studio.com",
 *     "sns": "https://instagram.com/studio"
 *   }
 * }
 * 
 * // 여러 필드 동시 업데이트 예시
 * {
 *   "description": "새로운 설명",              // Optional
 *   "addressDetail": "지하 1층",              // Optional
 *   "nearestStation": "역삼역",               // Optional
 *   "amenities": {                           // Optional
 *     "parking": [{"type": "paid", "price": 2000, "description": "유료 주차장"}],
 *     "shower": [{"available": true, "description": "프리미엄 샤워실"}]
 *   },
 *   "operatingHours": {                      // Optional
 *     "monday": { "open": "07:00", "close": "22:00" },
 *     "tuesday": { "open": "07:00", "close": "22:00" }
 *   }
 * }
 * 
 * Response:
 * {
 *   "id": "550e8400-e29b-41d4-a716-************",
 *   "name": "새로운 스튜디오 이름",
 *   "phone": "02-9876-5432",
 *   "description": "업데이트된 설명",
 *   "address": "서울시 강남구 테헤란로 123",
 *   "status": "active",
 *   "images": [
 *     {
 *       "path": "studios/partner-id/studio-id/featured-12345.jpg",
 *       "url": "https://example.com/featured.jpg"
 *     }
 *   ],
 *   "links": {
 *     "website": "https://studio.com",
 *     "instagram": "https://instagram.com/studio"
 *   },
 *   "createdAt": "2024-01-01T00:00:00.000Z",
 *   "updatedAt": "2024-01-01T12:00:00.000Z"
 * }
 * ```
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ studioId: string }> }
) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const studioId = (await params).studioId;
    const body = await request.json();
    
    // 이미지 개수 검증
    if (body.images && Array.isArray(body.images)) {
      if (body.images.length > 10) {
        return NextResponse.json(
          { message: '이미지는 최대 10장까지 업로드 가능합니다.' },
          { status: 400 }
        );
      }
    }
    
    // 요청 데이터 검증
    const validatedData = UpdateStudioSchema.parse(body);
    
    const studioService = new StudioService();
    const updatedStudio = await studioService.updateStudio(studioId, partner!.id, validatedData);
    
    if (!updatedStudio) {
      return NextResponse.json(
        { message: '스튜디오 수정에 실패했습니다.' },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedStudio);
  } catch (error) {
    console.error('스튜디오 수정 오류:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('올바르지 않은')) {
        return NextResponse.json(
          { message: error.message },
          { status: 422 }
        );
      }
      if (error.message.includes('찾을 수 없습니다') || error.message.includes('수정에 실패')) {
        return NextResponse.json(
          { message: error.message },
          { status: 404 }
        );
      }
    }
    
    // Zod 검증 오류
    if (error && typeof error === 'object' && 'issues' in error) {
      return NextResponse.json(
        { 
          message: '요청 데이터가 올바르지 않습니다.',
          errors: error
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: '스튜디오 수정 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}