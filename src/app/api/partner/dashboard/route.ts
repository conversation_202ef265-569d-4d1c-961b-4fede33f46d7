import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { 
  PartnerDashboardQuery, 
  PartnerDashboardSuccessResponse, 
  PartnerDashboardErrorResponse 
} from '@/types/partner-dashboard';

/**
 * 파트너 대시보드 데이터 조회 API
 * GET /api/partner/dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // URL 파라미터에서 timezone 추출
    const { searchParams } = new URL(request.url);
    const timezone = searchParams.get('timezone') || 'Asia/Seoul';

    // Supabase 클라이언트 생성
    const { supabase } = createClient(request);

    // 사용자 인증 확인
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json<PartnerDashboardErrorResponse>(
        {
          success: false,
          message: '인증이 필요합니다.',
          error: 'UNAUTHORIZED'
        },
        { status: 401 }
      );
    }

    // 파트너 정보 조회
    const { data: partnerData, error: partnerError } = await supabase
      .from('partners')
      .select('id, contact_name, status')
      .eq('user_id', user.id)
      .single();

    if (partnerError || !partnerData) {
      return NextResponse.json<PartnerDashboardErrorResponse>(
        {
          success: false,
          message: '파트너 정보를 찾을 수 없습니다.',
          error: 'PARTNER_NOT_FOUND'
        },
        { status: 404 }
      );
    }

    // 파트너 상태 확인
    if (partnerData.status !== 'ACTIVE') {
      return NextResponse.json<PartnerDashboardErrorResponse>(
        {
          success: false,
          message: '활성화된 파트너만 접근할 수 있습니다.',
          error: 'PARTNER_NOT_ACTIVE'
        },
        { status: 403 }
      );
    }

    // Mock 데이터 생성 (실제 구현 시 데이터베이스에서 조회)
    const dashboardData = generateMockDashboardData(partnerData);

    // 성공 응답
    return NextResponse.json<PartnerDashboardSuccessResponse>(
      {
        success: true,
        data: dashboardData
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('파트너 대시보드 API 오류:', error);
    
    return NextResponse.json<PartnerDashboardErrorResponse>(
      {
        success: false,
        message: '서버 오류가 발생했습니다.',
        error: 'INTERNAL_SERVER_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * Mock 대시보드 데이터 생성
 * TODO: 실제 데이터베이스 쿼리로 교체
 */
function generateMockDashboardData(partnerData: any) {
  return {
    partner: {
      id: partnerData.id,
      name: partnerData.contact_name || '파트너님',
      totalClasses: 15,
      monthlyClasses: 8,
    },
    classStats: {
      recruiting: 2,
      ongoing: 5,
      completed: 8,
      upcoming: 0,
    },
    monthlyStats: {
      totalClasses: 8,
      totalAttendance: 64,
      completionRate: 87,
    },
    bookingStats: {
      totalBookings: 156,
      expectedTuition: 2400000,
      completedTuition: 1680000,
    },
    studioStats: {
      total: 3,
      active: 2,
      classes: 15,
      instructors: 8,
    },
    todayClasses: [
      {
        id: 'today-1',
        classTemplateId: 'class-1',
        title: '요가 초급반',
        date: new Date().toISOString().split('T')[0],
        startTime: '10:00:00',
        endTime: '11:00:00',
        instructor: '김강사',
        students: 8,
        maxStudents: 12,
        status: 'confirmed' as const,
        studioName: '강남 스튜디오',
        groupName: '오전 요가반',
        notes: '매트 준비 필요',
      },
    ],
    thisWeekClasses: [
      {
        id: 'week-1',
        classTemplateId: 'class-1',
        title: '요가 초급반',
        date: new Date().toISOString().split('T')[0],
        startTime: '10:00:00',
        endTime: '11:00:00',
        instructor: '김강사',
        students: 8,
        maxStudents: 12,
        status: 'confirmed' as const,
        studioName: '강남 스튜디오',
        groupName: '오전 요가반',
      },
      {
        id: 'week-2',
        classTemplateId: 'class-2',
        title: '필라테스 중급반',
        date: new Date(Date.now() + 86400000).toISOString().split('T')[0], // 내일
        startTime: '14:00:00',
        endTime: '15:30:00',
        instructor: '이강사',
        students: 6,
        maxStudents: 10,
        status: 'confirmed' as const,
        studioName: '서초 스튜디오',
        groupName: '오후 필라테스반',
      },
    ],
    nextWeekClasses: [
      {
        id: 'next-1',
        classTemplateId: 'class-1',
        title: '요가 초급반',
        date: new Date(Date.now() + 7 * 86400000).toISOString().split('T')[0], // 다음 주
        startTime: '10:00:00',
        endTime: '11:00:00',
        instructor: '김강사',
        students: 5,
        maxStudents: 12,
        status: 'confirmed' as const,
        studioName: '강남 스튜디오',
        groupName: '오전 요가반',
      },
    ],
    notifications: [
      {
        id: 'notif-1',
        type: 'booking' as const,
        message: '새로운 예약이 있습니다.',
        createdAt: new Date().toISOString(),
        read: false,
      },
      {
        id: 'notif-2',
        type: 'review' as const,
        message: '새로운 리뷰가 등록되었습니다.',
        createdAt: new Date(Date.now() - 3600000).toISOString(), // 1시간 전
        read: true,
      },
    ],
  };
}

/**
 * POST 요청은 지원하지 않음
 */
export async function POST() {
  return NextResponse.json<PartnerDashboardErrorResponse>(
    {
      success: false,
      message: 'GET 요청만 지원됩니다.',
      error: 'METHOD_NOT_ALLOWED'
    },
    { status: 405 }
  );
}