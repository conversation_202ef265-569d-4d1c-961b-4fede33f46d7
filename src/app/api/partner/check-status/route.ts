import { NextRequest, NextResponse } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { PartnerCheckStatusResponse } from '@/types/partner';

/**
 * 파트너 상태 확인 API 엔드포인트
 * 
 * @description
 * - GET: 인증된 파트너의 현재 상태 조회
 * - 파트너 상태: PENDING, ACTIVE, SUSPENDED, REJECTED
 */

/**
 * 파트너 상태 확인 API
 * GET /api/partner/check-status
 */
export async function GET(request: NextRequest): Promise<NextResponse<PartnerCheckStatusResponse>> {
  try {
    // Supabase 서버 클라이언트 생성
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            cookieStore.set({ name, value, ...options });
          },
          remove(name: string, options: CookieOptions) {
            cookieStore.set({ name, value: '', ...options });
          },
        },
      }
    );

    // 1. 사용자 인증 확인
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({
        success: false,
        message: '인증이 필요합니다.',
      }, { status: 401 });
    }

    // 2. 파트너 정보 조회
    const { data: partner, error: partnerError } = await supabase
      .from('partners')
      .select('id, status, contact_name, contact_phone, created_at, updated_at')
      .eq('user_id', user.id)
      .single();

    if (partnerError) {
      console.error('파트너 정보 조회 오류:', partnerError);
      
      // 파트너 정보가 없는 경우
      if (partnerError.code === 'PGRST116') {
        return NextResponse.json({
          success: false,
          message: '파트너 정보를 찾을 수 없습니다.',
        }, { status: 404 });
      }

      return NextResponse.json({
        success: false,
        message: '파트너 상태 조회 중 오류가 발생했습니다.',
      }, { status: 500 });
    }

    if (!partner) {
      return NextResponse.json({
        success: false,
        message: '파트너 정보를 찾을 수 없습니다.',
      }, { status: 404 });
    }

    // 3. 상태별 메시지 생성
    let statusMessage: string | undefined;
    
    switch (partner.status) {
      case 'PENDING':
        statusMessage = '파트너 승인이 진행중입니다. 승인 완료 시 이메일로 안내드립니다.';
        break;
      case 'ACTIVE':
        statusMessage = '파트너 계정이 활성화되었습니다.';
        break;
      case 'SUSPENDED':
        statusMessage = '계정이 일시 정지되었습니다. 자세한 사항은 고객센터로 문의해주세요.';
        break;
      case 'REJECTED':
        statusMessage = '파트너 승인이 거부되었습니다. 자세한 사항은 고객센터로 문의해주세요.';
        break;
      default:
        statusMessage = '알 수 없는 상태입니다.';
    }

    // 4. 성공 응답
    return NextResponse.json({
      success: true,
      data: {
        partnerId: partner.id,
        status: partner.status,
        message: statusMessage,
      },
    }, { status: 200 });

  } catch (error) {
    console.error('파트너 상태 확인 API 오류:', error);
    
    return NextResponse.json({
      success: false,
      message: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
    }, { status: 500 });
  }
}