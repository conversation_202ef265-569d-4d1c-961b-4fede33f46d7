import { NextRequest, NextResponse } from 'next/server';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { UploadService } from '@/lib/services/upload.service';
import { createClient } from '@/lib/supabase/server-cookies';

/**
 * 파트너 이미지 업로드
 * @method POST
 * @description 파트너가 스튜디오, 클래스 등의 이미지를 업로드합니다.
 * 
 * @param {NextRequest} request - Next.js 요청 객체 (multipart/form-data)
 * 
 * Form Data:
 * - file: 이미지 파일 (Required)
 * - type: 업로드 타입 ('studio' | 'instructor' | 'class') (Required)
 * - studioId: 스튜디오 ID (type이 'studio'인 경우 필수)
 * - prefix: 파일명 prefix (Optional, 예: 'featured', 'gallery')
 * 
 * @returns {200} 업로드 성공
 * @returns {400} 잘못된 요청 (파일 없음, 타입 누락 등)
 * @returns {401} 인증되지 않은 요청
 * @returns {403} 권한 없음
 * @returns {422} 유효하지 않은 파일 (크기, 형식 등)
 * @returns {500} 서버 내부 오류
 * 
 * @example Response
 * {
 *   "success": true,
 *   "url": "https://[project-id].supabase.co/storage/v1/object/public/images/studios/[partnerId]/[studioId]/featured-123456789.jpg",
 *   "path": "studios/[partnerId]/[studioId]/featured-123456789.jpg"
 * }
 */
export async function POST(request: NextRequest) {
  console.log('🚀 === UPLOAD API DEBUG START ===');
  
  try {
    // === 1. 요청 기본 정보 로깅 ===
    console.log('📝 Request Info:');
    console.log('  - Method:', request.method);
    console.log('  - URL:', request.url);
    console.log('  - NextURL pathname:', request.nextUrl.pathname);
    
    // === 2. 헤더 상세 분석 ===
    console.log('📋 Request Headers:');
    const headers = Object.fromEntries(request.headers.entries());
    Object.entries(headers).forEach(([key, value]) => {
      console.log(`  - ${key}: ${value}`);
    });
    
    // === 3. Content-Type 분석 ===
    const contentType = request.headers.get('content-type');
    console.log('🎯 Content-Type Analysis:');
    console.log('  - Raw Content-Type:', contentType);
    
    if (contentType?.includes('multipart/form-data')) {
      const boundaryMatch = contentType.match(/boundary=([^;]+)/);
      console.log('  - Boundary found:', boundaryMatch ? boundaryMatch[1] : 'NOT FOUND');
    }
    
    // === 4. Content-Length 분석 ===
    const contentLength = request.headers.get('content-length');
    console.log('📏 Content-Length:', contentLength);

    // 파트너 인증 확인
    console.log('🔐 Partner authentication check...');
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      console.log('❌ Partner authentication failed');
      return errorResponse;
    }
    console.log('✅ Partner authenticated:', partner?.id);

    // === 5. FormData 파싱 시도 ===
    console.log('📦 === FormData 파싱 시도 시작 ===');
    let formData: FormData;
    
    try {
      console.log('⏳ Calling request.formData()...');
      formData = await request.formData();
      console.log('✅ FormData 파싱 성공!');
      
      // FormData 내용 로깅
      console.log('📄 FormData Contents:');
      for (const [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`  - ${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
        } else {
          console.log(`  - ${key}: ${value}`);
        }
      }
      
    } catch (error) {
      console.log('❌ === FormData 파싱 에러 상세 분석 ===');
      console.log('Error instance:', error instanceof Error ? 'Error' : typeof error);
      console.log('Error name:', (error as any)?.name);
      console.log('Error message:', (error as any)?.message);
      console.log('Error cause:', (error as any)?.cause);
      console.log('Error stack:', (error as any)?.stack);
      
      // 추가 에러 속성들 확인
      console.log('Additional error properties:');
      if (error && typeof error === 'object') {
        Object.getOwnPropertyNames(error).forEach(prop => {
          if (!['name', 'message', 'stack', 'cause'].includes(prop)) {
            console.log(`  - ${prop}:`, (error as any)[prop]);
          }
        });
      }
      
      // 원본 요청 바디 분석 시도
      console.log('🔍 === 원본 요청 바디 분석 시도 ===');
      try {
        // 새로운 요청을 clone해서 바디 분석
        const clonedRequest = request.clone();
        const arrayBuffer = await clonedRequest.arrayBuffer();
        const bodyText = new TextDecoder().decode(arrayBuffer);
        
        console.log('Raw body length:', arrayBuffer.byteLength);
        console.log('Raw body preview (first 500 chars):', bodyText.substring(0, 500));
        
        // CRLF 패턴 분석
        const crlfCount = (bodyText.match(/\r\n/g) || []).length;
        const lfCount = (bodyText.match(/\n/g) || []).length;
        const crCount = (bodyText.match(/\r/g) || []).length;
        
        console.log('Line ending analysis:');
        console.log(`  - CRLF (\\r\\n): ${crlfCount}`);
        console.log(`  - LF (\\n): ${lfCount}`);
        console.log(`  - CR (\\r): ${crCount}`);
        
      } catch (bodyError) {
        console.log('❌ 원본 바디 분석 실패:', bodyError);
      }
      
      // 기존 에러 처리 로직
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('CRLF') || errorMessage.includes('boundary')) {
        return NextResponse.json(
          { 
            error: 'multipart/form-data 형식이 올바르지 않습니다. curl 명령어나 Postman을 사용해 주세요.',
            details: 'Next.js 15의 FormData 파서와 호환되지 않는 multipart 형식입니다.',
            solution: 'curl -F "file=@yourfile.jpg" -F "type=studio" ... 형식을 사용하세요.',
            debugInfo: {
              errorName: (error as any)?.name,
              errorMessage: errorMessage,
              contentType: contentType,
              contentLength: contentLength
            }
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        { 
          error: 'FormData 파싱 중 오류가 발생했습니다. 요청 형식을 확인해 주세요.',
          debugInfo: {
            errorType: typeof error,
            errorMessage: errorMessage,
            contentType: contentType
          }
        },
        { status: 400 }
      );
    }
    
    // 필드 값 추출 (다중 파일 지원)
    const files = formData.getAll('files') as File[];
    const singleFile = formData.get('file') as File | null; // 기존 단일 파일 호환성
    const type = formData.get('type') as string | null;
    const studioId = formData.get('studioId') as string | null;
    const prefix = formData.get('prefix') as string | null;

    // 파일 목록 결정 (다중 우선, 단일 파일 fallback)
    const uploadFiles = files.length > 0 ? files : (singleFile ? [singleFile] : []);
    
    console.log('📁 업로드 파일 목록:');
    uploadFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file.name} (${file.size} bytes, ${file.type})`);
    });

    // 필수 필드 검증
    if (uploadFiles.length === 0) {
      return NextResponse.json(
        { error: '업로드할 파일이 없습니다.' },
        { status: 400 }
      );
    }

    if (!type || !['studio', 'instructor', 'class'].includes(type)) {
      return NextResponse.json(
        { error: '올바른 업로드 타입을 선택해주세요.' },
        { status: 400 }
      );
    }

    // 타입별 필수 필드 검증
    if (type === 'studio' && !studioId) {
      return NextResponse.json(
        { error: '스튜디오 ID가 필요합니다.' },
        { status: 400 }
      );
    }

    // Supabase 클라이언트 생성
    const supabase = await createClient();
    const uploadService = new UploadService(supabase);

    // 업로드 옵션 설정
    const uploadOptions = {
      partnerId: partner!.id,
      studioId: studioId || undefined,
      instructorId: type === 'instructor' ? partner!.id : undefined,
      prefix: prefix || 'image',
    };

    console.log('🚀 다중 파일 업로드 시작:', uploadFiles.length, '개 파일');

    // 단일 파일 vs 다중 파일 처리
    if (uploadFiles.length === 1) {
      // 단일 파일 - 기존 API 호환성 유지
      const result = await uploadService.uploadImage(
        uploadFiles[0],
        type as 'studio' | 'instructor' | 'class',
        uploadOptions
      );

      if (!result.success) {
        return NextResponse.json(
          { error: result.error || '업로드에 실패했습니다.' },
          { status: 422 }
        );
      }

      return NextResponse.json({
        success: true,
        url: result.url,
        path: result.path,
      });
      
    } else {
      // 다중 파일 처리
      const result = await uploadService.uploadMultipleImages(
        uploadFiles,
        type as 'studio' | 'instructor' | 'class',
        uploadOptions
      );

      console.log('✅ 다중 업로드 완료:', {
        success: result.success,
        uploaded: result.uploaded.length,
        failed: result.failed.length
      });

      // 부분 성공도 422가 아닌 200으로 반환 (클라이언트에서 개별 처리)
      return NextResponse.json({
        success: result.success,
        results: result.uploaded.map(item => ({
          success: true,
          url: item.url,
          path: item.path,
          index: item.index
        })).concat(result.failed.map(item => ({
          success: false,
          url: '',
          path: '',
          error: item.error,
          index: item.index
        }))).sort((a, b) => a.index - b.index), // 인덱스 순으로 정렬
        summary: {
          total: uploadFiles.length,
          uploaded: result.uploaded.length,
          failed: result.failed.length
        }
      });
    }
  } catch (error) {
    console.error('Image upload error:', error);
    return NextResponse.json(
      { error: '이미지 업로드 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

/**
 * 파트너 이미지 삭제
 * @method DELETE
 * @description 파트너가 업로드한 이미지를 삭제합니다.
 * 
 * @param {NextRequest} request - Next.js 요청 객체
 * 
 * Request Body:
 * {
 *   "path": "studios/[partnerId]/[studioId]/image-123456789.jpg" // Required
 * }
 * 
 * @returns {200} 삭제 성공
 * @returns {400} 잘못된 요청 (경로 누락)
 * @returns {401} 인증되지 않은 요청
 * @returns {403} 권한 없음 (본인 이미지가 아닌 경우)
 * @returns {500} 서버 내부 오류
 * 
 * @example Response
 * {
 *   "success": true
 * }
 */
export async function DELETE(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const body = await request.json();
    const { path } = body;

    if (!path) {
      return NextResponse.json(
        { error: '삭제할 파일 경로가 없습니다.' },
        { status: 400 }
      );
    }

    // 경로에서 파트너 ID 확인 (권한 검증)
    const pathParts = path.split('/');
    if (pathParts[0] === 'studios' && pathParts[1] !== partner!.id) {
      return NextResponse.json(
        { error: '이 이미지를 삭제할 권한이 없습니다.' },
        { status: 403 }
      );
    }

    // Supabase 클라이언트 생성
    const supabase = await createClient();
    const uploadService = new UploadService(supabase);

    // 이미지 삭제
    const result = await uploadService.deleteImage(path);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || '삭제에 실패했습니다.' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Image delete error:', error);
    return NextResponse.json(
      { error: '이미지 삭제 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}