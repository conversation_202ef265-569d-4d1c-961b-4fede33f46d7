# 이미지 업로드 테스트 설정 가이드

## ⚠️ 중요 공지: Next.js 15 호환성 문제

**VS Code REST Client의 multipart 형식은 Next.js 15의 FormData 파서와 호환되지 않습니다.**
- 에러: `TypeError: Failed to parse body as FormData. [cause]: [TypeError: expected CRLF]`
- **해결책**: curl 명령어나 Postman 사용 권장

---

## 1. 테스트 이미지 파일 준비

프로젝트 루트 디렉토리(`/Users/<USER>/Documents/shallwe/`)에 테스트용 이미지 파일을 배치하세요:

```bash
# 프로젝트 루트로 이동
cd /Users/<USER>/Documents/shallwe

# 테스트 이미지 파일 복사 (예시)
cp ~/Downloads/your-image.jpg ./test-image.jpg

# 또는 인터넷에서 테스트 이미지 다운로드
curl -o test-image.jpg https://picsum.photos/800/600.jpg
```

## 2. 파일 확인

```bash
# 파일이 존재하는지 확인
ls -la test-image.jpg

# 파일 크기 확인 (5MB 이하여야 함)
du -h test-image.jpg
```

## 3. 인증 토큰 획득

1. 브라우저에서 `http://localhost:3000/partner/login` 접속
2. 파트너 계정으로 로그인
3. 개발자 도구 (F12) 열기
4. Application > Storage > Cookies > localhost:3000
5. `sb-dxodiiizyfzpueyvoaqr-auth-token` 쿠키 값 복사

## 4. 테스트 방법 (우선순위별)

### ✅ A. curl 명령어 사용 (가장 안정적 - 권장)

**스튜디오 메인 이미지 업로드:**
```bash
curl -X POST http://localhost:3000/api/partner/upload \
  -H "Cookie: sb-dxodiiizyfzpueyvoaqr-auth-token=YOUR_ACTUAL_TOKEN_HERE" \
  -F "file=@test-image.jpg" \
  -F "type=studio" \
  -F "studioId=8d5cadcd-be18-4134-ab70-5a28002e1ee1" \
  -F "prefix=featured"
```

**스튜디오 갤러리 이미지 업로드:**
```bash  
curl -X POST http://localhost:3000/api/partner/upload \
  -H "Cookie: sb-dxodiiizyfzpueyvoaqr-auth-token=YOUR_ACTUAL_TOKEN_HERE" \
  -F "file=@test-image.jpg" \
  -F "type=studio" \
  -F "studioId=8d5cadcd-be18-4134-ab70-5a28002e1ee1" \
  -F "prefix=gallery"
```

### ✅ B. Postman 사용 (추천 대안)
1. `tests/api/postman-upload-collection.json` 파일을 Postman에 Import
2. Collection Variables에서 `authToken` 값 설정
3. Body → form-data에서 파일 선택하여 업로드

### ❌ C. VS Code REST Client (현재 작동 안함)
- Next.js 15의 FormData 파서와 호환성 문제로 인해 사용 불가
- `tests/api/studios.http` 파일의 multipart 형식은 CRLF 에러 발생

## 5. 성공 응답 예시

```json
{
  "success": true,
  "url": "https://dxodiiizyfzpueyvoaqr.supabase.co/storage/v1/object/public/images/studios/partner-id/studio-id/featured-1234567890.jpg",
  "path": "studios/partner-id/studio-id/featured-1234567890.jpg"
}
```

## 6. 일반적인 에러 해결

### "Failed to parse body as FormData. expected CRLF" (500)
- **원인**: VS Code REST Client의 multipart 형식이 Next.js 15와 호환되지 않음
- **해결**: curl 명령어나 Postman 사용

### "업로드할 파일이 없습니다" (400)
- `test-image.jpg` 파일이 프로젝트 루트에 있는지 확인
- curl에서 `@` 기호 확인: `-F "file=@test-image.jpg"`

### "인증되지 않은 요청" (401)
- 브라우저에서 로그인했는지 확인
- 토큰 값이 올바르게 복사되었는지 확인
- 토큰이 만료되었을 수 있으니 재로그인

### "파일 크기는 5MB 이하여야 합니다" (422)
- 이미지 파일 크기를 확인하고 필요시 압축

### "올바른 업로드 타입을 선택해주세요" (400)
- `type` 필드가 'studio', 'instructor', 'class' 중 하나인지 확인

### "스튜디오 ID가 필요합니다" (400)
- `studioId` 필드가 올바른 UUID 형식인지 확인

## 7. 파일 위치

- **테스트 이미지**: `./test-image.jpg` (프로젝트 루트)
- **HTTP 테스트**: `tests/api/studios.http`
- **Postman Collection**: `tests/api/postman-upload-collection.json`
- **설정 가이드**: `TEST-SETUP.md` (이 파일)