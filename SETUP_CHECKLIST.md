# ShallWe 프로젝트 설정 체크리스트

이 체크리스트를 따라하면 ShallWe 프로젝트를 처음부터 완전히 설정할 수 있습니다.

## 📋 사전 준비사항

### 시스템 요구사항
- [ ] **Node.js 18+** 설치 확인 (`node -v`)
- [ ] **npm** 또는 **yarn** 설치 확인
- [ ] **Git** 설치 확인
- [ ] **Docker** 설치 (Supabase 로컬 개발용, 선택사항)

### CLI 도구 설치
```bash
# Supabase CLI (필수)
npm install -g supabase

# Vercel CLI (배포용)
npm install -g vercel

# Drizzle Kit (ORM 도구)
npm install -g drizzle-kit

# 설치 확인
supabase --version
vercel --version
drizzle-kit --version
```

## 🏗️ 프로젝트 초기화

### 1단계: 기본 프로젝트 생성
- [ ] 현재 디렉토리에서 setup.sh 실행
```bash
chmod +x setup.sh
./setup.sh
```

- [ ] 프로젝트 구조 확인
```
shallwe/
├── app/                     # Remix 애플리케이션
├── supabase/               # Supabase 설정
├── plan.md                 # 프로젝트 계획서
├── README.md               # 프로젝트 문서
└── SETUP_CHECKLIST.md      # 이 파일
```

## 🔐 외부 서비스 계정 설정

### 2단계: Supabase 프로젝트 설정

#### A. Supabase 대시보드 설정
- [ ] [Supabase](https://supabase.com/dashboard) 계정 생성/로그인
- [ ] 새 프로젝트 생성
  - [ ] 프로젝트 이름: `shallwe-app`
  - [ ] 데이터베이스 비밀번호 설정 (안전한 곳에 저장)
  - [ ] 리전 선택 (서울/도쿄 권장)
- [ ] 프로젝트 생성 완료 대기 (2-3분)

#### B. Supabase 설정값 기록
- [ ] Project Settings → API에서 다음 값들 복사:
  - [ ] `Project URL`: `https://xxxxx.supabase.co`
  - [ ] `anon public`: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
  - [ ] `service_role secret`: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

#### C. 카카오 OAuth 사전 설정
- [ ] Authentication → Providers → Kakao 찾기 (아직 활성화하지 말 것)
- [ ] Redirect URL 확인: `https://xxxxx.supabase.co/auth/v1/callback`

### 3단계: 카카오 개발자 계정 설정

#### A. 카카오 개발자 콘솔 설정
- [ ] [카카오 개발자](https://developers.kakao.com/) 계정 생성/로그인
- [ ] 새 애플리케이션 생성
  - [ ] 앱 이름: `ShallWe`
  - [ ] 회사명: 개인/회사명 입력

#### B. 카카오 앱 설정
- [ ] **앱 키** 탭에서 `REST API 키` 복사 → Client ID로 사용
- [ ] **카카오 로그인** 활성화
  - [ ] 상태: ON
  - [ ] Redirect URI 등록: `https://xxxxx.supabase.co/auth/v1/callback`
- [ ] **보안** 탭에서 Client Secret 생성 및 활성화
- [ ] **동의항목** 설정:
  - [ ] 프로필 정보(닉네임/프로필 사진): 선택동의
  - [ ] 카카오계정(이메일): 필수동의

#### C. 카카오 설정값 기록
- [ ] `REST API 키` (Client ID)
- [ ] `Client Secret`

### 4단계: PortOne 계정 설정

#### A. PortOne 계정 생성
- [ ] [PortOne](https://admin.portone.io/) 계정 생성/로그인
- [ ] 사업자 등록 (개인/개인사업자도 가능)

#### B. PortOne 설정
- [ ] 새 상점 추가
- [ ] **가맹점 식별코드** 발급 및 기록
- [ ] **PG연동** → 테스트용 PG사 추가:
  - [ ] KG이니시스 (html5_inicis) 권장
  - [ ] 테스트 모드로 설정
- [ ] **API** 설정에서 API Key/Secret 발급

#### C. PortOne 설정값 기록
- [ ] `가맹점 식별코드` (User Code)
- [ ] `API Key`
- [ ] `API Secret`

## ⚙️ 환경 변수 설정

### 5단계: 환경 변수 파일 업데이트
- [ ] `app/.env.local` 파일 열기
- [ ] 다음 값들을 실제 값으로 교체:

```bash
# Supabase 설정
NEXT_PUBLIC_SUPABASE_URL=https://xxxxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# PortOne 설정
NEXT_PUBLIC_PORTONE_USER_CODE=imp12345678
PORTONE_API_KEY=your_api_key
PORTONE_API_SECRET=your_api_secret

# 애플리케이션 설정
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NODE_ENV=development

# 데이터베이스 (Drizzle ORM)
DATABASE_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres
```

### 6단계: Supabase와 카카오 연동
- [ ] Supabase 대시보드 → Authentication → Providers → Kakao 설정:
  - [ ] Enabled: ON
  - [ ] Client ID: 카카오 REST API 키 입력
  - [ ] Client Secret: 카카오 Client Secret 입력
  - [ ] Save 클릭

#### 7A단계: 데이터베이스 연결 URL 설정
- [ ] Supabase 대시보드 → Settings → Database
- [ ] Connection string 복사 (`postgresql://postgres:[password]@...`)
- [ ] 비밀번호를 실제 데이터베이스 비밀번호로 교체
- [ ] `app/.env.local` 파일의 `DATABASE_URL` 값으로 설정

## 🗄️ 데이터베이스 설정

### 8단계: Drizzle ORM 스키마 설정

#### A. Drizzle 스키마 확인
- [ ] `app/lib/db/schema.ts` 파일 생성 확인
- [ ] `app/lib/db/index.ts` 파일 생성 확인
- [ ] `drizzle.config.ts` 파일 생성 확인

#### B. 데이터베이스 마이그레이션 생성 및 적용
```bash
cd app

# Drizzle 마이그레이션 생성
npm run db:generate

# 마이그레이션 적용 (Supabase에 직접 적용)
npm run db:push
```

#### C. 대안: Supabase SQL Editor 사용
- [ ] Supabase 대시보드 → SQL Editor
- [ ] `supabase/migrations/001_initial_schema.sql` 파일 내용 복사
- [ ] SQL Editor에 붙여넣기 후 RUN 클릭
- [ ] 오류 없이 실행 완료 확인

### 9단계: 데이터베이스 확인
- [ ] Supabase 대시보드 → Table Editor
- [ ] 다음 테이블들이 생성되었는지 확인:
  - [ ] `profiles`
  - [ ] `products`
  - [ ] `orders`
  - [ ] `order_items`
  - [ ] `payments`
- [ ] `products` 테이블에 시드 데이터 3개 확인

### 10단계: Drizzle 스튜디오 실행 (선택사항)
```bash
# Drizzle Studio로 데이터베이스 탐색
npm run db:studio
```
- [ ] 브라우저에서 `https://local.drizzle.studio` 접속
- [ ] 테이블 구조 및 데이터 확인

## 🚀 개발 환경 실행

### 11단계: 개발 서버 시작
```bash
cd app
npm install  # 의존성 설치 (혹시 빠진 것이 있다면)
npm run dev  # 개발 서버 시작
```

### 12단계: 기본 동작 확인
- [ ] 브라우저에서 `http://localhost:3000` 접속
- [ ] Remix 기본 페이지 정상 로드 확인
- [ ] 콘솔에 오류가 없는지 확인

## ✅ 설정 검증

### 13단계: 연결 테스트

#### A. Drizzle ORM 연결 테스트
```typescript
// app/routes/_index.tsx에서 간단한 테스트 코드 추가
import { db } from '@/lib/db';
import { products } from '@/lib/db/schema';

export const loader = async () => {
  try {
    const testProducts = await db.select().from(products).limit(1);
    console.log('데이터베이스 연결 성공:', testProducts);
    return { success: true };
  } catch (error) {
    console.error('데이터베이스 연결 실패:', error);
    return { success: false, error: error.message };
  }
};
```

#### B. 환경 변수 검증
- [ ] 브라우저 개발자 도구에서 환경 변수 확인
- [ ] `NEXT_PUBLIC_` 접두사가 있는 변수들만 클라이언트에 노출되는지 확인
- [ ] `DATABASE_URL`이 서버에서만 접근 가능한지 확인

## 🎯 다음 단계 준비

### 14단계: 개발 환경 최종 점검
- [ ] Git 저장소 초기화 및 첫 커밋
```bash
cd shallwe
git init
git add .
git commit -m "Initial project setup with Drizzle ORM"
```

- [ ] 개발 브랜치 생성
```bash
git checkout -b develop
```

### 15단계: Drizzle 개발 워크플로우 확인
- [ ] 스키마 변경 시 마이그레이션 생성: `npm run db:generate`
- [ ] 마이그레이션 적용: `npm run db:push`
- [ ] 데이터베이스 탐색: `npm run db:studio`
- [ ] 타입 생성: TypeScript가 자동으로 스키마에서 타입 추론

## 🔧 문제 해결

### 일반적인 문제들

#### 환경 변수 관련
- **문제**: 환경 변수가 로드되지 않음
- **해결**: `.env.local` 파일 위치 확인 (`app/` 디렉토리 내부)
- **해결**: 파일명 정확히 확인 (`.env.local`, 확장자 없음)

#### Supabase 연결 관련
- **문제**: "Invalid API key" 오류
- **해결**: Project URL과 anon key 다시 확인
- **해결**: 따옴표나 공백 문자 제거

#### 카카오 로그인 관련
- **문제**: Redirect URI 오류
- **해결**: 카카오 콘솔의 URI와 Supabase URI 정확히 일치하는지 확인
- **해결**: 프로토콜(https)과 슬래시(/) 정확히 입력

#### PortOne 관련
- **문제**: 결제창이 뜨지 않음
- **해결**: 가맹점 식별코드 정확성 확인
- **해결**: PG사 설정이 테스트 모드인지 확인

## 📞 도움 받기

### 공식 문서
- [Remix 문서](https://remix.run/docs)
- [Supabase 문서](https://supabase.com/docs)
- [PortOne 개발자 가이드](https://developers.portone.io/)
- [카카오 로그인 API](https://developers.kakao.com/docs/latest/ko/kakaologin/rest-api)

### 커뮤니티
- [Remix Discord](https://discord.gg/remix)
- [Supabase Discord](https://discord.supabase.com/)

---

모든 체크리스트를 완료했다면 이제 실제 기능 개발을 시작할 준비가 완료되었습니다! 🎉

다음은 `plan.md`의 Phase 1부터 단계별로 구현을 진행하면 됩니다.