# =============================================================================
# 🔐 ShallWe 환경변수 설정 (프로덕션용)
# =============================================================================
# 이 파일은 기본값과 설명을 포함합니다.
# 실제 프로덕션 값은 배포 환경에서 별도로 설정하세요.

# -----------------------------------------------------------------------------
# 📊 Supabase 설정
# -----------------------------------------------------------------------------
# Supabase Dashboard → Settings → API에서 확인 가능
# 이 값들은 프로덕션 환경에서 환경변수로 설정되어야 합니다
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# -----------------------------------------------------------------------------
# 🗄️  데이터베이스 (Drizzle ORM)
# -----------------------------------------------------------------------------
# 프로덕션 데이터베이스 연결 문자열
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# -----------------------------------------------------------------------------
# 🟡 카카오 로그인 설정
# -----------------------------------------------------------------------------
# 카카오 개발자 콘솔에서 프로덕션용 앱으로 등록한 키 사용
NEXT_PUBLIC_KAKAO_REST_API_KEY=your_production_kakao_key
KAKAO_CLIENT_SECRET=your_production_kakao_secret

# -----------------------------------------------------------------------------
# 💳 PortOne 설정 (구 아임포트)
# -----------------------------------------------------------------------------
# 프로덕션용 PortOne 계정 정보
NEXT_PUBLIC_PORTONE_USER_CODE=your_production_portone_code
PORTONE_API_KEY=your_production_portone_api_key
PORTONE_API_SECRET=your_production_portone_api_secret

# -----------------------------------------------------------------------------
# 🌐 애플리케이션 설정
# -----------------------------------------------------------------------------
# 프로덕션 도메인으로 설정
NEXT_PUBLIC_BASE_URL=https://your-domain.com
# NODE_ENV=production

# -----------------------------------------------------------------------------
# 🔒 보안 설정
# -----------------------------------------------------------------------------
# JWT 서명을 위한 시크릿 키 (프로덕션에서 강력한 랜덤 문자열 사용)
NEXTAUTH_SECRET=your_super_secure_secret_key_here
NEXTAUTH_URL=https://your-domain.com

# -----------------------------------------------------------------------------
# 📝 프로덕션 배포 가이드
# -----------------------------------------------------------------------------
# 1. Vercel/Netlify 등 배포 플랫폼에서 환경변수 설정
# 2. 카카오 개발자 콘솔에서 프로덕션 도메인 리다이렉트 URI 등록
# 3. Supabase에서 프로덕션 도메인 허용 목록 추가
# 4. DATABASE_URL은 Supabase 프로덕션 인스턴스 사용
# 5. 모든 시크릿 키는 안전한 랜덤 문자열로 생성

# =============================================================================
# 🔗 프로덕션 리다이렉트 URI 목록
# =============================================================================
# 카카오 개발자 콘솔에 등록할 URI들:
# - https://your-domain.com/auth/callback
# - https://your-project.supabase.co/auth/v1/callback
# 
# Supabase 허용 도메인:
# - https://your-domain.com
# =============================================================================



# Naver Map
NEXT_PUBLIC_NAVER_MAP_CLIENT_ID=

# Kakao Api
KAKAO_MAP_API_KEY=

# toss
NEXT_PUBLIC_TOSS_CLIENT_KEY=