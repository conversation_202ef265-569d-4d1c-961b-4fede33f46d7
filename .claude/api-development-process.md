# API Development Process

GitHub Issue 기반 API 개발 워크플로우 문서입니다.

## 개발 프로세스 개요

```
GitHub Issue → Requirements → Design → (Tasks) → Implementation → Testing
```

### 기본 프로세스 (간소화)
1. **Issue 분석** - GitHub Issue 내용 파악
2. **Requirements** - 비즈니스 요구사항 문서화
3. **Design** - 기술 설계 문서화 (Drizzle Hybrid 방식)
4. **구현** - 실제 코딩 작업

### 확장 프로세스 (복잡한 경우)
1. **Issue 분석** - GitHub Issue 내용 파악
2. **Requirements** - 비즈니스 요구사항 문서화
3. **Design** - 기술 설계 문서화
4. **Tasks** - 상세 작업 계획 문서화
5. **구현** - 실제 코딩 작업

## 단계별 가이드

### 1. Issue 분석
```bash
# GitHub Issue 조회
gh issue view <issue_number>

# 관련 Epic 확인
gh issue view <epic_number>
```

**수행 작업:**
- Issue 내용과 Acceptance Criteria 파악
- 관련 UI 이미지 분석
- API 엔드포인트 목록 확인
- 우선순위 확인

### 2. Requirements 작성

**파일 위치:** `docs/{partner|client}/{domain}/{version}/{api_function}.md`

**네이밍 규칙:**
- `{partner|client}`: API 사용자 타입
- `{domain}`: 도메인 영역 (instructors, studios, classes 등)
- `{version}`: API 버전 (v1, v2 등)
- `{api_function}`: API가 수행하는 기능 (create_studio, list_classes 등)

**예시:**
- `/docs/partner/studios/v1/create_studio.md`
- `/docs/partner/studios/v1/list_studios.md`
- `/docs/client/classes/v1/search_classes.md`

**포함 내용:**
- 비즈니스 요구사항
- 사용자 스토리
- API 기능 명세
- 제약 조건 및 비기능 요구사항

### 3. Design 문서 작성 (Drizzle Hybrid 방식)

**파일 위치:** 동일한 경로에 `design_` 접두사 추가
- 예: `/docs/partner/studios/v1/design_create_studio.md`

**Hybrid 접근법:**
1. **Zod Schema 정의** (Contract)
2. **Drizzle Schema 정의** (DB Layer)
3. **Service Layer 설계** (비즈니스 로직)
4. **API Route 설계** (HTTP 계층)

**포함 내용:**
- 기술 스택 및 아키텍처
- 데이터 모델 (Drizzle Schema)
- API 계약 (Zod Schema)
- 에러 처리 전략
- 보안 고려사항

### 4. Tasks 문서 작성 (선택사항)

**작성 여부 판단 기준:**
다음 중 하나라도 해당하면 Tasks 문서 작성:
- [ ] 5개 이상의 파일 수정/생성
- [ ] 2개 이상의 테이블 관련
- [ ] 기존 API와의 의존성이 복잡
- [ ] 새로운 패턴 도입
- [ ] 2주 이상 소요 예상

**파일 위치:** 동일한 경로에 `tasks_` 접두사 추가
- 예: `/docs/partner/studios/v1/tasks_create_studio.md`

**포함 내용:**
- 상세 구현 단계
- 파일별 작업 내용
- 테스트 계획
- 체크리스트

## 구현 가이드

### 파일 구조
```
src/
├── lib/
│   ├── schemas/
│   │   └── {domain}.ts          # Zod schemas
│   ├── db/
│   │   └── schema/
│   │       └── {domain}.ts      # Drizzle schema
│   ├── repositories/
│   │   └── {domain}.repository.ts
│   └── services/
│       └── {domain}.service.ts
├── app/
│   └── api/
│       └── {endpoint}/
│           ├── route.ts         # POST, GET list
│           └── [id]/
│               └── route.ts     # GET, PUT, DELETE
└── api-tests/
    └── {domain}.http           # API 테스트 파일
```

### 구현 순서 (Hybrid 방식)
1. **Zod Schema** - Request/Response 스키마 정의
2. **Drizzle Schema** - 데이터베이스 테이블 설계
3. **Repository Layer** - 데이터 접근 계층
4. **Service Layer** - 비즈니스 로직 계층
5. **API Routes** - HTTP 엔드포인트 구현
6. **API Tests** - .http 파일로 테스트 작성

## 도구 및 컨벤션

### API 테스트
- **VS Code REST Client** 확장 사용
- `.http` 파일로 테스트 관리
- Git으로 버전 관리

### 네이밍 컨벤션
- **문서 경로:** `/docs/{partner|client}/{domain}/{version}/{api_function}.md`
- **Schema files:** `{domain}.ts` (예: `studio.ts`)
- **API routes:** `/api/{resource}` (예: `/api/studios`)
- **Test files:** `{domain}.http` (예: `studio.http`)

### 기술 스택
- **Next.js 15** - App Router
- **Drizzle ORM** - 데이터베이스 ORM
- **Zod** - 스키마 검증 및 타입 생성
- **TypeScript** - 타입 안정성

## 템플릿

### Requirements 템플릿
```markdown
# {API Name} Requirements

## 비즈니스 요구사항
- 목적 및 목표
- 대상 사용자

## 기능 요구사항
### API 엔드포인트
| Method | Endpoint | 설명 |
|--------|----------|------|
| POST   | /api/{resource} | 생성 |
| GET    | /api/{resource} | 목록 조회 |

## 비기능 요구사항
- 성능 요구사항
- 보안 요구사항
- 제약 조건
```

### Design 템플릿
```markdown
# {API Name} Design

## 아키텍처 개요
- Hybrid 접근법 (Zod + Drizzle)

## 데이터 모델
### Drizzle Schema
### Zod Schema

## API 설계
### 엔드포인트 명세
### 에러 처리

## 구현 계획
1. Schema 정의
2. Repository 구현
3. Service 구현
4. API Routes 구현
```

### Tasks 템플릿 (선택사항)
```markdown
# {API Name} Tasks

## 구현 태스크
- [ ] Zod Schema 정의
- [ ] Drizzle Schema 정의
- [ ] Repository 구현
- [ ] Service 구현
- [ ] API Routes 구현
- [ ] API 테스트 작성

## 테스트 계획
- [ ] 단위 테스트
- [ ] 통합 테스트
- [ ] API 테스트 (.http)
```

## 예시: 스튜디오 API

### Issue 분석
- GitHub Issue #48: [API] 스튜디오 관리
- 4개 엔드포인트: 등록, 조회, 수정, 목록
- UI: partner/partner-studio-new.jpg

### 문서 구조
```
docs/partner/studios/v1/
├── create_studio.md              # 스튜디오 등록 API
├── get_studio.md                 # 스튜디오 조회 API
├── update_studio.md              # 스튜디오 수정 API
├── list_studios.md               # 스튜디오 목록 API
├── design_create_studio.md       # 등록 API 설계 문서
├── design_get_studio.md          # 조회 API 설계 문서
├── design_update_studio.md       # 수정 API 설계 문서
└── design_list_studios.md        # 목록 API 설계 문서
```

### 코드 구조
```
src/lib/schemas/studio.ts
src/lib/db/schema/studio.ts
src/lib/repositories/studio.repository.ts
src/lib/services/studio.service.ts
src/app/api/studios/route.ts
src/app/api/studios/[id]/route.ts
api-tests/studio.http
```

## 재사용 가이드

1. **새로운 API 개발 시:**
   - 이 문서 참조
   - 템플릿 복사하여 사용
   - 프로젝트 컨벤션 준수

2. **팀 협업 시:**
   - 문서 리뷰 프로세스 추가
   - 각 단계별 Approval 과정 도입

3. **프로세스 개선:**
   - 경험을 바탕으로 이 문서 지속 업데이트
   - 새로운 패턴이나 도구 추가시 문서 반영