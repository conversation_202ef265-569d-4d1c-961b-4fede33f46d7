{"permissions": {"allow": ["Bash(npm run check-types:*)", "Bash(find:*)", "Bash(eza:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__ide__getDiagnostics", "Bash(npm run check-type:*)", "WebFetch(domain:orm.drizzle.team)", "Bash(gh issue create:*)", "Bash(npm run type-check:*)", "Bash(npm run lint)", "Bash(npx tsc:*)", "Bash(git checkout:*)", "Bash(git pull:*)", "Bash(git merge:*)", "Bash(git push:*)", "<PERSON>sh(gh issue close:*)", "Bash(git branch:*)", "<PERSON><PERSON>(gh issue view:*)", "WebFetch(domain:www.figma.com)", "<PERSON><PERSON>(gh issue edit:*)", "Bash(ls:*)", "Bash(gh label:*)", "WebFetch(domain:github.com)", "Bash(gh project:*)", "Bash(rg:*)", "Bash(gh api:*)"], "deny": []}}