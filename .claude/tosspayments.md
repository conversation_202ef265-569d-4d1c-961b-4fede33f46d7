# 토스페이먼츠 개발자센터

## Docs

- [Android, iOS 웹뷰에서 딥링크 열기](https://docs.tosspayments.com/blog/android-ios-webview-deeplink.md): 각 딥링크 유형의 특징과 차이점을 자세히 알아보고, Android와 iOS 웹뷰에서 딥링크로 국내 카드앱·은행앱으로 이동하는 예시를 살펴볼게요.
- [예제로 이해하는 async/await 문법](https://docs.tosspayments.com/blog/async-await-example.md): 오늘은 Promise의 단점을 알아보고, async/await 문법으로 어떻게 비동기 작업을 더 효율적으로 처리할 수 있는지 토스페이먼츠 결제위젯 예시로 자세히 살펴볼게요.
- [Basic 인증과 Bearer 인증의 모든 것](https://docs.tosspayments.com/blog/everything-about-basic-bearer-auth.md): 비밀번호로 이메일 계정의 권한을 확인하는 것 처럼, HTTP 인증으로 서버에 접근하는 클라이언트의 권한을 확인해요.
- [해싱과 암호화는 어떻게 다른가요?](https://docs.tosspayments.com/blog/hashing-and-encryption-difference.md): 이번 아티클에서는 해싱과 암호화의 기본 개념, 주요 차이점, 그리고 각각을 언제 사용해야 하는지 알아볼게요.
- [Apple Pay를 연동하는 세 가지 방법](https://docs.tosspayments.com/blog/how-to-integrate-apple-pay.md): 이번 포스트에서는 토스페이먼츠로 내 쇼핑몰에 Apple Pay를 연동할 수 있는 세 가지 방법을 알아볼게요.
- [TLS 1.2로 업그레이드하는 방법](https://docs.tosspayments.com/blog/how-to-raise-tls-version.md): 토스페이먼츠는 안전하지 않은 TLS 1.0, TLS 1.1 보안 채널에 대한 지원을 종료하기로 결정했습니다.
- [회원가입, 사업자번호 없이 결제 테스트하기](https://docs.tosspayments.com/blog/how-to-test-toss-payments.md): 오늘은 계약 전에 토스페이먼츠의 테스트 환경에서 온라인 결제를 연동하고 시뮬레이션하는 방법을 쉽고 간단하게 소개할게요.
- [딥링크 실전에서 잘 사용하는 방법](https://docs.tosspayments.com/blog/how-to-use-deep-links.md): 모바일 서비스에서 다른 앱을 열 때 사용하는 딥링크에 대해서 알아보고 커스텀 URL 스킴을 중심으로 특정 외부 앱을 실행하는 방법을 설명해요.
- [좋은 객체 ID(Object ID) 만들기](https://docs.tosspayments.com/blog/making-object-ids.md): 좋은 객체 ID를 만들어야 하는 이유와 방법을 고유성과 식별 가능성을 중심으로 알아볼게요.
- [빈 문자열과 null의 차이점](https://docs.tosspayments.com/blog/null-and-empty-string-difference.md): JavaScript에서 빈 문자열과 null은 어떻게 다른가요? 이 글에서는 두 값의 차이점과 각각이 사용되는 상황을 알아봅니다.
- [결제창에서 CORS 대응하기](https://docs.tosspayments.com/blog/payment-window-cors-error.md): 토스페이먼츠 결제창 케이스를 가지고 CORS(Cross-Origin Resource Sharing, 교차 출처 리소스 공유) 이슈와 해결 방법을 알아봅니다.
- [결제위젯으로 React의 useEffect 사용해보기](https://docs.tosspayments.com/blog/react-use-effect.md): useEffect는 React의 함수형 컴포넌트에서 컴포넌트의 라이프사이클을 관리할 수 있도록 도와주는 Hook입니다.
- [예제로 이해하는 React의 useState](https://docs.tosspayments.com/blog/react-use-state.md): 이번 아티클에서는 컴포넌트에 상태를 관리하는 `useState` Hook을 결제 예제로 알아볼게요.
- [리다이렉트 URL로 결제 정보 받는 법](https://docs.tosspayments.com/blog/redirect.md): 리다이렉트 URL의 개념과 결제 요청 후 돌아온 정보를 처리하는 방법을 자세히 알려드려요.
- [POST, PUT, PATCH의 차이점](https://docs.tosspayments.com/blog/rest-api-post-put-patch.md): REST API 디자인의 기본이 되는 POST, PUT, PATCH 메서드를 자세히 살펴볼게요.
- [시크릿 키 베스트 프랙티스](https://docs.tosspayments.com/blog/secret-key-best-practice.md): 시크릿 키는 이름 그대로 비밀로 유지되어야 되는 값인데요. 오늘은 시크릿 키가 정확히 어떤 역할을 하는지, 또 어떻게 안전하게 사용할 수 있는지 알려드릴게요.
- [구독 결제 서비스 구현하기 (1) 빌링키 발급](https://docs.tosspayments.com/blog/subscription-service-1.md): 이 시리즈에서는 빌링키를 발급한 후 간단한 구독 서비스를 구현하는 방법을 알려드려요.
- [구독 결제 서비스 구현하기 (2) 스케줄링](https://docs.tosspayments.com/blog/subscription-service-2.md): 이 시리즈에서는 빌링키를 발급한 후 간단한 구독 서비스를 구현하는 방법을 알려드려요.
- [토스페이먼츠에 가장 많이 물어보는 질문 세 가지](https://docs.tosspayments.com/blog/three-faqs-toss-payments.md): 토스페이먼츠를 연동하는 개발자가 가장 많이 물어보는 질문 세 가지를 담아봤어요. 결제 연동 단계에 따라 하나씩 알아볼게요.
- [토스페이먼츠 SDK v2를 소개합니다](https://docs.tosspayments.com/blog/tosspayments-sdk-v2.md): 토스페이먼츠 SDK v2를 소개합니다
- [결제와 거래, 차이점이 뭔가요?](https://docs.tosspayments.com/blog/transaction-flow.md): 결제와 거래의 차이를 이해하고 결제 흐름에 따른 객체의 변화를 알 수 있어요.
- [Promise 실전에서 사용해보기](https://docs.tosspayments.com/blog/using-promises.md): 토스페이먼츠 결제위젯 예제로 Promise가 무엇이고 어떻게 사용해야 되는지 알아볼게요.
- [가상계좌 연동할 때 웹훅이 꼭 필요한 이유](https://docs.tosspayments.com/blog/virtual-account-webhook.md): 가상계좌를 연동할 때 웹훅이 꼭 필요한 이유와, 웹훅으로 입금 알림을 받을 수 있는 방법을 알아봅니다.
- [멱등성이 뭔가요?](https://docs.tosspayments.com/blog/what-is-idempotency.md): 생소한 표현이지만 알고 보면 쉬워요. 멱등성에 대해 이해하고 API를 멱등하게 제공하기 위한 방법도 함께 알아봐요.
- [리다이렉트 URL은 어떻게 설정해야 돼요?](https://docs.tosspayments.com/blog/what-is-successurl.md): 결제 요청의 성공, 실패 결과는 리다이렉트 URL로 받는데요. 성공 리다이렉트 URL을 설정할 수 있는 방법 세 가지를 알아볼게요.
- [결제위젯 클라이언트 에러 가이드](https://docs.tosspayments.com/blog/widget-client-error-handling.md): 결제위젯 클라이언트 연동에서 만날 수 있는 에러와 대응 방법을 알려드릴게요.
- [데이터 타입](https://docs.tosspayments.com/codes/data-types.md): 토스페이먼츠 API/SDK에서 사용하는 데이터 타입을 설명합니다.
- [ENUM 코드](https://docs.tosspayments.com/codes/enum-codes.md): 토스페이먼츠 API/SDK에서 사용하는 ENUM 코드입니다.
- [기관 코드](https://docs.tosspayments.com/codes/org-codes.md): 파라미터로 전달할 수 있는 기관(카드사, 은행, 통신사) 코드 리스트입니다.
- [Payment APIs](https://docs.tosspayments.com/en/api-guide.md): The Payment APIs are essential to integrating Toss Payments, whether you are only using our APIs or our SDKs.
- [International merchants guide](https://docs.tosspayments.com/en/index.md): Understand the features and flow of Korean card payments and integrate Toss Payments.
- [Send payouts to your sellers](https://docs.tosspayments.com/en/integration-payouts.md): Learn how to integrate Toss Payments with Payment Window.
- [Card payments](https://docs.tosspayments.com/en/integration-types.md): Learn about the different ways to integrate card payments and find the best option for you.
- [Integrate payment widgets](https://docs.tosspayments.com/en/integration-widget.md): The Toss Payments widget is a low code payment solution. Embed the payment UI to your checkout page and customize no-code via admin.
- [Integrate payment windows](https://docs.tosspayments.com/en/integration.md): Start your payment window integration. Supported payment methods include card, digital wallets such as TossPay, NaverPay, SamsungPay, KakaoPay etc, transfer, virtual accounts, mobile phone, and gift certificates.
- [Online payment process](https://docs.tosspayments.com/en/overview.md): Explore the Korean online payment process, step by step.
- [Integrate webhooks](https://docs.tosspayments.com/en/webhooks.md): Receive real-time notifications with webhooks. Get notified for payment updates, payouts, and BrandPay customer changes.
- [결제 기초](https://docs.tosspayments.com/guides/basics/payment.md): 용어부터 정책까지, 초보자에게 필요한 모든 결제 기초 지식을 알려드려요.
- [토스페이먼츠 서비스 기초](https://docs.tosspayments.com/guides/basics/service.md): 토스페이먼츠 서비스와 결제수단을 이해해요.
- [자동결제(빌링) API로 연동하기](https://docs.tosspayments.com/guides/billing/integration-api.md): 구매자의 카드 정보를 등록해 빌링키를 얻고 자동결제를 연동하는 방법을 알아보세요.
- [퀵계좌이체 자동결제(빌링) 연동하기](https://docs.tosspayments.com/guides/billing/integration-quick.md): 퀵계좌이체에 구매자의 계좌를 한 번만 등록하고 나면, 별도의 구매자 인증 없이 간편하게 결제를 요청할 수 있습니다.
- [자동결제(빌링) 결제창 연동하기](https://docs.tosspayments.com/guides/billing/integration.md): 자동결제는 다른 이름으로 빌링, 또는 정기결제로 불리는 결제 방식입니다. 카드 등록창에서 구매자의 카드를 한 번만 등록하고 나면, 별도의 인증 없이 간편하게 결제를 요청할 수 있습니다.
- [자동결제(빌링) 이해하기(Version 1)](https://docs.tosspayments.com/guides/billing/overview.md): 자동결제는 정기 배송, 음악 스트리밍과 같은 구독형 서비스에서 사용하는 결제 방식입니다. 자동결제(빌링)의 개념과 과정을 설명합니다.
- [브랜드페이 인증하기](https://docs.tosspayments.com/guides/brandpay/auth.md): 브랜드페이 인증은 OAuth 2.0 인증 프레임워크를 기반으로 구현되어 있습니다. 브랜드페이에서 Access Token을 발급하고 사용하는 방법을 안내합니다.
- [브랜드페이 연동하기(Version 1)](https://docs.tosspayments.com/guides/brandpay/integration.md): 브랜드페이에서는 자체 간편결제에 사용할 수 있는 UI를 쉽게 가져다 쓸 수 있는 브랜드페이 JavaScript SDK 연동 가이드입니다.
- [브랜드페이 마이그레이션](https://docs.tosspayments.com/guides/brandpay/migration.md): 기존에 사용하던 커넥트페이를 브렌드페이로 마이그레이션 하는 방법을 안내합니다.
- [브랜드페이 이해하기](https://docs.tosspayments.com/guides/brandpay/overview.md): 브랜드페이는 상점에서 자체 간편결제를 구현할 수 있는 SDK와 API를 제공하는 서비스입니다. 브랜드페이가 제공하는 기능과 구현 방식을 소개합니다.
- [브랜드페이 위젯 연동하기](https://docs.tosspayments.com/guides/brandpay/widget.md): 브랜드페이 JavaScript SDK를 이용해 브랜드페이 위젯을 사용해보세요.
- [결제 취소하기](https://docs.tosspayments.com/guides/cancel-payment.md): 토스페이먼츠 결제 취소 API를 호출하세요. 결제 금액 전액 또는 일부를 구매자에게 환불하는 방법, 가상계좌 결제 취소하는 방법, 결제위젯에서 가상계좌 정보 확인하는 방법을 소개합니다.
- [환경 설정하기](https://docs.tosspayments.com/guides/environment.md): 토스페이먼츠 테스트 연동 환경 주의점입니다. 테스트 키를 사용할 때 라이브 환경과 다른 점을 소개해요.
- [해외 간편결제 연동](https://docs.tosspayments.com/guides/foreign-easy-pay.md): PayPal, Alipay 등 해외 간편결제창을 JavaScript SDK를 연동하는 방법을 알아봅니다.
- [간편결제 응답 확인하기](https://docs.tosspayments.com/guides/learn/easypay-response.md): 간편결제 응답을 확인하고 처리하는 방법입니다. 구매자가 선택한 결제수단에 따라 응답 객체가 달라집니다.
- [해외결제 연동하기](https://docs.tosspayments.com/guides/learn/foreign-payment.md): 해외결제는 해외에서 발급된 카드 또는 해외 간편결제 서비스를 사용한 결제입니다. 국내에 거주하는 외국인 또는 해외에 거주하는 구매자에게 상품을 판매할 때 사용하세요.
- [결제 흐름 이해하기](https://docs.tosspayments.com/guides/learn/payment-flow.md): 카드 결제 과정의 세 가지 핵심 단계인 요청, 인증, 승인을 이해할 수 있어요.
- [결제수단 이해하기](https://docs.tosspayments.com/guides/learn/payment-methods.md): 내 온라인 서비스에 적합한 결제수단을 알아보세요. 각 결제수단의 결제 한도, 환불 기한 등 정책을 확인하세요.
- [결제 결과 안내](https://docs.tosspayments.com/guides/learn/payment-results.md): 결제 승인이 완료된 이후 구매자에게 결제 결과를 안내하는 방법입니다. 결제 이메일, 매출전표, 현금영수증을 확인하는 방법입니다.
- [세금 처리](https://docs.tosspayments.com/guides/learn/tax.md): 면세 금액을 설정해서 결제하거나 취소하는 방법, 부가가치세 계산 등 세금 처리와 관련한 내용을 알아봅니다.
- [결제위젯 어드민 사용하기](https://docs.tosspayments.com/guides/payment-widget/admin.md): 토스페이먼츠와 계약을 완료했으면 어드민에서 결제 UI를 커스터마이징할 수 있어요.
- [배포 체크리스트](https://docs.tosspayments.com/guides/payment-widget/deploy-checklist.md): 연동을 마치고 최종 배포를 하기 전에 체크해야 할 항목을 알려드려요.
- [브랜드페이 연동하기](https://docs.tosspayments.com/guides/payment-widget/integration-brandpay.md): 결제위젯으로 편리하게 브랜드페이를 연동하세요. 브랜드페이는 자체 간편결제 시스템을 구축하는 결제 서비스예요.
- [해외 간편결제 연동하기](https://docs.tosspayments.com/guides/payment-widget/integration-foreignpay.md): PayPal, 중국 및 동남아 간편결제 등 다양한 해외 결제수단을 개별로 계약 및 연동할 필요 없이, 토스페이먼츠 결제위젯을 통해 한 번에 사용할 수 있어요.
- [키인 결제 추가하기](https://docs.tosspayments.com/guides/payment-widget/integration-key-in.md): 결제위젯에 키인 결제를 추가하는 방법입니다. 키인 결제는 카드 번호를 입력해서 결제하는 방식입니다.
- [PayPal 연동하기](https://docs.tosspayments.com/guides/payment-widget/integration-paypal.md): 토스페이먼츠 결제위젯 SDK로 PayPal 해외 간편결제를 연동하는 가이드입니다. 결제위젯으로 간편하게 주문서 페이지의 결제 UI를 연동할 수 있기 때문에 해외 간편결제 연동에 추천하는 방법입니다.
- [퀵계좌이체 추가하기](https://docs.tosspayments.com/guides/payment-widget/integration-quick.md): 토스페이먼츠 결제위젯 SDK로 퀵계좌이체를 연동하는 방법입니다. 결제위젯이 연동되어 있다면 퀵계좌이체는 자동으로 추가됩니다. 퀵계좌이체는 휴대폰만 가지고 있으면 쉽게 결제할 수 있는 계좌이체 결제수단입니다.
- [결제위젯 이해하기(Version 1)](https://docs.tosspayments.com/guides/payment-widget/overview.md): 결제위젯은 토스페이먼츠에서 수많은 상점을 분석해서 만든 최적의 주문서 UI입니다. 개발자가 최초 1회만 연동하면 결제수단 추가, 디자인 수정은 코드 없이 어드민에서 바로 가능해요.
- [커스텀 결제수단·직연동 추가하기](https://docs.tosspayments.com/guides/payment-widget/pro/custom.md): 토스페이먼츠가 지원하지 않는 결제수단(평생교육바우처, 사내 포인트 등)을 이용하거나 간편결제를 결제위젯에 직연동하고 싶을 때 커스텀 결제수단·직연동 기능을 사용하세요.
- [결제위젯 Pro 연동하기](https://docs.tosspayments.com/guides/payment-widget/pro/overview.md): 더 높은 자유도로 내 상점에 최적화된 결제 경험을 만들고 싶다면 결제위젯 Pro 플랜을 이용하세요.
- [고정 가상계좌 발급받기](https://docs.tosspayments.com/guides/payment/fixed-virtual-account.md): 구매자가 직접 카드 번호를 입력해서 결제할 수 있는 카드 번호 결제 API를 연동해보세요.
- [카드사 및 간편결제 자체창 바로 열기](https://docs.tosspayments.com/guides/payment/integration-direct.md): 카드사 및 간편결제 자체창을 연동하는 방법을 알아봅니다.
- [해외 간편결제창 연동하기](https://docs.tosspayments.com/guides/payment/integration-foreignpay.md): PayPal과 중국 및 동남아 결제를 결제창 SDK로 연동하는 방법입니다.
- [퀵계좌이체 연동하기](https://docs.tosspayments.com/guides/payment/integration-quick.md): 퀵계좌이체는 앱설치나 공동인증서가 없어도 간편결제처럼 한 번만 계좌등록을 해두면 비밀번호만 입력하고 쉽게 결제를 마칠 수 있는 계좌이체 서비스예요.
- [토스페이먼츠 결제창 연동하기(Version 1)](https://docs.tosspayments.com/guides/payment/integration.md): 카드 결제창을 연동해보세요.
- [다국어 결제창](https://docs.tosspayments.com/guides/payment/international-window.md): 해외 카드를 사용하는 구매자를 위해 다국어 결제창을 호출하는 방법을 알려드려요.
- [카드 번호 결제(키인 결제) API로 연동하기](https://docs.tosspayments.com/guides/payment/key-in.md): 구매자가 직접 카드 번호를 입력해서 결제할 수 있는 카드 번호 결제 API를 연동해보세요.
- [결제창 연동하기(Version 1)](https://docs.tosspayments.com/guides/payment/overview.md): 결제창 연동 가이드 모음입니다.
- [테스트용 실패 리다이렉트 페이지](https://docs.tosspayments.com/guides/payment/test-fail.md): 결제창 연동 가이드 테스트용 실패 리다이렉트 페이지입니다.
- [테스트용 성공 리다이렉트 페이지](https://docs.tosspayments.com/guides/payment/test-success.md): 결제창 연동 가이드 테스트용 실패 리다이렉트 페이지입니다.
- [가상계좌 API로 연동하기](https://docs.tosspayments.com/guides/payment/virtual-account-api.md): 가상계좌 API를 사용해 구매자가 원하는 은행의 가상계좌를 발급하는 것부터 결제를 완료하기까지의 과정을 알아봅니다.
- [가상계좌 결제창 연동하기](https://docs.tosspayments.com/guides/payment/virtual-account.md): 구매자가 원하는 은행으로 가상계좌를 발급한 뒤 입금 받아 결제하는 방식입니다. 이 가이드를 읽고 나면 토스페이먼츠 가상계좌 결제창에서 가상계좌를 발급받고, 입금 결과를 웹훅으로 받을 수 있어요.
- [정산 지급대행 v1](https://docs.tosspayments.com/guides/payouts.md): 토스페이먼츠 정산 지급대행 v1 API 및 웹훅 스펙입니다. 지급대행은 하위 상점(서브몰)의 정산 금액을 토스페이먼츠가 대신 지급하는 서비스입니다.
- [PayPal 연동하기](https://docs.tosspayments.com/guides/paypal.md): 토스페이먼츠 결제창 SDK로 PayPal 해외 간편결제를 연동하는 방법입니다. 결제 페이지를 구축해서 페이팔 버튼을 직접 만들어야 합니다.
- [자동결제(빌링) 이해하기](https://docs.tosspayments.com/guides/v2/billing.md): 자동결제는 정기 배송, 음악 스트리밍과 같은 구독형 서비스에서 사용할 수 있어요. 자동결제(빌링)의 개념과 과정을 설명합니다.
- [자동결제(빌링) API로 연동하기](https://docs.tosspayments.com/guides/v2/billing/integration-api.md): API만 사용해서 구매자의 카드 정보를 등록하고 자동결제를 연동하는 서버 투 서버 방법을 알아보세요.
- [퀵계좌이체 자동결제(빌링) 연동하기](https://docs.tosspayments.com/guides/v2/billing/integration-quick.md): 퀵계좌이체에 구매자의 계좌를 한 번만 등록하고 나면, 별도의 구매자 인증 없이 간편하게 결제를 요청할 수 있습니다.
- [자동결제(빌링) 결제창 연동하기](https://docs.tosspayments.com/guides/v2/billing/integration.md): 자동결제는 다른 이름으로 빌링, 또는 정기결제로 불리는 결제 방식입니다. 카드 등록창에서 구매자의 카드를 한 번만 등록하고 나면, 별도의 구매자 인증 없이 간편하게 결제를 요청할 수 있습니다.
- [브랜드페이 이해하기](https://docs.tosspayments.com/guides/v2/brandpay.md): 브랜드페이로 내 상점만의 자체 간편결제를 구현해보세요. 브랜드페이가 제공하는 기능과 구현 방식을 소개합니다.
- [브랜드페이 인증하기](https://docs.tosspayments.com/guides/v2/brandpay/auth.md): 브랜드페이 인증은 OAuth 2.0 인증 프레임워크를 기반으로 구현되어 있습니다. 브랜드페이에서 Access Token을 발급하고 사용하는 방법을 안내합니다.
- [브랜드페이 연동하기](https://docs.tosspayments.com/guides/v2/brandpay/integration.md): 브랜드페이는 자체 간편결제를 구축하는 서비스입니다.
- [결제 취소하기](https://docs.tosspayments.com/guides/v2/cancel-payment.md): 토스페이먼츠 결제 취소 API로 금액 전액・부분 환불하는 방법, 가상계좌 결제 취소하는 방법, 결제위젯에서 가상계좌 정보 확인하는 방법을 소개합니다.
- [배포 체크리스트](https://docs.tosspayments.com/guides/v2/deploy-checklist.md): 연동을 마치고 최종 배포를 하기 전에 체크해야 할 항목을 알려드려요.
- [간편결제 응답 확인하기](https://docs.tosspayments.com/guides/v2/easypay-response.md): 간편결제에서는 구매자가 카드, 계좌, 포인트를 함께 사용할 수도 있는데요. 구매자가 선택한 결제수단에 따라 결제 승인의 응답이 어떻게 바뀌는지 설명해드려요.
- [환경 설정하기](https://docs.tosspayments.com/guides/v2/get-started/environment.md): 테스트 환경 주의점, 방화벽 설정, 지원 플랫폼 및 브라우저를 알아보세요.
- [LLMs로 결제 연동하기](https://docs.tosspayments.com/guides/v2/get-started/llms-guide.md): Cursor, Windsurf, Claude Desktop 등 다양한 AI 도구(LLMs)를 활용해서 결제 연동을 쉽게 시작해보세요.
- [마이그레이션하기](https://docs.tosspayments.com/guides/v2/get-started/migration-guide.md): 최신 버전의 토스페이먼츠 SDK로 마이그레이션하는 방법을 알아보세요.
- [PG와 온라인 결제](https://docs.tosspayments.com/guides/v2/get-started/online-payment.md): PG사의 역할과 온라인 카드 결제 과정을 자세히 알아보세요.
- [결제 흐름 이해하기](https://docs.tosspayments.com/guides/v2/get-started/payment-flow.md): 카드 결제 과정의 세 가지 핵심 단계인 요청, 인증, 승인을 이해하고 결제 정보를 검증하는 방법을 알아보세요.
- [결제수단 정책](https://docs.tosspayments.com/guides/v2/get-started/payment-methods.md): 카드, 간편결제, 가상계좌 등 각 결제수단의 결제 한도, 환불 기한 등 정책을 확인하세요.
- [토스페이먼츠 결제제품](https://docs.tosspayments.com/guides/v2/get-started/payment-products.md): 토스페이먼츠에서 제공하는 결제위젯, 통합결제창, 자체창 결제 제품을 비교합니다.
- [해외결제 연동하기](https://docs.tosspayments.com/guides/v2/learn/foreign-payment.md): 해외결제는 해외에서 발급된 카드 또는 해외 간편결제 서비스를 사용한 결제입니다. 국내에 거주하는 외국인 또는 해외에 거주하는 구매자에게 상품을 판매할 때 사용하세요.
- [결제 결과 안내](https://docs.tosspayments.com/guides/v2/learn/payment-results.md): 결제 승인이 완료된 이후 구매자에게 결제 결과를 안내하는 방법입니다. 결제 이메일, 매출전표, 현금영수증을 확인하는 방법입니다.
- [세금 처리](https://docs.tosspayments.com/guides/v2/learn/tax.md): 면세 또는 복합 과세 상점을 위한 가이드입니다. 면세 상품을 결제하거나 환불하는 방법, 부가가치세를 계산하는 방법 등 세금 처리 관련 내용을 알아보세요.
- [결제위젯 이해하기](https://docs.tosspayments.com/guides/v2/payment-widget.md): 결제 UI를 주문서에 렌더링하고 결제수단 삭제・추가, UI 디자인은 노코드로 운영하세요.
- [결제위젯 어드민 설정하기](https://docs.tosspayments.com/guides/v2/payment-widget/admin.md): 토스페이먼츠와 전자결제 계약을 완료했으면 결제위젯 어드민에서 결제 UI를 커스터마이징할 수 있어요.
- [브랜드페이 연동하기](https://docs.tosspayments.com/guides/v2/payment-widget/integration-brandpay.md): 결제위젯으로 편리하게 브랜드페이를 연동하세요. 브랜드페이는 자체 간편결제 시스템을 구축하는 결제 서비스예요.
- [해외 간편결제 연동하기](https://docs.tosspayments.com/guides/v2/payment-widget/integration-foreignpay.md): PayPal, 중국 및 동남아 간편결제 등 다양한 해외 결제수단을 개별로 계약 및 연동할 필요 없이, 토스페이먼츠 결제위젯을 통해 한 번에 사용할 수 있어요.
- [PayPal 연동하기](https://docs.tosspayments.com/guides/v2/payment-widget/integration-paypal.md): 토스페이먼츠 결제위젯 SDK로 PayPal 해외 간편결제를 연동하는 가이드입니다. 결제위젯으로 간편하게 주문서 페이지의 결제 UI를 연동할 수 있기 때문에 해외 간편결제 연동에 추천하는 방법입니다.
- [결제위젯 Pro 기능 사용하기](https://docs.tosspayments.com/guides/v2/payment-widget/pro.md): 더 높은 자유도로 내 상점에 최적화된 결제 경험을 만들고 싶다면 결제위젯 Pro 플랜을 이용하세요.
- [커스텀 결제수단·직연동 추가하기](https://docs.tosspayments.com/guides/v2/payment-widget/pro/integration-custom.md): 토스페이먼츠가 지원하지 않는 결제수단(평생교육바우처, 사내 포인트 등)을 이용하거나 간편결제를 결제위젯에 직연동하고 싶을 때 커스텀 결제수단·직연동 기능을 사용하세요.
- [결제창 이해하기](https://docs.tosspayments.com/guides/v2/payment-window.md): 주문서 UI는 직접 만들고 각 결제수단의 결제창을 연동하세요.
- [카드사 및 간편결제 자체창 연동하기](https://docs.tosspayments.com/guides/v2/payment-window/integration-direct.md): 카드사 및 간편결제 자체창을 연동하는 방법을 알아봅니다.
- [해외 간편결제창 연동하기](https://docs.tosspayments.com/guides/v2/payment-window/integration-foreignpay.md): PayPal과 중국 및 동남아 결제를 결제창 SDK로 연동하는 방법입니다.
- [다국어 결제창 연동하기](https://docs.tosspayments.com/guides/v2/payment-window/integration-international.md): VISA, Mastercard 등 해외 카드를 사용하는 구매자를 위한 다국어 결제창입니다. 한국어, 영어, 중국어, 일본어를 지원하고 있습니다.
- [PayPal 연동하기](https://docs.tosspayments.com/guides/v2/payment-window/integration-paypal.md): PayPal 해외 간편결제를 연동하는 방법입니다. PayPal은 전 세계 200여 개 국가에서 4억 명 이상이 사용하는 글로벌 결제수단이에요.
- [퀵계좌이체 연동하기](https://docs.tosspayments.com/guides/v2/payment-window/integration-quick.md): 퀵계좌이체는 앱설치나 공동인증서가 없어도 간편결제처럼 한 번만 계좌등록을 해두면 비밀번호만 입력하고 쉽게 결제를 마칠 수 있는 계좌이체 서비스예요.
- [가상계좌 결제창 연동하기](https://docs.tosspayments.com/guides/v2/payment-window/integration-virtual-account.md): 토스페이먼츠 가상계좌 결제창에서 가상계좌를 발급받고, 입금 결과를 웹훅으로 받는 방법입니다.
- [카드/간편결제 통합결제창 연동하기](https://docs.tosspayments.com/guides/v2/payment-window/integration.md): 토스페이먼츠 카드/간편결제 통합결제창을 연동하는 방법이에요. 구매자가 결제창에서 결제수단, 결제 정보를 선택한 뒤에 카드 또는 간편결제 앱으로 이동해요.
- [지급대행하기](https://docs.tosspayments.com/guides/v2/payouts.md): 지급대행은 오픈마켓을 운영하는 사업자 대신 토스페이먼츠가 셀러(입점 판매자)에게 대금 지급을 대행 해주는 서비스예요.
- [웹훅(Webhook) 연결하기](https://docs.tosspayments.com/guides/v2/webhook.md): 토스페이먼츠 결제, 브랜드페이, 지급대행 상태에 변경사항이 있을 때 웹훅으로 실시간 업데이트를 받아보세요.
- [웹뷰(WebView) 연동하기](https://docs.tosspayments.com/guides/v2/webview.md): 모바일 웹뷰 결제는 앱투앱(App to App) 이동이 필요한데요. 결제기관의 앱스킴 목록과 OS별 이동 방법을 알아보세요.
- [웹훅(Webhook) 연결하기](https://docs.tosspayments.com/guides/webhook.md): 토스페이먼츠 결제, 브랜드페이, 지급대행 상태에 변경사항이 있을 때 웹훅으로 실시간 업데이트를 받아보세요.
- [웹뷰(WebView) 연동하기](https://docs.tosspayments.com/guides/webview.md): 모바일에서 결제창을 띄울 때 카드사별 결제수단을 인증하려면 외부 앱(3rd-party 앱)을 연동해야 합니다. 연동에 필요한 외부 앱 스킴(App URL Scheme)목록과 추가 로직을 살펴보세요.
- [구 모듈 에러 코드](https://docs.tosspayments.com/legacy/errors.md): 기존 전자결제 연동 모듈의 에러 코드입니다.
- [구 모듈 가이드](https://docs.tosspayments.com/legacy/guide.md): 기존 전자결제 서비스 연동과 운영에 필요한 모듈 가이드와 에러 코드, 데모 등을 확인해보세요.
- [API 키](https://docs.tosspayments.com/reference/using-api/api-keys.md): 토스페이먼츠 클라이언트 키 및 시크릿 키를 발급받고 사용하는 방법을 알아봅니다. 클라이언트 키는 SDK를 초기화할 때 사용하고 시크릿 키는 API를 호출할 때 사용합니다.
- [인증 및 기타 헤더 설정](https://docs.tosspayments.com/reference/using-api/authorization.md): 토스페이먼츠 API를 사용하기 위해 필요한 인증과 헤더 설정 방법입니다.
- [요청·응답 본문](https://docs.tosspayments.com/reference/using-api/req-res.md): 토스페이먼츠 API 요청과 응답 본문을 알아봅니다.
- [보안](https://docs.tosspayments.com/reference/using-api/security.md): 토스페이먼츠 API의 보안 정책과 방화벽 설정하는 방법을 안내합니다.
- [웹훅 이벤트](https://docs.tosspayments.com/reference/using-api/webhook-events.md): 토스페이먼츠에서 제공하는 웹훅 이벤트 목록입니다.
- [API 버전 정책](https://docs.tosspayments.com/reference/versioning.md): 토스페이먼츠 API의 버전 관리 정책과 릴리즈 정책을 소개합니다.
- [자주 묻는 질문](https://docs.tosspayments.com/resources/faq.md): 토스페이먼츠 결제 연동・운영 과정에서 가질 수 있는 궁금증을 해결하세요.
- [AES(고급 암호화 표준)](https://docs.tosspayments.com/resources/glossary/aes.md): AES(고급 암호화 표준)는 2001년에 미국 국립표준기술연구소(NIST)에서 설립한 대칭 키 암호화 알고리즘입니다.
- [자금세탁방지(AML)](https://docs.tosspayments.com/resources/glossary/aml.md): 자금세탁방지(AML)란 불법적으로 취득한 재산을 합법적으로 취득한 것처럼 위장하는 과정을 적발하고 예방하는 제도입니다.
- [API(Application Programming Interface)](https://docs.tosspayments.com/resources/glossary/api.md): API(Application Programming Interface)는 두 애플리케이션이 통신하는 방법입니다. 클라이언트가 요청을 보내면, 서버에서는 요청에 상응하는 응답을 보내줘요.
- [뱅크페이](https://docs.tosspayments.com/resources/glossary/bankpay.md): 뱅크페이는 금융결제원에서 운용하는 실시간계좌이체 PG 서비스입니다. 온라인 쇼핑몰에서 결제수단을 '계좌이체' 또는 '실시간계좌이체'로 선택하면 뱅크페이를 통해서 계좌이체를 하는 것이죠.
- [Base64](https://docs.tosspayments.com/resources/glossary/base64.md): Base64는 이진(Binary) 데이터를 64개의 ASCII 문자로 구성된 문자열로 변환하는 인코딩 방식입니다.
- [Basic 인증](https://docs.tosspayments.com/resources/glossary/basic-auth.md): Basic 인증 방식은 가장 기본적인 HTTP 인증 방식이에요. 인증 정보로 사용자 ID, 비밀번호를 사용해요.
- [Bearer 인증](https://docs.tosspayments.com/resources/glossary/bearer-auth.md): Bearer 인증은 OAuth 2.0 프레임워크에서 사용하는 토큰 인증 방식이에요. “Bearer”은 소유자라는 뜻인데, “이 토큰의 소유자에게 권한을 부여해줘”라는 의미로 이름을 붙였다고 해요.
- [자동결제(빌링)](https://docs.tosspayments.com/resources/glossary/billing.md): 자동결제는 구독형 서비스에서 사용합니다. 고객이 최초 주문에 본인인증을 마치고 결제 정보를 입력하면 상점에서 암호화된 고객의 결제 정보를 저장해요
- [카드 BIN](https://docs.tosspayments.com/resources/glossary/bin.md): 카드 BIN은 Bank Identification Number의 준말로 일반적으로 카드번호의 앞 6~8자리를 가리켜요. BIN에는 카드 발급사, 발급 나라, 등급 등의 정보가 있어요.
- [사업자번호](https://docs.tosspayments.com/resources/glossary/business-no.md): 사업자번호란 최초 사업자를 등록할 때 세무서에서 받는 고유 번호에요. 태어났을 때 출생신고를 하듯이, 사업을 시작할 때 사업자를 등록해야 해요.
- [할부](https://docs.tosspayments.com/resources/glossary/card-installment.md): 할부는 결제 금액을 한 번에 내지 않고, 월 단위로 나눠내는 것을 뜻해요. 고객 입장에서는 결제 금액을 여러 달에 걸쳐 나눠내지만, 실제로는 카드사가 고객 대신 결제 금액을 일시불로 지불해요.
- [카드 결제](https://docs.tosspayments.com/resources/glossary/card-payment.md): 카드 결제는 인증, 승인, 매입 순으로 이루어집니다. 각 단계에서 일어나는 일을 카드사, PG사, 고객 관점에서 알아볼게요.
- [카드](https://docs.tosspayments.com/resources/glossary/card.md): 카드는 가장 많이 사용되는 결제수단으로, 전체 결제에서 이용금액과 건수 모두 60% 이상을 차지하고 있어요. 카드 종류는 크게 신용카드, 체크카드, 선불카드 등으로 구분돼요.
- [현금영수증](https://docs.tosspayments.com/resources/glossary/cash-receipt.md): 현금영수증이란 소비자가 상품이나 서비스를 구매할 때 발생하는 현금 거래를 국세청에 알리고 받는 영수증이에요. 현금영수증에는 거래 금액, 일자, 거래처명, 상품 내용 등의 정보가 포함되어 있어요.
- [CDN(Content Delivery Network)](https://docs.tosspayments.com/resources/glossary/cdn.md): CDN(Content Delivery Network)이란 웹 콘텐츠를 세계 곳곳에 있는 여러 서버에 분산하여 저장하는 분산 서버 네트워크 시스템입니다.
- [자사몰](https://docs.tosspayments.com/resources/glossary/company-mall.md): 자사몰은 자체적으로 개발하고 운영하는 온라인 쇼핑몰을 말합니다. 온라인 쇼핑몰 웹사이트를 처음부터 끝까지 직접 만들고 유지하는 거죠. 호스팅사 기반으로 구축하는 것과 반대되는 개념이에요.
- [구매 전환율(Conversion Rate)](https://docs.tosspayments.com/resources/glossary/conversion.md): 구매 전환율은 쇼핑몰 방문자 중 실제 결제를 완료한 구매자의 비율을 뜻해요. 구매 전환율이 낮다면 구매 과정에서 이탈하는 고객이 많다는 뜻이라 개선이 필요해요.
- [법인카드](https://docs.tosspayments.com/resources/glossary/corporate-card.md): 법인카드는 기업, 기관, 협회, 기타 사업자등록번호가 있는 법인에서 발급받을 수 있는 카드에요. 법인에서 업무적으로 발생하는 경비를 결제할 때 편리하게 사용할 수 있어요.
- [CORS(교차 출처 리소스 공유)](https://docs.tosspayments.com/resources/glossary/cors.md): CORS를 번역하면 “교차 출처 리소스 공유”에요. ‘두 출처가 서로 다르다’는 뜻인데요. CORS를 설정한다는 건 ‘출처가 다른 서버 간의 리소스 공유’를 허용한다는 거죠.
- [크로스 플랫폼](https://docs.tosspayments.com/resources/glossary/cross-platform.md): 크로스 플랫폼 앱은 하나의 소스 코드로 Android, iOS에서 똑같이 작동하는 앱을 뜻하는데요. 대표적으로 React Native, Flutter가 있어요.
- [문화비 소득공제](https://docs.tosspayments.com/resources/glossary/culture-deduction.md): 문화비 소득공제는 도서 구입, 공연·영화 관람, 박물관·미술관 입장, 종이 신문 구독에 사용한 금액을 최대 100만원 안에서 소득공제해주는 제도에요.
- [상점관리자](https://docs.tosspayments.com/resources/glossary/dashboard.md): 토스페이먼츠 상점관리자는 내 상점의 정산, 결제 조회, 계약 정보, 부가세신고 자료를 모두 관리할 수 있는 서비스입니다.
- [딥링크(Deep Link)](https://docs.tosspayments.com/resources/glossary/deep-link.md): 웹링크가 사용자를 특정 웹사이트로 이동시키듯이, 딥링크는 사용자를 특정 앱으로 이동시켜서 원하는 화면을 보여주거나, 사용자 액션을 유도해요.
- [개발자센터](https://docs.tosspayments.com/resources/glossary/dev-center.md): 토스페이먼츠 개발자센터는 결제 연동에 필요한 모든 것을 제공합니다. 토스페이먼츠 결제 연동 문서, API 키, 테스트 내역, 웹훅 등록 등 개발에 필요한 정보와 기능을 확인할 수 있어요.
- [DNS(Domain Name System)](https://docs.tosspayments.com/resources/glossary/dns.md): DNS(Domain Name System)는 웹사이트의 도메인 주소를 IP 주소로 변환하는 시스템입니다.
- [문서 객체 모델(DOM)](https://docs.tosspayments.com/resources/glossary/dom.md): 문서 객체 모델(DOM)은 HTML 문서 구조를 객체로 표현한 것이에요. HTML 문서에는 `<body>` 등 여러 태그가 있어요. HTML 요소의 계층을 반영해서 만든 객체가 DOM이에요.
- [면세](https://docs.tosspayments.com/resources/glossary/duty-free.md): 면세는 제품 또는 서비스를 소비자에게 제공할 때 부가가치세가 제외된다는 뜻이에요. 제품이나 서비스에 얻는 부가가치에는 일반적으로 세금이 붙지만 면세 제품에는 부가가치에 대한 세금이 안 붙어요.
- [종단간 암호화(E2EE)](https://docs.tosspayments.com/resources/glossary/e2ee.md): 종단간 암호화는 클라이언트에서 메시지를 전송하는 단계부터 최종적으로 서버에 전달되는 단계까지 메시지를 평문으로 저장하지 않고 모두 암호화하는 안전한 통신 방법입니다.
- [간편결제](https://docs.tosspayments.com/resources/glossary/easypay.md): 카드나 계좌를 등록해놓고 복잡한 인증 절자 없이 간단하게 결제할 수 있는 서비스를 의미해요. 간편결제를 사용하면 비밀번호나 지문만으로 쉽고 간단하게 결제할 수 있죠.
- [에스크로](https://docs.tosspayments.com/resources/glossary/escrow.md): 에스크로(Escrow)는 고객이 온라인으로 제품을 구매할 때 지불한 돈을 PG사와 같은 제3자가 보관했다가, 고객이 제품을 정상적으로 받으면 상점에게 돈을 지급해 주는 시스템입니다.
- [FDS(이상거래탐지시스템)](https://docs.tosspayments.com/resources/glossary/fds.md): FDS는 'Fraud Detection System'의 준말로 거래 내역, 고객 정보, 평소 거래 패턴 등을 분석해서 의심되는 이상 거래를 탐지하고 차단하는 기술입니다.
- [방화벽(Firewall)](https://docs.tosspayments.com/resources/glossary/firewall.md): 방화벽은 특정 유형의 네트워크 트래픽을 차단하는 장치를 말해요. 신뢰할 수 있는 네트워크 트래픽은 허용하고, 신뢰할 수 없는 악성 트래픽을 막는 것이죠.
- [상품권 결제](https://docs.tosspayments.com/resources/glossary/gift-certificate.md): 상품권은 현금 가치를 가지며 상품이나 지정된 물품으로 교환할 수 있어요. 문화상품권, 도서문화상품권, 게임문화상품권, 백화점상품권이 있죠.
- [해싱(Hashing)](https://docs.tosspayments.com/resources/glossary/hashing.md): 해싱(Hashing)은 입력값에 수학적 알고리즘(해시 함수)을 적용하여 고정된 크기의 문자열을 출력하는 과정입니다.
- [호스팅사 기반 쇼핑몰](https://docs.tosspayments.com/resources/glossary/hosting-mall.md): 호스팅사 기반 쇼핑몰이란 인터넷 호스팅 업체에서 제공하는 쇼핑몰 솔루션을 사용하여 쇼핑몰을 운영하는 것을 말합니다. 자사몰을 직접 개발하는 방식과 반대입니다.
- [HTTP 헤더](https://docs.tosspayments.com/resources/glossary/http-header.md): HTTP 헤더는 클라이언트와 서버 간의 HTTP 요청과 응답에서 추가 정보를 전달하는 데 사용돼요. 요청의 유형, 응답의 유형, 인증 정보 등 다양한 메타데이터가 포함되죠.
- [HTTP 프로토콜](https://docs.tosspayments.com/resources/glossary/http-protocol.md): HTTP(Hypertext Transfer Protocol)는 웹에서 데이터를 주고받는 서버-클라이언트 모델의 프로토콜입니다. 쉽게 설명하자면 웹 브라우저가 서버와 통신하는 규칙이에요.
- [용어사전](https://docs.tosspayments.com/resources/glossary/index.md): 결제 및 결제 기술과 관련된 용어와 개념을 익혀보세요.
- [해외 역직구](https://docs.tosspayments.com/resources/glossary/intl-commerce.md): 해외 역직구는 해외 소비자가 국내 사이트에서 상품을 '직접 구매'한다는 뜻이에요. 우리나라 기업들이 해외 고객들에게 물건을 직접 판매하는 것이죠.
- [IP 주소](https://docs.tosspayments.com/resources/glossary/ip.md): IP(Internet Protocol) 주소란 인터넷에 연결된 기기를 식별하는 값을 뜻해요. 우편번호가 특정 장소를 가리키듯이, IP 주소는 인터넷에 연결된 특정 기기를 가리켜요.
- [JWT(JSON 웹 토큰)](https://docs.tosspayments.com/resources/glossary/jwt.md): JSON 웹 토큰(JWT)은 온라인 네트워크에서 정보를 안전하게 통신할 때 사용하는 인터넷 표준 토큰입니다.
- [키인 결제](https://docs.tosspayments.com/resources/glossary/keyin.md): 키인 결제는 카드 정보만 입력해서 완료되는 비인증 결제입니다. 필요한 카드 정보는 키인 결제 유형에 따라 다른데요. 키인 결제에는 안심결제와 카드번호 결제 두 개의 유형이 있어요.
- [KYC(고객확인)](https://docs.tosspayments.com/resources/glossary/kyc.md): KYC(Know Your Customer, 고객확인제도)는 금융회사가 거래하는 고객의 신원을 확인하고 고객의 자금이 합법적인 경로로 취득된 것인지 확인하는 제도입니다.
- [수동 정산](https://docs.tosspayments.com/resources/glossary/manual-settlement.md): 수동 정산은 일정 기간 쌓인 카드 결제 기록 중에 한 건만 선택해서 정산 받는 방법입니다.
- [소상공인](https://docs.tosspayments.com/resources/glossary/micro-business.md): 소상공인은 소기업이면서 상시 근로자가 10명 미만인 사업자를 뜻해요. 연평균 매출액(소기업 기준)과 월평균 상시 근로자 수를 기준으로 나라는 소상공인을 정의하고 있어요.
- [휴대폰 결제](https://docs.tosspayments.com/resources/glossary/mobile-payment.md): 휴대폰 결제는 간단한 본인인증을 한 뒤 휴대폰 통신사의 소액결제 한도를 이용하는 결제 방식입니다. 휴대폰으로 결제한 금액은 다음 달 휴대폰 요금에 추가돼요.
- [복합결제](https://docs.tosspayments.com/resources/glossary/multi-pay.md): 복합결제는 두 가지 이상의 결제 방식을 사용해서 결제하는 것을 뜻해요. 예를 들어, 토스페이에 등록된 카드와 토스 포인트를 한 결제에 사용하는 것입니다.
- [비인증 결제](https://docs.tosspayments.com/resources/glossary/non-auth-payment.md): 비인증 결제는 고객의 본인인증 없이 결제를 요청하는 방식을 뜻합니다. 비인증 결제는 결제수단의 소유자를 인증하지 않고 결제 정보만으로 결제를 요청합니다.
- [일반결제](https://docs.tosspayments.com/resources/glossary/normal-payment.md): 일반결제는 고객이 본인인증을 마치고 결제하는 방식이에요. 일반결제는 신용·체크카드, 계좌이체, 가상계좌, 휴대폰 결제를 지원해요. 휴대폰 인증, ARS 인증, 공인인증서로 본인인증을 할 수 있어요.
- [OAS(Open API Specification)](https://docs.tosspayments.com/resources/glossary/oas.md): OAS는 개발자가 RESTful API를 쉽게 관리하고 사용할 수 있게 도와주는 표준 명세 작성 방식이에요. OAS는 공개 API든 비공개 API든 그 구조와 작동 방식을 명확하게 문서화할 때 사용할 수 있어요.
- [OAuth 프레임워크](https://docs.tosspayments.com/resources/glossary/oauth.md): OAuth는 제3자의 클라이언트에게 보호된 리소스를 제한적으로 접근하게 해주는 프레임워크에요.
- [통신판매업](https://docs.tosspayments.com/resources/glossary/online-business.md): null
- [오픈뱅킹](https://docs.tosspayments.com/resources/glossary/open-banking.md): 오픈뱅킹은 금융기관에서 고객의 은행 데이터를 공유하는 서비스예요. 오픈뱅킹을 사용하는 금융기관은 고객이 다른 금융기관에 등록한 계좌 정보를 조회하고 이체를 대신할 수 있어요.
- [패킷(Packet)](https://docs.tosspayments.com/resources/glossary/packet.md): 패킷(Packet)이란 네트워크에서 데이터를 주고받을 때 사용되는 데이터 조각을 뜻해요.
- [결제창](https://docs.tosspayments.com/resources/glossary/payment-window.md): 결제창은 고객이 결제수단을 선택, 인증하고 결제를 완료하는 화면이에요. 결제창은 결제 경험의 중심에 있어요.
- [지급대행](https://docs.tosspayments.com/resources/glossary/payout.md): 지급대행은 오픈마켓에서 발생한 매출을 토스페이먼츠가 오픈마켓을 대신하여 셀러(입점 판매자)에게 지급하는 서비스입니다.
- [PG(Payment Gateway)](https://docs.tosspayments.com/resources/glossary/pg.md): PG는 '결제를 위한 관문'이란 뜻이에요. PG사는 사업자에 카드, 간편결제, 계좌이체, 가상계좌(무통장입금), 휴대폰결제 등 다양한 결제 방식을 연결해주는 역할을 해요.
- [프로미스(Promise)](https://docs.tosspayments.com/resources/glossary/promise.md): 프로미스는 비동기 함수가 반환하는 객체에요. 함수의 성공 또는 실패 상태를 알려줘요.
- [쿼리 파라미터(Query Parameter)](https://docs.tosspayments.com/resources/glossary/query-param.md): 쿼리(Query)는 문의 또는 물음표라는 뜻을 가지고 있는데요. 쿼리 파라미터(Query Parameter)는 URL 뒤에 붙는 키-값(Key-Value) 쌍입니다.
- [리다이렉트(Redirect)](https://docs.tosspayments.com/resources/glossary/redirect.md): 리다이렉트(Redirect)는 사용자가 처음 요청한 URL이 아닌, 다른 URL로 보내는 것을 뜻해요. 예를 들어, 웹사이트 A의 주소로 접속한 사용자를 웹사이트 B로 이동시키는 것이죠.
- [매출](https://docs.tosspayments.com/resources/glossary/revenue.md): 매출은 한자어로 팔 매(賣)에 나갈 출(出)로 만들어진 용어이며, 물건을 내다 파는 것을 의미해요.
- [매출전표](https://docs.tosspayments.com/resources/glossary/sales-statement.md): 매출전표는 신용・체크 카드 결제에 발급되는 영수증이에요. 매출전표에는 사용금액, 사업자등록번호, 일련번호, 승인번호를 비롯해 모든 카드 거래 정보가 제공돼요.
- [샌드박스](https://docs.tosspayments.com/resources/glossary/sandbox.md): 샌드박스란 소프트웨어 개발에서 사용되는 용어로, 격리된 환경을 의미해요. 이렇게 격리된 샌드박스 환경은 시스템의 운영 체제, 설치 프로그램 등 외부 영향을 주거나 받지 않아요.
- [SDK(Software Development Kit)](https://docs.tosspayments.com/resources/glossary/sdk.md): SDK(Software Development Kit)는 애플리케이션 개발 도구에요. SDK는 다른 회사에서 구현한 기능을 바로 내 애플리케이션에 사용할 수 있게 해줘요.
- [정산](https://docs.tosspayments.com/resources/glossary/settlement.md): 정산은 상품이나 서비스를 판매한 거래 대금 중에서 상점이 받을 수 있는 실제 금액을 계산하는 과정을 말합니다.
- [영중소 수수료](https://docs.tosspayments.com/resources/glossary/smm.md): 영중소 수수료란 영세・중소 사업자에 적용되는 신용카드, 체크카드 우대 수수료율을 뜻합니다.
- [SPA(Single Page Application)](https://docs.tosspayments.com/resources/glossary/spa.md): SPA(Single Page Application)란 단일 페이지 모던 웹 애플리케이션을 뜻합니다.
- [TCP(Transmission Control Protocol)](https://docs.tosspayments.com/resources/glossary/tcp.md): TCP(Transmission Control Protocol)는 애플리케이션 사이에서 안전하게 데이터를 통신하는 규약이에요.
- [타임아웃(Timeout)](https://docs.tosspayments.com/resources/glossary/timeout.md): 네트워크 타임아웃(Timeout)은 서버로 요청을 보냈지만 일정 시간 동안 답변을 받지 못하면 발생해요.
- [TLS(Transport Layer Security)](https://docs.tosspayments.com/resources/glossary/tls.md): TLS(Transport Layer Security)는 온라인 네트워크에서 데이터를 안전하게 주고받기 위한 암호화 프로토콜이에요.
- [거래](https://docs.tosspayments.com/resources/glossary/transaction.md): 결제 한 건에서 발생할 수 있는 결제 승인, 결제 취소, 부분 취소 각각을 '거래'라고 부릅니다.
- [계좌이체](https://docs.tosspayments.com/resources/glossary/transfer-payment.md): 계좌이체 결제는 고객의 은행 계좌에서 실시간 출금이체를 통해 결제하는 방식입니다. 그래서 '실시간 계좌이체'라고 불리기도 하고요.
- [UUID(Universally Unique Identifier)](https://docs.tosspayments.com/resources/glossary/uuid.md): UUID는 128-bit의 고유 식별자에요. UUID는 중앙 시스템에 등록하고 발급하는 과정이 없어서 상대적으로 빠르고 간단하게 만들 수 있어요.
- [VAN(Value Added Network)](https://docs.tosspayments.com/resources/glossary/van.md): VAN은 'Value Added Network'의 준말로 '부가가치 통신망'을 의미합니다. 쉽게 이해하자면 카드 결제를 받는 상점과 카드사 사이에 거래 정보를 주고받는 통신망이 VAN입니다.
- [부가가치세](https://docs.tosspayments.com/resources/glossary/vat.md): 부가가치세는 물건이나 서비스를 판매하면서 얻은 이윤에 대한 세금을 뜻해요.
- [가상계좌(무통장입금)](https://docs.tosspayments.com/resources/glossary/virtual-account.md): 가상계좌란 온라인 결제를 위해 발급되는 임시 계좌입니다. 고객이 가상계좌 결제를 요청하면 구매자명으로 임시 계좌가 발급됩니다. 고객은 해당 계좌에 주문 금액을 정확히 입금하면 결제가 완료돼요.
- [VPN(Virtual Private Network)](https://docs.tosspayments.com/resources/glossary/vpn.md): VPN이란 네트워크 망을 암호화해서 다른 지역에서도 인터넷으로 사설망을 안전하게 접근할 수 있게 하는 기술입니다
- [웹훅(Webhook)](https://docs.tosspayments.com/resources/glossary/webhook.md): 웹훅이란 데이터가 변경되었을 때 실시간으로 알림을 받을 수 있는 기능입니다. 웹 서비스의 이벤트 데이터를 전달하는 HTTP 기반 콜백 함수입니다.
- [웹뷰(WebView)](https://docs.tosspayments.com/resources/glossary/webview.md): 웹뷰(WebView)는 네이티브 앱에 내재되어 있는 웹 브라우저입니다. 웹뷰를 사용하면 웹 콘텐츠를 네이티브 앱 뷰와 같이 사용자에게 보여줄 수 있어요.
- [릴리즈 노트](https://docs.tosspayments.com/resources/release-note.md): 토스페이먼츠 제품의 새로운 기능과 변경 사항을 알려드려요.
- [브랜드페이 JavaScript SDK(Version 1)](https://docs.tosspayments.com/sdk/brandpay-js.md): 브랜드페이 JavaScript SDK를 추가하고 메서드를 사용하는 방법을 알아봅니다.
- [SDK 에러 코드(Version 1)](https://docs.tosspayments.com/sdk/error-codes.md): 토스페이먼츠 결제위젯, 브랜드페이, 결제창 SDK를 사용할 때 발생할 수 있는 에러를 살펴보세요.
- [결제창 JavaScript SDK(Version 1)](https://docs.tosspayments.com/sdk/payment-js.md): 토스페이먼츠 결제창 JavaScript SDK 사용에 필요한 준비와 메서드 사용법, 결제 실패 및 에러 처리 방법을 알아봅니다.
- [토스페이먼츠 SDK 에러 코드](https://docs.tosspayments.com/sdk/v2/error-codes.md): 토스페이먼츠 SDK를 사용할 때 발생할 수 있는 모든 에러를 살펴보세요.
- [토스페이먼츠 JavaScript SDK](https://docs.tosspayments.com/sdk/v2/js.md): 토스페이먼츠 JavaScript SDK를 추가하고 메서드를 사용하는 방법을 알아봅니다.
- [결제위젯 Android SDK(Version 1)](https://docs.tosspayments.com/sdk/widget-android.md): 결제위젯 Android SDK를 추가하고 메서드를 사용하는 방법을 알아봅니다.
- [결제위젯 Flutter SDK(Version 1)](https://docs.tosspayments.com/sdk/widget-flutter.md): 결제위젯 Flutter SDK를 추가하고 메서드를 사용하는 방법을 알아봅니다.
- [결제위젯 iOS SDK(Version 1)](https://docs.tosspayments.com/sdk/widget-ios.md): 결제위젯 iOS SDK를 추가하고 메서드를 사용하는 방법을 알아봅니다.
- [결제위젯 JavaScript SDK(Version 1)](https://docs.tosspayments.com/sdk/widget-js.md): 결제위젯 JavaScript SDK를 추가하고 메서드를 사용하는 방법을 알아봅니다.
- [결제위젯 React Native SDK(Version 1)](https://docs.tosspayments.com/sdk/widget-rn.md): 결제위젯 React Native SDK를 추가하고 메서드를 사용하는 방법을 알아봅니다.
- [코어 API](https://docs.tosspayments.com/reference.md): 토스페이먼츠 API 엔드포인트(Endpoint)와 객체 정보, 파라미터, 요청 및 응답 예제를 살펴보세요.
- [브랜드페이 자동결제 API](https://docs.tosspayments.com/reference/brandpay-billing.md): 브랜드페이에서 제공하는 자동결제 API 엔드포인트(Endpoint)와 객체 정보, 파라미터, 요청 및 응답 예제를 살펴보세요.
- [브랜드페이 API](https://docs.tosspayments.com/reference/brandpay.md): 브랜드페이에서 제공하는 API 엔드포인트(Endpoint)와 객체 정보, 파라미터, 요청 및 응답 예제를 살펴보세요.
- [API 에러 코드](https://docs.tosspayments.com/reference/error-codes.md): 토스페이먼츠 API 사용할 때 발생할 수 있는 에러를 살펴보세요.