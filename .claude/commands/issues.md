# GitHub Issues Generator

Create concise GitHub issues in Korean from requirements: $ARGUMENTS

## Repository
https://github.com/calmcomputerclub/shallwe



## Requirements Analysis Framework

### Input Processing
- **Requirement Text**: Analyze user's Korean/English requirements from ARGUMENTS
- **Context Detection**: Understand if it's frontend, backend, feature, bug, or improvement
- **Korean Output**: Always generate issues in Korean language

### Issue Structure Generation
- **Title**: Clear, actionable Korean title under 80 characters
- **Body**: Concise Korean requirement list with checkboxes
- **Priority**: Determine appropriate priority (High/Medium/Low)
- **Labels**: Auto-suggest relevant labels (frontend, backend, bug, feature, etc.)

## Issue Template

### Simple Issue Format
```
## 요구사항

[Brief description of what needs to be done]

### 할 일
- [ ] [Specific task 1]
- [ ] [Specific task 2]
- [ ] [Specific task 3]

### 계획
- [Implementation approach or strategy]

### 참고사항
- [Any important notes or context]
```


## Processing Rules

### Label Suggestions
- **frontend**: UI/UX, React components, styling
- **backend**: API, database, server logic
- **bug**: Fixing existing functionality
- **feature**: New functionality
- **improvement**: Enhancing existing features
- **epic**: Large feature spanning multiple issues

### Priority Guidelines
- **High**: Critical bugs, blocking features, user-facing issues
- **Medium**: Important improvements, nice-to-have features
- **Low**: Technical debt, minor enhancements, future considerations

## Output Format

Generate GitHub issue using `gh issue create` command with:
1. Concise Korean title
2. Simple checklist format
3. No excessive code examples
4. Clear action items
5. Relevant file paths
6. Appropriate labels and project assignment

Always include:
- `--project "MVP"` for project assignment (must use exact project name)
- Appropriate labels based on content analysis
- Korean language for title and body

Example command:
```bash
gh issue create --title "검색 기능 구현" --body "..." --label "frontend,feature" --project "MVP"
```

Note: Use the exact project name 'MVP' for proper project linking.