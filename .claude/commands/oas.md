# OpenAPI 3.0 Specification Extractor

Analyze route handler or page.tsx and generate OAS3 YAML: $ARGUMENTS

## Code Analysis Framework

### Route Handler Detection

- **Next.js App Router Analysis**: Extract route.ts/js handlers (GET, POST, PUT, DELETE, PATCH)
- **Next.js Pages Router Analysis**: Extract API routes from pages/api directory structure
- **Page Component Analysis**: Extract client-side API calls and data fetching patterns
- **Dynamic Route Analysis**: Identify [slug], [...slug], and [[...slug]] parameter patterns

### HTTP Method Extraction

- **Export Function Analysis**: Detect exported GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS functions
- **Request Handler Patterns**: Identify NextRequest/NextResponse usage patterns
- **Method Routing Logic**: Extract conditional method handling within single handlers
- **Middleware Integration**: Analyze middleware functions and authentication layers

### Parameter Analysis

- **Path Parameters**: Extract dynamic route segments and parameter validation
- **Query Parameters**: Analyze searchParams usage and URL parameter handling
- **Request Body Analysis**: Detect JSON, FormData, and multipart request body parsing
- **Header Parameter Extraction**: Identify required and optional header parameters

### Response Schema Extraction

- **Response Type Analysis**: Extract TypeScript interfaces and return type annotations
- **Status Code Mapping**: Identify HTTP status codes and their corresponding responses
- **Error Response Patterns**: Analyze error handling and exception response structures
- **Content Type Detection**: Determine response format (JSON, XML, text, binary)

## OpenAPI 3.0 Specification Generation

### Basic OpenAPI Structure

```yaml
openapi: 3.0.3
info:
  title: [Generated from file analysis]
  description: [Extracted from comments and JSDoc]
  version: [Derived from package.json or git tags]
  contact:
    name: [From package.json author]
    email: [From package.json author]
  license:
    name: [From package.json license]

servers:
  - url: [Environment-based URL detection]
    description: [Environment description]


```

## YAML File Generation Strategy

### Multi-File Output Structure

```
api-docs/
├── openapi.yaml           # Main OpenAPI specification file
├── components/
│   ├── schemas/          # Reusable schema definitions
│   ├── responses/        # Common response definitions
│   ├── parameters/       # Reusable parameter definitions
│   └── examples/         # Example data sets
├── paths/
│   ├── users.yaml       # User-related endpoints
│   ├── auth.yaml        # Authentication endpoints
│   └── [feature].yaml   # Feature-specific groupings
└── tags/
    └── descriptions.yaml # Tag descriptions and metadata

```

### Schema Component Extraction

- **Reusable Schema Identification**: Extract common data structures for component reuse
- **Reference Resolution**: Replace inline schemas with $ref to component definitions
- **Circular Reference Handling**: Manage recursive schema relationships
- **Schema Inheritance**: Handle TypeScript extends and implements patterns

### Example Data Generation

- **Realistic Example Creation**: Generate meaningful example data based on schema constraints
- **Test Data Integration**: Extract examples from existing test fixtures and mocks
- **Faker Integration**: Generate dynamic examples using faker.js patterns
- **Business Domain Examples**: Create contextually relevant example data