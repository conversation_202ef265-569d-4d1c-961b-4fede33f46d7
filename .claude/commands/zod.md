# Zod Schema Extractor and Generator

Extract Zod schemas from code and save to specified path: $ARGUMENTS

## Analysis and Generation Framework

### Code Analysis
- **Source Code**: Analyze ARGUMENTS[0] for TypeScript interfaces, API routes, form components, and validation patterns
- **Developer Intent Detection**: Identify validation requirements from existing code patterns, error handling, and business logic
- **Schema Requirements**: Extract field types, validation rules, constraints, and relationships

### Path Management
- **Output Path**: Save to ARGUMENTS[1] if provided, otherwise default to `schemas/` directory
- **File Organization**: Group related schemas and types together for maximum readability
- **Naming Convention**: Use descriptive names based on source file and domain context

### Zod Schema Generation
Create comprehensive Zod schemas with:
- **Type-safe validation**: Convert TypeScript types to runtime Zod schemas
- **Business rule enforcement**: Extract and encode business logic as validation rules
- **Error messaging**: Generate clear, actionable error messages
- **Schema composition**: Use Zod's composition features for complex validation logic

### Output Structure
Generate organized schema files with:
```typescript
// ============================================================================
// [DOMAIN] VALIDATION SCHEMAS
// Generated from: [source_file_path]
// ============================================================================

import { z } from 'zod';

// [Schema Name] Schema
export const [SchemaName]Schema = z.object({
  // Field definitions with validation rules
});

export type [SchemaName] = z.infer<typeof [SchemaName]Schema>;

// Related schemas grouped together
// Partial schemas for form steps or API subsets
// Utility schemas for common patterns