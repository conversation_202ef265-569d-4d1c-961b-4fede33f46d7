# API Endpoint Generator

Create a new REST API endpoint from file analysis: $ARGUMENTS

Process:
1. Analyze the provided file path and extract data structures
2. Design necessary data models based on file content
3. Design REST API endpoint structure
4. Generate Next.js route handler in src/app/api directory
5. Connect to the specified file path
6. Implement client-side fetch functions

Generate:
1. Next.js route handler function (src/app/api/*)
2. Zod validation schemas in schemas/ directory
3. Request/response TypeScript models based on schemas
4. Related client-side fetch function in src/lib/api directory
5. API documentation
6. Connect to the specified file path for data operations. Prefer server components for data fetching.
7. Ensure all code follows Next.js conventions and best practices

Follow these patterns:
- Use Next.js App Router conventions
- Implement proper error handling with HTTP status codes
- Add request/response logging
- Use TypeScript for type safety
- Connect to provided file path for data operations
- Compare Server Components vs Client-side data fetching strategies:
  
  **Server Components (Preferred for):**
  - Initial page loads and static data
  - SEO-critical content
  - Large data sets that don't need real-time updates
  - Authentication-sensitive data
  - Better performance for initial render
  
  **Client-side Fetching (Preferred for):**
  - Real-time updates and interactive features
  - User-specific actions (CRUD operations)
  - Data that changes frequently
  - Progressive enhancement and loading states
  - Optimistic updates
  
  Choose the most appropriate strategy based on data characteristics and user experience requirements.