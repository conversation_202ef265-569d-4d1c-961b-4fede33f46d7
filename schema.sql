-- ====================================
-- 클래스 라이프사이클 관리 시스템 스키마
-- ====================================
-- 기존 테이블들을 DROP하고 새로운 구조로 재생성
-- 클래스 템플릿, 스케줄 그룹, 스케줄, Occurrence, 출석 관리 포함

-- ====================================
-- 기존 테이블 DROP (의존성 순서 고려)
-- ====================================

-- 관계형 테이블 먼저 DROP
DROP TABLE IF EXISTS class_attendances CASCADE;
DROP TABLE IF EXISTS class_occurrences CASCADE;
DROP TABLE IF EXISTS class_enrollments CASCADE;
DROP TABLE IF EXISTS class_reviews CASCADE;
DROP TABLE IF EXISTS class_schedules CASCADE;
DROP TABLE IF EXISTS class_schedule_groups CASCADE;
DROP TABLE IF EXISTS class_templates CASCADE;
DROP TABLE IF EXISTS instructor_accounts CASCADE;
DROP TABLE IF EXISTS instructor_certificates CASCADE;
DROP TABLE IF EXISTS instructor_specialties CASCADE;
DROP TABLE IF EXISTS instructors CASCADE;
DROP TABLE IF EXISTS studios CASCADE;
DROP TABLE IF EXISTS members CASCADE;

-- ENUM 타입 DROP
DROP TYPE IF EXISTS member_status CASCADE;
DROP TYPE IF EXISTS member_role CASCADE;
DROP TYPE IF EXISTS gender CASCADE;
DROP TYPE IF EXISTS specialty CASCADE;

-- ====================================
-- 코드 레벨 enum만 사용 (DB Level enum 제거)
-- ====================================
-- 모든 상태값은 TEXT 타입으로 저장하고 코드 레벨에서 검증

-- ====================================
-- 회원 관련 테이블
-- ====================================

/**
 * 회원 정보 테이블
 * - Supabase auth.users와 1:1 관계
 * - 모든 회원 유형(학생, 강사, 관리자)의 기본 정보
 */
CREATE TABLE members (
  id UUID PRIMARY KEY,  -- auth.users.id와 동일한 UUID
  nickname TEXT,
  name TEXT,           -- 온보딩 1단계: 실명
  phone TEXT,          -- 온보딩 1단계: 전화번호
  gender TEXT,         -- 온보딩 1단계: 성별 ('MALE', 'FEMALE', 'OTHER')
  birth_date DATE,
  role TEXT NOT NULL DEFAULT 'STUDENT', -- 'STUDENT', 'INSTRUCTOR', 'ADMIN'
  status TEXT DEFAULT 'ACTIVE', -- 'ACTIVE', 'INACTIVE', 'SUSPENDED'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건 (코드 레벨 enum 검증)
  CONSTRAINT check_gender CHECK (gender IN ('MALE', 'FEMALE', 'OTHER')),
  CONSTRAINT check_role CHECK (role IN ('STUDENT', 'INSTRUCTOR', 'ADMIN')),
  CONSTRAINT check_status CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED'))
);

/**
 * 강사 정보 테이블
 * - members 테이블과 1:1 관계
 * - 강사 전용 정보 및 온보딩 3단계 정보
 */
CREATE TABLE instructors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL UNIQUE,  -- members.id 참조
  short_bio TEXT,     -- 온보딩 3단계: 한줄 소개
  detailed_bio TEXT,  -- 온보딩 3단계: 상세 설명
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

/**
 * 강사 전문분야 테이블
 * - 강사의 운동 분야와 경력 관리
 * - 온보딩 2단계 정보
 */
CREATE TABLE instructor_specialties (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  instructor_id UUID NOT NULL,  -- instructors.id 참조
  specialty TEXT NOT NULL, -- 'YOGA', 'PILATES', 'FITNESS' 등
  experience_years INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT unique_instructor_specialty UNIQUE (instructor_id, specialty),
  CONSTRAINT check_specialty CHECK (specialty IN (
    'YOGA', 'PILATES', 'FITNESS', 'CROSSFIT', 'SWIMMING', 
    'BOXING', 'DANCE', 'RUNNING', 'CLIMBING', 'MARTIAL_ARTS',
    'MEDITATION', 'STRETCHING', 'BARRE', 'SPINNING', 'ZUMBA', 
    'KICKBOXING', 'THERAPEUTIC'
  )),
  CONSTRAINT check_experience_years CHECK (experience_years >= 0)
);

/**
 * 강사 자격증 테이블
 * - 강사 보유 자격증 정보
 * - 온보딩 4단계 정보
 */
CREATE TABLE instructor_certificates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  instructor_id UUID NOT NULL,  -- instructors.id 참조
  certificate_name TEXT NOT NULL,
  issuing_organization TEXT NOT NULL,
  issue_date DATE NOT NULL,
  expiry_date DATE,              -- nullable
  certificate_number TEXT,       -- nullable
  image_url TEXT,                -- nullable
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT unique_instructor_certificate UNIQUE (instructor_id, certificate_name, issuing_organization)
);

/**
 * 강사 정산 계좌 테이블
 * - 수익 정산용 계좌 정보
 * - 온보딩 5단계 정보 (마지막 단계)
 */
CREATE TABLE instructor_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  instructor_id UUID NOT NULL UNIQUE,  -- instructors.id 참조
  bank_name TEXT NOT NULL,
  account_number TEXT NOT NULL,
  account_holder TEXT NOT NULL,
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ====================================
-- 클래스 도메인 테이블
-- ====================================

/**
 * 스튜디오 정보 테이블
 * - 피트니스 센터, 요가 스튜디오 등 운동 공간 정보
 */
CREATE TABLE studios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  address TEXT NOT NULL,
  studio_type TEXT NOT NULL,  -- 'fitness', 'yoga', 'pilates', etc.
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  nearest_station TEXT,       -- 근처 지하철역
  amenities JSONB,            -- 편의시설 목록
  links JSONB,                -- 관련 링크
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

/**
 * 클래스 템플릿 테이블 (새 설계)
 * - 클래스 기본 정보와 라이프사이클 관리
 * - 모집 기간, 수업 기간, 상태 관리 포함
 */
CREATE TABLE class_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  studio_id UUID NOT NULL,     -- studios.id 참조
  instructor_id UUID NOT NULL, -- instructors.id 참조
  
  -- 기본 정보
  title TEXT NOT NULL,
  description TEXT,
  curriculum JSONB,            -- 커리큘럼 정보
  category TEXT NOT NULL,      -- 'fitness', 'yoga', 'pilates', etc.
  specialty TEXT NOT NULL,     -- specialty 문자열
  level TEXT NOT NULL,         -- 'beginner', 'intermediate', 'advanced', 'all_levels'
  
  -- 수업 설정
  duration_minutes INTEGER NOT NULL,
  price_per_session DECIMAL(10, 2) NOT NULL,
  max_capacity INTEGER NOT NULL,
  
  -- 🆕 라이프사이클 관리 (핵심 추가사항)
  recruitment_start_date DATE,   -- 모집 시작일 (선택사항)
  recruitment_end_date DATE,     -- 모집 종료일 (선택사항)
  class_start_date DATE,         -- 수업 시작일 (선택사항)
  class_end_date DATE,           -- 수업 종료일 (선택사항)
  status TEXT NOT NULL DEFAULT 'upcoming', -- 'upcoming', 'recruiting', 'ongoing', 'completed', 'cancelled'
  
  -- 메타데이터
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_template_dates CHECK (recruitment_start_date <= recruitment_end_date),
  CONSTRAINT check_class_dates CHECK (class_start_date <= class_end_date),
  CONSTRAINT check_recruitment_before_class CHECK (recruitment_end_date <= class_start_date),
  CONSTRAINT check_status CHECK (status IN ('upcoming', 'recruiting', 'ongoing', 'completed', 'cancelled')),
  CONSTRAINT check_max_capacity CHECK (max_capacity > 0),
  CONSTRAINT check_price CHECK (price_per_session >= 0),
  CONSTRAINT check_duration CHECK (duration_minutes > 0)
);

/**
 * 클래스 스케줄 그룹 테이블 (새 설계)
 * - 하나의 클래스 템플릿 내 다른 시간대 그룹 관리
 * - "오후반", "저녁반" 등 시간대별 그룹
 */
CREATE TABLE class_schedule_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_template_id UUID NOT NULL,  -- class_templates.id 참조
  
  -- 그룹 정보
  group_name TEXT NOT NULL,          -- "오후반", "저녁반", "주말반"
  group_description TEXT,
  
  -- 그룹별 설정
  max_participants INTEGER NOT NULL, -- 그룹별 정원
  price_per_session DECIMAL(10, 2),  -- 그룹별 가격 (옵션)
  
  -- 메타데이터
  sessions_per_week INTEGER DEFAULT 0,  -- 계산값
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_max_participants CHECK (max_participants > 0),
  CONSTRAINT check_sessions_per_week CHECK (sessions_per_week >= 0)
);

/**
 * 클래스 스케줄 테이블 (새 설계)
 * - 검색 최적화와 반복 패턴 관리를 위한 정규화된 스케줄
 * - Schedule = 패턴 템플릿 (Occurrence 생성의 기준)
 */
CREATE TABLE class_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_group_id UUID NOT NULL,  -- class_schedule_groups.id 참조
  
  -- 🔍 검색 최적화 필드
  day_of_week TEXT NOT NULL,         -- 'MONDAY', 'TUESDAY', 'WEDNESDAY', etc.
  start_time TIME NOT NULL,          -- '14:00:00', '19:00:00'
  end_time TIME NOT NULL,            -- '15:00:00', '20:00:00'
  
  -- 기본 설정 (Occurrence에 상속됨)
  max_participants INTEGER,          -- 기본 정원 (그룹 설정 우선)
  
  -- 메타데이터
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_time_order CHECK (start_time < end_time),
  CONSTRAINT check_day_of_week CHECK (day_of_week IN (
    'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 
    'FRIDAY', 'SATURDAY', 'SUNDAY'
  )),
  CONSTRAINT unique_schedule_per_group UNIQUE (schedule_group_id, day_of_week, start_time)
);

/**
 * 클래스 Occurrence 테이블 (신규 - 핵심)
 * - 실제 수업 인스턴스 관리
 * - Schedule에서 생성되어 개별적으로 관리됨
 */
CREATE TABLE class_occurrences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- 📋 참조 관계
  class_template_id UUID NOT NULL,   -- 어떤 클래스인지
  schedule_group_id UUID NOT NULL,   -- 어떤 시간대 그룹인지
  class_schedule_id UUID NOT NULL,   -- 어떤 패턴에서 생성되었는지
  
  -- 📅 실제 수업 정보
  occurrence_date DATE NOT NULL,     -- '2024-12-02'
  start_time TIME NOT NULL,          -- 개별 조정 가능
  end_time TIME NOT NULL,            -- 개별 조정 가능
  max_participants INTEGER NOT NULL, -- 개별 조정 가능
  
  -- 📊 상태 관리
  status TEXT NOT NULL DEFAULT 'scheduled', -- 'scheduled', 'ongoing', 'completed', 'cancelled'
  attendance_count INTEGER DEFAULT 0,       -- 실제 출석자 수
  confirmed_enrollments INTEGER DEFAULT 0,  -- 확정 신청자 수
  
  -- 📝 운영 정보
  instructor_notes TEXT,             -- 강사 메모
  cancellation_reason TEXT,          -- 취소 사유
  
  -- 🔄 보강 수업 관리
  is_substitute_class BOOLEAN DEFAULT false,
  original_occurrence_id UUID,       -- 원본 수업 참조
  
  -- ⏰ 예약 정책 (추후 확장)
  booking_opens_at TIMESTAMP WITH TIME ZONE,
  booking_closes_at TIMESTAMP WITH TIME ZONE,
  cancellation_deadline TIMESTAMP WITH TIME ZONE,
  
  -- 메타데이터
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_occurrence_time CHECK (start_time < end_time),
  CONSTRAINT check_max_participants_occ CHECK (max_participants > 0),
  CONSTRAINT check_attendance_count CHECK (attendance_count >= 0),
  CONSTRAINT check_confirmed_enrollments CHECK (confirmed_enrollments >= 0),
  CONSTRAINT check_attendance_vs_enrollments CHECK (attendance_count <= confirmed_enrollments),
  CONSTRAINT check_occurrence_status CHECK (status IN ('scheduled', 'ongoing', 'completed', 'cancelled')),
  CONSTRAINT check_substitute_original CHECK (
    (is_substitute_class = false AND original_occurrence_id IS NULL) OR
    (is_substitute_class = true AND original_occurrence_id IS NOT NULL)
  )
);

/**
 * 클래스 등록 테이블 (수정)
 * - 수강생의 클래스 신청 정보
 * - 클래스 전체 + 특정 시간대 그룹에 신청
 */
CREATE TABLE class_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- 👤 신청자 정보
  member_id UUID NOT NULL,           -- members.id 참조
  
  -- 📋 신청 대상 (클래스 전체 + 특정 시간대)
  class_template_id UUID NOT NULL,   -- 전체 클래스에 신청
  schedule_group_id UUID NOT NULL,   -- 특정 시간대 그룹 선택
  
  -- 📅 신청 정보
  enrollment_status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'confirmed', 'cancelled', 'completed', 'refunded'
  enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 💳 결제 정보
  payment_id TEXT,
  paid_amount DECIMAL(10, 2),
  
  -- 💰 환불 관리
  refund_amount DECIMAL(10, 2),
  refunded_at TIMESTAMP WITH TIME ZONE,
  refund_reason TEXT,
  
  -- 📝 기타
  notes TEXT,
  
  -- 메타데이터
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_enrollment_status CHECK (enrollment_status IN (
    'pending', 'confirmed', 'cancelled', 'completed', 'refunded'
  )),
  CONSTRAINT check_paid_amount CHECK (paid_amount >= 0),
  CONSTRAINT check_refund_amount CHECK (refund_amount >= 0),
  CONSTRAINT check_refund_vs_paid CHECK (refund_amount <= paid_amount),
  CONSTRAINT unique_member_template_group UNIQUE (member_id, class_template_id, schedule_group_id)
);

/**
 * 클래스 출석 테이블 (신규 - 핵심)
 * - 개별 수업에 대한 출석 기록
 */
CREATE TABLE class_attendances (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- 📋 참조 관계
  class_occurrence_id UUID NOT NULL, -- 특정 수업
  member_id UUID NOT NULL,           -- 수강생
  enrollment_id UUID,                -- 신청 기록 참조 (옵션)
  
  -- ✅ 출석 정보
  attendance_status TEXT NOT NULL,   -- 'present', 'absent', 'late', 'excused'
  checked_in_at TIMESTAMP WITH TIME ZONE,
  checked_out_at TIMESTAMP WITH TIME ZONE,
  
  -- 📝 기타
  notes TEXT,                        -- 지각 사유, 조퇴 사유 등
  
  -- 메타데이터
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_attendance_status CHECK (attendance_status IN (
    'present', 'absent', 'late', 'excused'
  )),
  CONSTRAINT check_check_times CHECK (
    checked_out_at IS NULL OR checked_in_at IS NULL OR checked_out_at >= checked_in_at
  ),
  CONSTRAINT unique_occurrence_member UNIQUE (class_occurrence_id, member_id)
);

/**
 * 클래스 후기 테이블
 * - 클래스 또는 강사에 대한 후기와 평점
 */
CREATE TABLE class_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL,           -- members.id 참조
  class_template_id UUID NOT NULL,   -- class_templates.id 참조
  instructor_id UUID NOT NULL,       -- instructors.id 참조
  rating INTEGER NOT NULL,           -- 1~5 별점
  comment TEXT,
  is_anonymous BOOLEAN DEFAULT false,
  is_verified BOOLEAN DEFAULT false,
  helpful_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 제약 조건
  CONSTRAINT check_rating CHECK (rating >= 1 AND rating <= 5),
  CONSTRAINT check_helpful_count CHECK (helpful_count >= 0)
);

-- ====================================
-- 인덱스 생성
-- ====================================

-- 클래스 검색 최적화 인덱스
CREATE INDEX idx_templates_search ON class_templates (category, level, status, recruitment_end_date);
CREATE INDEX idx_templates_lifecycle ON class_templates (status, recruitment_start_date, recruitment_end_date, class_start_date, class_end_date);

-- 스케줄 검색 최적화 인덱스
CREATE INDEX idx_schedules_search ON class_schedules (day_of_week, start_time, end_time);
CREATE INDEX idx_schedules_time_range ON class_schedules (start_time, end_time, day_of_week);

-- Occurrence 검색 최적화 인덱스
CREATE INDEX idx_occurrences_date_status ON class_occurrences (occurrence_date, status);
CREATE INDEX idx_occurrences_upcoming ON class_occurrences (occurrence_date, start_time) WHERE status IN ('scheduled', 'ongoing');

-- 조인 최적화 인덱스
CREATE INDEX idx_schedule_groups_template ON class_schedule_groups (class_template_id);
CREATE INDEX idx_schedules_group ON class_schedules (schedule_group_id);
CREATE INDEX idx_occurrences_template ON class_occurrences (class_template_id);
CREATE INDEX idx_occurrences_schedule ON class_occurrences (class_schedule_id);
CREATE INDEX idx_occurrences_group ON class_occurrences (schedule_group_id);

-- 신청 및 출석 최적화 인덱스
CREATE INDEX idx_enrollments_member ON class_enrollments (member_id, enrollment_status);
CREATE INDEX idx_enrollments_template_group ON class_enrollments (class_template_id, schedule_group_id, enrollment_status);
CREATE INDEX idx_attendances_occurrence ON class_attendances (class_occurrence_id);
CREATE INDEX idx_attendances_member ON class_attendances (member_id, attendance_status);

-- 성능 최적화 인덱스
CREATE INDEX idx_enrollments_stats ON class_enrollments (class_template_id, schedule_group_id, enrollment_status, enrolled_at);
CREATE INDEX idx_attendances_stats ON class_attendances (class_occurrence_id, attendance_status, created_at);

-- 시간 기반 쿼리 최적화
CREATE INDEX idx_occurrences_instructor_upcoming ON class_occurrences (class_template_id, occurrence_date, status) WHERE status IN ('scheduled', 'ongoing');
CREATE INDEX idx_templates_recruiting ON class_templates (recruitment_start_date, recruitment_end_date) WHERE status = 'recruiting';

-- 복합 조건 검색 최적화
CREATE INDEX idx_full_class_search ON class_templates (category, level, status, recruitment_start_date, recruitment_end_date);
CREATE INDEX idx_schedule_datetime ON class_schedules (day_of_week, start_time, end_time, schedule_group_id);
CREATE INDEX idx_occurrence_operations ON class_occurrences (class_template_id, status, occurrence_date, confirmed_enrollments);

-- ====================================
-- 뷰 정의
-- ====================================

/**
 * 클래스 전체 정보 뷰
 * - 자주 사용되는 조인 쿼리를 뷰로 최적화
 */
CREATE VIEW class_full_info AS
SELECT 
  t.id as template_id,
  t.title,
  t.description,
  t.category,
  t.level,
  t.instructor_id,
  t.studio_id,
  t.duration_minutes,
  t.price_per_session,
  t.max_capacity,
  
  -- 라이프사이클 정보
  t.recruitment_start_date,
  t.recruitment_end_date,
  t.class_start_date,
  t.class_end_date,
  t.status as class_status,
  
  -- 스케줄 그룹 정보
  sg.id as group_id,
  sg.group_name,
  sg.max_participants as group_max_participants,
  sg.price_per_session as group_price,
  
  -- 스케줄 정보
  s.id as schedule_id,
  s.day_of_week,
  s.start_time,
  s.end_time,
  
  -- 통계 정보
  COUNT(DISTINCT e.id) as total_enrollments,
  COUNT(DISTINCT CASE WHEN e.enrollment_status = 'confirmed' THEN e.id END) as confirmed_enrollments,
  COUNT(DISTINCT o.id) as total_occurrences,
  COUNT(DISTINCT CASE WHEN o.status = 'completed' THEN o.id END) as completed_occurrences
  
FROM class_templates t
LEFT JOIN class_schedule_groups sg ON t.id = sg.class_template_id
LEFT JOIN class_schedules s ON sg.id = s.schedule_group_id
LEFT JOIN class_enrollments e ON t.id = e.class_template_id AND sg.id = e.schedule_group_id
LEFT JOIN class_occurrences o ON t.id = o.class_template_id AND sg.id = o.schedule_group_id

WHERE t.is_active = true 
  AND (sg.is_active = true OR sg.is_active IS NULL)
  AND (s.is_active = true OR s.is_active IS NULL)

GROUP BY 
  t.id, t.title, t.description, t.category, t.level, t.instructor_id, t.studio_id,
  t.duration_minutes, t.price_per_session, t.max_capacity,
  t.recruitment_start_date, t.recruitment_end_date, t.class_start_date, t.class_end_date, t.status,
  sg.id, sg.group_name, sg.max_participants, sg.price_per_session,
  s.id, s.day_of_week, s.start_time, s.end_time;

/**
 * 다가오는 수업 뷰
 * - 예약 가능한 수업 목록 최적화
 */
CREATE VIEW upcoming_occurrences AS
SELECT 
  o.id as occurrence_id,
  o.occurrence_date,
  o.start_time,
  o.end_time,
  o.status,
  o.max_participants,
  o.confirmed_enrollments,
  o.attendance_count,
  
  -- 클래스 정보
  t.id as template_id,
  t.title as class_title,
  t.category,
  t.level,
  t.instructor_id,
  
  -- 그룹 정보
  sg.group_name,
  
  -- 스케줄 정보
  s.day_of_week,
  
  -- 계산 필드
  (o.max_participants - o.confirmed_enrollments) as available_spots,
  ROUND(o.confirmed_enrollments::decimal / o.max_participants * 100, 1) as occupancy_rate,
  
  -- 시간 정보
  EXTRACT(dow FROM o.occurrence_date) as day_of_week_num,
  o.occurrence_date || ' ' || o.start_time as occurrence_datetime

FROM class_occurrences o
JOIN class_templates t ON o.class_template_id = t.id
JOIN class_schedule_groups sg ON o.schedule_group_id = sg.id
JOIN class_schedules s ON o.class_schedule_id = s.id

WHERE o.occurrence_date >= CURRENT_DATE
  AND o.status IN ('scheduled', 'ongoing')
  AND t.is_active = true

ORDER BY o.occurrence_date, o.start_time;

/**
 * 수강생 출석 통계 뷰
 * - 출석률 및 수강 이력 분석
 */
CREATE VIEW member_attendance_stats AS
SELECT 
  e.member_id,
  e.class_template_id,
  e.schedule_group_id,
  
  -- 신청 정보
  e.enrollment_status,
  e.enrolled_at,
  
  -- 출석 통계
  COUNT(DISTINCT o.id) as total_classes,
  COUNT(DISTINCT a.id) as attended_classes,
  COUNT(DISTINCT CASE WHEN a.attendance_status = 'present' THEN a.id END) as present_count,
  COUNT(DISTINCT CASE WHEN a.attendance_status = 'late' THEN a.id END) as late_count,
  COUNT(DISTINCT CASE WHEN a.attendance_status = 'absent' THEN a.id END) as absent_count,
  COUNT(DISTINCT CASE WHEN a.attendance_status = 'excused' THEN a.id END) as excused_count,
  
  -- 비율 계산
  ROUND(
    COUNT(DISTINCT CASE WHEN a.attendance_status = 'present' THEN a.id END)::decimal / 
    NULLIF(COUNT(DISTINCT o.id), 0) * 100, 1
  ) as attendance_rate,
  
  -- 날짜 정보
  MIN(o.occurrence_date) as first_class_date,
  MAX(o.occurrence_date) as last_class_date

FROM class_enrollments e
JOIN class_occurrences o ON e.class_template_id = o.class_template_id 
  AND e.schedule_group_id = o.schedule_group_id
LEFT JOIN class_attendances a ON o.id = a.class_occurrence_id 
  AND e.member_id = a.member_id

WHERE e.enrollment_status = 'confirmed'
  AND o.status = 'completed'

GROUP BY e.member_id, e.class_template_id, e.schedule_group_id, 
         e.enrollment_status, e.enrolled_at;

-- ====================================
-- 트리거 함수 (자동 업데이트)
-- ====================================

/**
 * updated_at 자동 업데이트 함수
 */
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- updated_at 트리거 적용
CREATE TRIGGER update_members_updated_at BEFORE UPDATE ON members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_instructors_updated_at BEFORE UPDATE ON instructors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_instructor_certificates_updated_at BEFORE UPDATE ON instructor_certificates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_instructor_accounts_updated_at BEFORE UPDATE ON instructor_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_studios_updated_at BEFORE UPDATE ON studios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_class_templates_updated_at BEFORE UPDATE ON class_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_class_schedule_groups_updated_at BEFORE UPDATE ON class_schedule_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_class_schedules_updated_at BEFORE UPDATE ON class_schedules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_class_occurrences_updated_at BEFORE UPDATE ON class_occurrences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_class_enrollments_updated_at BEFORE UPDATE ON class_enrollments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ====================================
-- 샘플 데이터 (개발/테스트용)
-- ====================================

-- 샘플 회원 데이터
INSERT INTO members (id, name, phone, gender, role) VALUES
('11111111-1111-1111-1111-111111111111', '김강사', '010-1234-5678', 'MALE', 'INSTRUCTOR'),
('22222222-2222-2222-2222-222222222222', '박학생', '010-2345-6789', 'FEMALE', 'STUDENT'),
('33333333-3333-3333-3333-333333333333', '이트레이너', '010-3456-7890', 'MALE', 'INSTRUCTOR');

-- 샘플 강사 데이터
INSERT INTO instructors (id, member_id, short_bio, detailed_bio) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', '10년 경력 요가 강사', '요가 수련 15년, 지도 경력 10년의 전문 강사입니다.'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '33333333-3333-3333-3333-333333333333', '피트니스 전문 트레이너', 'NSCA 인증 트레이너로 체계적인 운동 프로그램을 제공합니다.');

-- 샘플 스튜디오 데이터
INSERT INTO studios (id, name, address, studio_type) VALUES
('dddddddd-dddd-dddd-dddd-dddddddddddd', '강남 요가 스튜디오', '서울시 강남구 테헤란로 123', 'yoga'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '홍대 피트니스 센터', '서울시 마포구 홍익로 456', 'fitness');

-- 샘플 클래스 템플릿 데이터
INSERT INTO class_templates (
  id, studio_id, instructor_id, title, description, category, specialty, level,
  duration_minutes, price_per_session, max_capacity,
  recruitment_start_date, recruitment_end_date, class_start_date, class_end_date, status
) VALUES (
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '하타 요가 기초',
  '요가 입문자를 위한 기초 하타 요가 클래스입니다.',
  'yoga',
  'YOGA',
  'beginner',
  60,
  25000.00,
  12,
  '2024-11-01',
  '2024-11-30',
  '2024-12-02',
  '2024-12-27',
  'recruiting'
);

-- 샘플 스케줄 그룹 데이터
INSERT INTO class_schedule_groups (id, class_template_id, group_name, sessions_per_week, max_participants) VALUES
('ffffffff-ffff-ffff-ffff-ffffffffffff', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '오후반', 2, 8),
('gggggggg-gggg-gggg-gggg-gggggggggggg', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '저녁반', 2, 8);

-- 샘플 스케줄 데이터
INSERT INTO class_schedules (id, schedule_group_id, day_of_week, start_time, end_time) VALUES
-- 오후반: 월/수 14:00-15:00
('hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 'MONDAY', '14:00:00', '15:00:00'),
('iiiiiiii-iiii-iiii-iiii-iiiiiiiiiiii', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 'WEDNESDAY', '14:00:00', '15:00:00'),
-- 저녁반: 월/수 19:00-20:00
('jjjjjjjj-jjjj-jjjj-jjjj-jjjjjjjjjjjj', 'gggggggg-gggg-gggg-gggg-gggggggggggg', 'MONDAY', '19:00:00', '20:00:00'),
('kkkkkkkk-kkkk-kkkk-kkkk-kkkkkkkkkkkk', 'gggggggg-gggg-gggg-gggg-gggggggggggg', 'WEDNESDAY', '19:00:00', '20:00:00');

-- ====================================
-- 스키마 완료 메시지
-- ====================================

SELECT 'Class Lifecycle Management Schema Created Successfully!' as message;