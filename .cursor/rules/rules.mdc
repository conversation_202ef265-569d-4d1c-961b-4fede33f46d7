---
alwaysApply: true
---
- Use Tailwindcss v4 for default styling.
- Use drizzle-orm for db queries.
- Base UI components is in `src/components/ui`.
- Read `package.json` to understand project dependencies and scripts
- Project knowledge is in `docs/`.
- Use `zustand` for state management
- Use `@tanstack/react-query` for client data fetching. Prefer suspense query over normal query.
- Prefer Server component data fetching over client side data fetching.
- Hybrid data fetching: Set up initial data from server component and pass it to client component.
- Set up proper loading skeleton UI for loading feedback.
- Set up proper Error Boundary.
- Use `zod` for request, response validation.
- Avoid redundancy.
- Prefer colocation