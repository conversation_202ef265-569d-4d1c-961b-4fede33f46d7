<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>이미지 업로드 테스트</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        input, select, button {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .debug {
            background-color: #e2e3e5;
            border: 1px solid #d6d8db;
            color: #383d41;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .image-preview {
            margin-top: 20px;
            text-align: center;
        }
        .image-preview img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .upload-progress {
            margin-top: 20px;
            display: none;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .file-status {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
        }
        .file-status.pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .file-status.uploading {
            background-color: #cce5ff;
            color: #004085;
        }
        .file-status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .file-status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .auth-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 이미지 업로드 테스트</h1>
        
        <div class="auth-info">
            <strong>⚠️ 주의사항:</strong> 
            먼저 <a href="http://localhost:3000/partner/login" target="_blank">파트너 로그인</a>을 완료한 후 테스트하세요.
            <br>
            현재 인증 상태: <span id="authStatus">확인 중...</span>
        </div>

        <form id="uploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">📁 이미지 파일 선택 *</label>
                <input type="file" id="file" name="files" accept="image/*" multiple required>
                <small style="color: #666;">지원 형식: JPG, PNG, WEBP (최대 5MB, 최대 10개 파일)</small>
                <div id="filePreview" style="margin-top: 10px; display: none;">
                    <strong>선택된 파일:</strong>
                    <ul id="fileList" style="margin: 5px 0; padding-left: 20px;"></ul>
                </div>
            </div>

            <div class="form-group">
                <label for="type">📂 업로드 타입 *</label>
                <select id="type" name="type" required>
                    <option value="studio">Studio (스튜디오)</option>
                    <option value="instructor">Instructor (강사)</option>
                    <option value="class">Class (클래스)</option>
                </select>
            </div>

            <div class="form-group" id="studioIdGroup">
                <label for="studioId">🏢 스튜디오 ID</label>
                <input type="text" id="studioId" name="studioId" value="8d5cadcd-be18-4134-ab70-5a28002e1ee1">
                <small style="color: #666;">Studio 타입일 때 필수</small>
            </div>

            <div class="form-group">
                <label for="prefix">🏷️ 이미지 종류</label>
                <select id="prefix" name="prefix">
                    <option value="featured">Featured (대표 이미지)</option>
                    <option value="gallery">Gallery (갤러리 이미지)</option>
                    <option value="image">Image (일반 이미지)</option>
                </select>
            </div>

            <button type="submit" id="submitBtn">
                🚀 업로드 시작
            </button>
        </form>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>업로드 중...</p>
        </div>

        <div class="upload-progress" id="uploadProgress">
            <h3>📊 업로드 진행 상황</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <p id="progressText">0/0 파일 완료</p>
            <div id="fileStatusList"></div>
        </div>

        <div class="result" id="result"></div>
    </div>

    <script>
        // 인증 상태 확인
        function checkAuthStatus() {
            const cookies = document.cookie.split(';');
            const authCookie = cookies.find(cookie => 
                cookie.trim().startsWith('sb-dxodiiizyfzpueyvoaqr-auth-token=')
            );
            
            const authStatus = document.getElementById('authStatus');
            if (authCookie && authCookie.includes('base64-')) {
                authStatus.textContent = '✅ 로그인됨';
                authStatus.style.color = '#155724';
            } else {
                authStatus.textContent = '❌ 로그인 필요';
                authStatus.style.color = '#721c24';
            }
        }

        // 파일 선택 시 미리보기
        document.getElementById('file').addEventListener('change', function() {
            const filePreview = document.getElementById('filePreview');
            const fileList = document.getElementById('fileList');
            const files = this.files;
            
            if (files.length > 0) {
                fileList.innerHTML = '';
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const li = document.createElement('li');
                    li.innerHTML = `
                        <strong>${file.name}</strong> 
                        (${(file.size / 1024 / 1024).toFixed(2)} MB, ${file.type})
                        ${i === 0 ? '<span style="color: #007bff;">[대표 이미지]</span>' : '<span style="color: #666;">[갤러리]</span>'}
                    `;
                    fileList.appendChild(li);
                }
                filePreview.style.display = 'block';
            } else {
                filePreview.style.display = 'none';
            }
        });

        // 타입 선택에 따른 필드 표시/숨김
        document.getElementById('type').addEventListener('change', function() {
            const studioIdGroup = document.getElementById('studioIdGroup');
            const studioIdInput = document.getElementById('studioId');
            
            if (this.value === 'studio') {
                studioIdGroup.style.display = 'block';
                studioIdInput.required = true;
            } else {
                studioIdGroup.style.display = 'none';
                studioIdInput.required = false;
            }
        });

        // 다중 파일 일괄 업로드 처리 (개선됨)
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const uploadProgress = document.getElementById('uploadProgress');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const fileStatusList = document.getElementById('fileStatusList');
            const result = document.getElementById('result');
            
            const fileInput = document.getElementById('file');
            const files = Array.from(fileInput.files);
            const type = document.getElementById('type').value;
            const studioId = document.getElementById('studioId').value;
            
            if (files.length === 0) {
                alert('파일을 선택해주세요.');
                return;
            }
            
            // UI 상태 변경
            submitBtn.disabled = true;
            uploadProgress.style.display = 'block';
            result.style.display = 'none';
            
            // 파일 상태 초기화
            fileStatusList.innerHTML = '';
            files.forEach((file, index) => {
                const statusDiv = document.createElement('div');
                statusDiv.className = 'file-status pending';
                statusDiv.id = `file-status-${index}`;
                statusDiv.innerHTML = `
                    📄 ${file.name} 
                    ${index === 0 ? '<span style="color: #007bff;">[대표]</span>' : '<span style="color: #666;">[갤러리]</span>'}
                    - <span class="status-text">대기 중...</span>
                `;
                fileStatusList.appendChild(statusDiv);
            });
            
            console.log('=== 일괄 다중 파일 업로드 시작 ===');
            console.log(`총 ${files.length}개 파일 일괄 업로드`);
            
            // 진행률을 업로드 중으로 설정
            progressFill.style.width = '50%';
            progressText.textContent = '서버에서 처리 중...';
            
            // 모든 파일 상태를 업로드 중으로 변경
            files.forEach((file, index) => {
                const statusDiv = document.getElementById(`file-status-${index}`);
                const statusText = statusDiv.querySelector('.status-text');
                statusDiv.className = 'file-status uploading';
                statusText.textContent = '업로드 중...';
            });
            
            try {
                // 모든 파일을 하나의 FormData에 담아 일괄 전송
                const formData = new FormData();
                
                // 다중 파일 추가
                files.forEach((file, index) => {
                    formData.append('files', file);
                });
                
                // 기타 필드 추가
                formData.append('type', type);
                if (type === 'studio') {
                    formData.append('studioId', studioId);
                }
                formData.append('prefix', 'featured'); // 서버에서 첫 번째는 featured, 나머지는 gallery로 처리
                
                console.log('📦 일괄 업로드 요청 전송...');
                
                // 단일 API 호출로 모든 파일 처리
                const response = await fetch('/api/partner/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const responseData = await response.json();
                
                console.log('📥 서버 응답:', responseData);
                
                // 진행률 완료로 설정
                progressFill.style.width = '100%';
                progressText.textContent = `${files.length}/${files.length} 파일 처리 완료`;
                
                if (response.ok) {
                    // 단일 파일 응답 처리 (기존 호환성)
                    if (responseData.url && files.length === 1) {
                        const statusDiv = document.getElementById('file-status-0');
                        const statusText = statusDiv.querySelector('.status-text');
                        statusDiv.className = 'file-status success';
                        statusText.innerHTML = `✅ 성공 - <a href="${responseData.url}" target="_blank">보기</a>`;
                        
                        // 결과 표시
                        result.style.display = 'block';
                        result.className = 'result success';
                        result.innerHTML = `
                            <h3>✅ 업로드 성공!</h3>
                            <p><strong>이미지 URL:</strong> <a href="${responseData.url}" target="_blank">${responseData.url}</a></p>
                            <div class="image-preview">
                                <img src="${responseData.url}" alt="업로드된 이미지" style="max-width: 300px;" onerror="this.style.display='none'">
                            </div>
                        `;
                        return;
                    }
                    
                    // 다중 파일 응답 처리
                    if (responseData.results) {
                        responseData.results.forEach((fileResult, index) => {
                            const statusDiv = document.getElementById(`file-status-${index}`);
                            const statusText = statusDiv.querySelector('.status-text');
                            
                            if (fileResult.success) {
                                statusDiv.className = 'file-status success';
                                statusText.innerHTML = `✅ 성공 - <a href="${fileResult.url}" target="_blank">보기</a>`;
                            } else {
                                statusDiv.className = 'file-status error';
                                statusText.textContent = `❌ 실패: ${fileResult.error || '알 수 없는 오류'}`;
                            }
                        });
                        
                        // 전체 결과 표시
                        const summary = responseData.summary;
                        result.style.display = 'block';
                        
                        if (summary.failed === 0) {
                            result.className = 'result success';
                            result.innerHTML = `
                                <h3>🎉 모든 파일 업로드 성공!</h3>
                                <p><strong>성공:</strong> ${summary.uploaded}개 파일</p>
                                <div class="image-preview">
                                    ${responseData.results.filter(r => r.success).slice(0, 3).map(r => 
                                        `<img src="${r.url}" alt="업로드된 이미지" style="width: 150px; height: 150px; object-fit: cover; margin: 5px; border-radius: 4px;" onerror="this.style.display='none'">`
                                    ).join('')}
                                    ${summary.uploaded > 3 ? `<p>... 외 ${summary.uploaded - 3}개</p>` : ''}
                                </div>
                            `;
                        } else if (summary.uploaded === 0) {
                            result.className = 'result error';
                            result.innerHTML = `
                                <h3>❌ 모든 파일 업로드 실패</h3>
                                <p><strong>실패:</strong> ${summary.failed}개 파일</p>
                                <p>위의 개별 파일 상태를 확인해주세요.</p>
                            `;
                        } else {
                            result.className = 'result';
                            result.style.backgroundColor = '#fff3cd';
                            result.style.borderColor = '#ffeaa7';
                            result.style.color = '#856404';
                            result.innerHTML = `
                                <h3>⚠️ 부분 업로드 완료</h3>
                                <p><strong>성공:</strong> ${summary.uploaded}개 파일</p>
                                <p><strong>실패:</strong> ${summary.failed}개 파일</p>
                                <p>실패한 파일들을 다시 시도해보세요.</p>
                            `;
                        }
                    }
                } else {
                    // 전체 요청 실패
                    files.forEach((file, index) => {
                        const statusDiv = document.getElementById(`file-status-${index}`);
                        const statusText = statusDiv.querySelector('.status-text');
                        statusDiv.className = 'file-status error';
                        statusText.textContent = `❌ 요청 실패: ${responseData.error || '서버 오류'}`;
                    });
                    
                    result.style.display = 'block';
                    result.className = 'result error';
                    result.innerHTML = `
                        <h3>❌ 업로드 요청 실패</h3>
                        <p><strong>에러:</strong> ${responseData.error || '알 수 없는 오류'}</p>
                        ${responseData.details ? `<p><strong>상세:</strong> ${responseData.details}</p>` : ''}
                        ${responseData.solution ? `<p><strong>해결방법:</strong> ${responseData.solution}</p>` : ''}
                    `;
                }
                
                console.log('=== 일괄 업로드 완료 ===');
                console.log('서버 응답:', responseData);
                
            } catch (error) {
                console.error('업로드 에러:', error);
                
                // 모든 파일을 에러 상태로 변경
                files.forEach((file, index) => {
                    const statusDiv = document.getElementById(`file-status-${index}`);
                    const statusText = statusDiv.querySelector('.status-text');
                    statusDiv.className = 'file-status error';
                    statusText.textContent = `❌ 네트워크 오류: ${error.message}`;
                });
                
                result.style.display = 'block';
                result.className = 'result error';
                result.innerHTML = `
                    <h3>❌ 네트워크 오류</h3>
                    <p>${error.message}</p>
                `;
            } finally {
                // UI 상태 복원
                submitBtn.disabled = false;
            }
        });

        // 페이지 로드 시 인증 상태 확인
        checkAuthStatus();
        
        // 초기 필드 상태 설정
        document.getElementById('type').dispatchEvent(new Event('change'));
    </script>
</body>
</html>